<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
return [
    // 指令定义
    'commands' => [
        'app' => command\App::class, // 生成应用层
        'model' => command\Table::class, // 生成模型层
        'logic' => command\Logic::class, // 生成逻辑层
        'gsf' => command\GenerateSystemFields::class, // 生成系统字段
        'crontab' => 'app\command\Crontab',
        'bug:regenerate-statistics' => 'app\command\RegenerateBugStatisticsCommand',
    ],
];
