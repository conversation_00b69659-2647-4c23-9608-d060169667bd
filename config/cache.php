<?php

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

use think\facade\Env;

return [
    // 默认缓存驱动
    // 勿改，默认使用 file 方式，生产环境部署时请求不到 阿里redis
    'default' => env('cache.driver', 'file'),

    // 缓存连接方式配置
    'stores' => [
        'file' => [
            // 驱动方式
            'type' => 'File',
            // 缓存保存目录
            'path' => '',
            // 缓存前缀
            'prefix' => '',
            // 缓存有效期 0表示永久缓存
            'expire' => 0,
            // 缓存标签前缀
            'tag_prefix' => 'tag:',
            // 序列化机制 例如 ['serialize', 'unserialize']
            'serialize' => [],
        ],
        // 更多的缓存连接
        'redis' => [
            // 驱动方式
            'type' => 'redis',
            'host' => Env::get('redis.host'),
            'password' => Env::get('redis.password'),
            'port' => Env::get('redis.port'),
            'select' => Env::get('redis.select', 0),
            'serialize' => [
                function ($data) {
                    return $data;
                },
                function ($data) {
                    return $data;
                },
            ]
        ]
    ],

];
