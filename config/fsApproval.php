<?php
/**
 * Desc: 飞书审批相关配置
 * User: Plum
 * Date: 2025/5/20
 */

return [
    'fs_app_id' => '',//飞书应用id - 用于处理飞书免登【测试企业】
    'domain'    => env('FS_CONFIG.domain', ''),//请求域名 - uat|dev|test
    //        'domain'=>'https://fs.shijizhongyun.com',//请求域名 - prod
    'urls'      => [
        'definition'  => '/audit/tripartite/definition',//审批定义
        'example'     => '/audit/tripartite_task/example',//同步审批实例
        'syncExample' => '/audit/tripartite_task/syncExample',//更新实例状态
        'nodeTask'    => '/audit/tripartite_task/nodeTask',//同步实例任务
        'syncTask'    => '/audit/tripartite_task/syncTask',//更新任务状态
        'sendBot'     => '/audit/tripartite_task/sendBotMsg',//发送飞书审批bot消息
    ],

    'pc_url'     => env('FS_CONFIG.pc_url', '').'=',// 飞书审批详情地址
    'mobile_url' => env('FS_CONFIG.mobile_url', '').'=',//飞书审批详情地址
];
