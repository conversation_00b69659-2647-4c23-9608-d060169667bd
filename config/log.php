<?php

// +----------------------------------------------------------------------
// | 日志设置
// +----------------------------------------------------------------------
return [
    // 默认日志记录通道
    'default' => env('log.channel', 'file'),
    // 日志记录级别
    'level' => ['debug', 'info', 'notice', 'warning', 'error', 'critical', 'alert', 'emergency', 'sql'],
    // 日志类型记录的通道 ['error'=>'email',...]
    'type_channel' => [],
    // 关闭全局日志写入
    'close' => false,
    // 全局日志处理 支持闭包
    'processor' => null,

    // 日志通道列表
    'channels' => [
        'file' => [
            // 日志记录方式
            'type' => 'system\Log',
            // 日志保存目录
            'path' => '',
            // 单文件日志写入
            'single' => false,
            // 独立日志级别
            'apart_level' => [],
            // 最大日志文件数量
            'max_files' => 20,
            // 使用JSON格式记录
            'json' => false,
            // 日志处理
            'processor' => null,
            // 关闭通道日志写入
            'close' => false,
            // 日志输出格式化
            'format' => '[%s][%s] %s %s',
            // 是否实时写入
            'realtime_write' => false,
            'file_size' => 1024 * 1024 * 8,
        ],
        // 其它日志通道配置
        'platform' => [
            // 日志记录方式
            'type' => 'system\Log',
            // 日志保存目录
            'path' => runtime_path() . 'platform' . DIRECTORY_SEPARATOR . 'log',
            // 单文件日志写入
            'single' => false,
            // 独立日志级别
            'apart_level' => ['sql', 'debug', 'info', 'notice', 'warning', 'error', 'critical', 'alert', 'emergency'],
            'file_size' => 1024 * 1024 * 8,
            // 最大日志文件数量
            'max_files' => 20,
            // 使用JSON格式记录
            'json' => false,
            // 日志处理
            'processor' => null,
            // 关闭通道日志写入
            'close' => false,
            // 日志输出格式化
            'format' => '[%s][%s] %s %s',
            // 是否实时写入
            'realtime_write' => false,
        ],

    ],

];
