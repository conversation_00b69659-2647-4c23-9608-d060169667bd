<?php
/**
 * 测试分组值为0的处理逻辑
 * 
 * 这个测试文件用于验证分组值为0的数据是否正确归类到"无分组"
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\work_items\logic\WorkItemsEsLogic;
use app\work_items\logic\WorkItemsLogic;
use app\work_items\pipeline\processors\DataFetchingProcessor;
use app\work_items\pipeline\processors\GroupedDataProcessingProcessor;

class GroupZeroHandlingTest
{
    /**
     * 测试 WorkItemsEsLogic 的 isDefaultGroup 方法
     */
    public function testIsDefaultGroup()
    {
        $esLogic = WorkItemsEsLogic::getInstance();
        
        // 使用反射来访问私有方法
        $reflection = new ReflectionClass($esLogic);
        $method = $reflection->getMethod('isDefaultGroup');
        $method->setAccessible(true);
        
        // 测试各种值
        $testCases = [
            ['-1', true, 'ES missing key should be default group'],
            ['_default_group_', true, 'Default group key should be default group'],
            ['0', true, 'String zero should be default group'],
            [0, true, 'Integer zero should be default group'],
            ['1', false, 'String one should not be default group'],
            [1, false, 'Integer one should not be default group'],
            ['test', false, 'Regular string should not be default group'],
            [null, false, 'Null should not be default group'],
            ['', false, 'Empty string should not be default group'],
        ];
        
        foreach ($testCases as [$value, $expected, $message]) {
            $result = $method->invoke($esLogic, $value);
            if ($result === $expected) {
                echo "✓ PASS: {$message}\n";
            } else {
                echo "✗ FAIL: {$message} - Expected " . ($expected ? 'true' : 'false') . ", got " . ($result ? 'true' : 'false') . "\n";
            }
        }
    }
    
    /**
     * 测试 WorkItemsLogic 的 getGroupDisplayLabel 方法
     */
    public function testGetGroupDisplayLabel()
    {
        $workItemsLogic = new WorkItemsLogic();
        
        // 使用反射来访问私有方法
        $reflection = new ReflectionClass($workItemsLogic);
        $method = $reflection->getMethod('getGroupDisplayLabel');
        $method->setAccessible(true);
        
        $projectIds = [1];
        $groupByField = 'test_field';
        
        // 测试各种值
        $testCases = [
            ['-1', '--', 'ES missing key should display as --'],
            ['_default_group_', '--', 'Default group key should display as --'],
            ['0', '--', 'String zero should display as --'],
            [0, '--', 'Integer zero should display as --'],
            ['1', '1', 'String one should display as itself'],
            [1, '1', 'Integer one should display as string'],
            ['test', 'test', 'Regular string should display as itself'],
        ];
        
        foreach ($testCases as [$value, $expected, $message]) {
            $result = $method->invoke($workItemsLogic, $value, $groupByField, $projectIds);
            if ($result === $expected) {
                echo "✓ PASS: {$message}\n";
            } else {
                echo "✗ FAIL: {$message} - Expected '{$expected}', got '{$result}'\n";
            }
        }
    }
    
    /**
     * 测试分组数据处理逻辑
     */
    public function testGroupDataProcessing()
    {
        // 模拟 ES 返回的分组数据
        $mockEsResponse = [
            'aggregations' => [
                'group_by_field_agg' => [
                    'buckets' => [
                        [
                            'key' => '-1',
                            'doc_count' => 5,
                            'docs_in_group' => [
                                'hits' => [
                                    'hits' => [
                                        ['_source' => ['cnt_id' => 1, 'title' => 'Task 1']],
                                        ['_source' => ['cnt_id' => 2, 'title' => 'Task 2']],
                                    ]
                                ]
                            ]
                        ],
                        [
                            'key' => '0',
                            'doc_count' => 3,
                            'docs_in_group' => [
                                'hits' => [
                                    'hits' => [
                                        ['_source' => ['cnt_id' => 3, 'title' => 'Task 3']],
                                    ]
                                ]
                            ]
                        ],
                        [
                            'key' => '1',
                            'doc_count' => 2,
                            'docs_in_group' => [
                                'hits' => [
                                    'hits' => [
                                        ['_source' => ['cnt_id' => 4, 'title' => 'Task 4']],
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        
        echo "\n=== 测试分组数据处理 ===\n";
        echo "模拟 ES 返回数据包含三个分组：-1（missing）、0（零值）、1（正常值）\n";
        echo "预期结果：-1 和 0 应该合并到默认分组，1 保持独立分组\n\n";
        
        // 这里可以添加更多的测试逻辑来验证分组处理
        echo "✓ 测试数据结构准备完成\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "=== 开始测试分组值为0的处理逻辑 ===\n\n";
        
        echo "1. 测试 isDefaultGroup 方法:\n";
        $this->testIsDefaultGroup();
        
        echo "\n2. 测试 getGroupDisplayLabel 方法:\n";
        $this->testGetGroupDisplayLabel();
        
        echo "\n3. 测试分组数据处理:\n";
        $this->testGroupDataProcessing();
        
        echo "\n=== 测试完成 ===\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new GroupZeroHandlingTest();
    $test->runAllTests();
}
