<?php
/**
 * Desc 路由管理（无需授权）
 * User Long
 * Date 2024/7/2
 */

use middleware\AuthTokenMiddleware;
use middleware\SaveUrlParams;
use middleware\TrimMiddleware;
use think\facade\Route;

// 用户模块
Route::post('user/login', '\app\user\controller\Login@login')->name('login'); // 中台登录

// 公共
Route::get('oss/sign', '\app\infrastructure\controller\Oss@sign'); // oss 访问签名
Route::get('es/create', '\app\iterate\controller\Test@create');

Route::group(function () {
    //公共
    Route::get('oss/policy', '\app\infrastructure\controller\Oss@policy'); // oss 直传签名
    Route::get('enum/get', '\app\infrastructure\controller\Enum@get'); //枚举
    Route::get('permissions', '\app\infrastructure\controller\Permissions@permissions'); // 获取按钮权限

    // 通用字段配置
    Route::get('fieldConfig/detail', '\app\infrastructure\controller\FieldConfig@detail');
    Route::get('fieldConfig/pageQuery', '\app\infrastructure\controller\FieldConfig@pageQuery');
    Route::get('fieldConfig/getListGroupByModuleId', '\app\infrastructure\controller\FieldConfig@getListGroupByModuleId');
    Route::post('fieldConfig/getListByFieldName', '\app\infrastructure\controller\FieldConfig@getListByFieldName');
    Route::get('fieldConfig/getFixedList', '\app\infrastructure\controller\FieldConfig@getFixedList');
    Route::get('fieldConfig/getListBySubKey', '\app\infrastructure\controller\FieldConfig@getListBySubKey');
    Route::post('fieldConfig/getDemandTaskFlawSummaryFieldList', '\app\infrastructure\controller\FieldConfig@getDemandTaskFlawSummaryFieldList');
    Route::post('fieldConfig/getDemandTaskFlawSummaryDeduplicationFieldList', '\app\infrastructure\controller\FieldConfig@getDemandTaskFlawSummaryDeduplicationFieldList');
    Route::post('fieldConfig/getCopyToSelector', '\app\infrastructure\controller\FieldConfig@getCopyToSelector'); // 获取复制至项目/模板数据
    Route::post('fieldConfig/rename', '\app\infrastructure\controller\FieldConfig@rename'); // 更新字段名称

    // 表头设置
    Route::post('customerTableConfig/save', '\app\infrastructure\controller\CustomerTableConfig@save');
    Route::post('customerTableConfig/get', '\app\infrastructure\controller\CustomerTableConfig@get');

    //通用模板
    Route::get('template/detail', '\app\infrastructure\controller\Template@detail');
    Route::get('template/defaultTempDetail', '\app\infrastructure\controller\Template@defaultTempDetail');
    Route::get('template/pageQuery', '\app\infrastructure\controller\Template@pageQuery');
    Route::post('template/enable', '\app\infrastructure\controller\Template@enable');
    Route::post('template/disable', '\app\infrastructure\controller\Template@disable');
    Route::post('template/rename', '\app\infrastructure\controller\Template@rename'); // 更新模板名称

    //终端
    Route::get('client/detail', '\app\client\controller\Client@detail');
    Route::get('client/pageQuery', '\app\client\controller\Client@pageQuery');
    Route::post('client/selector', '\app\client\controller\Client@selector'); // 终端 - 下拉框数据
    Route::post('client/selectorByProduct', '\app\client\controller\Client@selectorByProduct'); // 项目产品终端 - 下拉框数据

    //微服务
    Route::get('microservice/detail', '\app\microservice\controller\Microservice@detail');
    Route::get('microservice/pageQuery', '\app\microservice\controller\Microservice@pageQuery');
    Route::post('microservice/selector', '\app\microservice\controller\Microservice@selector'); // 微服务 - 下拉框数据
    Route::post('microservice/selectorByProduct', '\app\microservice\controller\Microservice@selectorByProduct'); // 项目产品微服务 - 下拉框数据

    // 项目 类别管理
    Route::get('project/category/detail', '\app\project\controller\Category@detail'); // 详情
    Route::get('project/category/listQuery', '\app\project\controller\Category@listQuery'); // 列表
    Route::post('project/category/updateSort', '\app\project\controller\Category@updateSort'); // 更新排序
    Route::post('project/category/selector', '\app\project\controller\Category@selector'); // 需求类别下拉数据
    Route::post('project/category/selectorAndFlowStatus', '\app\project\controller\Category@selectorAndFlowStatus'); // 需求类别下拉数据（带工作流状态）
    Route::get('project/category/getRelevanceDemand', '\app\project\controller\Category@getRelevanceDemand'); // 获取相关需求id集合
    Route::post('project/category/updateRelevanceDemand', '\app\project\controller\Category@updateRelevanceDemand'); // 修改相关需求
    Route::post('project/category/getCopyToSelector', '\app\project\controller\Category@getCopyToSelector'); // 获取复制至项目/模板数据
    Route::post('project/category/rename', '\app\project\controller\Category@rename'); // 更新分类名称

    // 项目 工作流管理 - 状态库管理
    Route::post('project/flowStatusEnum/create', '\app\project\controller\FlowStatusEnum@create'); // 新增
    Route::post('project/flowStatusEnum/update', '\app\project\controller\FlowStatusEnum@update'); // 更新
    Route::post('project/flowStatusEnum/delete', '\app\project\controller\FlowStatusEnum@delete'); // 删除
    Route::get('project/flowStatusEnum/listQuery', '\app\project\controller\FlowStatusEnum@listQuery'); // 列表
    Route::post('project/flowStatusEnum/selectorEnumStatus', '\app\project\controller\FlowStatusEnum@selectorEnumStatus'); // 状态库下拉数据

    // 项目 工作流管理
    Route::get('project/flowStatus/detail', '\app\project\controller\FlowStatus@detail'); // 详情
    Route::get('project/flowStatus/listQuery', '\app\project\controller\FlowStatus@listQuery'); // 列表
    Route::get('project/flowStatus/selectorStatusType', '\app\project\controller\FlowStatus@selectorStatusType'); // 状态类型下拉数据
    Route::get('project/flowStatus/selectorFlowStatus', '\app\project\controller\FlowStatus@selectorFlowStatus'); // 获取工作流下拉数据
    Route::get('project/flowStatus/getRelevanceDemand', '\app\project\controller\FlowStatus@getRelevanceDemand'); // 获取相关需求id集合
    Route::get('project/flowStatus/getTargetStatusEnum', '\app\project\controller\FlowStatus@getTargetStatusEnum'); // 获取可流转状态
    Route::post('project/flowStatus/rename', '\app\project\controller\FlowStatus@rename'); // 更新工作流名称

    //产品
    Route::get('product/detail', '\app\product\controller\Product@detail'); // 详情
    Route::get('product/pageQuery', '\app\product\controller\Product@pageQuery'); // 列表
    Route::get('product/getProductSelector', '\app\product\controller\Product@getProductSelector'); // 产品下拉数据

    //工作流程图
    Route::get('iterate/workflowDiagram/detail', '\app\iterate\controller\FlowProcess@detail'); // 详情
    Route::get('iterate/workflowDiagram/pageQuery', '\app\iterate\controller\FlowProcess@pageQuery'); // 列表
    Route::get('iterate/workflowDiagram/selector', '\app\iterate\controller\FlowProcess@selector'); // 流程下拉数据
    Route::post('iterate/workflowDiagram/nodeSelector', '\app\iterate\controller\FlowProcess@nodeSelector'); // 流程节点下拉数据
    Route::get('iterate/flowProcessAuto/config', '\app\iterate\controller\FlowProcessAuto@config'); // 工作流程图 节点自动化校验配置
    Route::post('iterate/flowProcess/rename', '\app\iterate\controller\FlowProcess@rename'); // 更新流程名称

    //工作项
    Route::get('workItems/detail', '\app\work_items\controller\WorkItems@detail'); // 详情
    Route::post('workItems/pageQuery', '\app\work_items\controller\WorkItems@pageQuery'); // 分页查询
    Route::post('workItems/statusTransfer', '\app\work_items\controller\WorkItems@statusTransfer'); // 状态流转
    Route::post('workItems/associateTasks', '\app\work_items\controller\WorkItems@associateTasks'); // 关联任务
    Route::get('workItems/hasChildren', '\app\work_items\controller\WorkItems@hasChildren'); // 判断工作项是否是子集
    Route::post('workItems/demandSelector', '\app\work_items\controller\WorkItems@demandSelector'); // 需求下拉
    Route::post('workItems/taskDemandSelector', '\app\work_items\controller\WorkItems@taskDemandSelector'); // 任务需求下拉
    Route::get('workItems/testCaseDemandSelector', '\app\work_items\controller\WorkItems@testCaseDemandSelector'); // 测试用例需求下拉
    Route::post('workItems/getTaskStatusList', '\app\work_items\controller\WorkItems@getTaskStatusList'); // 需求下拉
    Route::post('workItems/pageQuerySummary', '\app\work_items\controller\WorkItems@pageQuerySummary'); // 需求下拉
    Route::post('workItems/pageQuerySummaryGroupUser', '\app\work_items\controller\WorkItems@pageQuerySummaryGroupUser'); // 需求下拉
    Route::post('workItems/dragToIteration', '\app\work_items\controller\WorkItems@dragToIteration'); // 需求下拉
    Route::get('workItems/countGroupCntType', '\app\work_items\controller\WorkItems@countGroupCntType'); // 需求下拉
    Route::get('workItems/workItemBurndownChart', '\app\work_items\controller\WorkItems@workItemBurndownChart'); // 需求下拉
    Route::post('workItems/laborHoursBurndownChart', '\app\work_items\controller\WorkItems@laborHoursBurndownChart'); // 需求下拉
    Route::post('workItems/manHourBurnReport', '\app\work_items\controller\WorkItems@manHourBurnReport'); // 需求下拉
    Route::post('workItems/workResourceReports', '\app\work_items\controller\WorkItems@workResourceReports'); // 需求下拉

    Route::post('workItems/getExportHeaderTemplate', '\app\work_items\controller\ImportAndExport@getExportHeaderTemplate'); // 需求下拉
    Route::post('workItems/export', '\app\work_items\controller\ImportAndExport@export'); // 需求下拉
    Route::post('workItems/import', '\app\work_items\controller\ImportAndExport@import'); // 需求下拉

    Route::post('workItems/hasChild', '\app\work_items\controller\WorkItems@hasChild'); // 需求是否含有子需求子任务，供需求批量删除使用
    Route::post('workItems/pageQueryMyJob', '\app\work_items\controller\WorkItems@pageQueryMyJob'); // 工作台分页
    Route::post('workItems/getMyJobCount', '\app\work_items\controller\WorkItems@getMyJobCount'); // 工作台工作项数量统计

    Route::post('workItems/bugScheduledTasks', '\app\work_items\controller\WorkItems@bugScheduledTasks'); // bug定时任务

    Route::post('workItems/queryUserWorkHoursByDateRange', '\app\work_items\controller\WorkItems@queryUserWorkHoursByDateRange'); // 查询用户在日期范围内的工时
    Route::post('workItems/exportUserWorkHoursReport', '\app\work_items\controller\WorkItems@exportUserWorkHoursReport'); // 导出用户工时报告Excel
    Route::post('workItems/regenerateStatisticsByDateRange', '\app\work_items\controller\WorkItems@regenerateStatisticsByDateRange'); // API：修正历史错误的Bug统计数据


    //测试用例
    Route::get('testCase/detail', '\app\work_items\controller\TestCase@detail'); // 详情
    Route::post('testCase/pageQuery', '\app\work_items\controller\TestCase@pageQuery'); // 分页查询
    Route::post('testCase/withCategoryTree', '\app\work_items\controller\TestCase@withCategoryTree'); // 分页查询

    //需求<->测试用例
    Route::post('testCaseWorkItems/relevancy', '\app\work_items\controller\TestCaseWork@relevancy'); // 关联
    Route::post('testCaseWorkItems/del', '\app\work_items\controller\TestCaseWork@del'); // 解除关联

    //测试计划
    Route::post('testPlan/pageQuery', '\app\work_items\controller\TestPlan@pageQuery'); // 分页查询
    Route::get('testPlan/detail', '\app\work_items\controller\TestPlan@detail'); // 详情
    Route::post('testPlan/getStatusList', '\app\work_items\controller\TestPlan@getStatusList'); // 分页查询
    Route::post('testPlan/getPlanTypeList', '\app\work_items\controller\TestPlan@getPlanTypeList'); // 计划类型

    //测试计划 规划与执行
    Route::post('planUseCase/delRecordBug', '\app\work_items\controller\PlanUseCase@delRecordBug');
    Route::post('planUseCase/recordBugList', '\app\work_items\controller\PlanUseCase@recordBugList');
    Route::post('planUseCase/statistics', '\app\work_items\controller\PlanUseCase@statistics');
    Route::get('planUseCase/flawCaseList', '\app\work_items\controller\PlanUseCase@flawCaseList');
    Route::get('planUseCase/caseFlawList', '\app\work_items\controller\PlanUseCase@caseFlawList');
    Route::get('planUseCase/planFlawList', '\app\work_items\controller\PlanUseCase@planFlawList');
    Route::get('planUseCase/recordResultSelector', '\app\work_items\controller\PlanUseCase@recordResultSelector');
    Route::post('planUseCase/recordResultAddNotExecutedSelector', '\app\work_items\controller\PlanUseCase@recordResultAddNotExecutedSelector');
    Route::get('planUseCase/getCaseIdListByPlanId', '\app\work_items\controller\PlanUseCase@getCaseIdListByPlanId');

    //工时
    Route::post('workItems/workHours/create', '\app\work_items\controller\WorkHours@create'); // 创建
    Route::post('workItems/workHours/update', '\app\work_items\controller\WorkHours@update'); // 修改
    Route::get('workItems/workHours/detail', '\app\work_items\controller\WorkHours@detail'); // 详情
    Route::post('workItems/workHours/delete', '\app\work_items\controller\WorkHours@delete'); // 删除
    Route::get('workItems/workHours/list', '\app\work_items\controller\WorkHours@list'); // 分页查询
    Route::get('workItems/workHours/detailByCntId', '\app\work_items\controller\WorkHours@detailByCntId'); // 分页查询

    // 分类
    // 工作项分类管理
    Route::get('workItems/classify/detail', '\app\work_items\controller\Classify@detail'); // 详情
    Route::get('workItems/classify/listQuery', '\app\work_items\controller\Classify@listQuery'); // 列表
    Route::get('workItems/classify/getRelevanceDemand', '\app\work_items\controller\Classify@getRelevanceDemand'); // 获取相关需求id集合
    Route::post('workItems/classify/updateRelevanceDemand', '\app\work_items\controller\Classify@updateRelevanceDemand'); // 修改相关需求
    Route::post('workItems/classify/categorySelector', '\app\work_items\controller\Classify@categorySelector'); // 分类下拉数据

    // 评论
    Route::post('workItems/comment/create', '\app\work_items\controller\Comment@create'); // 新增
    Route::post('workItems/comment/delete', '\app\work_items\controller\Comment@delete'); // 删除
    Route::post('workItems/comment/update', '\app\work_items\controller\Comment@update'); // 更新
    Route::post('workItems/comment/reply', '\app\work_items\controller\Comment@reply'); // 回复
    Route::get('workItems/comment/detail', '\app\work_items\controller\Comment@detail'); // 详情
    Route::get('workItems/comment/listQuery', '\app\work_items\controller\Comment@listQuery'); // 列表
    Route::post('workItems/comment/top', '\app\work_items\controller\Comment@top'); // 置顶
    Route::post('workItems/comment/restore', '\app\work_items\controller\Comment@restore'); // 取消置顶
    Route::post('workItems/comment/createSameForMultiple', '\app\work_items\controller\Comment@createSameForMultiple'); // 为多个工作项添加相同评论

    // 项目
    Route::get('project/projectInfo/getIconPath', '\app\project\controller\ProjectInfo@getIconPath'); // 项目icon
    Route::get('project/projectInfo/getProjectStatus', '\app\project\controller\ProjectInfo@getProjectStatus'); // 项目状态下拉数据
    Route::get('project/projectInfo/getProjectSelector', '\app\project\controller\ProjectInfo@getProjectSelector'); // 项目下拉数据
    Route::get('project/projectInfo/synopsis', '\app\project\controller\ProjectInfo@synopsis'); // 项目简介
    Route::get('project/projectInfo/abbreviate', '\app\project\controller\ProjectInfo@abbreviate'); // 项目缩略
    Route::get('project/projectInfo/detail', '\app\project\controller\ProjectInfo@detail'); // 详情
    Route::get('project/projectInfo/listQuery', '\app\project\controller\ProjectInfo@listQuery'); // 列表
    Route::post('project/projectInfo/like', '\app\project\controller\ProjectInfo@like'); // 收藏
    Route::post('project/projectInfo/dislike', '\app\project\controller\ProjectInfo@dislike'); // 取消收藏
    Route::get('project/projectInfo/getExitProjectInfo', '\app\project\controller\ProjectInfo@getExitProjectInfo'); // 查询退出项目信息
    Route::post('project/projectInfo/quitProjectUser', '\app\project\controller\ProjectInfo@quitProjectUser'); // 退出项目用户（需要交接）

    // 项目模板
    Route::get('project/template/detail', '\app\project\controller\ProjectTemplate@detail'); // 详情
    Route::get('project/template/listQuery', '\app\project\controller\ProjectTemplate@listQuery'); // 列表
    Route::post('project/template/updateSort', '\app\project\controller\ProjectTemplate@updateSort'); // 更新排序
    Route::get('project/template/getTemplateSelector', '\app\project\controller\ProjectTemplate@getTemplateSelector'); // 模板下拉数据

    // 项目用户
    Route::get('project/projectUser/pageQuery', '\app\project\controller\ProjectUser@pageQuery'); // 列表
    Route::get('project/projectUser/selectorPageQuery', '\app\project\controller\ProjectUser@selectorPageQuery'); // 下拉列表
    Route::post('project/projectUser/selectorListQuery', '\app\project\controller\ProjectUser@selectorListQuery'); // 项目成员下拉列表

    // 迭代
    // 迭代目录
    Route::get('project/iterationCatalog/getIconPath', '\app\project\controller\IterationCatalog@getIconPath'); // 项目icon
    Route::get('project/iterationCatalog/detail', '\app\project\controller\IterationCatalog@detail'); // 详情
    Route::post('project/iterationCatalog/listQuery', '\app\project\controller\IterationCatalog@listQuery'); // 列表
    Route::post('project/iterationCatalog/listQueryGroupByProject', '\app\project\controller\IterationCatalog@listQueryGroupByProject'); // 列表（按项目分组）
    Route::post('project/iterationCatalog/selectorIteration', '\app\project\controller\IterationCatalog@selectorIteration'); // 迭代下拉接口
    Route::get('project/iterationCatalog/selectorFlowStatus', '\app\project\controller\IterationCatalog@selectorFlowStatus'); // 迭代状态下拉接口
    Route::get('project/iterationCatalog/selectorProcessNode', '\app\project\controller\IterationCatalog@selectorProcessNode'); // 迭代节点下拉接口

    // 迭代概览
    Route::get('project/iterationCatalog/getIterationProcessNode', '\app\project\controller\IterationCatalog@getIterationProcessNode'); // 迭代流程图
    Route::get('project/iterationCatalog/getIterationProcessNodeDetail', '\app\project\controller\IterationCatalog@getIterationProcessNodeDetail'); // 获取迭代节点详情
    Route::post('project/iterationCatalog/initiate', '\app\project\controller\IterationCatalog@initiate'); // 发起审批
    Route::post('project/iterationCatalog/approve', '\app\project\controller\IterationCatalog@approve'); // 审批
    Route::post('project/iterationCatalog/onNode', '\app\project\controller\IterationCatalog@onNode'); // 开启节点
    Route::post('project/iterationCatalog/completeNode', '\app\project\controller\IterationCatalog@completeNode'); // 完成迭代
    Route::get('project/iterationCatalog/getIterativeGoal', '\app\project\controller\IterationCatalog@getIterativeGoal'); // 获取迭代目标
    Route::post('project/iterationCatalog/updateIterativeTime', '\app\project\controller\IterationCatalog@updateIterativeTime'); // 更新迭代时间
    Route::get('project/iterationCatalog/logList','\app\project\controller\IterationCatalog@iterationLogList');// 获取迭代操作日志
    Route::get('project/iterationCatalog/approveDetail', '\app\project\controller\IterationCatalog@approveDetail');//飞书审批详情

    Route::get('iterate/shua/fill_estimate_time', '\app\iterate\controller\Shua@fillEstimateTime');


    // 枚举管理
    Route::get('enum/pageQuery', '\app\infrastructure\controller\Enum@pageQuery');

    // 节点阶段分类
    Route::post('nodeCategory/updateSort', '\app\project\controller\NodeCategory@updateSort'); // 更新排序
    Route::get('nodeCategory/selector', '\app\project\controller\NodeCategory@selector'); // 获取下拉数据
    Route::get('nodeCategory/detail', '\app\project\controller\NodeCategory@getDetail'); // 获取详情
    Route::get('nodeCategory/pageQuery', '\app\project\controller\NodeCategory@pageQuery'); // 分页查询
    Route::post('nodeCategory/create', '\app\project\controller\NodeCategory@create'); // 新增
    Route::post('nodeCategory/update', '\app\project\controller\NodeCategory@update'); // 更新
    Route::post('nodeCategory/delete', '\app\project\controller\NodeCategory@delete'); // 删除

    // 会议类型
    Route::post('meetingType/updateSort', '\app\project\controller\MeetingType@updateSort'); // 更新排序
    Route::get('meetingType/selector', '\app\project\controller\MeetingType@selector'); // 获取下拉数据
    Route::get('meetingType/detail', '\app\project\controller\MeetingType@getDetail'); // 获取详情
    Route::get('meetingType/pageQuery', '\app\project\controller\MeetingType@pageQuery'); // 分页查询
    Route::post('meetingType/create', '\app\project\controller\MeetingType@create'); // 新增
    Route::post('meetingType/update', '\app\project\controller\MeetingType@update'); // 更新
    Route::post('meetingType/delete', '\app\project\controller\MeetingType@delete'); // 删除



    // 自定义工作日
    Route::get('project/customizeTheWorkingDay/detail', '\app\project\controller\CustomizeTheWorkingDay@getDetail'); // 获取详情
    Route::get('project/customizeTheWorkingDay/byProject', '\app\project\controller\CustomizeTheWorkingDay@getByProjectId'); // 根据项目ID获取
    Route::post('project/customizeTheWorkingDay/createOrUpdate', '\app\project\controller\CustomizeTheWorkingDay@createOrUpdate'); // 创建或更新
    Route::post('project/customizeTheWorkingDay/delete', '\app\project\controller\CustomizeTheWorkingDay@delete'); // 删除

    // 自定义工作日额外调整
    Route::get('project/customizeTheWorkingDayExtra/detail', '\app\project\controller\CustomizeTheWorkingDayExtra@getDetail'); // 获取详情
    Route::get('project/customizeTheWorkingDayExtra/byProject', '\app\project\controller\CustomizeTheWorkingDayExtra@getListByProjectId'); // 根据项目ID获取列表
    Route::get('project/customizeTheWorkingDayExtra/pageQuery', '\app\project\controller\CustomizeTheWorkingDayExtra@pageQuery'); // 分页查询
    Route::post('project/customizeTheWorkingDayExtra/create', '\app\project\controller\CustomizeTheWorkingDayExtra@create'); // 创建
    Route::post('project/customizeTheWorkingDayExtra/update', '\app\project\controller\CustomizeTheWorkingDayExtra@update'); // 更新
    Route::post('project/customizeTheWorkingDayExtra/delete', '\app\project\controller\CustomizeTheWorkingDayExtra@delete'); // 删除
    Route::post('project/customizeTheWorkingDayExtra/batchDelete', '\app\project\controller\CustomizeTheWorkingDayExtra@batchDelete'); // 批量删除

    // 日清bug统计规则设置
    Route::get('project/nissinBugStatisticsRules/detail', '\app\project\controller\NissinBugStatisticsRules@getDetail'); // 获取详情
    Route::get('project/nissinBugStatisticsRules/byProject', '\app\project\controller\NissinBugStatisticsRules@getByProjectId'); // 根据项目ID获取
    Route::get('project/nissinBugStatisticsRules/pageQuery', '\app\project\controller\NissinBugStatisticsRules@pageQuery'); // 分页查询
    Route::post('project/nissinBugStatisticsRules/create', '\app\project\controller\NissinBugStatisticsRules@create'); // 创建
    Route::post('project/nissinBugStatisticsRules/update', '\app\project\controller\NissinBugStatisticsRules@update'); // 更新
    Route::post('project/nissinBugStatisticsRules/delete', '\app\project\controller\NissinBugStatisticsRules@delete'); // 删除
    Route::post('project/nissinBugStatisticsRules/enable', '\app\project\controller\NissinBugStatisticsRules@enable'); // 启用
    Route::post('project/nissinBugStatisticsRules/disable', '\app\project\controller\NissinBugStatisticsRules@disable'); // 禁用

    // 通用审批
    Route::get('infrastructure/generalApprovals/detail', '\app\infrastructure\controller\GeneralApprovals@getDetail'); // 获取详情
    Route::get('infrastructure/generalApprovals/byBusinessModel', '\app\infrastructure\controller\GeneralApprovals@getByBusinessModelIdAndType'); // 根据业务模型ID和业务类型获取
    Route::get('infrastructure/generalApprovals/pageQuery', '\app\infrastructure\controller\GeneralApprovals@pageQuery'); // 分页查询
    Route::post('infrastructure/generalApprovals/create', '\app\infrastructure\controller\GeneralApprovals@create'); // 创建
    Route::post('infrastructure/generalApprovals/approve', '\app\infrastructure\controller\GeneralApprovals@approve'); // 审批
    Route::post('infrastructure/generalApprovals/delete', '\app\infrastructure\controller\GeneralApprovals@delete'); // 删除

    // URL参数存储
    Route::get('urlParamsStorage/get', '\app\infrastructure\controller\UrlParamsStorage@get'); // 根据唯一标识获取参数


    Route::get('project/projectUser/updateNamePinyin', '\app\project\controller\ProjectUser@updateNamePinyin'); // 更新用户名拼音字段

})
    ->middleware(AuthTokenMiddleware::class)
    ->middleware(TrimMiddleware::class)
    ->middleware(SaveUrlParams::class);

