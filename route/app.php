<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

//Route::get('clear-opcache', function () {
//    opcache_reset();
//});
//Route::get('hello/:name', 'index/hello');
//Route::get('test', 'index/test')->middleware(\middleware\AuthTokenMiddleware::class);

//Route::get('create', '\app\user\controller\Demo@create');
