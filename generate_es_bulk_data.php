<?php
// generate_es_bulk_data.php
// 请将此脚本放置在您的 ThinkPHP 项目根目录下执行

// --- 用户配置区域 ---
// 您可以根据需要修改以下这些值

// Elasticsearch 配置
$ES_INDEX_NAME = 'devops_content_index';   // Elasticsearch 目标索引名

// 数据库表配置
$DB_TABLE_NAME = 't_work_items'; // 数据库表名
$DB_ID_FIELD   = 'cnt_id';           // 表中用作 Elasticsearch _id 的字段名
$DB_JSON_FIELD = 'extends';      // 表中包含 JSON 数据的字段名

// 数据库连接配置 (请直接在此处填写您的数据库连接信息)
$DB_TYPE     = 'mysql';            // 数据库类型 (例如: mysql, pgsql, sqlite, sqlsrv)
$DB_HOSTNAME = '127.0.0.1';      // 数据库主机名或 IP 地址
$DB_PORT     = '3306';           // 数据库端口
$DB_DATABASE = 'devops';  // 数据库名称
$DB_USERNAME = 'devops';  // 数据库用户名
$DB_PASSWORD = 'J2deNWWzJ6TAHtA5';  // 数据库密码
$DB_CHARSET  = 'utf8mb4';        // 数据库字符集
$DB_PARAMS   = [];               // PDO 连接选项 (例如: [PDO::ATTR_PERSISTENT => true])

// 输出文件配置
$OUTPUT_FILENAME = 'work_items_bulk.ndjson'; // 输出数据的文件名
// --- 用户配置区域结束 ---

$fileHandle = null; // 初始化文件句柄变量

try {
    // 打开输出文件用于写入 (如果文件已存在则清空)
    $fileHandle = fopen($OUTPUT_FILENAME, 'w');
    if ($fileHandle === false) {
        throw new \Exception("无法打开文件 '{$OUTPUT_FILENAME}' 进行写入。请检查文件权限。");
    }

    if (empty($DB_HOSTNAME) || empty($DB_DATABASE) || empty($DB_USERNAME)) {
        $missingParams = [];
        if(empty($DB_HOSTNAME)) $missingParams[] = '数据库主机名 ($DB_HOSTNAME)';
        if(empty($DB_DATABASE)) $missingParams[] = '数据库名 ($DB_DATABASE)';
        if(empty($DB_USERNAME)) $missingParams[] = '数据库用户名 ($DB_USERNAME)';
        // 密码可以为空，所以不检查 $DB_PASSWORD
        throw new \Exception("数据库连接参数 (" . implode(', ', $missingParams) . ") 未在脚本中配置。请编辑脚本并填写正确的数据库连接信息。");
    }
    
    $dsn = sprintf(
        '%s:host=%s;port=%s;dbname=%s',
        $DB_TYPE,
        $DB_HOSTNAME,
        $DB_PORT,
        $DB_DATABASE
    );
    
    $pdo_options = $DB_PARAMS;
    if (strtolower($DB_TYPE) == 'mysql') { // 为 MySQL 设置字符集
        $pdo_options[PDO::MYSQL_ATTR_INIT_COMMAND] = "SET NAMES '$DB_CHARSET'";
    }
    // $pdo_options[PDO::ATTR_EMULATE_PREPARES] = true; // 如果需要，可以开启模拟预处理

    $pdo = new PDO($dsn, $DB_USERNAME, $DB_PASSWORD, $pdo_options);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 查询数据
    $stmt = $pdo->query("SELECT `{$DB_ID_FIELD}`, `{$DB_JSON_FIELD}` FROM `{$DB_TABLE_NAME}`");

    $processedCount = 0;
    $errorCount = 0;
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $docId = $row[$DB_ID_FIELD];
        $jsonData = $row[$DB_JSON_FIELD];

        if (empty($jsonData)) {
            // fwrite(STDERR, "提示: ID {$docId} 的 '{$DB_JSON_FIELD}' 字段为空，已跳过。\n");
            continue;
        }

        $data = json_decode($jsonData, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            // fwrite(STDERR, "警告: ID {$docId} 的 JSON 解析失败。错误: " . json_last_error_msg() . ". JSON (前100字符): " . substr($jsonData, 0, 100) . "...\n");
            $errorCount++;
            continue;
        }
        
        if (!is_array($data) || empty($data)) {
            // fwrite(STDERR, "警告: ID {$docId} 解析后的 JSON 不是有效的非空数组/对象，已跳过。\n");
            $errorCount++;
            continue;
        }

        // 写入 Elasticsearch bulk API 元数据行到文件
        fwrite($fileHandle, json_encode(['index' => ['_index' => $ES_INDEX_NAME, '_id' => (string)$docId]]) . "\n");
        // 写入 Elasticsearch 文档行到文件
        fwrite($fileHandle, json_encode($data) . "\n");
        $processedCount++;
    }
    
    // 输出处理总结到 STDERR (不会进入重定向的文件)
    if ($processedCount > 0) {
        fwrite(STDERR, "成功处理 {$processedCount} 条记录，并已写入文件 '{$OUTPUT_FILENAME}' 用于 Elasticsearch 批量导入。\n");
    } else {
        fwrite(STDERR, "未处理任何记录。请检查表 '{$DB_TABLE_NAME}' 或确保 '{$DB_JSON_FIELD}' 字段包含有效的非空 JSON。文件 '{$OUTPUT_FILENAME}' 可能为空或未创建。\n");
    }
    if ($errorCount > 0) {
        fwrite(STDERR, "处理过程中遇到 {$errorCount} 个与 JSON 解析或数据有效性相关的错误。\n");
    }

} catch (\PDOException $e) {
    fwrite(STDERR, "数据库连接/查询错误: " . $e->getMessage() . "\n");
    fwrite(STDERR, "请检查脚本中的数据库凭据和连接详情，并确保数据库服务器正在运行且可访问。\n");
    exit(1);
} catch (\Exception $e) {
    fwrite(STDERR, "发生错误: " . $e->getMessage() . "\n");
    exit(1);
} finally {
    // 确保文件句柄被关闭
    if ($fileHandle !== null && is_resource($fileHandle)) {
        fclose($fileHandle);
    }
}

?>
