<?php

namespace app;

use exception\BaseException;
use resp\Result;
use resp\StatusCode;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\facade\Log;
use think\Response;
use Throwable;

/**
 * 应用异常处理类
 */
class ExceptionHandle extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     *
     * @access public
     * @param Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @access public
     * @param \think\Request $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        $respData = [];
        $debug = env('APP_DEBUG');
        if ($debug) {
            $respData['trace'] = $e->getTrace();
            array_unshift($respData['trace'], [
                'message' => $e->getMessage(),
                'file' => $e->getFile() . ':' . $e->getLine(),
                'line' => $e->getLine(),
                'code' => $e->getCode(),
            ]);
        }


        $logContent = [
            'message' => $e->getMessage(),
            '__file__' => $e->getFile() . $e->getLine(),
            'params' => \think\facade\Request::param()
        ];
        Log::error(json_encode($logContent, 320));

        if ($e instanceof ValidateException) {
            return Result::error(StatusCode::PARAMS_ERROR_CODE, $e->getMessage(), $respData);
        }

        if ($e instanceof BaseException) {
            return Result::error($e->getStatusCode(), $e->getMessage(), $respData);
        }

        return Result::error(StatusCode::BUSINESS_CODE, $e->getMessage(), $respData);

        // 其他错误交给系统处理
        // return parent::render($request, $e);
    }
}
