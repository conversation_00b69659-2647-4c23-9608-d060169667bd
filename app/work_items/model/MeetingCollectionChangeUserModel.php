<?php
/**
 * Desc 会议集合变更用户关联 - 模型
 * User
 * Date 2024/12/10
 */

declare (strict_types=1);

namespace app\work_items\model;

use basic\BaseModel;
use think\model\relation\BelongsTo;
use traits\CreateAndUpdateModelTrait;
use traits\CreateModelTrait;

/**
 * This is the model class for table "meeting_collection_change_user".
 * @property int    $id          主键ID
 * @property int    $change_id   变更ID
 * @property int    $user_id     用户ID
 * @property int    $user_type   用户类型;1-节点负责人 2-主讲人
 * @property int    $is_delete   是否删除;0-否 1-是
 * @property string $create_at   创建时间
 * @property string $update_at   更新时间
 */
class MeetingCollectionChangeUserModel extends BaseModel
{
    use CreateModelTrait;

    protected $pk = 'id';
    protected $name = 'meeting_collection_change_user';

    // 用户类型常量
    const USER_TYPE_NODE_MANAGER = 1; // 节点负责人
    const USER_TYPE_SPEAKER = 2;      // 主讲人

    /**
     * 获取所属变更记录
     * @return BelongsTo
     */
    public function change(): BelongsTo
    {
        return $this->belongsTo(MeetingCollectionChangeModel::class, 'change_id', 'change_id');
    }

    /**
     * 批量创建用户关联记录
     * @param int $changeId 变更ID
     * @param array $userIds 用户ID数组
     * @param int $userType 用户类型
     * @return bool
     */
    public static function batchCreate(int $changeId, array $userIds, int $userType): bool
    {
        if (empty($userIds)) {
            return true;
        }

        $data = [];
        foreach ($userIds as $userId) {
            $data[] = [
                'change_id' => $changeId,
                'user_id'   => $userId,
                'user_type' => $userType,
                'is_delete' => self::DELETE_NOT
            ];
        }

        return (new self())->saveAll($data) ? true : false;
    }

    /**
     * 根据变更ID和用户类型查询用户
     * @param int $changeId 变更ID
     * @param int $userType 用户类型
     * @return array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findByChangeIdAndType(int $changeId, int $userType)
    {
        return self::where([
            'change_id' => $changeId,
            'user_type' => $userType,
            'is_delete' => self::DELETE_NOT
        ])->select();
    }
} 