<?php
/**
 * Desc 示例 - 模型
 * User
 * Date 2024/09/30
 * */

declare (strict_types=1);

namespace app\work_items\model;

use app\work_items\logic\PlanUseCaseLogic;
use app\work_items\logic\TestPlanLogic;
use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "plan_use_case".
 * @property string $plan_use_case_id id
 * @property string $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property string $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property string $test_plan_id 计划Id
 * @property string $execution_times 执行次数
 * @property string $bug_count 关联Bug数
 * @property string $test_case_id 用例id
 */
class PlanUseCaseModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'plan_use_case_id';
    protected $name = 'plan_use_case';

    public function case()
    {
        return $this->hasOne(TestCaseModel::class, 'test_case_id', 'test_case_id');
    }

    public function plan()
    {
        return $this->hasOne(TestPlanModel::class, 'test_plan_id', 'test_plan_id');
    }

    public function record()
    {
        return $this->hasMany(ExecutionRecordModel::class, 'plan_use_case_id')->where(['is_delete' => BaseModel::DELETE_NOT]);
    }

    public static function findById($id)
    {
        return static::where(['plan_use_case_id' => $id, 'is_delete' => self::DELETE_NOT])->find();
    }

    public static function findListById($id)
    {
        return static::where(['plan_use_case_id' => $id, 'is_delete' => self::DELETE_NOT])->select();
    }

    public function save(array $data = [], string $sequence = null): bool
    {
        if (!parent::save($data, $sequence)) {
            return false;
        }
        (new PlanUseCaseLogic)->triggerSaveCasePlanList($this->refresh());
        TestPlanLogic::getInstance()->triggerStatistics($this->test_plan_id);

        return true;
    }

    public static function getByPlanIdAndCaseId($planId, $caseId)
    {
        return self::status()->where([
            'test_plan_id' => $planId,
            'test_case_id' => $caseId
        ])->find();
    }


}
