<?php
/**
 * 工时打回记录模型
 */

declare (strict_types=1);

namespace app\work_items\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "work_hours_rejection_records".
 * @property int    $rejection_id     主键ID
 * @property string $create_by        创建人
 * @property string $create_by_name   创建人名称
 * @property string $create_at        创建时间
 * @property int    $is_delete        是否删除;1-是 0-否
 * @property string $update_by        更新人
 * @property string $update_by_name   更新人名称
 * @property string $update_at        更新时间
 * @property int    $cnt_id           任务ID
 * @property string $cnt_title        任务标题
 * @property int    $handler_uid      处理人ID
 * @property string $handler_name     处理人名称
 * @property string $rejection_reason 打回原因
 * @property int    $submitter_uid    提交人ID
 * @property string $submitter_name   提交人名称
 * @property string $submit_at        提交时间
 * @property string $approval_at      审批时间
 * @property int    $approval_id      关联的审批ID
 * @property float  $working_hours    工时数值
 */
class WorkHoursRejectionRecordsModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'rejection_id';
    protected $name = 'work_hours_rejection_records';

    /**
     * 列表字段
     */
    const LIST_FIELDS = [
        'rejection_id',
        'cnt_id',
        'cnt_title',
        'handler_uid',
        'handler_name',
        'rejection_reason',
        'submitter_uid',
        'submitter_name',
        'submit_at',
        'approval_at',
        'working_hours',
    ];

    /**
     * 根据ID查找记录
     * @param int $id
     * @return WorkHoursRejectionRecordsModel|null
     */
    public static function findById(int $id)
    {
        return self::where('rejection_id', $id)
            ->where('is_delete', self::DELETE_NOT)
            ->find();
    }

    /**
     * 根据任务ID查找记录
     * @param int $cntId
     * @return \think\Collection
     */
    public static function findByCntId(int $cntId)
    {
        return self::where('cnt_id', $cntId)
            ->where('is_delete', self::DELETE_NOT)
            ->order('create_at', 'desc')
            ->select();
    }

    /**
     * 转为详情数据
     * @return array
     */
    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }
} 