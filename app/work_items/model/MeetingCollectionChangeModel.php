<?php
/**
 * Desc 会议集合变更记录 - 模型
 * User
 * Date 2024/12/10
 */

declare (strict_types=1);

namespace app\work_items\model;

use basic\BaseModel;
use think\model\relation\HasMany;
use traits\CreateModelTrait;

/**
 * This is the model class for table "meeting_collection_change".
 * @property int    $change_id      变更ID
 * @property int    $project_id     项目ID
 * @property int    $task_id        任务ID
 * @property string $task_title     任务标题
 * @property string $meeting_type   会议类型（已弃用，改为使用meetingTypes关联）
 * @property int    $is_delete      是否删除;0-否 1-是
 * @property string $create_at      创建时间
 * @property string $update_at      更新时间
 */
class MeetingCollectionChangeModel extends BaseModel
{
    use CreateModelTrait;

    protected $pk = 'change_id';
    protected $name = 'meeting_collection_change';

    /**
     * 获取用户关联数据
     * @return HasMany
     */
    public function users(): HasMany
    {
        return $this->hasMany(MeetingCollectionChangeUserModel::class, 'change_id', 'change_id');
    }

    /**
     * 获取节点负责人
     * @return HasMany
     */
    public function nodeManagers(): HasMany
    {
        return $this->hasMany(MeetingCollectionChangeUserModel::class, 'change_id', 'change_id')
            ->where('user_type', 1);
    }

    /**
     * 获取主讲人
     * @return HasMany
     */
    public function speakers(): HasMany
    {
        return $this->hasMany(MeetingCollectionChangeUserModel::class, 'change_id', 'change_id')
            ->where('user_type', 2);
    }

    /**
     * 获取会议类型关联数据
     * @return HasMany
     */
    public function meetingTypes(): HasMany
    {
        return $this->hasMany(MeetingCollectionChangeTypeModel::class, 'change_id', 'change_id');
    }

    /**
     * 根据任务ID查询变更记录
     * @param int $taskId
     * @return array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findByTaskId(int $taskId)
    {
        return self::where(['task_id' => $taskId, 'is_delete' => self::DELETE_NOT])
            ->with(['users', 'meetingTypes'])
            ->order('change_id', 'desc')
            ->select();
    }

    /**
     * 根据项目ID查询变更记录
     * @param int $projectId
     * @return array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findByProjectId(int $projectId)
    {
        return self::where(['project_id' => $projectId, 'is_delete' => self::DELETE_NOT])
            ->with(['users', 'meetingTypes'])
            ->order('change_id', 'desc')
            ->select();
    }
} 