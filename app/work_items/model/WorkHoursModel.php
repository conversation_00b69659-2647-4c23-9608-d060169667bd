<?php
/**
 * Desc 示例 - 模型
 * User
 * Date 2024/08/28*/

declare (strict_types=1);

namespace app\work_items\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;
use utils\Ctx;

/**
 * This is the model class for table "work_hours".
 * @property string $work_hours_id  id
 * @property string $create_by      创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at      创建时间
 * @property string $is_delete      是否删除;1-是 0-否
 * @property string $update_by      更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at      更新时间
 * @property string $cnt_id         工作项id
 * @property string $type           类型;1-预估,2-实际
 * @property string $remark         描述
 * @property string $working_hours  工时
 * @property string $work_date      工作日期
 */
class WorkHoursModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'work_hours_id';
    protected $name = 'work_hours';


    const TYPE_ESTIMATED = 1;//预估
    const TYPE_ACTUAL = 2;//实际
    const TYPE_REMAINING = 3;//剩余

    const LIST_FIELDS
        = [
            'work_hours_id',
            'type',
            'cnt_id',
            'remark',
            'working_hours',
            'work_date',
            'create_at',
            'create_by',
            'create_by_name',
        ];


    public function getWorkDateAttr($value, $data)
    {
        if ( ! $value) {
            return "";
        }
        $date = (string)$value;

        return substr($date, 0, 4).'-'.substr($date, 4, 2).'-'.substr($date, 6, 2); // 输出 2022-10-19
    }

    public function setWorkDateAttr($value, $data)
    {
        return self::formatWorkDateToInt($value);
    }


    public static function findById($id)
    {
        return static::where(['work_hours_id' => $id, 'is_delete' => self::DELETE_NOT])->find();
    }

    public static function findListByCntId($cntId)
    {
        return static::where(['cnt_id' => $cntId, 'is_delete' => self::DELETE_NOT])->select();

    }


    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }

    //覆盖数据
    public static function overwriteOldData($cntId, $type, $workDate = null)
    {
        $where = [
            'cnt_id' => $cntId,
            'type'   => $type,
        ];

        if ($type == self::TYPE_ACTUAL) {
            $where['create_by'] = Ctx::$userId;
        }

        if ($workDate) {
            $where['work_date'] = self::formatWorkDateToInt($workDate);
        }
//        dd($where);
        WorkHoursModel::update(['is_delete' => BaseModel::DELETE_YES], $where);
    }

    public static function formatWorkDateToInt($workDate)
    {
        return str_replace('-', '', $workDate);
    }

}
