<?php
/**
 * Desc 示例 - 模型
 * User
 * Date 2024/10/15
 * */

declare (strict_types=1);

namespace app\work_items\model;

use app\work_items\logic\PlanUseCaseLogic;
use app\work_items\logic\TestPlanLogic;
use app\work_items\logic\TestPlanWorkCaseLogic;
use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "est_plan_work_case".
 * @property string $test_plan_work_case_id id
 * @property string $test_plan_id 计划id
 * @property string $cnt_id 需求id
 * @property string $test_case_id 用例id
 */
class TestPlanWorkCaseModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'test_plan_work_case_id';
    protected $name = 'test_plan_work_case';

    public static function findById($id)
    {
        return static::where(['test_plan_work_case_id' => $id])->find();
    }

    public static function findListById($id)
    {
        return static::where(['test_plan_work_case_id' => $id])->select();
    }

    public function delete(): bool
    {
        $result = parent::delete();
        //无关系联动删除计划下的用例
        if (TestPlanWorkCaseLogic::getListByPlanIdAndCaseId($this->test_plan_id, $this->test_case_id)->isEmpty()) {
            (new PlanUseCaseLogic)->del($this->test_plan_id, $this->test_case_id);
        }
        TestPlanLogic::getInstance()->triggerStatistics($this->test_plan_id);

        return $result;
    }

    public function save(array $data = [], string $sequence = null): bool
    {
        $result = parent::save($data, $sequence);
        TestPlanLogic::getInstance()->triggerStatistics($this->test_plan_id);

        return $result;
    }

}
