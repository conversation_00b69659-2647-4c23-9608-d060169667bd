<?php
/**
 * Desc 会议集合变更类型关联 - 模型
 * User
 * Date 2024/12/11
 */

declare (strict_types=1);

namespace app\work_items\model;

use basic\BaseModel;
use think\model\relation\BelongsTo;
use traits\CreateModelTrait;

/**
 * This is the model class for table "meeting_collection_change_type".
 * @property int    $id          主键ID
 * @property int    $change_id   变更ID
 * @property string $type_code   类型编码
 * @property string $type_name   类型名称
 * @property int    $is_delete   是否删除;0-否 1-是
 * @property string $create_at   创建时间
 * @property string $update_at   更新时间
 */
class MeetingCollectionChangeTypeModel extends BaseModel
{
    use CreateModelTrait;

    protected $pk = 'id';
    protected $name = 'meeting_collection_change_type';

    /**
     * 获取所属变更记录
     * @return BelongsTo
     */
    public function change(): BelongsTo
    {
        return $this->belongsTo(MeetingCollectionChangeModel::class, 'change_id', 'change_id');
    }

    /**
     * 批量创建会议类型关联记录
     * @param int $changeId 变更ID
     * @param array $types 会议类型数组，格式为 [['code' => 'xxx', 'name' => 'xxx'], ...]
     * @return bool
     */
    public static function batchCreate(int $changeId, array $types): bool
    {
        if (empty($types)) {
            return true;
        }

        $data = [];
        foreach ($types as $type) {
            $data[] = [
                'change_id' => $changeId,
                'type_code' => $type['code'] ?? '',
                'type_name' => $type['name'] ?? '',
                'is_delete' => self::DELETE_NOT
            ];
        }

        return (new self())->saveAll($data) ? true : false;
    }

    /**
     * 根据变更ID查询会议类型
     * @param int $changeId 变更ID
     * @return array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findByChangeId(int $changeId)
    {
        return self::where([
            'change_id' => $changeId,
            'is_delete' => self::DELETE_NOT
        ])->select();
    }
} 