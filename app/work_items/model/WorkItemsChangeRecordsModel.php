<?php
/**
 * 需求变更记录模型
 */

declare (strict_types=1);

namespace app\work_items\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "work_items_change_records".
 * @property int    $change_id          主键ID
 * @property string $create_by          创建人
 * @property string $create_by_name     创建人名称
 * @property string $create_at          创建时间
 * @property int    $is_delete          是否删除;1-是 0-否
 * @property string $update_by          更新人
 * @property string $update_by_name     更新人名称
 * @property string $update_at          更新时间
 * @property int    $project_id         项目ID
 * @property string $project_name       项目名称
 * @property int    $cnt_id             需求ID
 * @property string $cnt_title          需求标题
 * @property int    $handler_uid        需求负责人ID
 * @property string $handler_name       需求负责人名称
 * @property int    $iteration_id       迭代ID
 * @property string $iteration_name     迭代名称
 * @property int    $node_id            迭代节点ID
 * @property string $node_name          迭代节点名称
 * @property string $node_stage         迭代节点阶段
 * @property string $change_reason      变更原因
 * @property int    $submitter_uid      提交人ID
 * @property string $submitter_name     提交人名称
 * @property string $submit_at          提交时间
 * @property string $approval_at        审批时间
 * @property int    $approval_id        关联的审批ID
 */
class WorkItemsChangeRecordsModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'change_id';
    protected $name = 'work_items_change_records';

    /**
     * 列表字段
     */
    const LIST_FIELDS = [
        'change_id',
        'project_id',
        'project_name',
        'cnt_id',
        'cnt_title',
        'handler_uid',
        'handler_name',
        'iteration_id',
        'iteration_name',
        'node_id',
        'node_name',
        'node_stage',
        'change_reason',
        'submitter_uid',
        'submitter_name',
        'submit_at',
        'approval_at',
    ];

    /**
     * 根据ID查找记录
     * @param int $id
     * @return WorkItemsChangeRecordsModel|null
     */
    public static function findById(int $id)
    {
        return self::where('change_id', $id)
            ->where('is_delete', self::DELETE_NOT)
            ->find();
    }

    /**
     * 根据需求ID查找记录
     * @param int $cntId
     * @return \think\Collection
     */
    public static function findByCntId(int $cntId)
    {
        return self::where('cnt_id', $cntId)
            ->where('is_delete', self::DELETE_NOT)
            ->order('create_at', 'desc')
            ->select();
    }

    /**
     * 转为详情数据
     * @return WorkItemsChangeRecordsModel
     */
    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }
} 