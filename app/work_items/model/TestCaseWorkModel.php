<?php
/**
 * Desc 示例 - 模型
 * User
 * Date 2024/09/27
 * */

declare (strict_types=1);

namespace app\work_items\model;

use basic\BaseModel;
use think\Exception;
use traits\CreateAndUpdateModelTrait;
use traits\OperationLogJsonHandler;

/**
 * This is the model class for table "est_case_work_items".
 * @property string $id           id
 * @property string $test_case_id 测试用例id
 * @property string $cnt_id       工作项id
 */
class TestCaseWorkModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'test_case_work_id';
    protected $name = 'test_case_work';


    public function delete(): bool
    {
        $this->synchronizationUseCases('del');
        return parent::delete(); // TODO: Change the autogenerated stub
    }

    public function save(array $data = [], string $sequence = null): bool
    {
        $result = parent::save($data, $sequence); // TODO: Change the autogenerated stub
        $this->synchronizationUseCases('add');
        return $result;
    }


    /**
     * 同步数据至测试用例
     * @param $type
     * @return void
     * @throws Exception
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     */
    private function synchronizationUseCases($type)
    {

        $case = TestCaseModel::findById($this->test_case_id);
        switch ($type) {
        case 'add':
            $cntIdList = array_values(array_merge($case->extends['cnt_id_list'] ?? [],[$this->cnt_id])) ;
            break;
        case 'del':
            $cntIdList = array_values(array_diff($case->extends['cnt_id_list'] ?? [], [$this->cnt_id]));
            break;
        default:
            throw new Exception();
        }

        $testCase = TestCaseModel::findById($this->test_case_id);
        if ($testCase) {
            $testCase->save([
                'cnt_id_list' => $cntIdList
            ]);
        }
    }

}
