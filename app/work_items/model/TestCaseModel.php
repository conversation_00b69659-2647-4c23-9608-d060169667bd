<?php
/**
 * Desc 测试用例
 * User
 * Date 2024/09/27
 * */

declare (strict_types=1);

namespace app\work_items\model;

use app\infrastructure\model\FieldConfigModel;
use app\work_items\logic\TestCaseLogic;
use app\work_items\logic\TestCaseWorkLogic;
use basic\BaseModel;
use think\Model;
use think\model\relation\HasMany;
use traits\OptimLockTrait;
use utils\Ctx;

/**
 * This is the model class for table "test_case".
 * @property string $test_case_id     id
 * @property string $extends          自定义字段
 * @property string $case_tep         用例步骤
 * @property string $preconditions    前置条件
 * @property string $expected_results 预期结果
 * @property string $is_delete        是否删除;1-是 0-否
 * @property string $version          版本号
 */
class TestCaseModel extends BaseModel
{

    use OptimLockTrait;

    protected $pk = 'test_case_id';
    protected $name = 'test_case';

    //递归结束是否自动执行保存
    public static $autoSave = true;

    //调用一次save需修改的所有数据
    public static $saveSql = [];

    //save的递归深度，当等于0时才会执行保存操作
    public static $saveDepth = 0;

    public function setExtendsAttr($value, $data)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function getExtendsAttr($value, $data)
    {

        return json_decode($value ?? '{}', true);
    }

    public function testCaseWork(): HasMany
    {
        return $this->hasMany(TestCaseWorkModel::class, 'test_case_id');
    }


    //冗余在mysql中的字段，富文本不存es中
    const REDUNDANT_FIELDS
        = [
            'case_tep'         => '',
            'preconditions'    => '',
            'expected_results' => '',
            'is_delete'        => 0,
            'version'          => 0,
        ];


    public static function findById($id)
    {
        return static::where(['test_case_id' => $id, 'is_delete' => self::DELETE_NOT])->find();
    }

    public static function findListById($id)
    {
        return static::where(['test_case_id' => $id, 'is_delete' => self::DELETE_NOT])->select();
    }


    public function toDetail()
    {
        $this->refresh();
        $data = $this->getData();
        unset($data['extends']);
        $result = array_merge($this->extends, $data);
        $result = [$result];
        TestCaseLogic::getInstance()->mergeParnetName($result);

        return $result[0];
    }


    /**
     * 返回所有组件
     * @return FieldConfigModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/8/27 21:28
     */
    public function getFieldList()
    {
//        $fieldNameList = array_keys($this->extends);
        return FieldConfigModel::getListByFieldName(null, FieldConfigModel::MODULE_TYPE_TEST_CASE, $this->extends['project_id']);
    }


    /**
     * 获取所有冗余字段，有值就取值，无值用常量中的默认值
     * @return array
     * <AUTHOR>
     * @date   2024/9/5 11:14
     */
    public function getRedundantFields()
    {
        $oldData = $this->getData();
        $result = [];
        foreach (self::REDUNDANT_FIELDS as $k => $v) {
            $result[$k] = $oldData[$k] ?? $v;
        }

        return $result;
    }


    /**
     * 生成创建数据
     * @return array
     * <AUTHOR>
     * @date   2024/8/27 21:12
     */
    private function generationCreateData()
    {
        return [
            'create_by'      => Ctx::$user->userId,
            'create_by_name' => Ctx::$user->name,
            'create_at'      => date('Y-m-d H:i:s'),
        ];
    }

    /**
     * 生成修改数据
     * @return array
     * <AUTHOR>
     * @date   2024/8/27 21:12
     */
    private function generationUpdateData()
    {
        return [
            'update_by'      => Ctx::$user->userId,
            'update_by_name' => Ctx::$user->name,
            'update_at'      => date('Y-m-d H:i:s'),
        ];
    }

    /**
     * 唯一改变，data可为空
     * @param  array   $data
     * @param  array   $allowField
     * @param  bool    $replace
     * @param  string  $suffix
     * @return Model
     * <AUTHOR>
     * @date   2024/9/5 10:28
     */
    public static function create(array $data = [], array $allowField = [], bool $replace = false, string $suffix = ''): Model
    {
        return parent::create($data, $allowField, $replace, $suffix);
    }


    /**
     * 同步保存es,并更新工时
     * @param  array        $data
     * @param  string|null  $sequence
     * @return bool
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/9/5 16:50
     */
    public function save(array $data = [], string $sequence = null): bool
    {

        self::$saveDepth++;

        //生成test_case_id,不走下面逻辑
        if ($this->isEmpty()) {
            self::$saveDepth--;

            return parent::save(array_merge(self::REDUNDANT_FIELDS, $data), $sequence);
        }
        $logic = TestCaseLogic::getInstance();


        //将extends字段覆盖冗余字段，保持一致
        $fields = $this->getRedundantFields();
        $saveData = array_replace($fields, array_intersect_key($data, $fields));

        //旧数据
        $oldData = json_decode($this->getData()['extends'] ?? '{}', true);

        //es中不保存contents、version
        unset($data['contents'], $data['version']);


        //创建0，修改1
        $operationType = $this->extends ? 1 : 0;
        if ($operationType) {
            $data = array_merge($data, $this->generationUpdateData());
        } else {
            $data = array_merge($data, $this->generationCreateData());
        }


        if ($data['cnt_id_list'] ?? false) {
            $data['cnt_id_list'] = array_map(fn($v) => (int)$v, $data['cnt_id_list']);
        }

        //mysql需保存修改的数据以及没改的数据，es只需要保存修改的数据
        $saveData['extends'] = $data + $oldData;
        $result = parent::save($saveData, $sequence);//保存mysql
//        $logic->saveEs($this->test_case_id, $data, $operationType);//保存es
        self::$saveSql[] = array_merge($data, [$this->getPk() => $this->test_case_id]);


        if ($data['is_delete'] ?? false) {
            //删除计划中的用例
//            (new TestPlanWorkCaseLogic)->del(null, $this->test_case_id, null);
            (new TestCaseWorkLogic)->del(null, $this->test_case_id);
        }

        self::$saveDepth--;
        if (self::$saveDepth == 0 && self::$autoSave) {
            self::actualSaving();
        }

        return $result;
    }

    /**
     * 批量保存es
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     */
    static public function actualSaving()
    {
        if ( ! self::$saveSql) {
            return;
        }
        TestCaseLogic::getInstance()->bulk(self::$saveSql);
    }

    public function planUseCases()
    {
        return $this->hasMany(PlanUseCaseModel::class);
    }

}
