<?php
/**
 * 迭代需求关联记录模型
 */

declare (strict_types=1);

namespace app\work_items\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "iteration_demand_relation_records".
 * @property int    $record_id         主键ID
 * @property string $create_by         创建人
 * @property string $create_by_name    创建人名称
 * @property string $create_at         创建时间
 * @property int    $is_delete         是否删除;1-是 0-否
 * @property string $update_by         更新人
 * @property string $update_by_name    更新人名称
 * @property string $update_at         更新时间
 * @property int    $project_id        项目ID
 * @property string $project_name      项目名称
 * @property int    $cnt_id            需求ID
 * @property string $cnt_title         需求标题
 * @property int    $operator_uid      操作人ID
 * @property string $operator_name     操作人名称
 * @property int    $iteration_id      迭代ID
 * @property string $iteration_name    迭代名称
 * @property int    $node_id           迭代节点ID
 * @property string $node_name         迭代节点名称
 * @property string $node_stage        迭代节点阶段
 * @property int    $operation_type    操作类型;1-新增 2-修改
 * @property string $operation_time    操作时间
 * @property int    $old_iteration_id  原迭代ID(修改时记录)
 * @property string $old_iteration_name 原迭代名称(修改时记录)
 */
class IterationDemandRelationRecordsModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'record_id';
    protected $name = 'iteration_demand_relation_records';

    /**
     * 操作类型：新增
     */
    const OPERATION_TYPE_ADD = 1;

    /**
     * 操作类型：修改
     */
    const OPERATION_TYPE_MODIFY = 2;

    /**
     * 列表字段
     */
    const LIST_FIELDS = [
        'record_id',
        'project_id',
        'project_name',
        'cnt_id',
        'cnt_title',
        'operator_uid',
        'operator_name',
        'iteration_id',
        'iteration_name',
        'node_id',
        'node_name',
        'node_stage',
        'operation_type',
        'operation_time',
        'old_iteration_id',
        'old_iteration_name',
    ];

    /**
     * 根据ID查找记录
     * @param int $id
     * @return IterationDemandRelationRecordsModel|null
     */
    public static function findById(int $id)
    {
        return self::where('record_id', $id)
            ->where('is_delete', self::DELETE_NOT)
            ->find();
    }

    /**
     * 根据需求ID查找记录
     * @param int $cntId
     * @return \think\Collection
     */
    public static function findByCntId(int $cntId)
    {
        return self::where('cnt_id', $cntId)
            ->where('is_delete', self::DELETE_NOT)
            ->order('operation_time', 'desc')
            ->select();
    }

    /**
     * 根据迭代ID查找记录
     * @param int $iterationId
     * @return \think\Collection
     */
    public static function findByIterationId(int $iterationId)
    {
        return self::where('iteration_id', $iterationId)
            ->where('is_delete', self::DELETE_NOT)
            ->order('operation_time', 'desc')
            ->select();
    }

    /**
     * 转为详情数据
     * @return IterationDemandRelationRecordsModel
     */
    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }
} 