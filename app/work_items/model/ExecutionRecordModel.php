<?php
/**
 * Desc 示例 - 模型
 * User
 * Date 2024/09/30
 * */

declare (strict_types=1);

namespace app\work_items\model;

use app\project\model\ProjectUserModel;
use app\work_items\logic\TestPlanLogic;
use basic\BaseModel;
use think\Collection;
use think\model\relation\BelongsTo;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "execution_record".
 * @property string $execution_record_id id
 * @property string $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property string $test_plan_id 计划Id
 * @property string $plan_use_case_id 计划用例关联Id
 * @property string $result 1-通过;2-阻塞;4-不通过
 * @property string $remark 备注
 */
class ExecutionRecordModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'execution_record_id';
    protected $name = 'execution_record';


    const NOT_EXECUTED = 0;//未执行
    const PASS = 1;//通过
    const CLOG = 2;//阻塞
    const NOT_PASSED = 4;//不通过

    public function useCase()
    {
        return $this->hasOne(PlanUseCaseModel::class, 'plan_use_case_id', 'plan_use_case_id');
    }

    public function plan()
    {
        return $this->hasOne(TestPlanModel::class, 'test_plan_id', 'test_plan_id');
    }

    public function bugs()
    {
        return $this->hasMany(PlanUseCaseBugModel::class, 'execution_record_id')->where(['is_delete' => BaseModel::DELETE_NOT]);
    }
//    public function user()
//    {
//        return $this->hasOne(ProjectUserModel::class, 'id','created_by');
//    }

    const LIST_FIELDS = [
        'execution_record_id',
        'create_by',
        'create_by_name',
        'create_at',
        'test_plan_id',
        'plan_use_case_id',
        'result',
        'remark',
    ];

    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }

    public function save(array $data = [], string $sequence = null): bool
    {
        $result = parent::save($data, $sequence);
        TestPlanLogic::getInstance()->triggerStatistics($this->test_plan_id);

        return $result;
    }


    /**
     * 根据planUseCaseId查询最新的记录
     * @param $planUseCaseId
     * @return ExecutionRecordModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/10/30 17:01
     */
    public static function getLatestByPlanUseCaseId($planUseCaseId)
    {
        return ExecutionRecordModel::status()->where([
            'plan_use_case_id' => $planUseCaseId,
        ])->order('execution_record_id', 'desc')->find();
    }
}
