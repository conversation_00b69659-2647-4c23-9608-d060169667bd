<?php
/**
 * Desc 示例 - 模型
 * User
 * Date 2024/09/30
 * */

declare (strict_types=1);

namespace app\work_items\model;

use app\work_items\logic\PlanUseCaseLogic;
use basic\BaseModel;
use think\model\relation\BelongsTo;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "plan_use_case_bug".
 * @property string $id id
 * @property string $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property string $plan_use_case_id 计划用例关联id
 * @property string $cnt_id 缺陷Id
 * @property string $test_plan_id 计划id
 * @property string $execution_record_id 执行记录Id
 */
class PlanUseCaseBugModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'plan_use_case_bug';

    public static function findById($id)
    {
        return static::status()->where(['id' => $id])->find();
    }

    public static function getListByCntId($cntId)
    {
        return static::status()->where(['cnt_id' => $cntId])->select();
    }


    public function recordBug(): HasOne
    {
        return $this->hasOne(ExecutionRecordModel::class, 'execution_record_id', 'execution_record_id');
    }

    public function planUseCase(): HasOne
    {
        return $this->hasOne(PlanUseCaseModel::class, 'plan_use_case_id', 'plan_use_case_id');
    }

    public function bug(): HasOne
    {
        return $this->hasOne(WorkItemsModel::class, 'cnt_id', 'cnt_id');
    }

    const LIST_FIELDS = [
        'id',
        'cnt_id',
        'execution_record_id',
    ];

    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }


}
