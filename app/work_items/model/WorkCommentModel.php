<?php
/**
* Desc 需求、迭代、缺陷评论表 - 模型
* User Long
* Date 2024/08/28
 * */

declare (strict_types = 1);

namespace app\work_items\model;

use app\project\model\ProjectUserModel;
use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;
use think\model\relation\HasMany;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "work_items_comment".
* @property string $comment_id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $work_items_id 所属迭代内容id
* @property string $content 内容
* @property string $remark 其他说明
* @property string $reply_id 回复的评论id(可以理解为记录pid，评论记录0，回复记录对应comment_id)
* @property string $reply_comment_id 回复的评论id(当前所属评论id)
* @property string $is_top 是否置顶1-是 0-否
* @property string $is_fake_delete 是否假删1-是 0-否
*/
class WorkCommentModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'comment_id';
    protected $name = 'work_comment';

    const HIDDEN_FIELD = ['update_by', 'update_by_name', 'update_at', 'is_delete', 'work_items_id', 'reply_id', 'reply_comment_id'];

    // 判断是否顶级评论，reply_id 评论记录0，其他是未评论的回复
    const TOP_LEVEL_COMMENT = 0;

    // 是否置顶1-是 0-否
    const TOP_YES = 1;
    const TOP_NOT = 0;

    // 是否假删1-是 0-否
    const IS_FAKE_DELETE_YES = 1;
    const IS_FAKE_DELETE_NOT = 0;

    /**
     * 根据id集查询数据
     * @param int $commentType
     * @param array $commentIds
     * @return WorkCommentModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/26
     */
    public static function selectByCommentIds(int $commentType, array $commentIds)
    {
        return static::status()->where(['comment_type' => $commentType])->whereIn('comment_id', $commentIds)->select();
    }

    /**
     * 根据 评论id 查询评论
     * @param int $commentType
     * @param int $commentId
     * @return WorkCommentModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/26
     */
    public static function findByCommentId(int $commentType, int $commentId)
    {
        return static::status()->where(['comment_type' => $commentType, 'comment_id' => $commentId])->find();
    }

    public static function findListByWorkItemsId(int $commentId)
    {
        return static::status()->where(['work_items_id ' => $commentId])->select();
    }

    /**
     * 根据 回复的评论id 查询评论
     * @param int $commentType
     * @param int $replyCommentId
     * @return WorkCommentModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/26
     */
    public static function findByReplyCommentId(int $commentType, int $replyCommentId)
    {
        return static::status()->where(['comment_type' => $commentType, 'reply_comment_id' => $replyCommentId])->find();
    }

    /**
     * 详情页处理
     * @return WorkCommentModel
     * User Long
     * Date 2024/8/28
     */
    public function toDetail()
    {
        return $this->hidden(['is_delete', 'is_top']);
    }

    /**
     * 预加载 - 用户信息
     * @return \think\model\relation\HasOne
     * User Long
     * Date 2024/12/30
     */
    public function userInfo()
    {
        return $this->hasOne(ProjectUserModel::class, 'user_id', 'create_by')->where(['is_delete' => self::DELETE_NOT]);
    }

    /**
     * 预加载 - 回复内容
     * @return HasMany
     * User Long
     * Date 2024/8/28
     */
    public function reply()
    {
        return $this->hasMany(self::class, 'reply_comment_id', 'comment_id')->where(['is_delete' => self::DELETE_NOT]);
    }

    /**
     * 判断是否回复消息
     * @return bool
     * User Long
     * Date 2024/8/29
     */
    public function isReplyComment()
    {
        return $this->reply_comment_id != self::TOP_LEVEL_COMMENT;
    }
}
