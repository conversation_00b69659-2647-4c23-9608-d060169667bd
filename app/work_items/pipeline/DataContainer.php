<?php

declare(strict_types=1);

namespace app\work_items\pipeline;

/**
 * Class DataContainer
 * @package app\work_items\pipeline
 *
 * 在流水线处理步骤之间传递数据和状态。
 * @property array $rawQueryConditions 原始查询条件参数 (来自esSearchSummaryForTree的$params)
 * @property array $esQuery
 * @property ?array $rawEsResponse
 * @property array $flatListData
 * @property array $groupedData ES原始分组数据或处理后的带标签的分组数据
 * @property array $allNodesForTree 用于构建树的所有节点（扁平数组）
 * @property array $enrichedItemsMap 丰富后的节点映射 [cnt_id => item_details]
 * @property ?array $statistics 引用传递，最终会被填充
 * @property ?array $finalResult
 * @property ?int $projectId
 * @property array $processedFieldList
 * @property bool $isTreeOutput
 * @property bool $showParents
 * @property bool $showChildren
 * @property array $mainDataSort
 * @property ?string $mainDataGroupBy
 * @property array $childrenOrder
 * @property mixed $itemProcessor 可选的回调函数，用于处理每个获取到的数据项
 */
class DataContainer
{
    public array $rawQueryConditions = []; // 重命名，存储原始的查询条件参数
    public array $esQuery = [];
    public ?array $rawEsResponse = null;
    public array $flatListData = [];
    public array $groupedData = [];
    public array $allNodesForTree = [];
    public array $enrichedItemsMap = [];
    public ?array $statistics = null;
    public ?array $finalResult = null;
    public string|int|array|null $projectId = null;
    public array $processedFieldList = [];
    public bool $isTreeOutput = true;
    public bool $showParents = true;
    public bool $showChildren = true;
    public array $mainDataSort = [];
    public ?string $mainDataGroupBy = null;
    public array $childrenOrder = [];
    public $itemProcessor = null; // callable 类型不能直接用于属性类型提示

    public function __construct(array $initialParams)
    {
        // 确保从 $initialParams 中获取原始的查询条件数组
        // 之前在 WorkItemsEsLogic 中，原始 $params 被作为 $initialParams['original_params'] 传递
        $this->rawQueryConditions = $initialParams['original_params'] ?? [];
        // 从 initialParams 初始化其他相关属性
        $this->isTreeOutput = $initialParams['isTreeOutput'] ?? true;
        $this->showParents = $initialParams['showParents'] ?? true;
        $this->showChildren = $initialParams['showChildren'] ?? true;
        $this->mainDataSort = $initialParams['mainDataSort'] ?? [];
        $this->mainDataGroupBy = $initialParams['mainDataGroupBy'] ?? null;
        $this->childrenOrder = $initialParams['childrenOrder'] ?? [];
        $this->itemProcessor = $initialParams['itemProcessor'] ?? null;
        // $statistics 将通过引用传递，无需在此处初始化
        // $fieldList 将在 WorkItemsEsLogic 中处理后设置
    }
}