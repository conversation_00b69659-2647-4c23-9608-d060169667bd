<?php

declare(strict_types=1);

namespace app\work_items\pipeline\processors;

use app\work_items\model\WorkItemsModel;
use app\work_items\pipeline\DataContainer;
use app\work_items\pipeline\WorkItemProcessingStepInterface;
use app\work_items\logic\WorkItemsEsLogic; // 用于访问常量

/**
 * Class TreeBuildingProcessor
 * @package app\work_items\pipeline\processors
 *
 * 负责根据已获取和丰富的所有节点数据，构建最终的树形结构。
 */
class TreeBuildingProcessor implements WorkItemProcessingStepInterface
{
    /**
     * 构建树形结构。
     *
     * @param DataContainer $dataContainer 数据容器。
     * @param array $params 额外参数（当前未使用）。
     * @return DataContainer 处理后的数据容器。
     */
    public function process(DataContainer $dataContainer, array $params = []): DataContainer
    {
        if (!$dataContainer->isTreeOutput) {
            return $dataContainer; // 如果不输出树，则跳过此步骤
        }

        $inputGroupedData = $dataContainer->groupedData; // 这是 DataFetchingProcessor 或 GroupedDataProcessingProcessor 的输出
        $enrichedItemsMap = $dataContainer->enrichedItemsMap;
        $showParents = $dataContainer->showParents;
        $mainDataGroupBy = $dataContainer->mainDataGroupBy;
        $finalLabeledGroupDataForTree = []; // 将用于构建树的、结构一致的分组数据

        if ($mainDataGroupBy) {
            // 如果有分组，GroupedDataProcessingProcessor 应该已经处理了 $inputGroupedData
            // 使其结构为: [['group_key' => ..., 'group_label' => ..., 'items' => ..., 'count' => ...], ...]
            // TreeBuildingProcessor 期望的 $labeledGroupData 结构是:
            // ['group_key_1' => ['items' => [...], 'group_label' => 'Label1', 'count' => N], ...]
            // $inputGroupedData 此刻是由 GroupedDataProcessingProcessor 处理后的格式
            foreach ($inputGroupedData as $groupInfo) {
                if (isset($groupInfo['group_key'])) {
                    $finalLabeledGroupDataForTree[$groupInfo['group_key']] = [
                        'children'    => $groupInfo['children'], // 使用 'children'
                        'group_label' => $groupInfo['group_label'],
                        'count'       => $groupInfo['count']
                    ];
                }
            }
        } else {
            // 没有分组，但 isTreeOutput = true
            // 我们需要使用已经被 FlatListDataProcessingProcessor 处理过的 flatListData
            $processedFlatList = $dataContainer->flatListData;
            $defaultGroupKey = WorkItemsEsLogic::DEFAULT_ES_GROUP_MISSING_KEY;
            $finalLabeledGroupDataForTree = [
                $defaultGroupKey => [
                    'children'    => $processedFlatList, // 使用 'children'
                    'group_label' => $defaultGroupKey,
                    'count'       => count($processedFlatList)
                ]
            ];
        }

        $resultTree = [];
        // $isActuallyGrouped 的判断现在基于 $mainDataGroupBy，因为 $finalLabeledGroupDataForTree 总是 key-value 结构
        $isActuallyGrouped = (bool)$mainDataGroupBy;

        // 预先构建全局子节点映射表
        $trueGlobalChildrenMap = [];
        foreach ($enrichedItemsMap as $itemId => $itemData) {
            $directParentId = $itemData['parent_id'] ?? null;
            if ($directParentId !== null && isset($enrichedItemsMap[$directParentId])) {
                $trueGlobalChildrenMap[$directParentId][] = $itemId;
            }
        }

        foreach ($finalLabeledGroupDataForTree as $groupKey => $groupDetails) {
            // $groupDetails 现在保证有 'children', 'group_label', 'count' 键
            $mainItemsInGroup = $groupDetails['children'] ?? []; // 使用 'children'，并提供默认空数组
            $groupLabel = $groupDetails['group_label'];
            $groupCount = $groupDetails['count'];

            $groupRootNodes = [];
            $processedRootsInGroup = []; // 用于跟踪已处理的根节点，避免重复构建

            foreach ($mainItemsInGroup as $entryItemArray) {
                if (!isset($entryItemArray['cnt_id']) || !isset($enrichedItemsMap[$entryItemArray['cnt_id']])) {
                    continue;
                }
                $entryItemId = $entryItemArray['cnt_id'];
                $entryItemDetails = $enrichedItemsMap[$entryItemId];

                $actualRootCandidateDetails = $entryItemDetails;

                if (isset($entryItemDetails['cnt_type']) && (int)$entryItemDetails['cnt_type'] === WorkItemsModel::CNT_TYPE_FLAW) {
                    // 缺陷总是根。为避免重复，将其从其父节点的子节点列表中移除。
                    $parentId = $entryItemDetails['parent_id'] ?? null;
                    if ($parentId !== null && isset($trueGlobalChildrenMap[$parentId])) {
                        $childIndex = array_search($entryItemId, $trueGlobalChildrenMap[$parentId]);
                        if ($childIndex !== false) {
                            unset($trueGlobalChildrenMap[$parentId][$childIndex]);
                        }
                    }
                } elseif ($showParents) {
                    $currentItemIdToTrace = $entryItemId;
                    while (isset($enrichedItemsMap[$currentItemIdToTrace])) {
                        $currentTraceItem = $enrichedItemsMap[$currentItemIdToTrace];
                        if (isset($currentTraceItem['cnt_type']) && (int)$currentTraceItem['cnt_type'] === WorkItemsModel::CNT_TYPE_FLAW && $currentItemIdToTrace === $actualRootCandidateDetails['cnt_id']) {
                            break;
                        }
                        $directParentId = $currentTraceItem['parent_id'] ?? null;
                        if ($directParentId !== null && isset($enrichedItemsMap[$directParentId])) {
                            $potentialParentDetails = $enrichedItemsMap[$directParentId];
                            if (isset($potentialParentDetails['cnt_type']) && (int)$potentialParentDetails['cnt_type'] === WorkItemsModel::CNT_TYPE_FLAW) {
                                break;
                            }
                            $actualRootCandidateDetails = $potentialParentDetails;
                            $currentItemIdToTrace = $directParentId;
                        } else {
                            break;
                        }
                    }
                }
                
                $rootNodeIdForTree = $actualRootCandidateDetails['cnt_id'];

                if (!isset($processedRootsInGroup[$rootNodeIdForTree])) {
                    $treeNode = $this->buildCompleteTreeRecursive(
                        $rootNodeIdForTree,
                        $enrichedItemsMap,
                        $trueGlobalChildrenMap
                    );
                    if ($treeNode) {
                        $groupRootNodes[] = $treeNode;
                    }
                    $processedRootsInGroup[$rootNodeIdForTree] = true;
                }
            }

            if ($isActuallyGrouped) {
                $resultTree[] = [
                    'group_key'   => $groupKey,
                    'group_label' => $groupLabel,
                    'children'    => $groupRootNodes, // 使用 'children'
                    'count'       => $groupCount
                ];
            } else {
                // 如果没有实际分组（例如，只有一个默认组），则直接返回根节点列表
                $resultTree = $groupRootNodes;
            }
        }
        
        if ($isActuallyGrouped) { // 即 $mainDataGroupBy 不为 null
            $dataContainer->finalResult = [
                '_is_grouped_result' => true, // 添加标记
                'groups'             => $resultTree // $resultTree 此时是分组列表
            ];
        } else {
            // 非分组树， $resultTree 直接是根节点列表
            $dataContainer->finalResult = $resultTree;
        }
        
        return $dataContainer;
    }

    /**
     * 辅助函数：递归构建子树。
     */
    private function buildCompleteTreeRecursive(
        $currentNodeId,
        array &$allEnrichedItemsMap,
        array &$trueGlobalChildrenMap
    ): ?array {
        if (!isset($allEnrichedItemsMap[$currentNodeId])) {
            return null;
        }

        $node = $allEnrichedItemsMap[$currentNodeId];
        unset($node['parents_data']); // parents_data 通常用于扁平列表，树结构中不需要

        $node['children'] = [];
        $directChildIds = $trueGlobalChildrenMap[$currentNodeId] ?? [];

        foreach ($directChildIds as $childId) {
            $childNodeTree = $this->buildCompleteTreeRecursive(
                $childId,
                $allEnrichedItemsMap,
                $trueGlobalChildrenMap
            );
            if ($childNodeTree) {
                $node['children'][] = $childNodeTree;
            }
        }
        // 子节点排序逻辑可以根据需要在此处添加，
        // 例如使用 $dataContainer->childrenOrder (如果传递到此方法)
        return $node;
    }
}