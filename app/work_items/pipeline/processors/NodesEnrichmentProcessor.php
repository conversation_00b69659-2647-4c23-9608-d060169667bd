<?php

declare(strict_types=1);

namespace app\work_items\pipeline\processors;

use app\work_items\logic\WorkItemsEsLogic;
use app\work_items\pipeline\DataContainer;
use app\work_items\pipeline\WorkItemProcessingStepInterface;

/**
 * Class NodesEnrichmentProcessor
 * @package app\work_items\pipeline\processors
 *
 * 负责对所有为树构建收集到的节点进行统一的丰富处理。
 */
class NodesEnrichmentProcessor implements WorkItemProcessingStepInterface
{
    private WorkItemsEsLogic $workItemsEsLogic;

    public function __construct()
    {
        $this->workItemsEsLogic = WorkItemsEsLogic::getInstance();
    }

    /**
     * 丰富节点数据。
     *
     * @param DataContainer $dataContainer 数据容器。
     * @param array $params 额外参数（当前未使用）。
     * @return DataContainer 处理后的数据容器。
     */
    public function process(DataContainer $dataContainer, array $params = []): DataContainer
    {
        if (!$dataContainer->isTreeOutput || empty($dataContainer->allNodesForTree)) {
            // 如果不输出树，或者没有节点需要丰富，则跳过
            return $dataContainer;
        }

        $allNodesForTree = $dataContainer->allNodesForTree; // 由 TreeNodesFetchingProcessor 填充
        $processedFlatListData = $dataContainer->flatListData; // 由 FlatListDataProcessingProcessor 处理过的主数据项
        $itemProcessor = $dataContainer->itemProcessor;
        $processedFieldList = $dataContainer->processedFieldList;
        
        $enrichedItemsMap = [];

        // 1. 将已经由 FlatListDataProcessingProcessor 处理过的主数据项直接放入 enrichedItemsMap
        $processedFlatListIds = [];
        foreach ($processedFlatListData as $item) {
            if (isset($item['cnt_id'])) {
                $enrichedItemsMap[$item['cnt_id']] = $item;
                $processedFlatListIds[$item['cnt_id']] = true;
            }
        }

        // 2. 处理 allNodesForTree 中尚未处理的节点 (即新获取的父/子节点)
        $nodesToProcess = [];
        foreach ($allNodesForTree as $node) {
            if (isset($node['cnt_id']) && !isset($processedFlatListIds[$node['cnt_id']])) {
                $nodesToProcess[] = $node;
            } elseif (isset($node['cnt_id']) && isset($processedFlatListIds[$node['cnt_id']]) && !isset($enrichedItemsMap[$node['cnt_id']])) {
                // 如果节点在 flatListData 中但由于某种原因没进入 enrichedItemsMap (理论上不应发生)
                // 或者，如果 allNodesForTree 中的版本比 flatListData 中的更新（不太可能）
                // 为简单起见，如果已在 flatListData 中处理过，我们信任那个版本。
                // 如果需要确保 enrichedItemsMap 包含所有 allNodesForTree 中的项（即使是已处理的），
                // 可以在这里添加： $enrichedItemsMap[$node['cnt_id']] = $processedFlatListData[array_search($node['cnt_id'], array_column($processedFlatListData, 'cnt_id'))];
                // 但目前的逻辑是，已处理的 flatListData 中的项已经加入 enrichedItemsMap。
            }
        }
        
        if (!empty($nodesToProcess)) {
            // 应用 itemProcessor (如果提供)
            if ($itemProcessor && is_callable($itemProcessor)) {
                $tempProcessedNodes = [];
                foreach ($nodesToProcess as $item) {
                    $processedItem = call_user_func($itemProcessor, $item);
                    if ($processedItem !== null) {
                        $tempProcessedNodes[] = $processedItem;
                    }
                }
                $nodesToProcess = $tempProcessedNodes;
            }

            // 合并额外数据
            if (!empty($nodesToProcess)) {
                $fieldsForMerge = $processedFieldList;
                if (empty($fieldsForMerge) && !empty($nodesToProcess[0]) && is_array($nodesToProcess[0])) {
                    $fieldsForMerge = array_keys($nodesToProcess[0]);
                }
                $this->workItemsEsLogic->mergeData($fieldsForMerge, $nodesToProcess);
            }

            // 将新处理的节点添加到 enrichedItemsMap
            foreach ($nodesToProcess as $item) {
                if (isset($item['cnt_id'])) {
                    $enrichedItemsMap[$item['cnt_id']] = $item;
                }
            }
        }
        
        // 确保 enrichedItemsMap 包含 allNodesForTree 中的所有项的最新（已处理）版本
        // 对于那些最初来自 flatListData 的项，它们已经是处理过的。
        // 对于新获取的父/子项，它们现在也处理过了。
        // 如果 allNodesForTree 可能包含比 flatListData 中更新的 "主数据项" 版本（不太可能），则需要更复杂的合并。
        // 当前假设：flatListData 中的主数据项是权威的已处理版本，新获取的节点需要处理。
        // 最终，enrichedItemsMap 应包含 allNodesForTree 中所有节点的唯一、已处理版本。
        // 上述逻辑应该已经实现了这一点。

        $dataContainer->enrichedItemsMap = $enrichedItemsMap;

        return $dataContainer;
    }
}