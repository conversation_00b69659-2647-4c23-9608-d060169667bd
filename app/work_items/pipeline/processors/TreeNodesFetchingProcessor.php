<?php

declare(strict_types=1);

namespace app\work_items\pipeline\processors;

use app\work_items\logic\WorkItemsEsLogic;
use app\work_items\pipeline\DataContainer;
use app\work_items\pipeline\WorkItemProcessingStepInterface;
use Elastic\Elasticsearch\Client as EsClient;
use think\facade\Env;
use basic\BaseModel; // 用于 DELETE_NOT 常量

/**
 * Class TreeNodesFetchingProcessor
 * @package app\work_items\pipeline\processors
 *
 * 负责获取构建完整树所需的所有相关节点（父节点和子节点）。
 */
class TreeNodesFetchingProcessor implements WorkItemProcessingStepInterface
{
    private EsClient $esClient;
    private mixed $esIndex;

    public function __construct()
    {
        $this->esClient = \utils\EsClientFactory::getInstance();
        $this->esIndex = Env::get('es.content_index');
    }

    /**
     * 获取树节点。
     *
     * @param DataContainer $dataContainer 数据容器。
     * @param array $params 额外参数（当前未使用）。
     * @return DataContainer 处理后的数据容器。
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     */
    public function process(DataContainer $dataContainer, array $params = []): DataContainer
    {
        if (!$dataContainer->isTreeOutput) {
            return $dataContainer; // 如果不输出树，则跳过此步骤
        }

        $initialMainDataFlat = $dataContainer->flatListData; // 应该已经是处理过的
        $showParents = $dataContainer->showParents;
        $showChildren = $dataContainer->showChildren;
        $processedFieldList = $dataContainer->processedFieldList;
        $childrenOrder = $dataContainer->childrenOrder;

        $allNodesMap = []; // 以 cnt_id 为键，确保节点唯一性

        foreach ($initialMainDataFlat as $item) {
            if (isset($item['cnt_id'])) {
                $allNodesMap[$item['cnt_id']] = $item;
            }
        }

        // 获取父节点
        if ($showParents) {
            $parentIdsToFetchInNextIteration = [];
            foreach ($allNodesMap as $node) {
                if (isset($node['parent_id']) && $node['parent_id'] !== null && !isset($allNodesMap[$node['parent_id']])) {
                    $parentIdsToFetchInNextIteration[$node['parent_id']] = true;
                }
            }
            $parentIdsToFetchInNextIteration = array_keys($parentIdsToFetchInNextIteration);

            while (!empty($parentIdsToFetchInNextIteration)) {
                $currentBatchToFetch = $parentIdsToFetchInNextIteration;
                $parentIdsToFetchInNextIteration = [];

                $parentsEsParams = [
                    'index' => $this->esIndex,
                    'body'  => [
                        'query'   => [
                            'bool' => [
                                'must' => [
                                    ['terms' => ['cnt_id' => $currentBatchToFetch]],
                                    ['term' => ['is_delete' => BaseModel::DELETE_NOT]]
                                ]
                            ]
                        ],
                        '_source' => $processedFieldList,
                        'size'    => count($currentBatchToFetch)
                    ]
                ];
                $parentsResponse = $this->esClient->search($parentsEsParams)->getBody();
                $parentsResp = json_decode((string)$parentsResponse, true);
                $fetchedParents = array_column($parentsResp['hits']['hits'] ?? [], '_source');

                if (empty($fetchedParents)) {
                    break;
                }

                foreach ($fetchedParents as $pItem) {
                    if (isset($pItem['cnt_id']) && !isset($allNodesMap[$pItem['cnt_id']])) {
                        $allNodesMap[$pItem['cnt_id']] = $pItem;
                        if (isset($pItem['parent_id']) && $pItem['parent_id'] !== null && !isset($allNodesMap[$pItem['parent_id']])) {
                            $parentIdsToFetchInNextIteration[$pItem['parent_id']] = true;
                        }
                    }
                }
                $parentIdsToFetchInNextIteration = array_keys($parentIdsToFetchInNextIteration);
            }
        }

        // 获取子节点 (后代)
        if ($showChildren && !empty($initialMainDataFlat)) {
            $mainDataIds = array_column($initialMainDataFlat, 'cnt_id');
            $mainDataIds = array_filter(array_unique($mainDataIds));

            if (!empty($mainDataIds)) {
                $childrenEsParams = [
                    'index' => $this->esIndex,
                    'body'  => [
                        'query'   => [
                            'bool' => [
                                'must'     => [
                                    ['terms' => ['parents' => $mainDataIds]],
                                    ['term' => ['is_delete' => BaseModel::DELETE_NOT]]
                                ],
                                'must_not' => [
                                    ['ids' => ['values' => array_keys($allNodesMap)]]
                                ]
                            ]
                        ],
                        '_source' => $processedFieldList,
                        'sort'    => !empty($childrenOrder) ? $childrenOrder : [['_doc' => 'asc']],
                        'size'    => WorkItemsEsLogic::NOT_PAGE_MAX
                    ]
                ];

                $childrenResponse = $this->esClient->search($childrenEsParams)->getBody();
                $childrenResp = json_decode((string)$childrenResponse, true);
                $fetchedDescendants = array_column($childrenResp['hits']['hits'] ?? [], '_source');

                foreach ($fetchedDescendants as $dItem) {
                    if (isset($dItem['cnt_id']) && !isset($allNodesMap[$dItem['cnt_id']])) {
                        $allNodesMap[$dItem['cnt_id']] = $dItem;
                    }
                }
            }
        }
        
        $dataContainer->allNodesForTree = array_values($allNodesMap);
        return $dataContainer;
    }
}