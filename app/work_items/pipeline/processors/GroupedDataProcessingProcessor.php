<?php

declare(strict_types=1);

namespace app\work_items\pipeline\processors;

use app\work_items\logic\WorkItemsEsLogic;
use app\work_items\pipeline\DataContainer;
use app\work_items\pipeline\WorkItemProcessingStepInterface;
use app\infrastructure\model\FieldConfigModel;
use field_utils\Transfer;

/**
 * Class GroupedDataProcessingProcessor
 * @package app\work_items\pipeline\processors
 *
 * 负责处理分组数据，应用项目处理器，合并数据，并添加组标签。
 */
class GroupedDataProcessingProcessor implements WorkItemProcessingStepInterface
{
    private WorkItemsEsLogic $workItemsEsLogic;

    public function __construct()
    {
        $this->workItemsEsLogic = WorkItemsEsLogic::getInstance();
    }

    /**
     * 处理分组数据。
     *
     * @param  DataContainer  $dataContainer  数据容器。
     * @param  array          $params         额外参数（当前未使用）。
     * @return DataContainer 处理后的数据容器。
     */
    public function process(DataContainer $dataContainer, array $params = []): DataContainer
    {
        if ( ! $dataContainer->mainDataGroupBy) {
            // 如果没有指定分组字段，则跳过此处理器
            return $dataContainer;
        }

        $rawGroupedDataFromEs = $dataContainer->groupedData; // 这是ES返回的原始分组结构
        $mainDataGroupBy = $dataContainer->mainDataGroupBy;
        $projectId = $dataContainer->projectId;
        $processedFieldList = $dataContainer->processedFieldList;
        $itemProcessor = $dataContainer->itemProcessor;

        $processedGroups = [];
        $transferUtil = null;

        if ($mainDataGroupBy && $projectId !== null) {
            $moduleId = [
                FieldConfigModel::MODULE_TYPE_REQUIREMENT,
                FieldConfigModel::MODULE_TYPE_TASK,
                FieldConfigModel::MODULE_TYPE_DEFECT,
            ];
            $transferUtil = new Transfer($moduleId, $projectId);
        }

        foreach ($rawGroupedDataFromEs as $groupKey => $itemsInGroup) {
            $processedGroupItems = [];
            if ($itemProcessor && is_callable($itemProcessor)) {
                foreach ($itemsInGroup as $item) {
                    $processed = call_user_func($itemProcessor, $item);
                    if ($processed !== null) {
                        $processedGroupItems[] = $processed;
                    }
                }
            } else {
                $processedGroupItems = $itemsInGroup;
            }

            // 合并额外数据
            if ( ! empty($processedGroupItems)) {
                $this->workItemsEsLogic->mergeData($processedFieldList, $processedGroupItems);
            }

            $currentGroupKeyString = (string)$groupKey;
            // ES 返回的 missing key 可能是 WorkItemsEsLogic::DEFAULT_ES_GROUP_MISSING_KEY
            // 或者在 DataFetchingProcessor 中已转换为 '_default_group_'
            // 同时也要处理值为 0 的情况
            $actualGroupKeyForLabel = ($currentGroupKeyString === WorkItemsEsLogic::DEFAULT_ES_GROUP_MISSING_KEY ||
                                     $currentGroupKeyString === '_default_group_' ||
                                     $currentGroupKeyString === '0' ||
                                     $currentGroupKeyString === 0)
                ? '_default_group_'
                : $currentGroupKeyString;

            $groupLabel = $actualGroupKeyForLabel; // 默认标签

            if ($actualGroupKeyForLabel === '_default_group_' || $actualGroupKeyForLabel === WorkItemsEsLogic::DEFAULT_ES_GROUP_MISSING_KEY) {
                $groupLabel = '--';
            } elseif ($transferUtil) { // 仅当不是默认/缺失组且 transferUtil 可用时才尝试解析
                try {
                    $parsedLabel = $transferUtil->parse($mainDataGroupBy, $actualGroupKeyForLabel);
                    if (is_array($parsedLabel) && isset($parsedLabel['label'])) {
                        $groupLabel = $parsedLabel['label'];
                    } elseif (is_string($parsedLabel) || is_numeric($parsedLabel)) {
                        $groupLabel = (string)$parsedLabel;
                    }
                } catch (\Throwable $e) {
                    $groupLabel = (string)$mainDataGroupBy;
                }
            }

            // 获取原始聚合中的文档计数。
            // $rawGroupedDataFromEs 的结构是 ['group_key' => [items...]]
            // items 的数量就是该组的原始文档数（在 top_hits size 限制内）
            $originalBucketDocCount = count($itemsInGroup);


            $processedGroups[] = [
                'group_key'   => $actualGroupKeyForLabel, // 使用处理过的键
                'group_label' => $groupLabel,
                'children'    => $processedGroupItems, // 将 'items' 改为 'children'
                'count'       => $originalBucketDocCount
            ];
        }

        // 将默认分组移到末尾
        $defaultGroups = [];
        $nonDefaultGroups = [];

        foreach ($processedGroups as $group) {
            if ($group['group_key'] === '_default_group_') {
                $defaultGroups[] = $group;
            } else {
                $nonDefaultGroups[] = $group;
            }
        }

        // 重新排序：非默认分组在前，默认分组在后
        $processedGroups = array_merge($nonDefaultGroups, $defaultGroups);

        $dataContainer->groupedData = $processedGroups; // 更新为处理后的分组结构

        // 如果是非树形输出的分组列表，这也可能是最终结果
        if ($dataContainer->mainDataGroupBy && ! $dataContainer->isTreeOutput) {
            $dataContainer->finalResult = [
                '_is_grouped_result' => true,
                'groups'             => $processedGroups,
            ];
        }

        return $dataContainer;
    }
}