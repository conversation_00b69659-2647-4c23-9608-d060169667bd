<?php

declare(strict_types=1);

namespace app\work_items\pipeline\processors;

use app\work_items\logic\WorkItemsEsLogic;
use app\work_items\model\WorkItemsModel;
use app\work_items\pipeline\DataContainer;
use app\work_items\pipeline\WorkItemProcessingStepInterface;

/**
 * Class QueryBuildingProcessor
 * @package app\work_items\pipeline\processors
 *
 * 负责构建基础的Elasticsearch布尔查询。
 */
class QueryBuildingProcessor implements WorkItemProcessingStepInterface
{
    private WorkItemsEsLogic $workItemsEsLogic;

    // 理想情况下，WorkItemsEsLogic 实例应通过依赖注入传入
    // 但为简化，这里暂时在构造函数中获取实例或直接调用静态方法（如果适用）
    public function __construct()
    {
        // 注意：直接在这里实例化 WorkItemsEsLogic 可能不是最佳实践，
        // 因为 WorkItemsEsLogic 自身构造函数需要 ES Client。
        // 一个更好的方法是让流水线协调器传递一个 WorkItemsEsLogic 实例，
        // 或者将 generateEsQueryParams 和 getNotDelParams 变为静态或移至可访问的服务。
        // 假设 WorkItemsEsLogic::getInstance() 可以工作或相关方法是静态的。
        // 为简单起见，我们将假设 WorkItemsEsLogic 的相关方法是可访问的。
        // 如果 generateEsQueryParams 是 public，我们可以通过 WorkItemsEsLogic::getInstance() 调用它。
        $this->workItemsEsLogic = WorkItemsEsLogic::getInstance();
    }

    /**
     * 构建ES查询并将其存储在DataContainer中。
     *
     * @param  DataContainer  $dataContainer  数据容器。
     * @param  array          $params         额外参数（当前未使用）。
     * @return DataContainer 处理后的数据容器。
     */
    public function process(DataContainer $dataContainer, array $processorParams = []): DataContainer
    {
        $originalParams = $dataContainer->rawQueryConditions; // 使用新的属性名
        $esQueryBool = [];
        // 处理 public_field
        $publicFieldConditions = $originalParams['public_field'] ?? [];
        if ( ! is_array($publicFieldConditions)) {
            $publicFieldConditions = []; // 确保是数组
        }
        $publicFieldConditions[] = $this->workItemsEsLogic->getNotDelParams();
        // generateEsQueryParams 内部的 resolve 方法会通过引用修改 $publicFieldConditions
        $projectIdPar = [];
        if ($dataContainer->projectId) {
            $projectIdPar = [
                [
                    'field_name' => 'project_id',
                    'value'      => ! is_array($dataContainer->projectId) ? [$dataContainer->projectId] : $dataContainer->projectId,
                    'type'       => 'selector',
                ]
            ];
        }
        $esQueryBool = $this->workItemsEsLogic->generateEsQueryParams(array_merge($publicFieldConditions, $projectIdPar));

        $shouldClauses = [];

        // 处理 demand_field
        if (isset($originalParams['demand_field']) && ! empty($originalParams['demand_field'])) {
            $demandConditions = $originalParams['demand_field'];
            if ( ! is_array($demandConditions)) {
                $demandConditions = [];
            }
            $demandConditions[] = ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_DEMAND, 'type' => 'term', 'operate_type' => 'equal'];
            $shouldClauses[] = ['bool' => $this->workItemsEsLogic->generateEsQueryParams(array_merge($demandConditions, $projectIdPar))];
        }

        // 处理 task_field
        if (isset($originalParams['task_field']) && ! empty($originalParams['task_field'])) {
            $taskConditions = $originalParams['task_field'];
            if ( ! is_array($taskConditions)) {
                $taskConditions = [];
            }
            $taskConditions[] = ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_TASK, 'type' => 'term', 'operate_type' => 'equal'];
            $shouldClauses[] = ['bool' => $this->workItemsEsLogic->generateEsQueryParams(array_merge($taskConditions, $projectIdPar))];
        }

        // 处理 flaw_field
        if (isset($originalParams['flaw_field']) && ! empty($originalParams['flaw_field'])) {
            $flawConditions = $originalParams['flaw_field'];
            if ( ! is_array($flawConditions)) {
                $flawConditions = [];
            }
            $flawConditions[] = ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_FLAW, 'type' => 'term', 'operate_type' => 'equal'];
            $shouldClauses[] = ['bool' => $this->workItemsEsLogic->generateEsQueryParams(array_merge($flawConditions, $projectIdPar))];
        }

        if ( ! empty($shouldClauses)) {
            // 如果 $esQueryBool 已经有 'should' (不太可能，因为基础查询通常不包含should), 则合并
            // 否则，直接赋值
            if (isset($esQueryBool['should']) && is_array($esQueryBool['should'])) {
                $esQueryBool['should'] = array_merge($esQueryBool['should'], $shouldClauses);
            } else {
                $esQueryBool['should'] = $shouldClauses;
            }
            $esQueryBool["minimum_should_match"] = 1;
        }

        $dataContainer->esQuery = $esQueryBool;
        return $dataContainer;
    }
}