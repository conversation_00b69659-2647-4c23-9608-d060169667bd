<?php

declare(strict_types=1);

namespace app\work_items\pipeline\processors;

use app\work_items\logic\WorkItemsEsLogic;
use app\work_items\pipeline\DataContainer;
use app\work_items\pipeline\WorkItemProcessingStepInterface;

/**
 * Class FlatListDataProcessingProcessor
 * @package app\work_items\pipeline\processors
 *
 * 负责处理扁平数据列表，应用项目处理器和合并额外数据。
 */
class FlatListDataProcessingProcessor implements WorkItemProcessingStepInterface
{
    private WorkItemsEsLogic $workItemsEsLogic;

    public function __construct()
    {
        // 同样，理想情况下 WorkItemsEsLogic 实例应通过依赖注入传入。
        $this->workItemsEsLogic = WorkItemsEsLogic::getInstance();
    }

    /**
     * 处理扁平数据列表。
     *
     * @param DataContainer $dataContainer 数据容器。
     * @param array $params 额外参数（当前未使用）。
     * @return DataContainer 处理后的数据容器。
     */
    public function process(DataContainer $dataContainer, array $params = []): DataContainer
    {
        $flatData = $dataContainer->flatListData;
        $itemProcessor = $dataContainer->itemProcessor;
        $processedFieldList = $dataContainer->processedFieldList;

        if (empty($flatData)) {
            return $dataContainer;
        }

        // 1. 应用 itemProcessor (如果提供)
        if ($itemProcessor && is_callable($itemProcessor)) {
            $processedItems = [];
            foreach ($flatData as $item) {
                $processed = call_user_func($itemProcessor, $item);
                if ($processed !== null) { // 处理器可以返回null来指示移除该项
                    $processedItems[] = $processed;
                }
            }
            $flatData = $processedItems;
        }

        // 2. 合并额外数据 (例如来自MySQL的 'contents', 'node_name', 'parent_name')
        // 注意：mergeData 期望 $data 是一个引用数组。
        if (!empty($flatData)) {
            $this->workItemsEsLogic->mergeData($processedFieldList, $flatData);
        }
        
        $dataContainer->flatListData = $flatData;

        // 如果是非分组输出的扁平列表，这也可能是最终结果
        if (!$dataContainer->mainDataGroupBy && !$dataContainer->isTreeOutput) {
            $dataContainer->finalResult = $flatData;
        }

        return $dataContainer;
    }
}