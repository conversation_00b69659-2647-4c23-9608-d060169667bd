<?php

declare(strict_types=1);

namespace app\work_items\pipeline;

/**
 * Interface WorkItemProcessingStepInterface
 * @package app\work_items\pipeline
 *
 * 定义流水线中单个处理步骤的接口。
 */
interface WorkItemProcessingStepInterface
{
    /**
     * 处理数据容器中的数据。
     *
     * @param DataContainer $dataContainer 在流水线步骤之间共享的数据容器。
     * @param array $params 此特定步骤可能需要的额外参数。
     * @return DataContainer 处理后的数据容器。
     */
    public function process(DataContainer $dataContainer, array $params = []): DataContainer;
}