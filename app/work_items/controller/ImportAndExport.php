<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/12/17 上午10:09
 */

namespace app\work_items\controller;

use app\work_items\logic\ImportAndExportLogic;
use basic\BaseController;
use excel_utils\Hanlder;
use resp\Result;
use think\App;
use think\Exception;
use think\Request;

class ImportAndExport extends BaseController
{

    private $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);

        $moduleId = $this->request->param('module_id', 0);
        if (!$moduleId){
            throw new Exception("module_id必填");
        }

        $this->logic = (new ImportAndExportLogic($moduleId));
    }


    /**
     * 导出模版
     * @param  Request  $request
     * @return \think\response\File
     * @throws Exception
     * <AUTHOR>
     * @date   2024/12/17 下午5:21
     */
    public function getExportHeaderTemplate(Request $request)
    {

        $result = $this->logic->getExportHeaderTemplate($request->post());
//        dd($result);

        return Hanlder::outputFile($result['data'], $result['fileName']);
    }


    /**
     * 导出
     * @param  Request  $request
     * @return \think\response\File
     * <AUTHOR>
     * @date   2024/12/12 下午5:01
     */
    public function export(Request $request)
    {

        $result = $this->logic->export($request->post());


        return Hanlder::outputFile($result['data'], $result['fileName']);
    }


    public function import(Request $request)
    {
        $result = $this->logic->import($request->post() + $request->file());

        if (is_array($result)) {

            return Result::success($result);
        } else {
            return $result;
        }
    }
}
