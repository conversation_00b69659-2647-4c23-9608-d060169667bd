<?php
/**
 * Desc 分类管理 - 控制器
 * User Long
 * Date 2024/08/27
 */

declare (strict_types=1);


namespace app\work_items\controller;


use app\project\logic\ProjectClassifyLogic;
use basic\BaseController;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\MissingParameterException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use resp\Result;
use think\App;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\response\Json;
use Throwable;

class Classify extends BaseController
{
    private ProjectClassifyLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->logic = new ProjectClassifyLogic($this->getSettingType());
    }

    /**
     * 新增分类
     * @return Json
     * User Long
     * Date 2024/9/26
     */
    public function create(): <PERSON><PERSON>
    {
        $params = $this->request->post([
            'project_id',
            'category_name',
            'pid',
            'remark',
            'sort'
        ]);

        $this->logic->create($params);

        return Result::success();
    }

    /**
     * 根据分类id集删除
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/26
     */
    public function delete(): Json
    {
        $categoryIds = $this->request->post('category_ids/a');

        $this->logic->delete($categoryIds);

        return Result::success();
    }

    /**
     * 根据分类id更新数据
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/9/26
     */
    public function update(): Json
    {
        $params = $this->request->post([
            'category_id',
            'category_name',
            'pid',
            'remark',
            'sort',
            'sort_data' => []
        ]);

        $this->logic->update($params['category_id'], $params);

        return Result::success();
    }

    /**
     * 分类详情
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/26
     */
    public function detail(): Json
    {
        $categoryId = $this->request->get('category_id/d');

        $res = $this->logic->detail((int)$categoryId);

        return Result::success($res);
    }

    /**
     * 分类列表
     * @return Json
     * @throws ClientResponseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws ServerResponseException
     * User Long
     * Date 2024/9/26
     */
    public function listQuery(): Json
    {
        $projectId = $this->request->get('project_id/d');

        $res = $this->logic->listQuery((int)$projectId);

        return Result::success($res);
    }

    /**
     * 分类 - 更新排序
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/26
     */
    public function updateSort(): Json
    {
        $sortData = $this->request->post('sort_data/a', []);

        $this->logic->updateSort($sortData);

        return Result::success();
    }

    /**
     * 获取相关需求id集合
     * @return Json
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/10/8
     */
    public function getRelevanceDemand(): Json
    {
        $categoryId = $this->request->get('category_id/d');

        $res = $this->logic->getRelevanceDemand((int)$categoryId);

        return Result::success($res);
    }

    /**
     * 修改相关需求
     * @return Json
     * @throws ClientResponseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws MissingParameterException
     * @throws ModelNotFoundException
     * @throws ServerResponseException
     * @throws Throwable
     * User Long
     * Date 2024/9/26
     */
    public function updateRelevanceDemand(): Json
    {
        $workItemIds = $this->request->post('work_item_ids/a');
        $targetCategoryId = $this->request->post('target_category_id/d');

        $this->logic->updateRelevanceDemand($workItemIds, (int)$targetCategoryId);

        return Result::success();
    }

    /**
     * 需求分类下拉数据
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/26
     */
    public function categorySelector(): Json
    {
        $projectId = convertProjectId($this->request->post('project_id'));
        $res = $this->logic->categorySelector($projectId);

        return Result::success($res);
    }
}
