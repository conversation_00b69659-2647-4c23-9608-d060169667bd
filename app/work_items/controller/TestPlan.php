<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/7/5 上午10:33
 */

namespace app\work_items\controller;

use app\infrastructure\logic\EnumLogic;
use app\work_items\logic\TestPlanLogic;
use basic\BaseController;
use resp\Result;
use think\App;
use think\Request;
use app\work_items\validate\TestPlanValidate;

class TestPlan extends BaseController
{
    private $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = TestPlanLogic::getInstance();
    }


    /**
     * 创建
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/26 17:50
     */
    public function create(Request $request)
    {
        $param = $request->post();
        $model = $this->logic->create($param);

        return Result::success($model);
    }


    /**
     * 删除
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function delete(Request $request)
    {
        $this->logic->delete($request->post('test_plan_id'));

        return Result::success();
    }


    /**
     * 更新
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function update(Request $request)
    {
        $param = $request->post();
        $model = $this->logic->update($param);

        return Result::success($model);
    }


    /**
     * 详情
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/27 21:25
     */
    public function detail(Request $request)
    {
        $res = $this->logic->detail($request->get('test_plan_id'));

        return Result::success($res);
    }


    /**
     * 分页查询
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function pageQuery(Request $request)
    {

        $data = $this->logic->pageQuery($request->post());

        return Result::success($data);
    }

    /**
     * 状态列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/10/28 15:39
     */
    public function getStatusList()
    {
        return Result::success(
            (new EnumLogic)->getList('plan_status_type')->map(function ($v) {
                return ['label' => $v['enum_name'], 'value' => $v['enum_value']];
            })
        );
    }

    /**
     * 测试计划类型
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/12/6 下午3:04
     */
    public function getPlanTypeList()
    {
        return Result::success(
            (new EnumLogic)->getList('test_plan_type')->map(function ($v) {
                return ['label' => $v['enum_name'], 'value' => $v['enum_value']];
            })
        );
    }


    /**
     * 批量删除
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * @throws \Throwable
     */
    public function batchDelete(Request $request)
    {
        $params = $request->post([
            'id_list',
        ]);

        $this->logic->batchDelete($params['id_list']);

        return Result::success(['msg'=>'删除成功']);
    }

    /**
     * 批量更新测试计划
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/12/7
     */
    public function batchUpdate(Request $request)
    {
        $params = $request->post();
        validate(TestPlanValidate::class)->scene('batchUpdate')->check($params);
        $result = $this->logic->batchUpdate($params);

        return Result::success($result);
    }
}
