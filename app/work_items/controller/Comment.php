<?php
/**
 * Desc 评论管理 - 控制器
 * User Long
 * Date 2024/08/28
 */

declare (strict_types=1);


namespace app\work_items\controller;

use app\work_items\logic\CommentLogic;
use basic\BaseController;
use resp\Result;
use think\App;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\response\Json;
use Throwable;

class Comment extends BaseController
{
    private CommentLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new CommentLogic($this->getSettingType());
    }

    /**
     * 新增评论
     * @return Json
     * User Long
     * Date 2024/8/28
     */
    public function create(): Json
    {
        $params = $this->request->post([
            'work_items_id',
            'content',
            'remark'
        ]);

        $this->logic->create($params);

        return Result::success();
    }

    /**
     * 根据评论id集删除
     * @return Json
     * User Long
     * Date 2024/8/28
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     */
    public function delete(): Json
    {
        $commentIds = $this->request->post('comment_ids/a');

        $this->logic->delete($commentIds);

        return Result::success();
    }

    /**
     * 根据评论id更新数据
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function update(): Json
    {
        $params = $this->request->post([
            'comment_id',
            'work_items_id',
            'content',
            'remark'
        ]);

        $this->logic->update((int)$params['comment_id'], $params);

        return Result::success();
    }

    /**
     * 回复评论
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function reply(): Json
    {
        $params = $this->request->post([
            'reply_id',
            'content',
            'remark'
        ]);

        $this->logic->reply((int)$params['reply_id'], $params);

        return Result::success();
    }


    /**
     * 评论详情
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function detail(): Json
    {
        $commentId = $this->request->get('comment_id/d');

        $res = $this->logic->detail((int)$commentId);

        return Result::success($res);
    }

    /**
     * 评论分页
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function listQuery(): Json
    {
        $workItemsId = $this->request->get('work_items_id/d');

        $res = $this->logic->listQuery((int)$workItemsId);

        return Result::success($res);
    }

    /**
     * 评论 - 置顶
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function top(): Json
    {
        $commentIds = $this->request->post('comment_ids/a');

        $this->logic->top($commentIds);

        return Result::success();
    }

    /**
     * 评论 - 取消置顶
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function restore(): Json
    {
        $commentIds = $this->request->post('comment_ids/a');

        $this->logic->restore($commentIds);

        return Result::success();
    }

    /**
     * 为多个工作项添加相同的评论
     * @return Json
     * @throws Throwable
     * User Long
     * Date 2024/9/25
     */
    public function createSameForMultiple(): Json
    {
        $params = $this->request->post([
            'work_items_ids',
            'content',
            'remark'
        ]);

        $this->logic->createSameForMultiple($params);

        return Result::success();
    }
}
