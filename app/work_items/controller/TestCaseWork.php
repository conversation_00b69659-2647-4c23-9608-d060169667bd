<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/9/29 14:49
 */

namespace app\work_items\controller;

use app\work_items\logic\TestCaseLogic;
use app\work_items\logic\TestCaseWorkLogic;
use app\work_items\validate\TestCaseWorkItemsValidate;
use basic\BaseController;
use exception\ParamsException;
use resp\Result;
use think\App;
use think\Request;

class TestCaseWork extends BaseController
{

    private $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new TestCaseWorkLogic();
    }

    /**
     * 关联
     * @param Request $request
     * @return \think\response\Json
     * @throws \Throwable
     * <AUTHOR>
     * @date 2024/10/10 14:50
     */
    public function relevancy(Request $request)
    {
        $params = $request->post([
            'id',
            'id_list',
            'relevancy_type',
        ]);

        validate(TestCaseWorkItemsValidate::class)->scene('relevancy')->check($params);

        $this->logic->relevancy($params['id'], $params['id_list'], $params['relevancy_type']);

        return Result::success();
    }

    /**
     * 解除关联
     * @param Request $request
     * @return \think\response\Json
     * @throws \Throwable
     * <AUTHOR>
     * @date 2024/10/10 14:53
     */
    public function del(Request $request)
    {
        $params = $request->post([
            'id',
            'id_list',
            'relevancy_type',
        ]);

        validate(TestCaseWorkItemsValidate::class)->scene('relevancy')->check($params);


        [$cntId, $caseId] = match ($params['relevancy_type']) {
            $this->logic::TYPE_CNT_ID => [
                $params['id'],
                $params['id_list'],
            ],
            $this->logic::TYPE_TEST_CASE_ID => [
                $params['id_list'],
                $params['id'],
            ],
            default => throw new ParamsException(),
        };
        $this->logic->del($cntId, $caseId);

        return Result::success();
    }

}