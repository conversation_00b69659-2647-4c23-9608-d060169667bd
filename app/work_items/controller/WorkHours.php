<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/7/5 上午10:33
 */

namespace app\work_items\controller;

use app\work_items\logic\WorkHoursLogic;
use basic\BaseController;
use resp\Result;
use think\App;
use think\Request;

class WorkHours extends BaseController
{
    private $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new WorkHoursLogic();
    }


    /**
     * 创建
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/26 17:50
     */
    public function create(Request $request)
    {
        $param = $request->post([
            'cnt_id',
            'type',
            'remark',
            'working_hours',
            'work_date',
        ]);
        // 获取autoChangeStatus参数，默认为true
        $autoChangeStatus = $request->post('auto_change_status', true);
        $model = $this->logic->create($param, 0, $autoChangeStatus);

        return Result::success($model);
    }


    /**
     * 删除
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function delete(Request $request)
    {
        // 获取autoChangeStatus参数，默认为true
        $autoChangeStatus = $request->post('auto_change_status', true);
        $this->logic->delete($request->post('work_hours_id'), $autoChangeStatus);

        return Result::success();
    }


    /**
     * 更新
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function update(Request $request)
    {
        $param = $request->post([
            'work_hours_id',
            'remark',
            'working_hours',
            'work_date',
        ]);
        // 获取autoChangeStatus参数，默认为true
        $autoChangeStatus = $request->post('auto_change_status', true);
        $model = $this->logic->update($param, $autoChangeStatus);

        return Result::success($model);
    }


    /**
     * 详情
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/27 21:25
     */
    public function detail(Request $request)
    {
        $res = $this->logic->detail($request->get('work_hours_id'));

        return Result::success($res);
    }

    /**
     *
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/27 21:25
     */
    public function detailByCntId(Request $request)
    {
        $param = $request->get([
            'cnt_id',
            'type',
            'work_date',
        ]);
        $res = $this->logic->detailByCntId($param);

        return Result::success($res);
    }


    /**
     * 分页查询
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function list(Request $request)
    {
        $params = $request->get([
            'cnt_id'
        ]);
        $data = $this->logic->pageQuery($params);

        return Result::success($data);
    }

}
