<?php
/**
 * 计划中的用例
 * User: 袁志凡
 * Date-Time: 2024/7/5 上午10:33
 */

namespace app\work_items\controller;

use app\infrastructure\logic\EnumLogic;
use app\infrastructure\model\EnumModel;
use app\work_items\logic\TestCaseLogic;
use app\work_items\logic\PlanUseCaseLogic;
use app\work_items\logic\WorkItemsLogic;
use app\work_items\validate\PlanUseCaseValidate;
use basic\BaseController;
use resp\Result;
use think\App;
use think\Request;

class PlanUseCase extends BaseController
{
    private $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = (new PlanUseCaseLogic());
    }


    /**
     * 通过目录添加用例
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/10/8 14:20
     */
    public function addBugByCategory(Request $request)
    {
        $params = $request->post([
            'test_plan_id',
            'id_list',
            'cnt_id',
        ]);
//        validate(TestPlanValidate::class)->scene('relevancy')->check($params);

        $this->logic->addBugByCategory($params['test_plan_id'], $params['id_list'], $params['cnt_id'] ?? 0);

        return Result::success();
    }

    /**
     * 通过需求添加用例
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/10/8 14:20
     */
    public function addBugByDemand(Request $request)
    {
        $params = $request->post([
            'test_plan_id',
            'id_list',
        ]);
        $this->logic->addBugByDemand($params['test_plan_id'], $params['id_list']);

        return Result::success();
    }

    /**
     * 移除（移除计划）或删除（需求下的用例联动删除计划外的关系）用例
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/10/8 14:20
     */
    public function delCase(Request $request)
    {
        $params = $request->post([
            'test_plan_id',
            'id_list',
            'cnt_id',
            'link',
        ]);
//        validate(TestPlanValidate::class)->scene('relevancy')->check($params);

        $this->logic->delCase($params['test_plan_id'], $params['id_list'], $params['cnt_id'] ?? null, $params['link'] ?? false);

        return Result::success();
    }


    /**
     * 移除（移除计划）或删除（需求下的用例联动删除计划外的关系）用例
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/10/8 14:20
     */
    public function delDemand(Request $request)
    {
        $params = $request->post([
            'test_plan_id',
            'cnt_id',
        ]);
//        validate(TestPlanValidate::class)->scene('relevancy')->check($params);

        $this->logic->delDemand($params['test_plan_id'], $params['cnt_id']);

        return Result::success();
    }


    /**
     * 移除（移除计划）或删除（需求下的用例联动删除计划外的关系）用例
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/10/8 14:20
     */
    public function delCategory(Request $request)
    {
        $params = $request->post([
            'test_plan_id',
            'project_category_id',
        ]);
//        validate(TestPlanValidate::class)->scene('relevancy')->check($params);

        $this->logic->delCategory($params['test_plan_id'], $params['project_category_id']);

        return Result::success();
    }


    /**
     * 分页查询
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function pageQuery(Request $request)
    {

        $params = $request->post([
            'test_plan_id',
            'field_list',
            'searchParams',
//            'order',
        ]);
        $data = $this->logic->pageQuery(
            $params['test_plan_id'],
            $params['field_list'],
            $params['searchParams'],
//            $params['order'] ?? []
        );

        return Result::success($data);
    }

    /**
     * 执行
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function execute(Request $request)
    {
        $params = $request->post([
            'test_plan_work_case_id',
            'result',
            'remark',
            'cnt_id_list',
        ]);

        validate(PlanUseCaseValidate::class)->scene('execute')->check($params);
        $this->logic->execute($params['test_plan_work_case_id'], $params['result'], $params['remark'] ?? '', $params['cnt_id_list'] ?? []);

        return Result::success();
    }

    /**
     * 批量执行
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function batchExecute(Request $request)
    {
        $params = $request->post([
            'test_plan_work_case_id_list',
            'result',
            'remark',
        ]);

        validate(PlanUseCaseValidate::class)->scene('batchExecute')->check($params);
        $this->logic->batchExecute($params['test_plan_work_case_id_list'], $params['result'], $params['remark'] ?? '');

        return Result::success();
    }

    /**
     * 执行用例明细
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function recordList(Request $request)
    {
        $params = $request->post([
            'test_plan_id',
            'test_case_id',
        ]);
        $data = $this->logic->recordList($params['test_plan_id'], $params['test_case_id']);

        return Result::success($data);
    }


    /**
     * 用例执行明细中的缺陷删除
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function delRecordBug(Request $request)
    {
        $params = $request->post([
            'id',
            'test_plan_work_case_id',
        ]);
        $this->logic->delRecordBug($params['id'], $params['test_plan_work_case_id']);

        return Result::success();
    }

    /**
     * 获取用例执行明细所有的关联缺陷
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function recordBugList(Request $request)
    {
        $params = $request->post([
            'test_plan_id',
            'test_case_id',
        ]);
        $data = $this->logic->recordBugList($params['test_plan_id'], $params['test_case_id']);

        return Result::success($data);
    }

    /**
     * 将关联的缺陷追加到最新的一条执行明细中，必须已有执行明细才关联
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function addBugToLatestRecord(Request $request)
    {
        $params = $request->post([
            'test_plan_work_case_id',
            'cnt_id_list',
        ]);
        $this->logic->addBugToLatestRecord($params['test_plan_work_case_id'], $params['cnt_id_list']);

        return Result::success();
    }

    /**
     * 统计
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function statistics(Request $request)
    {
        $params = $request->post([
            'test_plan_id',
        ]);
        $data = $this->logic->statistics($params['test_plan_id']);

        return Result::success($data);
    }

    /**
     * 缺陷的测试用例tab页
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function flawCaseList(Request $request)
    {
        $params = $request->get([
            'cnt_id',
        ]);
        $data = $this->logic->flawCaseList($params['cnt_id']);

        return Result::success($data);
    }

    /**
     * 测试用例的的缺陷tab页
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function caseFlawList(Request $request)
    {
        $params = $request->get([
            'test_case_id',
        ]);
        $data = $this->logic->caseFlawList($params['test_case_id']);

        return Result::success($data);
    }

    /**
     * 测试计划的缺陷tab页
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function planFlawList(Request $request)
    {
        $params = $request->get([
            'test_plan_id',

        ]);
        $data = $this->logic->planFlawList($params['test_plan_id']);

        return Result::success($data);
    }

    /**
     * 执行记录结果下拉
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function recordResultSelector()
    {
        $data = $this->logic->recordResultSelector();

        return Result::success($data);
    }

    /**
     * 执行记录结果下拉，包含未执行
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/26 17:51
     */
    public function recordResultAddNotExecutedSelector(Request $request)
    {
        $data = $this->logic->recordResultAddNotExecutedSelector();

        return Result::success($data);
    }

    /**
     * 获取用例id集合
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/10/18 14:05
     */
    public function getCaseIdListByPlanId(Request $request)
    {
        $params = $request->get([
            'test_plan_id',
            'cnt_id',
        ]);
        $data = $this->logic->getCaseIdListByPlanId($params['test_plan_id'], $params['cnt_id'] ?? null);

        return Result::success($data);
    }

}
