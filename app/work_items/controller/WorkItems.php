<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/7/5 上午10:33
 */

namespace app\work_items\controller;

use app\infrastructure\logic\EnumLogic;
use app\infrastructure\logic\UrlParamsStorageLogic;
use app\work_items\logic\WorkItemsLogic;
use app\work_items\validate\WorkItemsValidate;
use basic\BaseController;
use excel_utils\Hanlder;
use resp\Result;
use think\App;
use think\Request;
use app\project\scheduled_tasks\BugStatistics;
use think\facade\Console; // 新增: 引入 Console 类
use utils\Log;

// 新增: 引入 BugStatistics 类
use think\facade\Validate;

// 新增: 引入 Validate 类用于参数校验
class WorkItems extends BaseController
{
    private $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = (new WorkItemsLogic);
    }


    /**
     * 创建
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/8/26 17:50
     */
    public function create(Request $request)
    {
        $param = $request->post();
        $model = $this->logic->create($param);

        return Result::success($model);
    }


    /**
     * 删除
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/8/26 17:51
     */
    public function delete(Request $request)
    {
        $this->logic->delete($request->post('cnt_id'));

        return Result::success();
    }


    /**
     * 更新
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/8/26 17:51
     */
    public function update(Request $request)
    {
        $param = $request->post();
        $model = $this->logic->update($param);

        return Result::success($model);
    }


    /**
     * 详情
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/8/27 21:25
     */
    public function detail(Request $request)
    {
        $res = $this->logic->detail($request->get('cnt_id'));

        return Result::success($res);
    }


    /**
     * 分页查询
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/8/26 17:51
     */
    public function pageQuery(Request $request)
    {

        $data = $this->logic->pageQuery($request->post());

        return Result::success($data);
    }

    /**
     * 接收多组查询参数
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/8/26 17:51
     */
    public function pageQuerySummary(Request $request)
    {

        $data = $this->logic->pageQuerySummary($request->post());

        return Result::success($data);
    }

    /**
     * 我的工作，接收多组查询参数
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function pageQueryMyJob(Request $request)
    {
        $data = $this->logic->pageQueryMyJob($request->post());

        return Result::success($data);
    }

    /**
     * 我的工作，获取数量
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     */
    public function getMyJobCount(Request $request)
    {
        $projectIds = $request->post('project_id');
        $data = $this->logic->getMyJobCount($projectIds);

        return Result::success($data);
    }


    /**
     * 接收多组查询参数按用户分组
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/8/26 17:51
     */
    public function pageQuerySummaryGroupUser(Request $request)
    {

        $data = $this->logic->pageQuerySummaryGroupUser($request->post());

        return Result::success($data);
    }


    /**
     * 状态流转
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/8/30 14:16
     */
    public function statusTransfer(Request $request)
    {
//        $params = $request->post([
//            'cnt_id',
//            'status_enum_id',
//            'handler_uid',
//            'priority',
//            'content',
//            'remark',
//        ]);
        $params = $request->post();
        $this->logic->statusTransfer($params);

        return Result::success();
    }

    /**
     * 关联任务
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/8/30 16:34
     */
    public function associateTasks(Request $request)
    {
        $params = $request->post([
            'cnt_id',
            'cnt_id_list',
            'cnt_type'
        ]);
        $this->logic->associateTasks($params);

        return Result::success();
    }

    /**
     * 任务批量关联流程节点
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/8/30 16:34
     */
    public function flowNodeAssociateTasks(Request $request)
    {
        $params = $request->post([
            'node_id',
            'cnt_id_list',
        ]);
        $this->logic->flowNodeAssociateTasks($params);

        return Result::success();
    }

    /**
     * 判断工作项是否是子集
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/8/31 14:28
     */
    public function hasChildren(Request $request)
    {
        $params = $request->get([
            'cnt_id',
        ]);

        $res = $this->logic->hasChildren($params);

        return Result::success($res);
    }

    /**
     * 需求下拉
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/9/3 11:33
     */
    public function demandSelector(Request $request)
    {
        $params = $request->post([
            'project_id',
            'title',
            'scenario',
        ]);

        $res = $this->logic->demandSelector($params);

        return Result::success($res);
    }

    /**
     * 任务需求下拉
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/9/3 11:33
     */
    public function taskDemandSelector(Request $request)
    {
        $params = $request->post([
            'project_id',
        ]);

        $res = $this->logic->taskDemandSelector($params);

        return Result::success($res);
    }

    /**
     * 测试用例需求下拉
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/9/3 11:33
     */
    public function testCaseDemandSelector(Request $request)
    {
        $params = $request->get([
            'project_id',
        ]);

        $res = $this->logic->testCaseDemandSelector($params);

        return Result::success($res);
    }

    /**
     * 返回任务状态
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/9/3 18:58
     */
    public function getTaskStatusList()
    {
        return Result::success(
            (new EnumLogic)->getList('task_status_type')->map(function ($v) {
                return ['label' => $v['enum_name'], 'value' => $v['enum_value']];
            })
        );
    }


    /**
     * 拖动
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/11/11 17:46
     */
    public function dragToIteration(Request $request)
    {
        $params = $request->post([
            'iteration_id',
            'cnt_id_list',
        ]);

        $res = $this->logic->dragToIteration($params['iteration_id'], $params['cnt_id_list']);

        return Result::success($res);
    }

    /**
     * 工作项个数以及已结束个数
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/11/13 11:31
     */
    public function countGroupCntType(Request $request)
    {
        $params = $request->get([
            'iteration_id',
        ]);

        $res = $this->logic->countGroupCntType($params['iteration_id']);

        return Result::success($res);
    }

    /**
     * 工作项燃尽图
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/11/13 11:31
     */
    public function workItemBurndownChart(Request $request)
    {
        $params = $request->get([
            'iteration_id',
        ]);

        $res = $this->logic->workItemBurndownChart($params['iteration_id']);

        return Result::success($res);
    }

    /**
     * 工时燃尽图
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/11/13 11:31
     */
    public function laborHoursBurndownChart(Request $request)
    {
        $params = $request->post([
            'iteration_id',
            'cnt_type_list',
        ]);

        $res = $this->logic->laborHoursBurndownChart($params['iteration_id'], $params['cnt_type_list']);

        return Result::success($res);
    }


    /**
     * 工时燃烧报告
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \DateMalformedStringException
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * @throws \think\Exception
     * <AUTHOR>
     * @date   2024/12/5 下午4:41
     */
    public function manHourBurnReport(Request $request)
    {
        $params = $request->post([
            'iteration_id',
        ]);
        $res = $this->logic->manHourBurnReport($params['iteration_id']);

        return Result::success($res);
    }

    /**
     * 工时资源报告
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \DateMalformedStringException
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * @throws \think\Exception
     * <AUTHOR>
     * @date   2024/12/5 下午4:41
     */
    public function workResourceReports(Request $request)
    {
        $params = $request->post([
            'iteration_id',
        ]);
        $res = $this->logic->workResourceReports($params['iteration_id']);

        return Result::success($res);
    }


    /**
     * 批量删除工作项
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \DateMalformedStringException
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * @throws \think\Exception
     * <AUTHOR>
     * @date   2024/12/5 下午4:41
     */
    public function batchDelete(Request $request)
    {
        $params = $request->post([
            'cnt_id_list',
        ]);

        $res = $this->logic->batchDelete($params['cnt_id_list']);

        return Result::success($res);
    }

    /**
     * 是否含子任务，子需求
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \DateMalformedStringException
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * @throws \think\Exception
     * <AUTHOR>
     * @date   2024/12/5 下午4:41
     */
    public function hasChild(Request $request)
    {
        $params = $request->post([
            'cnt_id_list',
        ]);

        $res = $this->logic->hasChild($params['cnt_id_list']);

        return Result::success($res);
    }

    /**
     * 批量状态流转
     * @return \think\response\Json
     * @throws \Throwable
     */
    public function batchStatusTransfer()
    {
        $params = $this->request->param();

        validate(WorkItemsValidate::class)
            ->scene('batchStatusTransfer')
            ->check($params);

        $logic = new WorkItemsLogic();
        $logic->batchStatusTransfer($params);

        return Result::success();
    }

    /**
     * 批量更新工作项
     * @return \think\response\Json
     * @throws \Throwable
     */
    public function batchUpdate()
    {
        $params = $this->request->param();

        validate(WorkItemsValidate::class)
            ->scene('batchUpdate')
            ->check($params);

        $logic = new WorkItemsLogic();
        $result = $logic->batchUpdate($params);

        return Result::success($result);
    }

    /**
     * 批量更新工作项类别
     * @return \think\response\Json
     * @throws \Throwable
     */
    public function batchUpdateCategory()
    {
        $params = $this->request->param();

        validate(WorkItemsValidate::class)
            ->scene('batchUpdateCategory')
            ->check($params);

        $logic = new WorkItemsLogic();
        $result = $logic->batchUpdateCategory($params['cnt_ids'], $params['type_id'], $params['status_enum_id'] ?? null);

        return Result::success($result);
    }

    /**
     * 拖拽排序更新工作项的排序顺序
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/7/24
     */
    public function updateItemSortOrder(Request $request)
    {
        $params = $request->post();

        validate(WorkItemsValidate::class)->scene('updateItemSortOrder')->check($params);

        $cntIdList = $params['cnt_id_list'];
        $prevCntId = $params['prev_cnt_id'] ?? null;
        $nextCntId = $params['next_cnt_id'] ?? null;

        $this->logic->updateItemSortOrder($cntIdList, $prevCntId, $nextCntId);

        return Result::success();
    }

    /**
     * bug定时任务
     * @return \think\response\Json
     * @throws \Throwable
     */
    public function bugScheduledTasks()
    {
        $this->logic->bugScheduledTasks();

        return Result::success();
    }



    /**
     * API接口：修正历史错误的Bug统计数据
     * @param  Request  $request
     * @return \think\response\Json|void
     * <AUTHOR>
     * @date   2025/05/26
     */
    public function regenerateStatisticsByDateRange(Request $request)
    {

        $params = $request->post();

        // 1. 参数校验
        $validate = Validate::rule([
            'startDate' => 'require|dateFormat:Y-m-d',
            'endDate'   => 'require|dateFormat:Y-m-d',
        ])->message([
            'startDate.require'    => '开始日期 (startDate) 不能为空',
            'startDate.dateFormat' => '开始日期 (startDate) 格式必须为 YYYY-MM-DD',
            'endDate.require'      => '结束日期 (endDate) 不能为空',
            'endDate.dateFormat'   => '结束日期 (endDate) 格式必须为 YYYY-MM-DD',
        ]);

        $validate->check($params);

        $startDate = $params['startDate'];
        $endDate = $params['endDate'];


        Log::instance(Log::PLATFORM)->error("test Background task (via fastcgi_finish_request) started for command bug:regenerate-statistics with params: $startDate, $endDate.");

        // 检查 fastcgi_finish_request 是否可用
        if (function_exists('fastcgi_finish_request')) {
            // 先准备好要发送的响应
            $response = Result::success([], '后台任务已启动，请稍后查看结果。');

            // 关闭 session 和 cookie，确保它们在发送响应前被处理
            if (session_status() === PHP_SESSION_ACTIVE) {
                session_write_close();
            }
            \think\facade\Cookie::save();

            // 发送响应
            // 确保 $response 是一个可以被 send() 的对象，或者直接 echo json
            if ($response instanceof \think\Response) {
                $response->send();
            } else {
                // 假设 Result::success 返回的是数组或可以直接 json 编码的内容
                // 对于非 think\Response 对象，需要手动设置 header 和 echo
                if (!headers_sent()) {
                    header('Content-Type: application/json; charset=utf-8');
                }
                echo json_encode($response); // 假设 Result::success 返回的是标准结构
            }

            // 核心：告诉 PHP-FPM 完成请求处理，之后脚本继续执行
            fastcgi_finish_request();

            // ---- 后台执行的代码 ----
            // 以下代码将在客户端断开连接后继续执行
            try {
                // 尝试解除PHP执行时间限制，以便后台任务可以长时间运行
                // 注意：这仍然可能受到PHP-FPM主进程的超时限制 (如 request_terminate_timeout)
                if (function_exists('set_time_limit')) {
                    set_time_limit(0);
                }
                // 可选：确保即使“连接”被异常关闭（理论上 fastcgi_finish_request 后已关闭），脚本也尝试继续执行
                if (function_exists('ignore_user_abort')) {
                    ignore_user_abort(true);
                }

                Log::instance(Log::PLATFORM)->error("Background task (via fastcgi_finish_request) started for command bug:regenerate-statistics with params: $startDate, $endDate.");

                // 由于已经是“后台”，可以直接同步调用 Console::call
                // 注意：这里的执行仍然受限于 PHP-FPM 进程的生命周期和超时设置
                $exitCode = Console::call('bug:regenerate-statistics', [$startDate, $endDate]);

                if ($exitCode === 0) {
                    Log::instance(Log::PLATFORM)->error("Background task bug:regenerate-statistics completed successfully for $startDate to $endDate.");

                } else {
                    Log::instance(Log::PLATFORM)->error("Background task bug:regenerate-statistics failed with exit code $exitCode for $startDate to $endDate.");

                }
            } catch (\Throwable $e) {
                Log::instance(Log::PLATFORM)->error("Exception in background task (fastcgi_finish_request) for $startDate to $endDate: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            }

            // 后台任务执行完毕后，脚本应自然结束或显式退出
            // exit; // 通常不需要显式 exit，除非有后续不应执行的框架代码
            return; // 从控制器方法返回，避免任何后续输出

        } else {
            // fastcgi_finish_request 不可用
            Log::instance(Log::PLATFORM)->error('fastcgi_finish_request is not available. Cannot perform async operation for bug:regenerate-statistics.');
            return Result::error(0,'服务器当前配置不支持此类后台任务的即时启动。请联系管理员。');
        }
    }

    /**
     * 查询指定用户的实际工时信息，按日期分组，并填充工作项内容。
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \Throwable
     */
    public function queryUserWorkHoursByDateRange(Request $request)
    {
        $params = $request->post();
        // 参数验证可以在 WorkItemsValidate 中添加一个新场景 'queryUserWorkHours'
        validate(WorkItemsValidate::class)->scene('queryUserWorkHours')->check($params);

        $data = $this->logic->queryUserWorkHoursByDateRange($params);

        return Result::success($data);
    }

    /**
     * 导出用户工时报告Excel
     * @param  Request  $request
     * @return \think\Response|\think\response\Json
     * <AUTHOR>
     * @date   2025/05/22
     */
    public function exportUserWorkHoursReport(Request $request)
    {
        $params = $request->post(); // 或者 $request->param() 如果也接受GET请求

        // 验证参数，复用 'queryUserWorkHours' 场景
        validate(WorkItemsValidate::class)->scene('queryUserWorkHours')->check($params);

        return $this->logic->exportUserWorkHoursByDateRangeToExcel($params);

    }
}
