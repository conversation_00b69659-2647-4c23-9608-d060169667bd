<?php
declare (strict_types=1);

namespace app\work_items\logic;

use app\project\logic\IterationProcessNodeLogic;
use app\project\model\IterationModel;
use app\project\model\IterationProcessNodeModel;
use app\work_items\model\IterationDemandRelationRecordsModel;
use app\work_items\model\WorkItemsModel;
use basic\BaseLogic;
use exception\NotFoundException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\Paginator;
use utils\Ctx;

/**
 * 迭代需求关联记录逻辑类
 */
class IterationDemandRelationRecordsLogic extends BaseLogic
{
    /**
     * 创建迭代需求关联记录
     * @param  WorkItemsModel  $workItem        工作项模型
     * @param  array           $data            迭代相关数据
     * @param  int             $oldIterationId  旧迭代ID（用于区分新增还是修改）
     * @return IterationDemandRelationRecordsModel
     * @throws Exception
     */
    public function create(WorkItemsModel $workItem, array $data, int $oldIterationId = 0): IterationDemandRelationRecordsModel
    {
        // 获取迭代信息
        $iteration = null;
        $iterationName = '';
        $newIterationId = $data['iteration_id'] ?? 0;
        if ($newIterationId) {
            $iteration = IterationModel::findById($newIterationId);
            if ($iteration) {
                $iterationName = $iteration->iteration_name;
            }
        }


        // 获取迭代节点信息
        $node = IterationProcessNodeLogic::getHighestPriorityInProgressNode($newIterationId);
        $nodeName = '';
        $nodeStage = '';
        $nodeId = 0;
        if ($node) {
            $nodeId = $node->iteration_process_node_id;
            $nodeName = $node->node_name;
            $nodeStage = $node->node_data['node_setting']['node_stage'] ?? '';
        }


        // 获取旧迭代信息
        $oldIterationName = '';
        if ($oldIterationId) {
            $oldIteration = IterationModel::findById($oldIterationId);
            if ($oldIteration) {
                $oldIterationName = $oldIteration->iteration_name ?? '';
            }
        }

        // 判断操作类型：新增或修改
        $operationType = $oldIterationId > 0 ? IterationDemandRelationRecordsModel::OPERATION_TYPE_MODIFY : IterationDemandRelationRecordsModel::OPERATION_TYPE_ADD;

        // 创建记录
        $record = new IterationDemandRelationRecordsModel();
        $record->save([
            'project_id'         => $workItem->extends['project_id'] ?? 0,
            'project_name'       => $workItem->extends['project_name'] ?? '',
            'cnt_id'             => $workItem->cnt_id,
            'cnt_title'          => $workItem->extends['title'] ?? '',
            'operator_uid'       => Ctx::$userId,
            'operator_name'      => Ctx::$user->name,
            'iteration_id'       => $newIterationId,
            'iteration_name'     => $iterationName,
            'node_id'            => $nodeId,
            'node_name'          => $nodeName,
            'node_stage'         => $nodeStage,
            'operation_type'     => $operationType,
            'operation_time'     => date('Y-m-d H:i:s'),
            'old_iteration_id'   => $oldIterationId,
            'old_iteration_name' => $oldIterationName,
        ]);

        return $record;
    }

    /**
     * 分页查询迭代需求关联记录
     * @param  array  $params  查询参数
     * @return Paginator
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function pageQuery(array $params): Paginator
    {
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['page_size']) ? intval($params['page_size']) : 10;

        $query = IterationDemandRelationRecordsModel::where('is_delete', IterationDemandRelationRecordsModel::DELETE_NOT);

        // 按项目查询
        if ( ! empty($params['project_id'])) {
            $query->where('project_id', $params['project_id']);
        }

        // 按需求查询
        if ( ! empty($params['cnt_id'])) {
            $query->where('cnt_id', $params['cnt_id']);
        }

        // 按操作人查询
        if ( ! empty($params['operator_uid'])) {
            $query->where('operator_uid', $params['operator_uid']);
        }

        // 按迭代查询
        if ( ! empty($params['iteration_id'])) {
            $query->where('iteration_id', $params['iteration_id']);
        }

        // 按迭代节点查询
        if ( ! empty($params['node_id'])) {
            $query->where('node_id', $params['node_id']);
        }

        // 按需求标题模糊查询
        if ( ! empty($params['cnt_title'])) {
            $query->whereLike('cnt_title', '%'.$params['cnt_title'].'%');
        }

        // 按操作类型查询
        if ( ! empty($params['operation_type'])) {
            $query->where('operation_type', $params['operation_type']);
        }

        // 按时间范围查询
        if ( ! empty($params['start_time'])) {
            $query->where('operation_time', '>=', $params['start_time']);
        }
        if ( ! empty($params['end_time'])) {
            $query->where('operation_time', '<=', $params['end_time']);
        }

        return $query->order('operation_time', 'desc')
            ->paginate([
                'list_rows' => $pageSize,
                'page'      => $page,
            ]);
    }

    /**
     * 获取详情
     * @param  int  $id  记录ID
     * @return IterationDemandRelationRecordsModel
     * @throws NotFoundException
     */
    public function detail(int $id): IterationDemandRelationRecordsModel
    {
        $model = IterationDemandRelationRecordsModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('记录不存在');
        }

        return $model->toDetail();
    }
} 