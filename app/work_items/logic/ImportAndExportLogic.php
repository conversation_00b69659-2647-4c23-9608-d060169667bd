<?php

/**
 *
 * User: 袁志凡
 * Date-Time: 2024/12/17 上午10:10
 */

namespace app\work_items\logic;

use app\infrastructure\logic\FieldConfigLogic;
use app\infrastructure\model\FieldConfigModel;
use app\project\logic\ProjectCategorySettingsLogic;
use app\project\logic\ProjectClassifyLogic;
use app\project\model\ProjectModel;
use app\work_items\model\TestCaseModel;
use app\work_items\model\TestPlanModel;
use app\work_items\model\WorkItemsModel;
use app\work_items\validate\importAndExport\DemandValidate;
use app\work_items\validate\importAndExport\FlawValidate;
use app\work_items\validate\importAndExport\ImportAndExportValidate;
use app\work_items\validate\importAndExport\TaskValidate;
use app\work_items\validate\importAndExport\TestCaseValidate;
use basic\BaseLogic;
use basic\BaseModel;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use excel_utils\Hanlder;
use field_utils\Transfer;
use think\Exception;
use think\facade\Config;
use think\facade\Filesystem;
use think\facade\Validate;
use think\File;
use utils\DBTransaction;
use utils\Log;

class ImportAndExportLogic extends BaseLogic
{

    private $module_id;
    private $settingType;
    private $esLogic;
    private $logic;
    private $model;
    //    private $subKey;
    private $workItmeType;
    private $moduleLabel;

    /**
     * @param $moduleId int \app\infrastructure\model\FieldConfigModel::@$module_id
     * @throws Exception
     */
    public function __construct(int $moduleId)
    {
        $this->module_id = $moduleId;
        $this->esLogic = $this->getEsLogic($moduleId);
        $this->settingType = $this->getSettingType($moduleId);
        $this->logic = $this->getLogic($moduleId);
        //        $this->subKey = $this->getOutputSubKey($moduleId);
        $this->workItmeType = $this->getWorkItmeType($moduleId);
        $this->model = $this->getModel($moduleId);
        $this->moduleLabel = $this->getModuleLabel($moduleId);
    }


    private function getEsLogic($moduleId)
    {
        return match ($moduleId) {
            FieldConfigModel::MODULE_TYPE_REQUIREMENT,
            FieldConfigModel::MODULE_TYPE_TASK,
            FieldConfigModel::MODULE_TYPE_DEFECT => WorkItemsEsLogic::getInstance(),
            FieldConfigModel::MODULE_TYPE_TEST_CASE => TestCaseLogic::getInstance(),
            default => throw new Exception()
        };
    }

    private function getLogic($moduleId)
    {
        return match ($moduleId) {
            FieldConfigModel::MODULE_TYPE_REQUIREMENT,
            FieldConfigModel::MODULE_TYPE_TASK,
            FieldConfigModel::MODULE_TYPE_DEFECT => (new WorkItemsLogic),
            FieldConfigModel::MODULE_TYPE_TEST_CASE => TestCaseLogic::getInstance(),
            default => throw new Exception()
        };
    }

    private function getModel($moduleId)
    {
        return match ($moduleId) {
            FieldConfigModel::MODULE_TYPE_REQUIREMENT,
            FieldConfigModel::MODULE_TYPE_TASK,
            FieldConfigModel::MODULE_TYPE_DEFECT => (new WorkItemsModel()),
            FieldConfigModel::MODULE_TYPE_TEST_CASE => (new TestCaseModel()),
            default => throw new Exception()
        };
    }

    private function getModuleLabel($moduleId)
    {
        return match ($moduleId) {
            FieldConfigModel::MODULE_TYPE_REQUIREMENT => "需求",
            FieldConfigModel::MODULE_TYPE_TASK => "任务",
            FieldConfigModel::MODULE_TYPE_DEFECT => "缺陷",
            FieldConfigModel::MODULE_TYPE_TEST_CASE => "测试用例",
            default => throw new Exception()
        };
    }


    private function getSettingType($moduleId)
    {
        return match ($moduleId) {
            FieldConfigModel::MODULE_TYPE_REQUIREMENT => BaseModel::SETTING_TYPE_DEMAND,
            FieldConfigModel::MODULE_TYPE_TASK => BaseModel::SETTING_TYPE_TASK,
            FieldConfigModel::MODULE_TYPE_DEFECT => BaseModel::SETTING_TYPE_DEFECT,
            FieldConfigModel::MODULE_TYPE_TEST_CASE => BaseModel::SETTING_TYPE_TEST_CASE,
            default => throw new Exception()
        };
    }

    private function getWorkItmeType($moduleId)
    {
        return match ($moduleId) {
            FieldConfigModel::MODULE_TYPE_REQUIREMENT => WorkItemsModel::CNT_TYPE_DEMAND,
            FieldConfigModel::MODULE_TYPE_TASK => WorkItemsModel::CNT_TYPE_TASK,
            FieldConfigModel::MODULE_TYPE_DEFECT => WorkItemsModel::CNT_TYPE_FLAW,
            default => 0
        };
    }

    //    private function getOutputSubKey($moduleId)
    //    {
    //        return match ($moduleId) {
    //            FieldConfigModel::MODULE_TYPE_REQUIREMENT => 'demand_output_update',
    //            FieldConfigModel::MODULE_TYPE_TASK => 'task_output_update',
    //            FieldConfigModel::MODULE_TYPE_DEFECT => 'flaw_output_update',
    //            FieldConfigModel::MODULE_TYPE_TEST_CASE => 'case_output_update',
    //            default => throw new Exception()
    //        };
    //    }

    private function isWorkItem()
    {
        return match ($this->module_id) {
            FieldConfigModel::MODULE_TYPE_REQUIREMENT,
            FieldConfigModel::MODULE_TYPE_TASK,
            FieldConfigModel::MODULE_TYPE_DEFECT => true,
            FieldConfigModel::MODULE_TYPE_TEST_CASE => false,
            default => throw new Exception()
        };
    }

    /**
     * 获取导入验证器
     * @param $subKey
     * @return \think\Validate
     * <AUTHOR>
     * @date   2024/12/20 上午11:39
     */
    private function getImportValidate($subKey)
    {
        ['class' => $class, 'scene' => $scene] = match ($subKey) {
            'demand_output_add' => ['class' => DemandValidate::class, 'scene' => 'create'],
            'demand_output_update' => ['class' => DemandValidate::class, 'scene' => 'update'],
            'task_output_add' => ['class' => TaskValidate::class, 'scene' => 'create'],
            'task_output_update' => ['class' => TaskValidate::class, 'scene' => 'update'],
            'flaw_output_add' => ['class' => FlawValidate::class, 'scene' => 'create'],
            'flaw_output_update' => ['class' => FlawValidate::class, 'scene' => 'update'],
            'case_output_add' => ['class' => TestCaseValidate::class, 'scene' => 'create'],
            'case_output_update' => ['class' => TestCaseValidate::class, 'scene' => 'update'],
            default => 0
        };


        return validate($class)->scene($scene);
    }


    public function isCreate($subKey)
    {
        return str_ends_with($subKey, 'output_add');
    }

    public function isDemand()
    {
        return $this->module_id == FieldConfigModel::MODULE_TYPE_REQUIREMENT;
    }

    public function isTask()
    {
        return $this->module_id == FieldConfigModel::MODULE_TYPE_TASK;
    }

    public function isFlaw()
    {
        return $this->module_id == FieldConfigModel::MODULE_TYPE_DEFECT;
    }

    public function isTestCase()
    {
        return $this->module_id == FieldConfigModel::MODULE_TYPE_TEST_CASE;
    }


    /**
     * 导出模版
     * @param $params
     * @return array
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/12/17 下午5:21
     */
    public function getExportHeaderTemplate($params)
    {
        validate(ImportAndExportValidate::class)->scene('getExportHeaderTemplate')->check($params);

        $fieldList = FieldConfigLogic::getListBySubKey($params['sub_key'], $params['project_id'])->column(null, 'field_name');;
        $allowedColumns = array_keys($fieldList);
        $head = array_values(array_intersect($params['field_list'], $allowedColumns));
        $result = [];
        foreach ($head as $v) {
            $result[0][] = $fieldList[$v]['field_label'];
        }

        $project = ProjectModel::findById((int)$params['project_id']);

        if ( ! $project) {
            throw new Exception("项目不存在");
        }
        if ($this->isCreate($params['sub_key'])) {
            $createOrUpdateTemplate = '导入新建模版';
        } else {
            $createOrUpdateTemplate = '导入更新模版';
        }

        if ( ! $createOrUpdateTemplate) {
            throw new Exception('sub_key参数错误');
        }

        return [
            'data'     => $result,
            'fileName' => "{$project->project_name}_{$this->moduleLabel}_{$createOrUpdateTemplate}_".date("YmdHis"),
        ];
    }


    /**
     * 导出
     * @param $params
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/12/12 下午5:01
     */
    public function export($params)
    {
        set_time_limit(0); // 执行时间限制
        ini_set('memory_limit', '1024M'); // 内存限制


        validate(ImportAndExportValidate::class)->scene('export')->check($params);

        $params['searchParams'] = array_column($params['searchParams'], null, 'field_name');
        $params['searchParams']['project_id'] = ['field_name' => 'project_id', 'value' => $params['project_id'], 'type' => "term", 'operate_type' => "equal"];
        if (isset($params['searchParams']['category_id']['value']) && $params['searchParams']['category_id']['value'] == 0) {
            unset($params['searchParams']['category_id']);
        }

        //工作项需要带出父级，不管父级是否符合查询条件
        if ($this->isDemand()) {
            $dataList = $this->esLogic->esSearch([
                ['field_name' => 'cnt_id', 'value' => $this->esLogic->getParentsByParams($params['searchParams']), 'type' => 'selector']
            ], $this->esLogic::NOT_PAGE_MAX, 1, $params['field_list'])->getCollection()->toArray();
        } else {
            $dataList = $this->esLogic->esSearch($params['searchParams'], $this->esLogic::NOT_PAGE_MAX, 1, $params['field_list'])->getCollection()->toArray();
        }


        $project = ProjectModel::findById((int)$params['project_id']);
        if ( ! $project) {
            throw new Exception("项目不存在");
        }

        $categoryKeyList = ProjectClassifyLogic::getCategoryListParentList($this->settingType, $params['project_id']);


        $transfer = new Transfer($this->module_id, $params['project_id']);

        $fieldList = (new FieldConfigLogic())->getListByFieldName([
            'module_id'  => $this->module_id,
            'project_id' => $params['project_id'],
            'field_name' => $params['field_list'],
        ])->column(null, 'field_name');
        $result = [];
        foreach ($params['field_list'] as $v) {
            $result[0][] = $fieldList[$v]['field_label'];
        }


        foreach ($dataList as $v) {
            $item = [];
            foreach ($params['field_list'] as $s) {
                $field = $fieldList[$s];
                $fieldValue = $v[$s] ?? '';

                if ( ! $fieldValue) {
                    $item[] = '';
                    continue;
                }

                //分类特殊处理
                if ($field['field_name'] == 'category_id') {
                    $item[] = $categoryKeyList[$fieldValue] ?? '';
                    continue;
                }

                //测试用例关联需求
                if ($field['field_name'] == 'cnt_id_list') {
                    $item[] = implode('|', array_column($v['cnt_id_list_name'] ?? [], 'label'));
                    continue;
                }
                //工作项需求
                if ($field['field_name'] == 'parent_id') {
                    $item[] = $v['parent_id_name']['label'] ?? '';
                    continue;
                }

                try {
                    if (($field['field_component']['props']['multiple'] ?? false) || ($field['field_component']['multiple'] ?? false)) {

                        if ( ! is_array($fieldValue)) {
                            $item[] = $this->exportFormat($transfer, $field, $fieldValue);
                        } else {
                            $item[] = implode('|', array_map(function ($v) use ($transfer, $field) {
                                return $this->exportFormat($transfer, $field, $v);
                            }, $fieldValue));
                        }
                    } else {
                        $item[] = $this->exportFormat($transfer, $field, $fieldValue);
                    }
                } catch (\Throwable $e) {
                    $item[] = $e->getMessage();
                    Log::instance(Log::PLATFORM)->error($e);
                }
            }
            $result[] = $item;
        }


        return [
            'data'     => $result,
            'fileName' => "{$project->project_name}_{$this->moduleLabel}_".date("YmdHis"),
        ];
    }


    public function import($params)
    {
        set_time_limit(0); // 执行时间限制
        ini_set('memory_limit', '1024M'); // 内存限制

        // 处理文件读取失败
        if (empty($params['file']) && empty($_FILES['file']['tmp_name'])) {
            throw new Exception("文件读取失败，请尝试将文件另存为 .xlsx 后重试！");
        }

        validate(ImportAndExportValidate::class)->scene('import')->check($params);

        $dataList = Hanlder::readFile($params['file']);

        $transfer = new Transfer($this->module_id, $params['project_id']);

        [
            'fieldList'       => $fieldList, //字段组件集合
            'head'            => $head, //表头
            'headColumnIndex' => $headColumnIndex, //表头反转,值是列索引
            'nameToLabel'     => $nameToLabel, //所有的字段名称，field_name=>field_label
            'dataList'        => $dataList, //剔除表头后的数据
            'ignoredColumns'  => $ignoredColumns, //被忽略的列集合
        ]
            = $this->formatImortData($params['sub_key'], $params['project_id'], $dataList);


        $dataList = array_reverse($dataList);

        if ($this->isCreate($params['sub_key'])) {
            if ( ! in_array($nameToLabel['title'], $head)) {
                throw new Exception("请检查文件中是否包含标题表头");
            }
            if ($this->isWorkItem() && ! in_array($nameToLabel['type_id'], $head)) {
                $head[] = $nameToLabel['type_id'];
                //                throw new Exception("请检查文件中是否包含{$nameToLabel['type_id']}表头");
            }
        } else {
            if ( ! in_array('ID', $head)) {
                throw new Exception("请检查文件中是否包含ID表头");
            }
        }

        if ( ! $dataList) {
            throw new Exception("文件无数据需导入,请检查文件");
        }

        // 2025-2-6 测试导入上限
        //        if (count($dataList) > 500) {
        //            throw new Exception("限导入500条数据");
        //        }

        DBTransaction::begin();
        try {

            if (isset($headColumnIndex[$nameToLabel['category_id'] ?? null])) {
                $categoryIdIndex = $headColumnIndex[$nameToLabel['category_id']];

                $params['category_id'] = (int)($params['category_id'] ?? 0);

                $categoryNameList = ProjectClassifyLogic::getCategoryListParentList($this->settingType, $params['project_id']);
                $categoryKeyList = array_flip($categoryNameList);
                if ($params['category_id'] == 0) {
                    $createCategoryList = array_unique(array_diff(array_filter(array_column($dataList, $categoryIdIndex)), $categoryNameList));
                    //创建分类
                    if ($createCategoryList) {
                        if ($this->isDemand()) {
                            $delimiter = '>';
                        } elseif ($this->isTestCase()) {
                            $delimiter = '-';
                        } else {
                            throw new Exception('未知的分类分隔符');
                        }
                        ProjectClassifyLogic::createCategoryListParentList($this->settingType, $params['project_id'], $createCategoryList, $delimiter);
                    }
                    $categoryNameList = ProjectClassifyLogic::getCategoryListParentList($this->settingType, $params['project_id'], true);
                } else {
                    if ( ! in_array($params['category_id'], $categoryKeyList)) {
                        throw new Exception("指定的分类/目录不存在");
                    }
                }
            }


            $titleList = [];
            $repeatTitle = [];
            $repeatId = [];
            $parent = $headColumnIndex[$nameToLabel['parent_id'] ?? null] ?? false;
            $title = $headColumnIndex[$nameToLabel['title'] ?? null] ?? false;
            $cntIdList = $headColumnIndex[$nameToLabel['cnt_id_list'] ?? null] ?? false;

            if ($this->isDemand() && $parent !== false && $title !== false) {
                //排序，要保证父级在子级之前，这样才可以在父级保存后得到自增id，然后更新子集的父级id
                $dataList = $this->sortMultipleTrees($dataList, $title, $parent);
            }


            if ($this->isDemand() && $parent !== false) {
                $titleList = array_flip(array_filter(array_column($dataList, $title)));
            }
            $demandIdList = [];
            if ($parent) {
                $demandIdList = array_unique(array_filter(array_column($dataList, $parent)));
            } elseif ($cntIdList) {
                $columns = array_map(function ($v) {
                    return explode(str_contains($v, '|') ? '|' : ';', $v);
                }, array_filter(array_column($dataList, $cntIdList)));
                $demandIdList = array_unique(array_filter(array_merge(...$columns)));
            }
            //需求集合
            $demandList = $this->getDemandListByProjectIdAndCntIdList($params['project_id'], $demandIdList)->column(null, 'cnt_id');

            //任务缺陷还可以通过指定父级标题查找
            if ($this->isTask() || $this->isFlaw()) {
                $demandList = $demandList + $this->getDemandListByProjectIdAndCntTitleList($params['project_id'], $demandIdList)->column(null, 'title');
            }

            //第一个类别名称
            $firstTypeIdName = (new ProjectCategorySettingsLogic($this->settingType))->listQuery($params['project_id'])->first()->category_name ?? null;

            $validate = $this->getImportValidate($params['sub_key']);
            $result = [];
            $errResult = [];
            unset($v);
            foreach ($dataList as $dataOrder => $v) {
                $item = [];
                $error = [];
                foreach ($head as $k => $vv) {
                    try {
                        $field = $fieldList[$vv];
                        $fieldValue = &$v[$k];

                        if ($this->isWorkItem() && $field['field_name'] == 'type_id') {
                            //                            dd($fieldValue,$firstTypeIdName);
                            if ( ! $fieldValue) {
                                if ( ! $firstTypeIdName) {
                                    throw new Exception('无默认类别!');
                                } else {
                                    $fieldValue = $firstTypeIdName;
                                    $item[$field['field_name']] = $firstTypeIdName;
                                }
                            }
                        }

                        $validate->only([$field['field_name']])->check([$field['field_name'] => $fieldValue]);

                        //富文本组件数据库字段是text类型，无法插入null，改为空串
                        if ( ! $fieldValue || $fieldValue == '--') {
                            if ($field['field_component']['componentType'] == 'Editor') {
                                $item[$field['field_name']] = '';
                            } else {
                                $item[$field['field_name']] = null;
                            }
                            continue;
                        }

                        //分类特殊处理
                        if ($field['field_name'] == 'category_id') {
                            if ($params['category_id']) {
                                $item[$field['field_name']] = $params['category_id'];
                            } else {
                                $item[$field['field_name']] = $categoryNameList[$fieldValue] ?? '';
                            }
                            continue;
                        }

                        //父需求特殊处理
                        if ($field['field_name'] == 'parent_id') {
                            if (isset($titleList[$fieldValue])) {
                                $item[$field['field_name']] = 'key-'.$titleList[$fieldValue];
                                continue;
                            }

                            if (isset($demandList[$fieldValue])) {

                                if (isset($demandList[$fieldValue]['isEnd']) && $demandList[$fieldValue]['isEnd']) {
                                    throw new Exception("{$nameToLabel['parent_id']}已关闭");
                                }

                                if ($this->isDemand() && isset($demandList[$fieldValue]['subset_cnt_type']) && $demandList[$fieldValue]['subset_cnt_type'] == WorkItemsModel::CNT_TYPE_TASK) {
                                    throw new Exception("{$nameToLabel['parent_id']}已存在任务");
                                }

                                if ($this->isTask() && isset($demandList[$fieldValue]['subset_cnt_type']) && $demandList[$fieldValue]['subset_cnt_type'] == WorkItemsModel::CNT_TYPE_DEMAND) {
                                    throw new Exception("{$nameToLabel['parent_id']}已存在子需求");
                                }

                                $item[$field['field_name']] = $demandList[$fieldValue]['cnt_id'];
                                continue;
                            } else {
                                throw new Exception("{$nameToLabel['parent_id']}不存在");
                            }
                        }

                        //测试用例关联需求
                        if ($field['field_name'] == 'cnt_id_list') {
                            $separator = str_contains($fieldValue, '|') ? '|' : ';';
                            foreach (explode($separator, $fieldValue) as $q) {
                                if (isset($demandList[$q])) {
                                    $item[$field['field_name']][] = $demandList[$q]['cnt_id'];
                                } else {
                                    throw new Exception("{$nameToLabel['cnt_id_list']}不存在");
                                }
                            }
                            continue;
                        }


                        //标题特殊处理
                        if ($field['field_name'] == 'title' && ( ! $params['is_repeatable_titles'])) {
                            if (
                                isset($repeatTitle[$fieldValue])
                                || $this->esLogic->esSearch([
                                    ['field_name' => 'title', 'value' => $fieldValue, 'type' => 'term', 'operate_type' => 'equal'],
                                    ['field_name' => 'project_id', 'value' => $params['project_id'], 'type' => 'term', 'operate_type' => 'equal'],
                                ])->getCollection()->toArray()
                            ) {
                                $repeatTitle[$fieldValue] = 0;
                                throw new Exception("标题重复");
                            }
                        }

                        //ID特殊处理
                        if ($field['field_name'] == 'cnt_id') {
                            if (isset($repeatId[$fieldValue])) {
                                $repeatId[$fieldValue] = 0;
                                throw new Exception("ID重复");
                            }
                        }

                        //多选组件
                        if (($field['field_component']['props']['multiple'] ?? false) || ($field['field_component']['multiple'] ?? false)) {
                            $separator = str_contains($fieldValue, '|') ? '|' : ';';
                            $item[$field['field_name']] = array_map(function ($v) use ($transfer, $field) {
                                return $this->importFormat($transfer, $field, $v);
                            }, explode($separator, $fieldValue));
                        } else {
                            $item[$field['field_name']] = $this->importFormat($transfer, $field, $fieldValue);
                        }
                    } catch (\Throwable $e) {
                        //                        throw $e;
                        $error = array_merge($error, [$e->getMessage()]);
                        $fieldValue = [
                            'value'   => $fieldValue,
                            'isError' => 1,
                        ];
                    }
                }
                if ($error) {
                    $v[] = implode(' | ', $error);
                    $errResult[$dataOrder] = $v;
                } else {

                    $item['project_id'] = $params['project_id'];
                    if ($this->isWorkItem()) {
                        $item['cnt_type'] = $this->workItmeType;
                    }
                    //                指定了分类，不管数据中是否含有分类列，或含有分类列但是空值，都会将数据导入指定的分类中
                    if ($params['category_id']) {
                        $item['category_id'] = $params['category_id'];
                    } else {
                        //分类列不存在或未指定
                        if ( ! ($item['category_id'] ?? false)) {
                            $item['category_id'] = '-1';
                        }
                    }

                    $result[$dataOrder] = $item;
                }
            }

            WorkItemsModel::$autoSave = false;
            TestPlanModel::$autoSave = false;
            TestCaseModel::$autoSave = false;

            $successResult = [];
            //导入需求时可以指定文件中的数据为父级，所以这里每插入一条都需查询是否有将当前数据指定为父级的数据，有的话替换父级标题为自增id
            foreach ($result as $key => &$v) {
                if ($this->isDemand()) {
                    if (isset($v['parent_id']) && (strpos($v['parent_id'], 'key-') !== false)) {
                        $errResult[$key] = $dataList[$key];
                        $errResult[$key][] = "父需求：\"{$errResult[$key][$parent]}\"不存在";
                        $errResult[$key][$parent] = [
                            'value'   => $errResult[$key][$parent],
                            'isError' => 1,
                        ];
                        continue;
                    }
                }

                if (isset($v[$this->model->getPk()])) {
                    $model = $this->logic->update($v);
                } else {
                    $model = $this->logic->create($v);
                }
                $successResult[] = $v;
                if ($this->isDemand()) {
                    $id = $model[$this->model->getPk()];

                    foreach ($result as &$s) {
                        if ( ! empty($s['parent_id']) && ($s['parent_id'] === ('key-'.$key))) {
                            $s['parent_id'] = $id;
                        }
                    }
                }
            }

            WorkItemsModel::actualSaving();
            TestPlanModel::actualSaving();
            TestCaseModel::actualSaving();


            $fileUrl = '';
            if ($errResult) {
                array_unshift($errResult, array_merge($head, ['错误原因']));
                $errorFile = Hanlder::outputFile($errResult, time());
                //                return $errorFile;
                try {
                    // 生成临时文件
                    $tmpFilePath = tempnam(sys_get_temp_dir(), 'upload_').".xlsx"; // 创建临时文件
                    file_put_contents($tmpFilePath, $errorFile->getContent());         // 写入内容

                    // 构造 think\File 对象
                    $file = new File($tmpFilePath, false);
                    $fileUrl = Filesystem::disk('public')->putFile('import_error_file', $file);
                    $fileUrl = '/upload/'.$fileUrl;
                    unlink($tmpFilePath);
                } catch (\Throwable $e) {
                    unlink($tmpFilePath);
                    throw $e;
                }
            }

            DBTransaction::commit();


            // 强制进行垃圾回收
            gc_collect_cycles();

            return [
                'total'           => count($dataList),
                'success_total'   => count($successResult),
                'fail_total'      => count($errResult) - intval($errResult),
                'ignored_columns' => $ignoredColumns,
                'fail_file_url'   => $fileUrl,
            ];
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }
    }

    /**
     * 获取当前项目下的所有需求
     * @param $projectId
     * @param $cntIdList
     * @return \think\Collection|\think\model\Collection
     * @throws ClientResponseException
     * @throws ServerResponseException
     */
    public function getDemandListByProjectIdAndCntIdList($projectId, $cntIdList)
    {
        $cntIdList = array_filter($cntIdList, function ($v) {
            return is_numeric($v) && $v <= 2147483647; //不能超过es的integer类型的最大值，否则查询会报错，因为cnt_id是integer类型
        });

        return WorkItemsEsLogic::getInstance()->esSearch([
            ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_DEMAND, 'type' => 'term', 'operate_type' => 'equal'],
            ['field_name' => 'project_id', 'value' => $projectId, 'type' => 'term', 'operate_type' => 'equal'],
            ['field_name' => 'cnt_id', 'value' => $cntIdList, 'type' => 'selector'],
        ], WorkItemsEsLogic::NOT_PAGE_MAX)->getCollection();
    }

    /**
     * 获取当前项目下的所有需求
     * @param $projectId
     * @return \think\Collection|\think\model\Collection
     * @throws ClientResponseException
     * @throws ServerResponseException
     */
    public function getDemandListByProjectIdAndCntTitleList($projectId, $titleList)
    {
        return WorkItemsEsLogic::getInstance()->theTitleIsDeduplicatedAndTheLastOneIsKept([
            ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_DEMAND, 'type' => 'term', 'operate_type' => 'equal'],
            ['field_name' => 'project_id', 'value' => $projectId, 'type' => 'term', 'operate_type' => 'equal'],
            ['field_name' => 'title', 'value' => $titleList, 'type' => 'selector'],
        ]);
    }


    /**
     * 导出数据的格式转换
     * @param  Transfer  $transfer
     * @param            $field
     * @param            $value
     * @return mixed|string
     * @throws Exception
     * <AUTHOR>
     * @date   2024/12/12 下午4:00
     */
    public function exportFormat(Transfer $transfer, $field, $value)
    {

        $newValue = $transfer->parse($field['field_name'], $value);
        switch (true) {
        case $field['component_type'] == "Casader":
            $result = implode('/', $newValue);
            break;
        default:
            $result = $newValue;
        }

        return $result;
    }


    /**
     * 导入数据的格式转换
     * @param  Transfer  $transfer
     * @param            $field
     * @param            $value
     * @return mixed|string
     * @throws Exception
     * <AUTHOR>
     * @date   2024/12/12 下午4:00
     */
    public function importFormat(Transfer $transfer, $field, $value)
    {

        switch (true) {
        case $field['component_type'] == "Casader": //级联组件
            $result = $transfer->parse($field['field_label'], explode('/', $value));
            break;
        case $field['field_name'] == "cnt_id_list":
        case $field['field_name'] == "parent_id": //父需求为id，返回也需id
            $transfer->parse($field['field_name'], $value);
            $result = $value;
            break;
        case isset($field['field_component']['type']) && $field['field_component']['type'] == 'date': //处理时间格式
            $format = match ($field['field_component']['format']) {
                'YYYY-MM-DD HH:mm:ss' => 'Y-m-d H:i:s',
                'YYYY-MM-DD' => 'Y-m-d',
                default => throw new Exception('组件时间格式错误'),
            };
            if ( ! Validate::is($value, 'date')) {
                throw new Exception('日期类型格式错误');
            }
            $result = $transfer->parse($field['field_label'], date($format, strtotime($value)));
            break;
        default:
            $result = $transfer->parse($field['field_label'], $value);
        }

        return $result;
    }

    private function sortMultipleTrees(array $data, $idField, $parentIdField): array
    {
        foreach ($data as $v) {
            if ($v[$idField] == $v[$parentIdField]) {
                throw new Exception("不可指定自身为父级，标题:$v[$idField]，请检查数据后重试！");
            }
        }
        // 找出所有的id
        $allIds = array_filter(array_column($data, $idField));
        // 推断根节点（pid 不在任何 id 中的节点）
        $rootNodes = array_filter($data, fn($item) => ! in_array($item[$parentIdField], $allIds));
        // 将数据按 pid 分组
        $groupedData = [];
        foreach ($data as $item) {
            $groupedData[$item[$parentIdField]][] = $item;
        }

        // 初始化排序结果
        $sorted = [];
        foreach ($rootNodes as $rootNode) {
            $sorted[] = $rootNode; // 根节点可以放在前或后
            $this->buildSortedData($groupedData, $rootNode[$idField], $sorted, $idField);
        }

        return $sorted;
    }

    // 递归函数：按层级添加数据
    private function buildSortedData(array &$groupedData, $pid, array &$sorted, $idField)
    {
        if ($pid && isset($groupedData[$pid])) {
            $children = $groupedData[$pid];
            unset($groupedData[$pid]);
            foreach ($children as $item) {
                $sorted[] = $item; // 添加当前节点
                $this->buildSortedData($groupedData, $item[$idField], $sorted, $idField); // 递归处理子节点
            }
        }
    }


    /**
     *
     * @param $subKey
     * @param $projectId
     * @param $dataList
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/12/19 上午10:56
     */
    private function formatImortData($subKey, $projectId, $dataList)
    {

        $fieldList = FieldConfigLogic::getListBySubKey($subKey, $projectId)->column(null, 'field_label');;

        $nameToLabel = [];
        foreach ($fieldList as $s) {
            $nameToLabel[$s['field_name']] = $s['field_label'];
        }
        $nameToLabel = array_merge($nameToLabel, array_flip($nameToLabel));


        $head = array_shift($dataList);


        $allowedColumns = array_keys($fieldList);
        $ignoredColumns = array_diff($head, $allowedColumns);
        $ignoredColumnsIndex = array_keys($ignoredColumns);
        $ignoredColumns = array_values($ignoredColumns);
        $head = array_values(array_intersect($head, $allowedColumns));
        $headColumnIndex = array_flip(array_filter($head));

        foreach ($dataList as &$v) {
            foreach ($v as $k => $vv) {
                if (in_array($k, $ignoredColumnsIndex)) {
                    unset($v[$k]);
                }
            }
            $v = array_values($v);
        }

        return [
            'fieldList'       => $fieldList,
            'head'            => $head,
            'headColumnIndex' => $headColumnIndex,
            'dataList'        => $dataList,
            'ignoredColumns'  => $ignoredColumns,
            'nameToLabel'     => $nameToLabel,
        ];
    }
}
