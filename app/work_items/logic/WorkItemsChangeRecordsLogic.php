<?php
declare (strict_types=1);

namespace app\work_items\logic;

use app\infrastructure\model\GeneralApprovalsModel;
use app\project\logic\IterationProcessNodeLogic;
use app\project\model\IterationModel;
use app\work_items\model\WorkItemsChangeRecordsModel;
use app\work_items\model\WorkItemsModel;
use basic\BaseLogic;
use exception\NotFoundException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\Paginator;

/**
 * 需求变更记录逻辑类
 */
class WorkItemsChangeRecordsLogic extends BaseLogic
{
    /**
     * 创建需求变更记录
     * @param  GeneralApprovalsModel  $model  审批模型
     * @param  array                  $data   额外数据
     * @return WorkItemsChangeRecordsModel
     * @throws Exception
     */
    public function create(GeneralApprovalsModel $model, array $data = []): WorkItemsChangeRecordsModel
    {
        // 获取工作项信息
        $workItem = WorkItemsModel::findById($model->business_model_id);
        if ( ! $workItem) {
            throw new NotFoundException('工作项不存在');
        }

        // 获取迭代信息
        $iteration = null;
        $iterationName = '';
        $nodeName = '';
        $nodeStage = '';
        $nodeId = 0;
        if ( ! empty($workItem->extends['iteration_id'])) {
            $iteration = IterationModel::findById($workItem->extends['iteration_id']);
            if ($iteration) {
                $iterationName = $iteration->extends['iteration_name'] ?? '';

                // 获取迭代节点信息
                $node = IterationProcessNodeLogic::getHighestPriorityInProgressNode($iteration->iteration_id);
                if ($node) {
                    $nodeId = $node->iteration_process_node_id;
                    $nodeName = $node->node_name;
                    $nodeStage = $node->node_data['node_setting']['node_stage'] ?? '';
                }
            }
        }


        // 创建记录
        $record = new WorkItemsChangeRecordsModel();
        $record->save([
            'project_id'     => $workItem->extends['project_id'] ?? 0,
            'project_name'   => $workItem->extends['project_name'] ?? '',
            'cnt_id'         => $model->business_model_id,
            'cnt_title'      => $workItem->extends['title'] ?? '',
            'handler_uid'    => $workItem->extends['handler'] ?? 0,
            'handler_name'   => $workItem->extends['handler_name'] ?? '',
            'iteration_id'   => $workItem->extends['iteration_id'] ?? 0,
            'iteration_name' => $iterationName,
            'node_id'        => $nodeId,
            'node_name'      => $nodeName,
            'node_stage'     => $nodeStage,
            'change_reason'  => $model->content['changes_content'] ?? '',
            'submitter_uid'  => $model->create_by,
            'submitter_name' => $model->create_by_name,
            'submit_at'      => $model->create_at,
            'approval_at'    => $model->update_at,
            'approval_id'    => $model->general_approvals_id,
        ]);

        return $record;
    }

    /**
     * 分页查询需求变更记录
     * @param  array  $params  查询参数
     * @return Paginator
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function pageQuery(array $params): Paginator
    {
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['page_size']) ? intval($params['page_size']) : 10;

        $query = WorkItemsChangeRecordsModel::where('is_delete', WorkItemsChangeRecordsModel::DELETE_NOT);

        // 按项目查询
        if ( ! empty($params['project_id'])) {
            $query->where('project_id', $params['project_id']);
        }

        // 按需求查询
        if ( ! empty($params['cnt_id'])) {
            $query->where('cnt_id', $params['cnt_id']);
        }

        // 按需求负责人查询
        if ( ! empty($params['handler_uid'])) {
            $query->where('handler_uid', $params['handler_uid']);
        }

        // 按迭代查询
        if ( ! empty($params['iteration_id'])) {
            $query->where('iteration_id', $params['iteration_id']);
        }

        // 按迭代节点查询
        if ( ! empty($params['node_id'])) {
            $query->where('node_id', $params['node_id']);
        }

        // 按需求标题模糊查询
        if ( ! empty($params['cnt_title'])) {
            $query->whereLike('cnt_title', '%'.$params['cnt_title'].'%');
        }

        // 按时间范围查询
        if ( ! empty($params['start_time'])) {
            $query->where('submit_at', '>=', $params['start_time']);
        }
        if ( ! empty($params['end_time'])) {
            $query->where('submit_at', '<=', $params['end_time']);
        }

        return $query->order('create_at', 'desc')
            ->paginate([
                'list_rows' => $pageSize,
                'page'      => $page,
            ]);
    }

    /**
     * 获取详情
     * @param  int  $id  变更记录ID
     * @return WorkItemsChangeRecordsModel
     * @throws NotFoundException
     */
    public function detail(int $id): WorkItemsChangeRecordsModel
    {
        $model = WorkItemsChangeRecordsModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('记录不存在');
        }

        return $model->toDetail();
    }
} 