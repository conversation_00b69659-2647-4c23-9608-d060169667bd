<?php
/**
 * Desc 项目评论管理 - 逻辑层
 * User Long
 * Date 2024/08/27
 */
declare (strict_types=1);

namespace app\work_items\logic;

use app\project\logic\ProjectUserLogic;
use app\work_items\model\WorkCommentModel;
use app\work_items\validate\CommentValidate;
use basic\BaseLogic;
use basic\BaseModel;
use exception\NotFoundException;
use exception\ParamsException;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use Throwable;
use utils\Ctx;
use utils\DBTransaction;

class CommentLogic extends BaseLogic
{
    private int $commentType; // 评论所属 1=>迭代 2=>需求 3=>任务 4=>缺陷 5=>测试用例 6=>测试计划

    public function __construct(int $commentType = BaseModel::SETTING_TYPE_DEMAND)
    {
        $this->commentType = $commentType;
    }

    /**
     * 校验操作人
     * @param int $userId
     * User Long
     * Date 2024/9/2
     */
    private function validateOperateUser(int $userId): void
    {
        if (Ctx::$userId != $userId) {
            throw new ParamsException('仅支持操作自己的评论');
        }
    }

    /**
     * 保存数据
     * @param array $params
     * User Long
     * Date 2024/8/28
     */
    private function saveData(array $params): void
    {
        switch ($this->commentType) {
//            case BaseModel::SETTING_TYPE_ITERATION:
//                WorkItemsLogic::findWorkItemsInfoById((int)$params['work_items_id']);
//                break;
            case BaseModel::SETTING_TYPE_DEMAND:
                WorkItemsLogic::findWorkItemsInfoById((int)$params['work_items_id']);
                break;
//            case BaseModel::SETTING_TYPE_TASK:
//                WorkItemsLogic::findWorkItemsInfoById((int)$params['work_items_id']);
//                break;
//            case BaseModel::SETTING_TYPE_DEFECT:
//                WorkItemsLogic::findWorkItemsInfoById((int)$params['work_items_id']);
//                break;
            case BaseModel::SETTING_TYPE_TEST_CASE:
                TestCaseLogic::findWorkItemsInfoById((int)$params['work_items_id']);
                break;
//            case BaseModel::SETTING_TYPE_TEST_PLAN:
//                WorkItemsLogic::findWorkItemsInfoById((int)$params['work_items_id']);
//                break;
            default:
                break;
        }

        $model = new WorkCommentModel();
        $params['comment_type'] = $this->commentType; // 设置分类类型

        $model->save($params);
    }

    /**
     * 插入一条记录
     * @param array $params
     * User Long
     * Date 2024/8/28
     */
    public function create(array $params): void
    {
        // 验证器校验
        validate(CommentValidate::class)->scene('create')->check($params);

       $this->saveData($params);
    }

    /**
     * 根据评论id集删除评论
     * @param array $commentIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/9/2
     */
    public function delete(array $commentIds): void
    {
        $models = WorkCommentModel::selectByCommentIds($this->commentType, $commentIds);

        try {
            Db::startTrans();

            foreach ($models as $model) {
                // 校验是否本人操作
                $this->validateOperateUser((int)$model->create_by);

                // 判断是否为回复消息
                if ($model->isReplyComment()) {
                    $model->save(['is_delete' => BaseModel::DELETE_YES]);

                    // 评论已删除，删回复同时真删评论
                    $replyModel = WorkCommentModel::findByReplyCommentId($this->commentType, (int)$model->comment_id);
                    if (!$replyModel) {
                        $commentModel = WorkCommentModel::findByCommentId($this->commentType, (int)$model->reply_comment_id);
                        if ($commentModel && $commentModel->is_fake_delete == WorkCommentModel::IS_FAKE_DELETE_YES) {
                            $commentModel->save(['is_delete' => BaseModel::DELETE_YES]);
                        }
                    }
                    continue;
                }

                // 评论删除判断
                $replyModel = WorkCommentModel::findByReplyCommentId($this->commentType, (int)$model->comment_id);
                if (!$replyModel) {
                    $model->save(['is_delete' => BaseModel::DELETE_YES]);
                } else {
                    $model->save(['is_fake_delete' => WorkCommentModel::IS_FAKE_DELETE_YES]);
                }
            }
            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();

            throw $e;
        }

    }

    /**
     * 根据评论id更新数据
     * @param int $commentId
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function update(int $commentId, array $params): void
    {
        // 验证器校验
        validate(CommentValidate::class)->scene('update')->check($params);

        $model = WorkCommentModel::findByCommentId($this->commentType, $commentId);
        if (!$model) {
            throw new NotFoundException();
        }
        // 校验是否本人操作
        $this->validateOperateUser((int)$model->create_by);

        $model->save($params);
    }

    /**
     * 插入一条记录
     * @param int $replyId
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function reply(int $replyId, array $params): void
    {
        // 验证器校验
        validate(CommentValidate::class)->scene('reply')->check($params);

        $model = WorkCommentModel::findByCommentId($this->commentType, $replyId);
        if (!$model) {
            throw new NotFoundException('评论不存在，请刷新页面');
        }

        $params['work_items_id'] = $model->work_items_id;
        // 判断是否回复消息，回复存数据库中评论id,否则记录前端传的评论id
        $params['reply_comment_id'] = $model->isReplyComment() ? $model->reply_comment_id : $replyId;

        $this->saveData($params);
    }

    /**
     * 评论详情
     * @param int $commentId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function detail(int $commentId): array
    {
        $model = WorkCommentModel::findByCommentId($this->commentType, $commentId);

        if (!$model) {
            throw new NotFoundException();
        }

        $model->toDetail();
        return $model->toArray();
    }

    /**
     * 评论分页
     * @param int $workItemsId
     * @return Collection|array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function listQuery(int $workItemsId): Collection|array
    {
        return WorkCommentModel::status()
            ->with([
                'reply' => function($sql) {
                    $sql->with(['userInfo' => function ($sql) {
                        $sql->where('avatar', '<>', '')->bind(['avatar']);
                    }])->order('create_at DESC')
                        ->hidden(array_merge(WorkCommentModel::HIDDEN_FIELD, ['is_fake_delete', 'is_top']));
                },
                'userInfo' => function ($sql) {
                    $sql->where('avatar', '<>', '')->bind(['avatar']);
                }
            ])
            ->where([
                'work_items_id' => $workItemsId,
                'reply_id' => WorkCommentModel::TOP_LEVEL_COMMENT,
                'comment_type' => $this->commentType
            ])
            ->hidden(WorkCommentModel::HIDDEN_FIELD)
//            ->order('is_top DESC, create_at ASC')
            ->order('is_top DESC, create_at desc')
            ->select()
            ->toArray();

        /**TODO 2024-12-30 废弃接口*/
//        // 获取所有的评论人id
//        $userIds = array_column($res, 'create_by');
//        foreach ($res as &$workItem) {
//            $workItem['avatar'] = '';
//            foreach ($workItem['reply'] as &$item) {
//                $item['avatar'] = '';
//                $userIds[] = $item['create_by'];
//            }
//        }
//        // 消除引用
//        unset($workItem, $item);
//
//        // 获取用户头像
//        $userAvatarData = ProjectUserLogic::getUserAvatar($userIds);
//        foreach ($res as &$workItem) {
//            if (isset($userAvatarData[$workItem['create_by']])) {
//                $workItem['avatar'] = $userAvatarData[$workItem['create_by']];
//            }
//            foreach ($workItem['reply'] as &$item) {
//                if (isset($userAvatarData[$item['create_by']])) {
//                    $item['avatar'] = $userAvatarData[$item['create_by']];
//                }
//            }
//        }
//        // 消除引用
//        unset($workItem, $item);
//
//        return $res;
    }

    /**
     * 设置置顶
     * @param array $commentIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function top(array $commentIds): void
    {
        $models = WorkCommentModel::selectByCommentIds($this->commentType, $commentIds);

        foreach ($models as $model) {
            $model->save(['is_top' => WorkCommentModel::TOP_YES]);
        }
    }

    /**
     * 取消置顶
     * @param array $commentIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/28
     */
    public function restore(array $commentIds): void
    {
        $models = WorkCommentModel::selectByCommentIds($this->commentType, $commentIds);

        foreach ($models as $model) {
            $model->save(['is_top' => WorkCommentModel::TOP_NOT]);
        }
    }


    /**
     * 复制评论
     * @param $oldCommentId
     * @param $newCommentId
     * @return void
     * @throws \Exception
     * <AUTHOR>
     * @date   2024/12/9 上午10:11
     */
    public function copy($oldCommentId, $newCommentId): void
    {
        DBTransaction::begin();
        try {

            $oldCommentList = WorkCommentModel::findListByWorkItemsId($oldCommentId);
            if (!$oldCommentList){
                return;
            }
            $data = $oldCommentList->toArray();
            foreach ($data as &$v) {
                unset($v['work_items_id']);
                if ( ! $v['update_at']) {
                    $v['update_at'] = BaseModel::DEFAULT_TIME;
                }
                $v['comment_type'] = $this->commentType;
                $v['work_items_id'] = $newCommentId;

            }
            $topList = (new Collection($data))->where('reply_id', '=', 0);

            unset($v);
            foreach ($topList->toArray() as $v){
                $this->recursivelyUpdateTheParent($v,$data);
            }


            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }

    }

    /**
     * 递归修改
     * reply_id
     * reply_comment_id
     * @param $topData
     * @param $data
     * @return void
     * <AUTHOR>
     * @date   2024/12/9 上午11:36
     */
    public function recursivelyUpdateTheParent($topData, &$data)
    {
        $oldPid = $topData['comment_id'];
        unset($topData['comment_id']);
        $newModel = (new WorkCommentModel);
        $newModel->save($topData);

        foreach ($data as &$v){
            if ($v['reply_comment_id']!=$oldPid){
                continue;
            }
            $v['reply_comment_id'] = $newModel->comment_id;
        }

        unset($v);
        foreach ($data as $v){
            if ($v['reply_id']!=$oldPid){
                continue;
            }
            $v['reply_id'] = $newModel->comment_id;
            if ($v['reply_comment_id'] == $oldPid) {
                $v['reply_comment_id'] = $newModel->comment_id;
            }

            $this->recursivelyUpdateTheParent($v, $data);
        }

    }

    /**
     * 为多个工作项添加相同的评论
     * @param array $params 包含 work_items_ids 数组和评论内容
     * @return void
     * @throws Throwable
     * User Long
     * Date 2024/9/25
     */
    public function createSameForMultiple(array $params): void
    {
        // 验证器校验
        validate(CommentValidate::class)->scene('createSameForMultiple')->check($params);

        try {
            Db::startTrans();

            foreach ($params['work_items_ids'] as $workItemId) {
                $commentParams = [
                    'comment_type' => $this->commentType,
                    'work_items_id' => $workItemId,
                    'content' => $params['content'],
                    'remark' => $params['remark'] ?? ''
                ];

                $this->saveData($commentParams);
            }

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }

}
