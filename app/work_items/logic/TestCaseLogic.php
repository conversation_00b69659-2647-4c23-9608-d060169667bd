<?php
declare (strict_types=1);

namespace app\work_items\logic;

use app\infrastructure\logic\FieldConfigLogic;
use app\infrastructure\model\FieldConfigModel;
use app\infrastructure\model\FieldSubsetModel;
use app\project\logic\ProjectClassifyLogic;
use app\project\model\ProjectCategoryModel;
use app\project\model\ProjectModel;
use app\work_items\model\TestCaseModel;
use app\work_items\model\WorkItemsModel;
use app\work_items\validate\TestCaseValidate;
use basic\BaseModel;
use Elastic\Elasticsearch\ClientInterface;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use exception\BusinessException;
use exception\NotFoundException;
use basic\BaseLogic;
use exception\ParamsException;
use think\facade\Db;
use think\facade\Env;
use think\model\Collection;
use think\Paginator;
use utils\DBTransaction;
use utils\Es;
use utils\EsClientFactory;

class TestCaseLogic extends BaseLogic
{

    const NOT_PAGE_MAX = 10000;//不分页时的最大条数

    //关系相关字段必须要有
    const REQUIRED_FIELD
        = [
//        'test_case_id', 'parent_id', 'root_id', 'cnt_type', 'flow_status_id', 'status_enum_id', 'type_id'
            'test_case_id',
        ];


    private ClientInterface $esClient;
    private mixed $esIndex;

    public function __construct(ClientInterface $client)
    {
        $this->esClient = $client;
        $this->esIndex = Env::get('es.test_case_index');
    }

    public static function getInstance()
    {
        return new self(EsClientFactory::getInstance());
    }


    /**
     * 处理 工作项 与 类别 枚举定义类型不一致问题
     * @param $projectCategorySettingsType
     * @return int
     * User Long
     * Date 2024/9/4
     */
    private static function handleProjectCategorySettingsType($projectCategorySettingsType)
    {
        return match ($projectCategorySettingsType) {
            BaseModel::SETTING_TYPE_DEMAND => WorkItemsModel::CNT_TYPE_DEMAND,
            BaseModel::SETTING_TYPE_TASK => WorkItemsModel::CNT_TYPE_TASK,
            BaseModel::SETTING_TYPE_DEFECT => WorkItemsModel::CNT_TYPE_FLAW,
            default => 0,
        };
    }

    /**
     * 获取默认查询字段
     * @param  array  $fieldList
     * @return array|string[]
     * <AUTHOR>
     * @date   2024/8/29 22:18
     */
    private function getFiledList(array $fieldList)
    {
        return array_merge($fieldList, self::REQUIRED_FIELD);
    }


    /**
     * 新增
     * @param $params
     * @return array|string
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function create($params)
    {
        validate(TestCaseValidate::class)->scene('create')->check($params);

        Db::startTrans();
        try {
            $model = TestCaseModel::create();

            $params['test_case_id'] = $model->test_case_id;
            $params['is_delete'] = TestCaseModel::DELETE_NOT;
//            if (!isset($params['parent_id'])) {
//                $params['parent_id'] = 0;
//            }

            //无category_id或为0都表示未分类
            if ( ! ($params['category_id'] ?? false)) {
                $params['category_id'] = '-1';
            }


            if ($params['cnt_id_list'] ?? false) {
                (new TestCaseWorkLogic)->relevancy($model->test_case_id, $params['cnt_id_list'], TestCaseWorkLogic::TYPE_TEST_CASE_ID);
            }

            $model->save($params);

            Db::commit();

            return $model->toDetail();
        } catch (\Throwable $e) {
            Db::rollback();
            throw  $e;
        }

    }

    /**
     * 修改
     * @param $params
     * @return TestCaseModel|array|mixed|\think\Model
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function update($params)
    {
        validate(TestCaseValidate::class)->scene('update')->check($params);

        $model = TestCaseModel::findById($params['test_case_id']);
        if ( ! $model) {
            throw new NotFoundException();
        }

        if (isset($params['category_id']) && ! $params['category_id']) {
            $params['category_id'] = '-1';
        }

        if (isset($params['title']) && trim($params['title']) == '') {
            throw new ParamsException("标题不可为空！");
        }

        Db::startTrans();
        try {

            if ($params['cnt_id_list'] ?? false) {
                (new TestCaseWorkLogic)->relevancy($model->test_case_id, $params['cnt_id_list'], TestCaseWorkLogic::TYPE_TEST_CASE_ID);
                unset($params['cnt_id_list']);
            }


            $model->refresh();
            $model->save($params);

            Db::commit();

            return $model->toDetail();
        } catch (\Throwable $e) {
            Db::rollback();
            throw  $e;
        }

    }


    /**
     * 详情
     * @param $id
     * @return array
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function detail($id)
    {

        $model = TestCaseModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }



        $details = $model->toDetail();

        if ($details['project_id'] ?? false) {
            $details['project_name'] = ProjectModel::findById((int)$details['project_id'])['project_name'] ?? '';
        }

//        $children = $this->getChildren($model->test_case_id);

        return [
//            'hasChildDemand' => $children->where('cnt_type', '=', TestCaseModel::CNT_TYPE_DEMAND)->isEmpty() ? 0 : 1,
//            'hasChildTask' => $children->where('cnt_type', '=', TestCaseModel::CNT_TYPE_TASK)->isEmpty() ? 0 : 1,
//            'hasChildFlaw' => $children->where('cnt_type', '=', TestCaseModel::CNT_TYPE_FLAW)->isEmpty() ? 0 : 1,
            'data'       => $details,
            'field_list' => $model->getFieldList(),
        ];
    }


    /**
     * 删除
     * @param $id
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/9/10 11:56
     */
    public function delete($id)
    {
        $model = TestCaseModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }

        if ($model->extends['is_delete'] === BaseModel::DELETE_YES) {
            return;
        }

        Db::startTrans();
        try {
            $model->save(['is_delete' => BaseModel::DELETE_YES]);

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }


    /**
     * 分页查询
     * @param $params
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function pageQuery($params, $listRows = 20, $page = 1)
    {
        validate(TestCaseValidate::class)->scene('pageQuery')->check($params);

        $params['searchParams'] = array_column($params['searchParams'], null, 'field_name');
        $params['searchParams']['project_id'] = ['field_name' => 'project_id', 'value' => $params['project_id'], 'type' => "term", 'operate_type' => "equal"];
        if (isset($params['searchParams']['category_id']['value']) && $params['searchParams']['category_id']['value'] == 0) {
            unset($params['searchParams']['category_id']);
        }

        if ($params['cnt_id'] ?? false) {
            $params['searchParams']['test_case_id'] = ['field_name' => 'test_case_id', 'value' => TestCaseWorkLogic::getIdListByType($params['cnt_id'], TestCaseWorkLogic::TYPE_CNT_ID), 'type' => "selector",];
        }

        // 处理排序参数
        $order = $this->formatOrderParams($params['order'] ?? []);

        $itemList = $this->esSearch($params['searchParams'], $listRows, $page, $params['field_list'], $params['test_plan_id'] ?? 0, $order);

        return Paginator::make($itemList->getCollection()->toArray(), $itemList->listRows(), $itemList->currentPage(), $itemList->total());
    }


    /**
     * 带目录树形结构
     * @param $params
     * @return array
     * <AUTHOR>
     * @date   2024/9/5 16:38
     */
    public function withCategoryTree($params)
    {
        $params['searchParams'] = array_column($params['searchParams'], null, 'field_name');
        $params['searchParams']['project_id'] = ['field_name' => 'project_id', 'value' => $params['project_id'], 'type' => "term", 'operate_type' => "equal"];
        if (isset($params['searchParams']['category_id']['value']) && $params['searchParams']['category_id']['value'] == 0) {
            unset($params['searchParams']['category_id']);
        }

        $caseList = $this->esSearch($params['searchParams'], self::NOT_PAGE_MAX, 1, [
            'test_case_id', 'title', 'category_id'
        ])->each(function ($item) {
            $item['pid'] = $item['category_id'];
            $item['id'] = $item['test_case_id'];
            $item['project_category_id'] = 'case-'.$item['test_case_id'];
            $item['node_type'] = 2;

            return $item;
        });

        $categoryList = ProjectClassifyLogic::list((int)$params['project_id'], ProjectCategoryModel::PROJECT_CATEGORY_TYPE_TEST_CASE)->unshift([
            'project_category_id' => '-1',
            'category_name'       => "未分类",
            'pid'                 => 0,
        ])->each(function ($item) {
            $item['node_type'] = 1;
            $item['id'] = $item['project_category_id'];
            $item['title'] = $item['category_name'];

            return $item;
        });

        $tree = $this->buildTree(array_merge($categoryList->toArray(), $caseList->getCollection()->toArray()));

        $parent = ['node_type' => 1, 'children' => $tree];
        $this->removeNotChildNode($parent);


        return $parent['children'];

    }

    /**
     * 清除空目录
     * @param $node
     * @return bool
     * <AUTHOR>
     * @date   2024/10/30 17:41
     */
    function removeNotChildNode(&$node)
    {
        // 如果当前节点是叶子节点（数据节点），返回 1
        if ($node['node_type'] == 2) {
            return false;
        }

        // 遍历每个子节点，递归计算其下的叶子节点数量
        foreach ($node['children'] as $key => &$child) {
            if ($this->removeNotChildNode($child)) {
                unset($node['children'][$key]);
            }
        }
        $node['children'] = array_values($node['children']);

        return empty($node['children']);
    }

    /**
     * 格式化为树
     * @param $data
     * @param $parentId
     * @return array
     * <AUTHOR>
     * @date   2024/8/28 11:59
     */
    public function buildTree($data)
    {
        $tree = [];
        $itemsById = [];

        // 先将数据按 id 重新索引
        foreach ($data as $item) {
            $itemsById[$item['project_category_id']] = $item;
            $itemsById[$item['project_category_id']]['children'] = []; // 初始化子节点
        }

        // 遍历所有项，构建树
        foreach ($itemsById as &$item) {
            // 如果 parent_id 为 null 或者不存在于数据中，则是顶级节点

            if ($item['pid'] === 0) {
                $tree[] = &$item;
            } elseif ( ! isset($itemsById[$item['pid']])) {
                continue;
            } else {
                // 如果有父节点，将当前节点作为父节点的子节点
                $itemsById[$item['pid']]['children'][] = &$item;
            }
        }

        return $tree;
    }


    /**
     * 根据id保存es数据
     * @param       $id
     * @param       $data
     * @param  int  $operationType  操作类型0创建，1更新
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/8/27 21:09
     */
    public function saveEs($id, $data, int $operationType)
    {
        $params = [
            'index'   => $this->esIndex,
            'id'      => $id,
            'body'    => [],
            'refresh' => config('es.refresh'),//同步
        ];

        if ($operationType) {
            $params['body']['doc'] = $data;
            $response = $this->esClient->update($params)->getBody();
        } else {
            $params['body'] = $data;
            $response = $this->esClient->index($params)->getBody();
        }

        $response = json_decode((string)$response, true);
        if ( ! (isset($response['result']) && ($response['result'] === 'updated' || $response['result'] === 'created' || $response['result'] === 'noop'))) {
            throw new BusinessException("数据保存错误！");
        }
    }

    /**
     * 批量保存数据
     * @param $data array 数据集
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     */
    public function bulk($data)
    {
        $bulk = ['body' => [], 'refresh' => config('es.refresh'),];
        foreach ($data as $item) {
            $bulk['body'][] = ['update' => ['_index' => $this->esIndex, '_id' => $item['test_case_id']]];
            $bulk['body'][] = ['doc' => $item, 'doc_as_upsert' => true];
        }

        $response = $this->esClient->bulk($bulk)->getBody();

        $response = json_decode((string)$response, true);
        if ( ! array_key_exists('errors', $response) || $response['errors']) {
            throw new BusinessException("数据保存错误！");
        }
    }


    /**
     * 返回没删除的es查询条件
     * @return array
     * <AUTHOR>
     * @date   2024/9/5 16:39
     */
    private function getNotDelParams()
    {
        return ['field_name' => 'is_delete', 'value' => BaseModel::DELETE_NOT, 'type' => 'term', 'operate_type' => 'equal'];
    }

    /**
     * 查询es数据
     * @param $params
     * @param $listRows
     * @param $page
     * @param $fieldList
     * @param int $testPlanId
     * @param array $order 排序参数
     * @return Paginator|\think\paginator\driver\Bootstrap
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/9/5 16:40
     */
    public function esSearch($params, $listRows = 20, $page = 1, $fieldList = null, $testPlanId = 0, array $order = [])
    {
        $params[] = $this->getNotDelParams();

        //规划与执行字段
        $planFieldList = FieldConfigLogic::getListByModuleId(['module_id' => FieldConfigModel::MODULE_TYPE_TEST_CASE_PLAN])->column('field_name');

        $field = (new Collection($params))->where('field_name', 'not in', $planFieldList)->toArray();
        $where = $this->generateEsQueryParams($field);

        if ($testPlanId) {//nested字段查询
            $nestedField = (new Collection($params))->where('field_name', 'in', $planFieldList)->toArray();

            foreach ($nestedField as $key => $item) {
                $nestedField[$key]['field_name'] = "plan_list.{$testPlanId}.{$item['field_name']}";
            }
            $nestedField[] = ['field_name' => "plan_list.{$testPlanId}.test_plan_id", 'value' => $testPlanId, 'type' => "term", 'operate_type' => "equal"];

            $nestedWhere = $this->generateEsQueryParams($nestedField);

            //nested字段搜索
            $where['filter'][] = [
                'nested' => [
                    'path'  => 'plan_list',
                    'query' => [
                        'bool' => $nestedWhere
                    ]
                ]
            ];
        }


        $params = [
            'index' => $this->esIndex,
            'body'  => [
//                '_source' => [
//                    'parent_id',
//                    'test_case_id',
//                ],
                'query' => [
                    'bool' => $where
                ],
                'sort'  => !empty($order) ? $order : [
                    'test_case_id' => [
                        'order' => 'desc'
                    ]
                ],
            ],
            'from'  => ($page - 1) * $listRows, // 起始位置
            'size'  => $listRows, // 每页数量

        ];

        if ($fieldList) {
            $params['body']['_source'] = $this->getFiledList($fieldList);
        }

        $response = $this->esClient->search($params)->getBody();

        $resp = json_decode((string)$response, true);
        $total = $resp['hits']['total']['value'];
        $data = $resp['hits']['hits'];
        $data = array_column($data, '_source');

        $this->mergeContents($fieldList, $data);


        return Paginator::make($data, $listRows, $page, $total);
    }


    /**
     * 将es查询出的值追加mysql中的contents
     * @param  mixed  $fieldList
     * @param  array  $data
     * @return void
     */
    private function mergeContents(mixed $fieldList, array &$data)
    {

        $richTextFields = array_intersect(['case_tep', 'preconditions', 'expected_results'], $fieldList ?? []);
        if ($richTextFields) {

            $contentsList = TestCaseModel::where(['test_case_id' => array_column($data, 'test_case_id')])
                ->column(
                    array_merge([
                        'test_case_id',
                    ], $richTextFields), 'test_case_id'
                );

            foreach ($data as &$v) {
                foreach ($richTextFields as $field) {
                    $v[$field] = $contentsList[$v['test_case_id']][$field];
                }
            }
        }

        if (in_array('cnt_id_list', $fieldList ?? [])) {
            $this->mergeParnetName($data);
        }


    }

    /**
     * 合并需求名称
     * @param $data
     * @return void
     * <AUTHOR>
     * @date   2024/11/15 14:49
     */
    public function mergeParnetName(&$data)
    {
        $cntIdListList = array_unique(array_filter(array_merge(...array_filter(array_column($data, 'cnt_id_list')))));
        if ( ! $cntIdListList) {
            return;
        }
        $resultList = WorkItemsEsLogic::getInstance()->esSearch([
            ['field_name' => 'cnt_id', 'value' => $cntIdListList, 'type' => 'selector']
        ], self::NOT_PAGE_MAX, 1, [
            'cnt_id',
            'title'
        ])->getCollection()->column('title', 'cnt_id');
        foreach ($data as &$v) {
            if (isset($v['cnt_id_list']) && $v['cnt_id_list']) {
                foreach ($v['cnt_id_list'] as $cntId) {
                    if ($resultList[$cntId] ?? false) {
                        $v['cnt_id_list_name'][] = ['label' => $resultList[$cntId], 'value' => $cntId];
                    }
                }
            }
        }

    }

    /**
     * 生成es查询参数
     * @param $params
     * @return array|array[]
     * <AUTHOR>
     * @date   2024/8/28 11:19
     */
    private function generateEsQueryParams($params)
    {
        return WorkItemsEsLogic::getInstance()->generateEsQueryParams($params);
    }

    /**
     * 将text类型查询条件移动至最后一个
     * @param $params
     * @return array
     * <AUTHOR>
     * @date   2024/9/10 17:27
     */
    private function textFieldMoveEnd($params)
    {
        $result = [];
        $textFieldList = [];
        foreach ($params as $key => $v) {
            if ($v['type'] == 'text') {
                $textFieldList[] = $v;
            } else {
                $result[] = $v;
            }
        }

        return array_merge($result, $textFieldList);
    }


    /**
     * 获取在指定分类下的工作项id集合
     * @param $categoryId
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/10/8
     */
    public static function getItemByCategoryId($categoryId)
    {
        return Es::getInstance()
            ->setEsTable(\env('es.test_case_index'))
            ->setQueryParam(['field_name' => 'category_id', 'value' => $categoryId, 'type' => 'term', 'operate_type' => 'equal'])
            ->setPage(self::NOT_PAGE_MAX)
            ->getPaginator()
            ->getCollection()
            ->column('test_case_id');
    }

    /**
     * 批量设置工作项分类
     * @param  array  $testCaseIds
     * @param  int    $categoryId
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Throwable
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/10/8
     */
    public function setItemsCategoryId(array $testCaseIds, int $categoryId)
    {
        try {
            Db::startTrans();

            TestCaseModel::where(['test_case_id' => $testCaseIds, 'is_delete' => BaseModel::DELETE_NOT])
                ->select()->each(function (TestCaseModel $model) use ($categoryId) {
                    $model->save(['category_id' => $categoryId]);
                });

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 根据工作项id查询工作信息
     * @param  int  $testCaseId
     * @return TestCaseModel|array|mixed|\think\Model
     * User Long
     * Date 2024/10/8
     */
    public static function findWorkItemsInfoById(int $testCaseId)
    {
        $model = TestCaseModel::findById($testCaseId);
        if ( ! $model) {
            throw new NotFoundException('当前内容已删除，请刷新页面');
        }

        return $model;
    }


    /**
     * 保存planList
     * @param $caseId
     * @param $planId
     * @param $data
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * <AUTHOR>
     * @date   2024/10/10 17:40
     */
    public static function savePlanList($caseId, $planId, $data)
    {
        $model = TestCaseModel::findById($caseId);
        if ( ! $model) {
            return;
        }
        $planList = $model->toDetail()['plan_list'] ?? [];
        if ($data) {
            $planList[$planId] = $data;
        } else {
            //data为空就删除
            unset($planList[$planId]);
        }

        $model->save(['plan_list' => $planList]);
    }

    /**
     * 获取分类，需求数 es聚合数据
     * @param $projectId
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/10/28
     */
    public static function getAggsCategoryByProjectId($projectId)
    {
        return Es::getInstance()
            ->setEsTable(\env('es.test_case_index'))
            ->setQueryParam(['field_name' => 'project_id', 'value' => $projectId, 'type' => 'term', 'operate_type' => 'equal'])
            ->setAggsFidld('category_id')
            ->setLimit(self::NOT_PAGE_MAX)
            ->getAggData();
    }

    /**
     * 批量删除
     * @param  array  $ids  测试用例ID数组
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/3/21
     */
    public function batchDelete(array $ids)
    {
        if (empty($ids)) {
            throw new ParamsException('请选择要删除的测试用例');
        }

        $models = TestCaseModel::where('test_case_id', 'in', $ids)->select();
        if ($models->isEmpty()) {
            return;
        }

        DBTransaction::begin();
        try {
            TestCaseModel::$autoSave = false;
            foreach ($models as $model) {
                if ($model->extends['is_delete'] === BaseModel::DELETE_YES) {
                    continue;
                }
                $model->save(['is_delete' => BaseModel::DELETE_YES]);
            }
            TestCaseModel::actualSaving();

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 批量更新测试用例
     * @param array $params 包含test_case_ids和field_list的参数数组
     * @return array 更新结果
     * @throws \Throwable
     */
    public function batchUpdate(array $params)
    {
        $caseIds = $params['test_case_ids'];

        // 只处理field_list格式的参数
        if (!isset($params['field_list']) || !is_array($params['field_list'])) {
            throw new ParamsException("field_list参数不能为空");
        }

        // 获取可编辑字段列表
        $allowedFields = $this->getAllowedFields();
        if (empty($allowedFields)) {
            throw new ParamsException("未找到可编辑字段配置");
        }

        $updateFields = [];
        foreach ($params['field_list'] as $field) {
            if (isset($field['field_name'])) {
                // 验证字段是否在允许的范围内
                if (!in_array($field['field_name'], $allowedFields)) {
                    continue; // 跳过不允许编辑的字段
                }

                // 处理vlaue拼写错误，同时兼容value正确拼写
                $value = $field['value'] ?? $field['vlaue'] ?? null;
                $updateFields[$field['field_name']] = $value;
            }
        }

        if (empty($caseIds)) {
            throw new ParamsException("测试用例ID不能为空");
        }

        if (empty($updateFields)) {
            throw new ParamsException("更新字段不能为空");
        }

        $result = [];
        $hasSuccess = false;

        DBTransaction::begin();
        try {
            TestCaseModel::$autoSave = false;
            foreach ($caseIds as $id) {
                $model = TestCaseModel::findById($id);
                if (!$model || $model->extends['is_delete'] === BaseModel::DELETE_YES) {
                    continue;
                }

                // 构建更新数据
                $updateData = [];
                foreach ($updateFields as $field => $value) {
                    // 特殊字段处理
                    switch ($field) {
                        default:
                            $updateData[$field] = $value;
                            break;
                    }
                }

                // 执行更新
                if (!empty($updateData)) {
                    $model->save($updateData);
                }

                $hasSuccess = true;
                $result[] = $id;
            }

            TestCaseModel::actualSaving();
            DBTransaction::commit();

            $msg = $hasSuccess ? "批量更新成功" : "批量更新失败";

            return [
                'msg'      => $msg,
                'data'     => [
                    'result'  => $result,
                    'success' => $hasSuccess
                ],
                'msg_type' => $hasSuccess ? 1 : 0
            ];
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 获取测试用例允许编辑的字段列表
     * @return array 允许编辑的字段名称数组
     */
    private function getAllowedFields(): array
    {
        $fieldList = FieldSubsetModel::getByKey('case_edit');
        if (empty($fieldList)) {
            return [];
        }

        // 提取字段名称
        $allowedFields = [];
        foreach ($fieldList['field_list'] as $field) {
            if (isset($field['field_name'])) {
                $allowedFields[] = $field['field_name'];
            }
        }

        return $allowedFields;
    }

    /**
     * 格式化排序参数
     * @param  mixed  $order  排序参数，支持字符串（如 "field desc, field2 asc"）或数组格式
     * @return array Elasticsearch排序格式的数组
     * <AUTHOR>
     * @date   2024/7/24
     */
    private function formatOrderParams($order)
    {
        $result = [];

        // 空值处理
        if (empty($order)) {
            return $result;
        }

        // 字符串格式: "field desc, field2 asc"
        if (is_string($order)) {
            $orderItems = explode(',', $order);
            foreach ($orderItems as $item) {
                $item = trim($item);
                if (empty($item)) {
                    continue;
                }

                $parts = explode(' ', $item, 2);
                $field = trim($parts[0]);
                $direction = isset($parts[1]) ? strtolower(trim($parts[1])) : 'desc';

                if ($direction !== 'asc' && $direction !== 'desc') {
                    $direction = 'desc'; // 默认降序
                }

                $result[$field] = ['order' => $direction];
            }
            return $result;
        }

        // 数组格式1: ['field' => 'desc', 'field2' => 'asc']
        if (is_array($order) && ! isset($order[0])) {
            foreach ($order as $field => $direction) {
                $direction = strtolower(trim($direction));
                if ($direction !== 'asc' && $direction !== 'desc') {
                    $direction = 'desc'; // 默认降序
                }
                $result[$field] = ['order' => $direction];
            }
            return $result;
        }

        // 数组格式2: [['field' => 'field1', 'direction' => 'desc'], [...]]
        if (is_array($order) && isset($order[0]) && is_array($order[0])) {
            foreach ($order as $item) {
                if ( ! isset($item['field'])) {
                    continue;
                }

                $field = $item['field'];
                $direction = isset($item['direction']) ? strtolower(trim($item['direction'])) : 'desc';

                if ($direction !== 'asc' && $direction !== 'desc') {
                    $direction = 'desc'; // 默认降序
                }

                $result[$field] = ['order' => $direction];
            }
            return $result;
        }

        return $result;
    }
}
