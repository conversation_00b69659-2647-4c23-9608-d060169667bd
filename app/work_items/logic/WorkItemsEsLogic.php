<?php

declare(strict_types=1);

namespace app\work_items\logic;

use app\project\logic\IterationProcessNodeLogic;
use app\project\model\IterationProcessNodeModel;
use app\work_items\model\WorkItemsModel;
use basic\BaseModel;
use Elastic\Elasticsearch\Client;
use field_utils\Transfer;
use app\infrastructure\model\FieldConfigModel;

// ClientInterface 已更改为 Client
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use exception\BusinessException;
use basic\BaseLogic;
use exception\ParamsException;
use think\Collection;
use think\facade\Env;
use think\Paginator;
use utils\EsClientFactory;
use app\work_items\pipeline\DataContainer;
use app\work_items\pipeline\processors\QueryBuildingProcessor;
use app\work_items\pipeline\processors\DataFetchingProcessor;
use app\work_items\pipeline\processors\FlatListDataProcessingProcessor;
use app\work_items\pipeline\processors\GroupedDataProcessingProcessor;
use app\work_items\pipeline\processors\TreeNodesFetchingProcessor;
use app\work_items\pipeline\processors\NodesEnrichmentProcessor;
use app\work_items\pipeline\processors\TreeBuildingProcessor;

/**
 * 工作项es逻辑类
 * User: 袁志凡
 * Date-Time: 2024/11/12 15:49
 */
class WorkItemsEsLogic extends BaseLogic
{
    const NOT_PAGE_MAX = 100000; //不分页时的最大条数
    const DEFAULT_ES_GROUP_MISSING_KEY = '-1'; // ES聚合时处理missing字段的默认键
    const MAX_INNER_RESULT_WINDOW = 100000; // es桶最大数

    //关系相关字段必须要有
    const REQUIRED_FIELD
        = [
            'cnt_id',
            'parent_id',
            'root_id',
            'cnt_type',
            'flow_status_id',
            'status_enum_id',
            'type_id',
            'parents',
            'isEnd',
            'iteration_id',
            'iteration_process_node_id',
            'flaw_id',
            'demand_id',
            'project_id',
        ];

    private Client $esClient; // ClientInterface 已更改为 Client
    private mixed $esIndex;

    private function __construct(Client $client) // ClientInterface 已更改为 Client
    {
        $this->esClient = $client;
        $this->esIndex = Env::get('es.content_index');
    }

    public static function getInstance()
    {
        return new self(EsClientFactory::getInstance());
    }

    /**
     * 获取默认查询字段
     * @param  array  $fieldList
     * @return array|string[]
     * <AUTHOR>
     * @date   2024/8/29 22:18
     */
    public function getFiledList(array $fieldList)
    {
        return array_merge($fieldList, self::REQUIRED_FIELD);
    }

    /**
     * 将es查询出的值追加mysql中的contents
     * @param $data
     * @return mixed
     * <AUTHOR>
     * @date   2024/9/3 15:12
     */
    private function mergeContents(&$data)
    {
        if (empty($data)) {
            return $data;
        }
        $ids = array_column($data, 'cnt_id');

        $contentsList = WorkItemsModel::where(['cnt_id' => $ids])
            ->column('cnt_id,contents', 'cnt_id');

        foreach ($data as &$v) {
            $v['contents'] = $contentsList[$v['cnt_id']]['contents'] ?? '';
        }

        return $data;
    }

    /**
     * 合并迭代节点名称
     * @param $data
     * @return void
     * <AUTHOR>
     * @date   2024/11/15 14:49
     */
    private function mergeNodeName(&$data)
    {
        $iterationProcessNodeIdList = array_column($data, 'iteration_process_node_id') ?? [];
        if ( ! $iterationProcessNodeIdList) {
            return;
        }
        $resultList = IterationProcessNodeModel::findByIdList(array_column($data, 'iteration_process_node_id'))
            ->column('node_name', 'iteration_process_node_id');

        foreach ($data as &$v) {
            if (isset($v['iteration_process_node_id']) && isset($resultList[$v['iteration_process_node_id']])) {
                $v['iteration_process_node_name'] = $resultList[$v['iteration_process_node_id']];
            }
        }
    }

    /**
     * 合并父级名称
     * @param $data
     * @return void
     * <AUTHOR>
     * @date   2024/11/15 14:49
     */
    public function mergeParnetName(&$data)
    {
        $parentIdList = array_unique(array_filter(array_column($data, 'parent_id')));

        if ( ! $parentIdList) {
            return;
        }

        $resultList = $this->esSearch([
            ['field_name' => 'cnt_id', 'value' => $parentIdList, 'type' => 'selector']
        ], self::NOT_PAGE_MAX, 1, [
            'cnt_id',
            'title',
            'contents'
        ])->getCollection()->column(null, 'cnt_id');

        foreach ($data as &$v) {
            if (isset($v['parent_id']) && isset($resultList[$v['parent_id']])) {
                $v['parent_id_name'] = ['label' => $resultList[$v['parent_id']]['title'], 'value' => $v['parent_id']];
                $v['parent_title'] = $resultList[$v['parent_id']]['title'];
                $v['parent_contents'] = $resultList[$v['parent_id']]['contents'] ?? '';
            }
        }
    }

    /**
     * 根据id保存es数据
     * @param       $id
     * @param       $data
     * @param  int  $operationType  操作类型0创建，1更新
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/8/27 21:09
     */
    public function save($id, $data, int $operationType)
    {
        $params = [
            'index'   => $this->esIndex,
            'id'      => $id,
            'body'    => [],
            'refresh' => config('es.refresh'), //同步
        ];
        if ($operationType) {
            $params['body']['doc'] = $data;
            $response = $this->esClient->update($params)->getBody();
        } else {
            $params['body'] = $data;
            $response = $this->esClient->index($params)->getBody();
        }

        $response = json_decode((string)$response, true);
        if ( ! (isset($response['result']) && ($response['result'] === 'updated' || $response['result'] === 'created' || $response['result'] === 'noop'))) {
            throw new BusinessException("数据保存错误！");
        }
    }

    /**
     * 批量保存数据
     * @param $data array 数据集
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     */
    public function bulk($data)
    {
        $bulk = ['body' => [], 'refresh' => config('es.refresh'),];
        foreach ($data as $item) {
            $bulk['body'][] = ['update' => ['_index' => $this->esIndex, '_id' => $item['cnt_id']]];
            $bulk['body'][] = ['doc' => $item, 'doc_as_upsert' => true];
        }

        $response = $this->esClient->bulk($bulk)->getBody();

        $response = json_decode((string)$response, true);
        if ( ! array_key_exists('errors', $response) || $response['errors']) {
            throw new BusinessException("数据保存错误！");
        }
    }

    /**
     * 批量修改数据
     * @param $where array|int|string 数组 where条件
     * @param $data  array 键值对数组
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/8/27 21:09
     */
    public function saveEsByQuery($where, $data)
    {
        if ( ! $where) {
            throw new ParamsException("条件不可为空！");
        }
        $params = [
            'index'   => $this->esIndex,
            'body'    => [
                'query'  => [
                    'bool' => $this->generateEsQueryParams($where),
                ],
                'script' => [
                    'source' => $this->generateElasticsearchSource($data),
                    'lang'   => 'painless',
                ]
            ],
            'refresh' => config('es.refresh'), //同步
        ];
        $response = $this->esClient->updateByQuery($params)->getBody();

        $response = json_decode((string)$response, true);

        if ( ! isset($response['updated']) && $response['updated']) {
            throw new BusinessException("数据保存错误！");
        }
    }

    /**
     * 批量删除数据
     * @param $where array|int|string 数组 where条件
     * @param $data  array 键值对数组
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/8/27 21:09
     */
    public function deleteEsByQuery($where)
    {
        if ( ! $where) {
            throw new ParamsException("条件不可为空！");
        }
        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'query' => [
                    'bool' => $this->generateEsQueryParams($where),
                ]
            ],
            //            'refresh' => config('es.refresh'),//同步
        ];
        $response = $this->esClient->deleteByQuery($params)->getBody();

        $response = json_decode((string)$response, true);

        if ( ! isset($response['deleted']) && $response['deleted']) {
            throw new BusinessException("数据保存错误！");
        }
    }

    /**
     * 生成es 键值对字段脚本
     * @param $fields
     * @return string
     * <AUTHOR>
     * @date   2024/9/5 16:34
     */
    private function generateElasticsearchSource($fields)
    {
        // 初始化source字符串
        $source = '';

        // 遍历传入的键值对数组，生成脚本语句
        foreach ($fields as $key => $value) {
            // 如果值是字符串，需要加上引号
            if (is_string($value)) {
                $value = '"'.$value.'"';
            }

            // 拼接字段更新的脚本
            $source .= 'ctx._source["'.$key.'"] = '.$value.'; ';
        }

        return $source;
    }

    /**
     * 返回没删除的es查询条件
     * @return array
     * <AUTHOR>
     * @date   2024/9/5 16:39
     */
    public function getNotDelParams()
    {
        return ['field_name' => 'is_delete', 'value' => BaseModel::DELETE_NOT, 'type' => 'term', 'operate_type' => 'equal'];
    }

    /**
     * 将text类型查询条件移动至最后一个
     * @param $params
     * @return array
     * <AUTHOR>
     * @date   2024/9/10 17:27
     */
    private function textFieldMoveEnd($params)
    {
        $result = [];
        $textFieldList = [];
        foreach ($params as $key => $v) {
            if (($v['type'] ?? '') == 'text') {
                $textFieldList[] = $v;
            } else {
                $result[] = $v;
            }
        }

        return array_merge($result, $textFieldList);
    }


    /**
     * 查询es数据
     * @param $params
     * @param $listRows
     * @param $page
     * @param $fieldList
     * @return Paginator|\think\paginator\driver\Bootstrap
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/9/5 16:40
     */
    public function esSearch($params, $listRows = 20, $page = 1, $fieldList = null, $order = [])
    {
        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);

        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'track_total_hits' => true, //获取精确总数
                //                '_source' => [
                //                    'parent_id',
                //                    'cnt_id',
                //                ],
                'query' => [
                    'bool' => $where
                ],
                'sort'  => array_merge([
                    'sort_order' => [
                        'order' => 'asc'
                    ]
                ], $order),
            ],
            'from'  => ($page - 1) * $listRows, // 起始位置
            'size'  => $listRows, // 每页数量

        ];

        if ($fieldList) {
            $params['body']['_source'] = $this->getFiledList($fieldList);
        }

        $response = $this->esClient->search($params)->getBody();

        $resp = json_decode((string)$response, true);
        $total = $resp['hits']['total']['value'];
        $data = $resp['hits']['hits'];
        $data = array_column($data, '_source');

        $this->mergeData($fieldList, $data);


        return Paginator::make($data, $listRows, $page, $total);
    }

    /**
     * 接收多组查询参数
     * @param $params
     * @param $listRows
     * @param $page
     * @param $fieldList
     * @return array|Paginator|\think\paginator\driver\Bootstrap
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/7 17:56
     */
    public function esSearchSummary($params, $listRows = 20, $page = 1, $fieldList = null, &$statistics = null, $groupByField = null, $projectId = null)
    {

        $params['public_field'][] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params['public_field']);

        $projectIdPar = [];
        if ($projectId) {
            $projectIdPar = [
                [
                    'field_name' => 'project_id',
                    'value'      => is_array($projectId) ? $projectId : [$projectId],
                    'type'       => 'selector',
                ]
            ];
        }

        if ($params['demand_field']) {
            $params['demand_field'][] = ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_DEMAND, 'type' => 'term', 'operate_type' => 'equal'];
            $whereDemand = $this->generateEsQueryParams(array_merge($params['demand_field'], $projectIdPar));
            //nested字段搜索
            $where['should'][] = [
                'bool' => $whereDemand
            ];
        }
        if ($params['task_field']) {
            $params['task_field'][] = ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_TASK, 'type' => 'term', 'operate_type' => 'equal'];
            $taskDemand = $this->generateEsQueryParams(array_merge($params['task_field'], $projectIdPar));
            //nested字段搜索
            $where['should'][] = [
                'bool' => $taskDemand
            ];
        }
        if ($params['flaw_field']) {
            $params['flaw_field'][] = ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_FLAW, 'type' => 'term', 'operate_type' => 'equal'];
            $flawDemand = $this->generateEsQueryParams(array_merge($params['flaw_field'], $projectIdPar));
            //nested字段搜索
            $where['should'][] = [
                'bool' => $flawDemand
            ];
        }
        if ($where['should']) {
            $where["minimum_should_match"] = 1;
        }

        $esQueryParams = [
            'index' => $this->esIndex,
            'body'  => [
                'track_total_hits' => true, //获取精确总数
                'query' => [
                    'bool' => $where
                ],
                'aggs'  => [
                    'cnt_type_count' => [ //数量
                        'terms' => [
                            'field' => 'cnt_type',
                        ],
                        'aggs'  => [
                            'not_end' => [
                                'filter' => [
                                    'term' => [
                                        'isEnd' => true, //已结束数量
                                    ]
                                ],
                            ]
                        ]
                    ],
                    'work_hour_sum'  => [ //工时
                        'filter' => [
                            'terms' => [
                                'cnt_type' => [WorkItemsModel::CNT_TYPE_TASK, WorkItemsModel::CNT_TYPE_FLAW] //只统计任务和缺陷的
                            ],
                        ],
                        'aggs'   => [
                            'estimated_work_hours' => [
                                'sum' => [
                                    'field' => 'estimated_work_hours', //预计
                                ],
                            ],
                            'actual_work_hours'    => [
                                'sum' => [
                                    'field' => 'actual_work_hours', //实际/完成
                                ],
                            ],
                            'remaining_work'       => [
                                'sum' => [
                                    'field' => 'remaining_work', //剩余
                                ],
                            ],
                        ]
                    ],
                ]
            ]
        ];

        if ($groupByField) {
            $esQueryParams['body']['size'] = 0; // 当分组时，不直接获取hits，而是通过聚合获取
            $esQueryParams['body']['aggs']['group_by_field_agg'] = [
                'terms' => [
                    'field'   => $groupByField,
                    'size'    => self::NOT_PAGE_MAX,
                    'missing' => '-1' // 为没有该字段的文档设置默认分组键
                ],
                'aggs'  => [
                    'docs_in_group' => [
                        'top_hits' => [
                            'size'    => self::MAX_INNER_RESULT_WINDOW, // 修改 top_hits size 以避免超出 index.max_inner_result_window 限制
                            '_source' => $this->getFiledList($fieldList ?? []),
                            // 可选：添加组内排序
                            'sort'    => [['create_at' => ['order' => 'desc']]]
                        ]
                    ]
                ]
            ];
        } else {
            $esQueryParams['body']['sort'] = [
                'cnt_type'  => ['order' => 'asc'],
                'create_at' => ['order' => 'desc'],
            ];
            $esQueryParams['from'] = ($page - 1) * $listRows;
            $esQueryParams['size'] = $listRows;
        }


        if ($fieldList && ! $groupByField) { // 分组时 _source 在 top_hits 中定义
            $esQueryParams['body']['_source'] = $this->getFiledList($fieldList);
        }

        $response = $this->esClient->search($esQueryParams)->getBody();
        $resp = json_decode((string)$response, true);

        // 填充全局统计信息 (这部分对于分组和非分组都适用)
        $cntTypeCount = array_column($resp['aggregations']['cnt_type_count']['buckets'] ?? [], null, 'key');
        $statistics = [
            'demand_count'         => $cntTypeCount[WorkItemsModel::CNT_TYPE_DEMAND]['doc_count'] ?? 0,
            'task_count'           => $cntTypeCount[WorkItemsModel::CNT_TYPE_TASK]['doc_count'] ?? 0,
            'flaw_count'           => $cntTypeCount[WorkItemsModel::CNT_TYPE_FLAW]['doc_count'] ?? 0,
            'demand_end_count'     => $cntTypeCount[WorkItemsModel::CNT_TYPE_DEMAND]['not_end']['doc_count'] ?? 0,
            'task_end_count'       => $cntTypeCount[WorkItemsModel::CNT_TYPE_TASK]['not_end']['doc_count'] ?? 0,
            'flaw_end_count'       => $cntTypeCount[WorkItemsModel::CNT_TYPE_FLAW]['not_end']['doc_count'] ?? 0,
            'estimated_work_hours' => (float)number_format($resp['aggregations']['work_hour_sum']['estimated_work_hours']['value'] ?? 0, 2, '.', ''),
            'actual_work_hours'    => (float)number_format($resp['aggregations']['work_hour_sum']['actual_work_hours']['value'] ?? 0, 2, '.', ''),
            'remaining_work'       => (float)number_format($resp['aggregations']['work_hour_sum']['remaining_work']['value'] ?? 0, 2, '.', ''),
        ];

        if ($groupByField) {
            $groupedData = [];
            if (isset($resp['aggregations']['group_by_field_agg']['buckets'])) {
                foreach ($resp['aggregations']['group_by_field_agg']['buckets'] as $bucket) {
                    $groupKey = $bucket['key'];
                    $itemsInGroup = array_column($bucket['docs_in_group']['hits']['hits'], '_source');
                    $this->mergeData($fieldList, $itemsInGroup);
                    $groupedData[] = [
                        'group_key' => $groupKey,
                        'items'     => $itemsInGroup,
                        'count'     => $bucket['doc_count']
                    ];
                }
            }
            return [
                '_is_grouped_result' => true,
                'groups'             => $groupedData,
            ];
        } else {
            $total = $resp['hits']['total']['value'];
            $data = $resp['hits']['hits'];
            $data = array_column($data, '_source');
            $this->mergeData($fieldList, $data);
            return Paginator::make($data, $listRows, $page, $total);
        }
    }

    /**
     * 接收多组查询参数按用户分组
     * @param $params
     * @param $listRows
     * @param $page
     * @param $fieldList
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/7 17:56
     */
    public function esSearchSummaryGroupUser($params, $fieldList = null)
    {

        $params['public_field'][] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params['public_field']);

        if ($params['demand_field'] ?? false) {
            $params['demand_field'][] = ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_DEMAND, 'type' => 'term', 'operate_type' => 'equal'];
            $whereDemand = $this->generateEsQueryParams($params['demand_field']);
            //nested字段搜索
            $where['should'][] = [
                'bool' => $whereDemand
            ];
        }
        if ($params['task_field'] ?? false) {
            $params['task_field'][] = ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_TASK, 'type' => 'term', 'operate_type' => 'equal'];
            $taskDemand = $this->generateEsQueryParams($params['task_field']);
            //nested字段搜索
            $where['should'][] = [
                'bool' => $taskDemand
            ];
        }
        if ($params['flaw_field'] ?? false) {
            $params['flaw_field'][] = ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_FLAW, 'type' => 'term', 'operate_type' => 'equal'];
            $flawDemand = $this->generateEsQueryParams($params['flaw_field']);
            //nested字段搜索
            $where['should'][] = [
                'bool' => $flawDemand
            ];
        }
        if ($where['should']) {
            $where["minimum_should_match"] = 1;
        }
        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'query' => [
                    'bool' => $where
                ],
                'sort'  => [
                    'cnt_type'  => [
                        'order' => 'asc'
                    ],
                    'create_at' => [
                        'order' => 'desc'
                    ]
                ],
                'aggs'  => [
                    'group_user' => [
                        'terms' => [
                            'field'   => 'handler_uid',
                            'missing' => -1,
                        ],
                        'aggs'  => [
                            'cnt_type_count' => [ //数量
                                'terms' => [
                                    'field' => 'cnt_type'
                                ],
                                'aggs'  => [
                                    'not_end' => [
                                        'filter' => [
                                            'term' => [
                                                'isEnd' => true, //已结束数量
                                            ]
                                        ],
                                    ]
                                ]
                            ],
                            'work_hour_sum'  => [ //工时
                                'filter' => [
                                    'terms' => [
                                        'cnt_type' => [WorkItemsModel::CNT_TYPE_TASK, WorkItemsModel::CNT_TYPE_FLAW] //只统计任务和缺陷的
                                    ],
                                ],
                                'aggs'   => [
                                    'estimated_work_hours' => [
                                        'sum' => [
                                            'field' => 'estimated_work_hours', //预计
                                        ],
                                    ],
                                    'actual_work_hours'    => [
                                        'sum' => [
                                            'field' => 'actual_work_hours', //实际/完成
                                        ],
                                    ],
                                    'remaining_work'       => [
                                        'sum' => [
                                            'field' => 'remaining_work', //剩余
                                        ],
                                    ],
                                ]
                            ],
                        ]
                    ]

                ]

            ],
            'size'  => self::NOT_PAGE_MAX, // 每页数量
        ];

        if ($fieldList) {
            $params['body']['_source'] = $this->getFiledList(array_merge(['handler_uid'], $fieldList));
        }

        $response = $this->esClient->search($params)->getBody();

        $resp = json_decode((string)$response, true);
        //        dd($resp);

        $total = $resp['hits']['total']['value'];
        $data = $resp['hits']['hits'];
        $data = array_column($data, '_source');

        $this->mergeData($fieldList, $data);

        $userAggs = array_column($resp['aggregations']['group_user']['buckets'], null, 'key');


        return ['data' => $data, 'statistics' => $userAggs];
    }


    /**
     * 树形数据查询或扁平/分组列表查询。
     * @param  array          $params           查询参数数组
     * @param  bool           $isTreeOutput     是否返回树形结构，false则返回扁平或分组列表，默认为 true
     * @param  bool           $showParents      当 $isTreeOutput 为 true 时，是否显示父级节点，默认为 true
     * @param  bool           $showChildren     当 $isTreeOutput 为 true 时，是否显示子级节点，默认为 true
     * @param  mixed|null     $fieldList        需要从Elasticsearch检索的字段列表，null表示默认
     * @param  array|null     $statistics       用于接收统计数据的引用数组（由Elasticsearch聚合计算）
     * @param  array          $mainDataSort     主数据排序参数数组
     * @param  string|null    $mainDataGroupBy  用于对主数据进行分组的字段名，null表示不分组
     * @param  array          $childrenOrder    当 $isTreeOutput 为 true 时，子节点排序参数数组
     * @param  callable|null  $itemProcessor    可选的回调函数，用于处理每个获取到的数据项
     * @return array 返回构建好的树形结构数组或处理后的列表/分组列表
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR> (AI 协助重构)
     * @date   2024/12/19 (增加 isTreeOutput 参数于 2025/05/20)
     */
    public function esSearchSummaryForTree(
        array $params,
        bool $isTreeOutput = true, // 新参数，控制输出结构
        bool $showParents = true,
        bool $showChildren = true,
        mixed $fieldList = null,
        array &$statistics = null,
        array $mainDataSort = [],
        ?string $mainDataGroupBy = null,
        array $childrenOrder = [],
        ?callable $itemProcessor = null
    ): array {
        // 初始化 DataContainer
        $dataContainer = new DataContainer([
            'original_params' => $params,
            'isTreeOutput'    => $isTreeOutput,
            'showParents'     => $showParents,
            'showChildren'    => $showChildren,
            'mainDataSort'    => $mainDataSort,
            'mainDataGroupBy' => $mainDataGroupBy,
            'childrenOrder'   => $childrenOrder,
            'itemProcessor'   => $itemProcessor,
            // 原始查询参数 $params 会在 DataContainer 构造函数中设置
        ]);
        // $dataContainer->params = $params; // 构造函数已处理，此行多余
        $dataContainer->processedFieldList = $fieldList ? $this->getFiledList($fieldList) : [];
        $dataContainer->projectId = $this->_extractProjectIdFromParams($params);
        // $statistics 是引用传递，DataFetchingProcessor 会直接修改传入的 $statistics 变量
        // 因此，我们将 DataContainer 中的 statistics 属性也设为引用
        $dataContainer->statistics = &$statistics;


        // 定义流水线步骤
        $pipelineSteps = [
            new QueryBuildingProcessor(),
            new DataFetchingProcessor(),
            // FlatListDataProcessingProcessor 会处理所有从ES获取的 flatListData
            // GroupedDataProcessingProcessor (如果需要) 会基于已处理的 flatListData 或原始分组数据进行操作
        ];

        // 根据条件添加处理器
        // 无论是否分组，都先处理扁平数据，因为分组数据也可能需要扁平数据中的信息或处理方式
        $pipelineSteps[] = new FlatListDataProcessingProcessor();

        if ($dataContainer->mainDataGroupBy) {
            $pipelineSteps[] = new GroupedDataProcessingProcessor();
        }

        if ($dataContainer->isTreeOutput) {
            $pipelineSteps[] = new TreeNodesFetchingProcessor();
            $pipelineSteps[] = new NodesEnrichmentProcessor();
            $pipelineSteps[] = new TreeBuildingProcessor();
        }
        // 如果不是树形输出，FlatListDataProcessingProcessor 或 GroupedDataProcessingProcessor
        // 应该已经将结果存入 $dataContainer->finalResult

        // 执行流水线
        foreach ($pipelineSteps as $step) {
            $dataContainer = $step->process($dataContainer);
        }

        // 确保 $statistics 变量被正确更新 (如果 DataContainer 中的引用没有直接作用)
        // 通常情况下，如果 DataContainer->statistics 是对 $statistics 的引用，这里不需要再次赋值
        // 但为保险起见，可以取消下面这行注释，或者确保 DataFetchingProcessor 正确地通过引用更新了 $statistics
        // $statistics = $dataContainer->statistics;

        return $dataContainer->finalResult ?? []; // 返回最终结果，如果未设置则返回空数组
    }

    /**
     * 从参数中提取项目ID。
     *
     * @param  array  $params  参数数组
     * @return string|int|null|array 项目ID，如果未找到则返回null
     */
    private function _extractProjectIdFromParams(array $params): int|array|string|null
    {
        $projectId = null;
        if (isset($params['public_field']) && is_array($params['public_field'])) {
            foreach ($params['public_field'] as $field) {
                if (isset($field['field_name']) && $field['field_name'] === 'project_id' && isset($field['value'])) {
                    $projectId = $field['value'];
                    break;
                }
            }
        }
        return $projectId;
    }


    /**
     * 工作项个数以及已结束个数
     * @param $params
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/13 11:31
     */
    public function countGroupCntType($params)
    {
        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);

        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'query' => [
                    'bool' => $where
                ],
                'aggs'  => [
                    'cnt_type_count' => [ //数量
                        'terms' => [
                            'field' => 'cnt_type'
                        ],
                        'aggs'  => [
                            'not_end' => [
                                'filter' => [
                                    'term' => [
                                        'isEnd' => true, //已结束数量
                                    ]
                                ],
                            ]
                        ]
                    ],
                ]
            ],
            'size'  => 0, // 每页数量
        ];

        $response = $this->esClient->search($params)->getBody();
        $resp = json_decode((string)$response, true);

        $cntTypeCount = array_column($resp['aggregations']['cnt_type_count']['buckets'], null, 'key');


        return $cntTypeCount;
    }

    /**
     * 工作项燃尽图
     * @param $params
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/13 11:31
     */
    public function workItemBurndownChart($params, $endTime)
    {
        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);

        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'query' => [
                    'bool' => $where
                ],
                'aggs'  => [
                    'date_counts' => [ //数量
                        'terms' => [
                            'script' => [ //分组思路，返回每条数据的开始时间-结束时间之间的所有日期集合，结束时间如果不存在则取传入的时间
                                'source' => "
                                    def result = [];

                                        // 获取毫秒级时间戳
                                        long timestamp = doc['create_at'].value.toInstant().toEpochMilli();
                                        //将时间戳转换为日期对象（UTC时区） 去除时分秒，转换为当天的开始时间（00:00:00）
                                        ZonedDateTime startOfDay = Instant.ofEpochMilli(timestamp).atZone(ZoneId.of('UTC')).toLocalDate().atStartOfDay(ZoneId.of('UTC'));
                                        // 返回新的时间戳（毫秒级）
                                        def startDate= startOfDay.toInstant().toEpochMilli();


                                        // 格式化字符串：'2024-11-15'
                                        def dateFormat = new java.text.SimpleDateFormat('yyyy-MM-dd HH:mm:ss');
                                        def defaultEndDate =  Instant.ofEpochMilli(dateFormat.parse('{$endTime}').getTime()).atZone(ZoneId.of('UTC')).toLocalDate().atStartOfDay(ZoneId.of('UTC')).toInstant().toEpochMilli();

                                        // 获取 finish_time (工作项结束当天就不计入工作燃尽图数据中,所以减一天)的日期，若没有则使用默认日期
                                        def endDate = (doc.containsKey('finish_time') && doc['finish_time'].size() > 0) ? (doc['finish_time'].value.toInstant().toEpochMilli()  - 86400000) : defaultEndDate;

                                        // 从开始日期到结束日期循环，生成日期数组
                                        def currentDate = startDate;
                                        while (currentDate <= endDate) {
                                            ZonedDateTime dateTime = Instant.ofEpochMilli(currentDate).atZone(ZoneId.of('UTC'));

                                            result.add(dateTime.format(DateTimeFormatter.ofPattern('yyyy-MM-dd')));
                                            currentDate += 86400000;  // 每次增加 1 天（86400000 毫秒）
                                        }

                                        return result;
                                        ",
                                'lang'   => 'painless'
                            ],
                            'size'   => self::NOT_PAGE_MAX, // 每页数量
                        ],
                        'aggs'  => [
                            'cnt_type_count' => [ //数量
                                'terms' => [
                                    'field' => 'cnt_type',
                                ]
                            ],
                        ]
                    ],
                ]
            ],
            'size'  => 0, // 每页数量
        ];

        $response = $this->esClient->search($params)->getBody();
        $resp = json_decode((string)$response, true);
        //        dd($resp);

        //        dd(array_column(array_column($resp['hits']['hits'],'_source'),'create_at'));
        //        dd(array_column(array_column($resp['hits']['hits'],'_source'),'finish_time'));
        $date_counts = array_column($resp['aggregations']['date_counts']['buckets'], null, 'key');

        //        dd($date_counts);

        return $date_counts;
    }

    /**
     * 工时燃尽图
     * @param $params
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/13 11:31
     */
    public function laborHoursBurndownChart($params)
    {
        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);

        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'query' => [
                    'bool' => $where
                ],
                'aggs'  => [
                    'cnt_id_list' => [ //数量
                        'terms' => [
                            'field' => 'cnt_id',
                            'size'  => self::NOT_PAGE_MAX, // 每页数量
                        ]
                    ],
                ]
            ],
            'size'  => 0, // 每页数量
        ];

        $response = $this->esClient->search($params)->getBody();
        $resp = json_decode((string)$response, true);
        //        dd($resp);

        //        dd(array_column(array_column($resp['hits']['hits'],'_source'),'create_at'));
        //        dd(array_column(array_column($resp['hits']['hits'],'_source'),'finish_time'));
        $cntIdList = array_column($resp['aggregations']['cnt_id_list']['buckets'], 'key');

        return $cntIdList;
    }


    /**
     * 工作项燃尽图
     * @param $params
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/13 11:31
     */
    public function manHourBurnReport($params)
    {
        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);

        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'query'   => [
                    'bool' => $where
                ],
                'aggs'    => [
                    'cnt_id_list' => [ //数量
                        'terms' => [
                            'field' => 'cnt_id',
                            'size'  => self::NOT_PAGE_MAX, // 每页数量
                        ]
                    ],
                ],
                '_source' => [],
            ],
            'size'  => 0, // 每页数量
        ];

        $response = $this->esClient->search($params)->getBody();
        $resp = json_decode((string)$response, true);
        //        dd($resp);

        //        dd(array_column(array_column($resp['hits']['hits'],'_source'),'create_at'));
        //        dd(array_column(array_column($resp['hits']['hits'],'_source'),'finish_time'));
        $cntIdList = array_column($resp['aggregations']['cnt_id_list']['buckets'], 'key');

        return $cntIdList;
    }


    /**
     * 按照标题去重并保留最后一个
     * @param $params
     * @return Collection
     * @throws ClientResponseException
     * @throws ServerResponseException
     */
    public function theTitleIsDeduplicatedAndTheLastOneIsKept($params)
    {
        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);

        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'query' => [
                    'bool' => $where
                ],
                'aggs'  => [
                    'unique_field' => [ //数量
                        'terms' => [
                            'field' => 'title',
                            'size'  => self::NOT_PAGE_MAX, // 每页数量
                        ],
                        'aggs'  => [
                            'latest_doc' => [
                                'top_hits' => [
                                    'size' => 1,
                                    'sort' => [
                                        'cnt_id' => [
                                            'order' => 'desc'
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                ],
            ],
            'size'  => 0, // 每页数量
        ];


        $response = $this->esClient->search($params)->getBody();

        $resp = json_decode((string)$response, true);

        $esResult = array_column($resp['aggregations']['unique_field']['buckets'], 'latest_doc');
        $result = [];
        foreach ($esResult as $doc) {
            $result[] = $doc['hits']['hits'][0]['_source'] ?? null;
        }
        return new Collection(array_filter($result));
    }


    /**
     * 合并不存在es中的数据列
     * @param  mixed  $fieldList
     * @param  array  $data
     * @return void
     * <AUTHOR>
     * @date   2024/11/15 14:40
     */
    public function mergeData(mixed $fieldList, array &$data)
    {
        if (empty($data)) {
            return;
        }
        $currentFieldList = $fieldList ?: [];
        if (!$currentFieldList || (is_array($fieldList) && in_array('contents', $currentFieldList))) {
            $this->mergeContents($data);
        }


        if (!$currentFieldList || (is_array($currentFieldList) && in_array('iteration_process_node_name', $currentFieldList))) {
            $this->mergeNodeName($data);
        }

        if (!$currentFieldList || (is_array($currentFieldList) && in_array('parent_id', $currentFieldList))) { // 假设 parent_id 意味着需要 parent_title 等。
            $this->mergeParnetName($data);
        }
    }

    /**
     * 工时资源报告
     * @param $params
     * @param $fieldList
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/12/5 下午8:52
     */
    public function workResourceReports($params, $fieldList = null)
    {

        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);


        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'query' => [
                    'bool' => $where
                ],
                'sort'  => [
                    'cnt_type'  => [
                        'order' => 'asc'
                    ],
                    'create_at' => [
                        'order' => 'desc'
                    ]
                ],
                'aggs'  => [
                    'group_user' => [
                        'terms' => [
                            'field'   => 'handler_uid',
                            'missing' => -1,
                            'size'    => self::NOT_PAGE_MAX, // 每页数量
                        ],
                        'aggs'  => [
                            'all_work_hour'     => [ //总预估工时
                                'sum' => [
                                    'field' => 'estimated_work_hours'
                                ],
                            ],
                            'cnt_id_list'       => [
                                'terms' => [
                                    'field' => 'cnt_id',
                                    'size'  => self::NOT_PAGE_MAX, // 每页数量

                                ],
                            ],
                            'not_end_work_hour' => [ //总未结束预估工时
                                'filter' => [
                                    'term' => [
                                        'isEnd' => false
                                    ],
                                ],
                                'aggs'   => [
                                    'all_work_hour' => [
                                        'sum' => [
                                            'field' => 'estimated_work_hours'
                                        ],
                                    ],
                                    'cnt_id_list'   => [
                                        'terms' => [
                                            'field' => 'cnt_id',
                                            'size'  => self::NOT_PAGE_MAX, // 每页数量

                                        ],
                                    ],
                                ]
                            ],
                        ]
                    ]

                ]

            ],
            'size'  => self::NOT_PAGE_MAX, // 每页数量
        ];

        if ($fieldList) {
            $params['body']['_source'] = $this->getFiledList(array_merge(['handler_uid'], $fieldList));
        }

        $response = $this->esClient->search($params)->getBody();

        $resp = json_decode((string)$response, true);
        //        dd($resp);
        $data = $resp['hits']['hits'];
        $data = array_column($data, '_source');

        $this->mergeData($fieldList, $data);

        $userAggs = array_column($resp['aggregations']['group_user']['buckets'], null, 'key');
        return ['data' => $data, 'statistics' => $userAggs];
    }


    /**
     * 获取迭代平均进度
     * @param $params
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/12/16 上午10:13
     */
    public function completeProgress($params)
    {
        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);

        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'query' => [
                    'bool' => $where
                ],
                'aggs'  => [
                    'is_end_count' => [ //数量
                        'terms' => [
                            'field' => 'isEnd',
                            'size'  => self::NOT_PAGE_MAX, // 每页数量
                        ]
                    ],
                ]
            ],
            'size'  => 0, // 每页数量
        ];

        $response = $this->esClient->search($params)->getBody();
        $resp = json_decode((string)$response, true);
        return array_column($resp['aggregations']['is_end_count']['buckets'], 'doc_count', 'key');
    }


    /**
     * 生成es查询参数
     * @param $params array 查询条件数组
     * @return array|array[]
     * <AUTHOR>
     * @date   2024/8/28 11:19
     */
    public function generateEsQueryParams($params)
    {
        (new WorkItemsLogic)->resolveStatusEnumId($params);
        (new WorkItemsLogic)->resolveNull($params);
        //        dd($params);
        //        dd($this->parseConditions($params));
        return $this->parseConditions($params);
    }


    /**
     * 递归解析查询条件
     * @param  array  $conditions
     * @return array
     */
    private function parseConditions($conditions)
    {
        $where = [
            'filter'   => [],
            'must_not' => [],
            'should'   => []
        ];

        foreach ($conditions as $condition) {
            // 处理单个条件
            if (isset($condition['field_name'])) {
                $this->handleSingleCondition($condition, $where);
                continue;
            }

            // 处理分组条件
            if (is_array($condition)) {
                $groupType = key($condition);
                if (
                    ! in_array($groupType, ['and', 'or', 'not'])
                ) {
                    continue;
                }

                $groupQuery = ['bool' => []];
                $subWhere = $this->parseConditions($condition[$groupType]);

                switch ($groupType) {
                case 'or':
                    $groupQuery['bool']['should'] = array_merge(
                        $subWhere['filter'],
                        array_map(function ($item) {
                            return ['bool' => ['must_not' => [$item]]];
                        }, $subWhere['must_not'])
                    );
                    $groupQuery['bool']['minimum_should_match'] = 1;
                    $where['filter'][] = $groupQuery;
                    break;

                case 'and':
                    $groupQuery['bool']['must'] = $subWhere['filter'];
                    $groupQuery['bool']['must_not'] = $subWhere['must_not'];
                    $where['filter'][] = $groupQuery;
                    break;

                case 'not':
                    $groupQuery['bool']['must'] = $subWhere['must_not'];
                    $groupQuery['bool']['must_not'] = $subWhere['filter'];
                    $where['filter'][] = $groupQuery;
                    break;
                }
            }
        }

        return $where;
    }


    /**
     * 处理单个查询条件
     * @param  array  $value
     * @param  array &$where
     */
    private function handleSingleCondition($value, &$where)
    {
        if ( ! is_array($value) || ! isset($value['field_name'])) {
            return;
        }

        $key = $value['field_name'];

        if (isset($value['exists'])) {
            if ($value['exists']) {
                $where['filter'][] = ['exists' => ['field' => $key]];
            } else {
                $where['must_not'][] = ['exists' => ['field' => $key]];
            }
            return;
        }

        if ( ! isset($value['type'])) {
            return;
        }

        switch ($value['type']) {
        case 'text':
            $where['filter'][] = ['wildcard' => [$key => "*{$value['value']}*"]];
            break;

        case 'term':
            $this->handleTermCondition($value, $key, $where);
            break;

        case 'selector':
            $where['filter'][] = ['terms' => [$key => array_values($value['value'])]];
            break;

        case 'date':
            $this->handleDateCondition($value, $key, $where);
            break;
        }
    }

    /**
     * 处理term类型的查询条件
     * @param  array   $value
     * @param  string  $key
     * @param  array & $where
     */
    private function handleTermCondition($value, $key, &$where)
    {
        switch ($value['operate_type']) {
        case 'equal':
            $where['filter'][] = ['term' => [$key => $value['value']]];
            break;
        case 'not_equal':
            $where['must_not'][] = ['term' => [$key => $value['value']]];
            break;
        case 'between':
            $where['filter'][] = [
                'range' => [
                    $key => [
                        "gte" => $value['value'][0],
                        "lte" => $value['value'][1],
                    ]
                ]
            ];
            break;
        default:
            throw new ParamsException("未知的operate_type：".$value['operate_type']);
        }
    }

    /**
     * 处理date类型的查询条件
     * @param  array   $value
     * @param  string  $key
     * @param  array & $where
     */
    private function handleDateCondition($value, $key, &$where)
    {
        $result = [];
        if ($value['value'][0]) {
            $result['gte'] = $value['value'][0];
        }
        if ($value['value'][1]) {
            $result['lte'] = $value['value'][1];
        }

        if ( ! empty($result)) {
            $where['filter'][] = ['range' => [$key => $result]];
        }
    }

    /**
     * 根据查询参数获取匹配工作项的所有父级ID
     * @param  array  $params  查询参数
     * @return array 合并去重后的所有父级ID数组
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR> AI
     * @date   2024/7/24
     */
    public function getParentsByParams(array $params)
    {
        // 验证参数
        if (empty($params)) {
            return [];
        }

        // 构建查询条件
        $searchParams = [
            'index' => $this->esIndex,
            'body'  => [
                'query'   => [
                    'bool' => [
                    ]
                ],
                '_source' => ['parents']
            ],
            'size'  => self::NOT_PAGE_MAX
        ];

        // 构建查询条件
        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);
        $searchParams['body']['query']['bool'] = $where;

        // 执行查询
        $response = $this->esClient->search($searchParams)->getBody();
        $resp = json_decode((string)$response, true);
        // 提取并合并所有 parents 字段
        $allParents = [];
        if ( ! empty($resp['hits']['hits'])) {
            foreach ($resp['hits']['hits'] as $item) {
                if ( ! empty($item['_source']['parents'])) {
                    $allParents = array_merge($allParents, $item['_source']['parents']);
                }
            }
        }

        // 去重并返回
        return array_values(array_unique($allParents));
    }

    /**
     * 判断分组键是否应该被视为默认分组（无分组）
     * @param mixed $groupKey
     * @return bool
     */
    private function isDefaultGroup($groupKey): bool
    {
        return $groupKey === self::DEFAULT_ES_GROUP_MISSING_KEY ||
               $groupKey === '_default_group_' ||
               $groupKey === '0' ||
               $groupKey === 0;
    }
}
