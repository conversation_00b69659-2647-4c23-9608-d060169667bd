<?php
declare (strict_types=1);

namespace app\work_items\logic;

use app\infrastructure\logic\EnumLogic;
use app\iterate\model\IterationModel;
use app\work_items\model\WorkHoursModel;
use app\work_items\model\WorkItemsModel;
use app\work_items\validate\WorkHoursValidate;
use basic\BaseModel;
use exception\NotFoundException;
use basic\BaseLogic;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Db;
use think\Model;
use think\Paginator;
use Throwable;
use utils\Ctx;

class WorkHoursLogic extends BaseLogic
{

    /**
     * 判断工时操作权限
     * @param $type   int 工时类型
     * @param $cnt_id int 工作项id
     * @return bool
     * @throws Exception
     */
    private function permissions($type, $cnt_id)
    {
        $cnt = WorkItemsModel::findById($cnt_id)->extends;
        if ( ! $cnt) {
            throw new NotFoundException();
        }
        $userIdList = [];
        switch (true) {
        case $type == WorkHoursModel::TYPE_ESTIMATED:
        case $type == WorkHoursModel::TYPE_REMAINING:
            $userIdList = $cnt['handler_uid'] ?? [];

            if ($cnt['iteration_id'] ?? false) {
                $iteration = json_decode(IterationModel::findById($cnt['iteration_id'])->extends ?? '{}', true);

                $userIdList = array_merge($userIdList, $iteration['iteration_leader'] ?? []);
            }
            break;
        case $type == WorkHoursModel::TYPE_ACTUAL:
            $userIdList = $cnt['handler_uid'] ?? [];
            break;
        default:
            throw new Exception();
        }
        //为空代表无leader无处理人，都可以填
        return in_array(Ctx::$userId, $userIdList);
    }


    /**
     * 新增
     * @param        $params
     * @param  int   $isCreateWorkItem  是否是创建工作项时同时创建工时
     * @param  bool  $autoChangeStatus  是否自动变更状态
     * @return WorkHoursModel
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function create($params, $isCreateWorkItem = 0, $autoChangeStatus = false)
    {
        validate(WorkHoursValidate::class)->scene('create')->check($params);
        $itemModel = WorkItemsModel::findById($params['cnt_id']);
        if ( ! $itemModel) {
            throw new NotFoundException();
        }
//        if ($itemModel->isDemand()) {
//            throw new ParamsException("需求无法创建工时");
//        }
        if ( ! $isCreateWorkItem) {
            if ( ! $this->permissions($params['type'], $params['cnt_id'])) {
                throw new Exception("无工时操作权限！");
            }
        }
        Db::startTrans();
        try {
            $model = new WorkHoursModel();
            if ($params['type'] == WorkHoursModel::TYPE_ESTIMATED) {
                WorkHoursModel::overwriteOldData($params['cnt_id'], $params['type']);
            } elseif ($params['type'] == WorkHoursModel::TYPE_ACTUAL) {
                WorkHoursModel::overwriteOldData($params['cnt_id'], $params['type'], $params['work_date']);
            }

            $model->save($params);
            $this->countHours($model->cnt_id, $autoChangeStatus);

            Db::commit();

            return $model->toDetail();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }

    }


    /**
     * 删除
     * @param        $workHoursId
     * @param  bool  $autoChangeStatus  是否自动变更状态
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/8/28 17:25
     */
    public function delete($workHoursId, $autoChangeStatus = false)
    {

        Db::startTrans();

        try {
            $model = WorkHoursModel::findById($workHoursId);
            if ( ! $model) {
                throw new NotFoundException();
            }
//            if ( ! $this->permissions($model->type, $model->cnt_id)) {
//                throw new Exception("无工时操作权限！");
//            }
            $model->is_delete = BaseModel::DELETE_YES;
            $model->save();
            $this->countHours($model->cnt_id, $autoChangeStatus);
            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }

    }

    /**
     * 修改
     * @param        $params
     * @param  bool  $autoChangeStatus  是否自动变更状态
     * @return WorkHoursModel|array|mixed|Model
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function update($params, $autoChangeStatus = false)
    {
        Db::startTrans();
        try {
            validate(WorkHoursValidate::class)->scene('update')->check($params);
            $model = WorkHoursModel::findById($params['work_hours_id']);
            if ( ! $model) {
                throw new NotFoundException();
            }
            if ( ! $this->permissions($model->type, $model->cnt_id)) {
                throw new Exception("无工时操作权限！");
            }
            $model->save($params);
            $this->countHours($model->cnt_id, $autoChangeStatus);

            Db::commit();

            return $model->toDetail();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }


    /**
     * 详情
     * @param $id
     * @return WorkHoursModel
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function detail($id)
    {
        $model = WorkHoursModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }

        return $model->toDetail();
    }

    /**
     * 通过工作项获取最新的指定类型的工时
     * @param $params
     * @return WorkHoursModel|array
     * <AUTHOR>
     * @date   2024/8/29 15:10
     */
    public function detailByCntId($params)
    {
        if ($params['type'] == WorkHoursModel::TYPE_REMAINING) {
            return null;
        }
        $where = [
            ['cnt_id', '=', $params['cnt_id']],
            ['type', '=', $params['type']],
            ['is_delete', '=', BaseModel::DELETE_NOT],
        ];
        if ($params['type'] == WorkHoursModel::TYPE_ACTUAL) {
            $where[] = ['work_date', '=', WorkHoursModel::formatWorkDateToInt($params['work_date'] ?? date("Ymd"))];
            $where[] = ['create_by', '=', Ctx::$user->userId];
        }
        $model = WorkHoursModel::where($where)
            ->order('work_hours_id desc')
            ->find();

        return $model ? $model->toDetail() : null;
    }

    /**
     * 分页查询
     * @param $params
     * @return WorkHoursModel[]|array|Collection|Paginator
     * @throws DbException
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function pageQuery($params)
    {

        $where = [
            ['cnt_id', '=', $params['cnt_id']]
        ];

        return (new WorkHoursModel)
            ->where(['is_delete' => BaseModel::DELETE_NOT])
            ->where($where)
            ->field(WorkHoursModel::LIST_FIELDS)->order('create_at desc')
            ->select();
    }


    /**
     * 统计工时
     * @param  string|int  $cntId             工作项ID
     * @param  bool        $autoChangeStatus  是否自动变更状态
     * @return void
     */
    public function countHours($cntId, $autoChangeStatus = false)
    {
        $hours = WorkHoursModel::findListByCntId($cntId);
        $estimated = $hours->where('type', '=', WorkHoursModel::TYPE_ESTIMATED)->first();
        $actual = $hours->where('type', '=', WorkHoursModel::TYPE_ACTUAL)->toArray();
        $remaining = $hours->where('type', '=', WorkHoursModel::TYPE_REMAINING)->toArray();

        $estimated_work_hours = $estimated['working_hours'] ?? 0;
        $actual_work_hours = array_sum(array_column($actual, 'working_hours'));
        $remaining_work = array_sum(array_column($remaining, 'working_hours'));


        $remaining_work = $estimated_work_hours - $actual_work_hours + $remaining_work;
        if ($remaining_work < 0) {
            $remaining_work = 0;
        }

        $exceeding_working_hours = $actual_work_hours + $remaining_work - $estimated_work_hours;

        if ($estimated_work_hours + $exceeding_working_hours) {
            $speed_of_progress = (int)bcmul(bcdiv((string)$actual_work_hours, (string)($estimated_work_hours + $exceeding_working_hours), 2), '100');
//            $speed_of_progress = (int)($actual_work_hours / ($estimated_work_hours + $exceeding_working_hours)) * 100;
        } else {
            $speed_of_progress = 0;
        }


//实际工时：每日填写的工时相加=实际工时
//剩余工时：预估工时—实际工时+手动添加工时=剩余工时（当剩余工时为负数时，展示0）
//超出工时：实际工时+剩余工时—预估工时=超出工时
//进度：实际时间➗（预估工时+超出工时）=进度

        $data = [
            'estimated_work_hours'    => $estimated_work_hours,//预估工时
            'actual_work_hours'       => $actual_work_hours,//实际工时
            'remaining_work'          => $remaining_work,//剩余工时
            'exceeding_working_hours' => $exceeding_working_hours,//超出工时
            'speed_of_progress'       => $speed_of_progress,//进度
        ];

        //格式化为带有小数点的浮点数
//        foreach ($data as &$item) {
//            $item = (float)number_format($item, 2);
//        }
//        unset($item);
//        dd($data);

        (new WorkItemsLogic)->updateWorkHours($cntId, $data);


        $model = WorkItemsModel::findById($cntId);
        if ($model && $model->isTask()) {
            // 1. 剩余工时为0时且状态为进行中时，自动将任务状态变更为已完成
            if ( ! $remaining_work && $autoChangeStatus) {
                // 获取已完成状态和进行中状态的枚举ID
                $completedStatusId = EnumLogic::findEnumValue('completed');
                if ($completedStatusId) {
                    // 更新状态为已完成
                    $model->save([
                        'status_enum_id' => $completedStatusId,
                        'isEnd'          => true
                    ]);
                }
            } // 2. 实际工时从0变更时且任务状态为"未开始"，状态自动变更为"进行中"
            elseif ($actual_work_hours > 0) {
                $notStartedStatusId = EnumLogic::findEnumValue('notStarted');
                $inProgressStatusId = EnumLogic::findEnumValue('inProgress');
                // 只有当任务状态为"未开始"时才变更为"进行中"
                if ($inProgressStatusId && $notStartedStatusId && $model->extends['status_enum_id'] == $notStartedStatusId) {
                    $model->save([
                        'status_enum_id' => $inProgressStatusId,
                        'isEnd'          => false
                    ]);
                }
            }
        }
    }


}
