<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/9/29 14:46
 */

namespace app\work_items\logic;

use app\work_items\model\TestCaseModel;
use app\work_items\model\TestPlanModel;
use app\work_items\model\TestPlanWorkCaseModel;
use app\work_items\model\WorkItemsModel;
use basic\BaseLogic;
use exception\ParamsException;
use Throwable;
use utils\DBTransaction;

class TestPlanWorkCaseLogic extends BaseLogic
{

    /**
     * 计划<->需求<->用例
     * @param $planId
     * @param $caseId
     * @param $cntId
     * @return void
     * @throws Throwable
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/10/16 11:14
     */
    public function relevancy($planId, $caseId, $cntId)
    {

        $params = [
            'test_plan_id' => $planId,
            'cnt_id' => $cntId,
            'test_case_id' => $caseId,
        ];
        $notArrParams = [];
        $newKeyList = $this->separateArray($params, $notArrParams);
        $oldData = TestPlanWorkCaseModel::where($notArrParams)->field([
            'test_plan_id',
            'cnt_id',
            'test_case_id',
        ])->select();

        $oldKeyList = [];
        $oldData->each(function ($item) use (&$oldKeyList) {
            $oldKeyList["{$item['test_plan_id']}-{$item['cnt_id']}-{$item['test_case_id']}"] = $item->toArray();
        })->toArray();

        $removeList = array_diff_key($oldKeyList, $newKeyList);
        $newList = array_diff_key($newKeyList, $oldKeyList);

        try {
            DBTransaction::begin();

            //删除
            $removeList && $this->del(
                array_column($removeList, 'test_plan_id'),
                array_column($removeList, 'test_case_id'),
                array_column($removeList, 'cnt_id'),
            );

            //新增
            $newList && (new TestPlanWorkCaseModel())->saveAll($newList);


            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }
    }

    public function separateArray($params, &$notArrParams)
    {
        $isArrParams = array_filter($params, 'is_array');
        $notArrParams = array_filter($params, function ($item) {
            return !is_array($item);
        });

        if (count($isArrParams) > 1) {
            throw new ParamsException("参数中只可以有一个数组");
        }
        $newKeyList = [];
        if (count($isArrParams) == 0) {
            $newKeyList["{$params['test_plan_id']}-{$params['cnt_id']}-{$params['test_case_id']}"] = $params;
        } else {
            $firstKey = array_key_first($isArrParams);
            foreach ($isArrParams[$firstKey] as $param) {
                $data = array_merge([$firstKey => $param], $notArrParams);
                $newKeyList["{$data['test_plan_id']}-{$data['cnt_id']}-{$data['test_case_id']}"] = $data;
            }
        }

        return $newKeyList;
    }

//    public function mergeArray($params)
//    {
//        $data = [];
//        $keys = array_keys($params);
//        foreach ($keys as $key) {
//            $columns = array_unique(array_column($params, $key));
//            dd($columns);
//            if (count($columns) == 1) {
//                $columns = $columns[0];
//            }
//            $data[$key] = $columns;
//        }
//
//        return $data;
//    }

    /**
     * 删除关系
     * @param $planId
     * @param $caseId
     * @param $cntId
     * @return void
     * @throws Throwable
     * <AUTHOR>
     * @date 2024/10/16 11:13
     */
    public function del($planId, $caseId, $cntId)
    {
        $where = [];
        if (!is_null($planId)) {
            $where['test_plan_id'] = $planId;
        }
        if (!is_null($caseId)) {
            $where['test_case_id'] = $caseId;
        }

        if (!is_null($cntId)) {
            $where['cnt_id'] = $cntId;
        }

        if (!$where) {
            throw new ParamsException();
        }

        try {
            DBTransaction::begin();

            WorkItemsModel::$autoSave = false;
            TestPlanModel::$autoSave = false;
            TestCaseModel::$autoSave = false;
            TestPlanLogic::$isTriggerStatistics = false;


            TestPlanWorkCaseModel::where($where)->select()->each(function (TestPlanWorkCaseModel $item) {
                $item->delete();
                if ($item->test_case_id == 0) {
                    $this->del($item->test_plan_id, null, $item->cnt_id);
                }
            });

            WorkItemsModel::actualSaving();
            TestPlanModel::actualSaving();
            TestCaseModel::actualSaving();
            TestPlanLogic::$isTriggerStatistics = true;
            TestPlanLogic::triggerStatistics($planId);

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }
    }


    public static function getListByPlanIdAndCaseId($testPlanId, $caseId = null, $cntId = null)
    {
        $where = [
            'test_plan_id' => $testPlanId,
        ];
        if (!is_null($caseId)) {
            $where['test_case_id'] = $caseId;
        }
        if (!is_null($cntId)) {
            $where['cnt_id'] = $cntId;
        }

        return TestPlanWorkCaseModel::where($where)->select();
    }

}