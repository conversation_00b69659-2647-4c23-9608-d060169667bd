<?php
declare (strict_types=1);

namespace app\work_items\logic;

use app\infrastructure\model\GeneralApprovalsModel;
use app\work_items\model\WorkHoursRejectionRecordsModel;
use app\work_items\model\WorkItemsModel;
use basic\BaseLogic;
use exception\NotFoundException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\Paginator;

/**
 * 工时打回记录逻辑类
 */
class WorkHoursRejectionRecordsLogic extends BaseLogic
{
    /**
     * 创建工时打回记录
     * @param GeneralApprovalsModel $model 审批模型
     * @param array $data 额外数据
     * @return WorkHoursRejectionRecordsModel
     * @throws Exception
     */
    public function create(GeneralApprovalsModel $model, array $data = []): WorkHoursRejectionRecordsModel
    {
        // 获取工作项信息
        $workItem = WorkItemsModel::findById($model->business_model_id);
        if (!$workItem) {
            throw new NotFoundException('工作项不存在');
        }


        // 创建记录
        $record = new WorkHoursRejectionRecordsModel();
        $record->save([
            'cnt_id' => $model->business_model_id,
            'cnt_title' => $workItem->extends['title'] ?? '',
            'handler_uid' => $model->update_by,
            'handler_name' => $model->update_by_name,
            'rejection_reason' => $model->content['hit_back_reason'] ?? '',
            'submitter_uid' => $model->create_by,
            'submitter_name' => $model->create_by_name,
            'submit_at' => $model->create_at,
            'approval_at' => $model->update_at,
            'approval_id' => $model->general_approvals_id,
            'working_hours' => $model->content['working_hours'] ?? 0,
        ]);

        return $record;
    }

    /**
     * 分页查询工时打回记录
     * @param array $params 查询参数
     * @return Paginator
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function pageQuery(array $params): Paginator
    {
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['page_size']) ? intval($params['page_size']) : 10;

        $query = WorkHoursRejectionRecordsModel::where('is_delete', WorkHoursRejectionRecordsModel::DELETE_NOT);

        // 按任务ID查询
        if (!empty($params['cnt_id'])) {
            $query->where('cnt_id', $params['cnt_id']);
        }

        // 按处理人查询
        if (!empty($params['handler_uid'])) {
            $query->where('handler_uid', $params['handler_uid']);
        }

        // 按提交人查询
        if (!empty($params['submitter_uid'])) {
            $query->where('submitter_uid', $params['submitter_uid']);
        }

        // 按任务标题模糊查询
        if (!empty($params['cnt_title'])) {
            $query->whereLike('cnt_title', '%' . $params['cnt_title'] . '%');
        }

        // 按时间范围查询
        if (!empty($params['start_time'])) {
            $query->where('submit_at', '>=', $params['start_time']);
        }
        if (!empty($params['end_time'])) {
            $query->where('submit_at', '<=', $params['end_time']);
        }

        return $query->order('create_at', 'desc')
            ->paginate([
                'list_rows' => $pageSize,
                'page' => $page,
            ]);
    }

    /**
     * 获取详情
     * @param int $id 打回记录ID
     * @return array
     * @throws NotFoundException
     */
    public function detail(int $id): array
    {
        $model = WorkHoursRejectionRecordsModel::findById($id);
        if (!$model) {
            throw new NotFoundException('记录不存在');
        }

        return $model->toDetail();
    }
} 