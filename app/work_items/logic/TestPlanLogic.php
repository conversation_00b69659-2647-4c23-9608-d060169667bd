<?php
declare (strict_types=1);

namespace app\work_items\logic;

use app\infrastructure\logic\EnumLogic;
use app\infrastructure\model\FieldSubsetModel;
use app\project\model\ProjectModel;
use app\work_items\model\TestPlanModel;
use app\work_items\validate\TestPlanValidate;
use basic\BaseModel;
use Elastic\Elasticsearch\ClientInterface;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use exception\BusinessException;
use exception\NotFoundException;
use basic\BaseLogic;
use exception\ParamsException;
use think\Exception;
use think\facade\Db;
use think\facade\Env;
use think\Paginator;
use utils\DBTransaction;
use utils\EsClientFactory;

class TestPlanLogic extends BaseLogic
{

    const NOT_PAGE_MAX = 10000;//不分页时的最大条数

    //关系相关字段必须要有
    const REQUIRED_FIELD
        = [
            'test_plan_id'
        ];

    //是否执行统计
    public static $isTriggerStatistics = true;

    private ClientInterface $esClient;
    private mixed $esIndex;

    public function __construct(ClientInterface $client)
    {
        $this->esClient = $client;
        $this->esIndex = Env::get('es.test_plan_index');
    }

    public static function getInstance()
    {
        return new self(EsClientFactory::getInstance());
    }


    /**
     * 获取默认查询字段
     * @param  array  $fieldList
     * @return array|string[]
     * <AUTHOR>
     * @date   2024/8/29 22:18
     */
    private function getFiledList(array $fieldList)
    {
        return array_merge($fieldList, self::REQUIRED_FIELD);
    }


    /**
     * 新增
     * @param $params
     * @return array|string
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function create($params)
    {
        validate(TestPlanValidate::class)->scene('create')->check($params);

        Db::startTrans();
        try {
            $model = TestPlanModel::create();

            $params['test_plan_id'] = $model->test_plan_id;
            $params['is_delete'] = TestPlanModel::DELETE_NOT;


            $model->save($params);

            //同时关联迭代工作项
            if ($params['associate_iteration_work_items'] ?? false) {
                if (empty($params['iteration_id'])) {
                    throw new Exception("未选择迭代不可勾选\"同时关联迭代工作项\"");
                }
                $demand = WorkItemsLogic::findDemandIdListByIterationId($params['iteration_id']);

                if ($demand) {
                    (new PlanUseCaseLogic)->addBugByDemand($model->test_plan_id, $demand);
                }
            }

            Db::commit();

            return $model->toDetail();
        } catch (\Throwable $e) {
            Db::rollback();
            throw  $e;
        }

    }

    /**
     * 修改
     * @param $params
     * @return TestPlanModel|array|mixed|\think\Model
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function update($params)
    {
        validate(TestPlanValidate::class)->scene('update')->check($params);

        $model = TestPlanModel::findById($params['test_plan_id']);
        if ( ! $model) {
            throw new NotFoundException();
        }

        if (isset($params['iteration_id'])) {
            if ( ! $this->isSetIteration($model)) {
                throw new ParamsException('不可设置迭代！');
            }
        }

        if (isset($params['title']) && trim($params['title']) == '') {
            throw new ParamsException("标题不可为空！");
        }

        Db::startTrans();
        try {

            $model->save($params);

            Db::commit();

            return $model->toDetail();
        } catch (\Throwable $e) {
            Db::rollback();
            throw  $e;
        }

    }


    /**
     * 详情
     * @param $id
     * @return array
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function detail($id)
    {


        $model = TestPlanModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }


        $details = $model->toDetail();

        if ($details['project_id'] ?? false) {
            $details['project_name'] = ProjectModel::findById((int)$details['project_id'])['project_name'] ?? '';
        }

        return [
            'data'       => $details,
            'field_list' => $model->getFieldList(),
        ];
    }


    /**
     * 删除
     * @param $id
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/9/10 11:56
     */
    public function delete($id)
    {
        $model = TestPlanModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }

        if ($model->extends['is_delete'] === BaseModel::DELETE_YES) {
            return;
        }

        Db::startTrans();
        try {
            $model->save(['is_delete' => BaseModel::DELETE_YES]);

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }


    /**
     * 分页查询
     * @param $params
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function pageQuery($params)
    {
        validate(TestPlanValidate::class)->scene('pageQuery')->check($params);

        if ($params['iteration_id'] ?? false) {
            $params['searchParams'][] = ['field_name' => 'iteration_id', 'value' => $params['iteration_id'], 'type' => "term", 'operate_type' => "equal"];
        }
        $params['searchParams'] = array_column($params['searchParams'], null, 'field_name');
        $params['searchParams']['project_id'] = ['field_name' => 'project_id', 'value' => $params['project_id'], 'type' => "term", 'operate_type' => "equal"];

        // 处理排序参数
        $order = $this->formatOrderParams($params['order'] ?? []);

        $pageSize = getPageSize();
        $itemList = $this->esSearch($params['searchParams'], $pageSize['list_rows'], $pageSize['page'], $params['field_list'], $order);

        return Paginator::make($itemList->getCollection()->toArray(), $itemList->listRows(), $itemList->currentPage(), $itemList->total())->each($this->listHander());
    }

    /**
     * 列表数据处理器
     * @return \Closure
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/9/9 14:25
     */
    public function listHander()
    {
        $closeStatus = EnumLogic::findEnumValue('plan_status_close');

        return function ($v) use ($closeStatus) {

            $v['isEnd'] = $closeStatus == ($v['status'] ?? 0);
            $v['is_set_iteration'] = $this->isSetIteration($v);


            return $v;
        };
    }


    /**
     * 是否可设置迭代
     * @param $item
     * @return bool
     * <AUTHOR>
     * @date   2024/11/14 14:50
     */
    public function isSetIteration($item)
    {
        return ! ($item['iteration_id'] ?? false);
    }

    public function isEnd($data)
    {
        return ($data['status'] ?? 0) == EnumLogic::findEnumValue('plan_status_close');
    }


    /**
     * 根据id保存es数据
     * @param       $id
     * @param       $data
     * @param  int  $operationType  操作类型0创建，1更新
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/8/27 21:09
     */
    public function saveEs($id, $data, int $operationType)
    {
        $params = [
            'index'   => $this->esIndex,
            'id'      => $id,
            'body'    => [],
            'refresh' => config('es.refresh'),//同步
        ];

        if ($operationType) {
            $params['body']['doc'] = $data;
            $response = $this->esClient->update($params)->getBody();
        } else {
            $params['body'] = $data;
            $response = $this->esClient->index($params)->getBody();
        }

        $response = json_decode((string)$response, true);
        if ( ! (isset($response['result']) && ($response['result'] === 'updated' || $response['result'] === 'created' || $response['result'] === 'noop'))) {
            throw new BusinessException("数据保存错误！");
        }
    }

    /**
     * 批量保存数据
     * @param $data array 数据集
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     */
    public function bulk($data)
    {
        $bulk = ['body' => [], 'refresh' => config('es.refresh'),];
        foreach ($data as $item) {
            $bulk['body'][] = ['update' => ['_index' => $this->esIndex, '_id' => $item['test_plan_id']]];
            $bulk['body'][] = ['doc' => $item, 'doc_as_upsert' => true];
        }

        $response = $this->esClient->bulk($bulk)->getBody();

        $response = json_decode((string)$response, true);
        if ( ! array_key_exists('errors', $response) || $response['errors']) {
            throw new BusinessException("数据保存错误！");
        }
    }


    /**
     * 返回没删除的es查询条件
     * @return array
     * <AUTHOR>
     * @date   2024/9/5 16:39
     */
    private function getNotDelParams()
    {
        return ['field_name' => 'is_delete', 'value' => BaseModel::DELETE_NOT, 'type' => 'term', 'operate_type' => 'equal'];
    }

    /**
     * 查询es数据
     * @param $params
     * @param $listRows
     * @param $page
     * @param $fieldList
     * @param array $order 排序参数
     * @return Paginator|\think\paginator\driver\Bootstrap
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/9/5 16:40
     */
    public function esSearch($params, $listRows = 20, $page = 1, $fieldList = null, array $order = [])
    {
        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);

        $params = [
            'index' => $this->esIndex,
            'body'  => [
//                '_source' => [
//                    'parent_id',
//                    'test_plan_id',
//                ],
                'query' => [
                    'bool' => $where
                ],
                'sort'  => !empty($order) ? $order : [
                    'test_plan_id' => [
                        'order' => 'desc'
                    ]
                ],
            ],
            'from'  => ($page - 1) * $listRows, // 起始位置
            'size'  => $listRows, // 每页数量

        ];

        if ($fieldList) {
            $params['body']['_source'] = $this->getFiledList($fieldList);
        }

        $response = $this->esClient->search($params)->getBody();

        $resp = json_decode((string)$response, true);
        $total = $resp['hits']['total']['value'];
        $data = $resp['hits']['hits'];
        $data = array_column($data, '_source');

        if (in_array('contents', $fieldList ?? [])) {
            $data = $this->mergeContents($data);
        }


        return Paginator::make($data, $listRows, $page, $total);
    }


    /**
     * 将es查询出的值追加mysql中的contents
     * @param $data
     * @return mixed
     * <AUTHOR>
     * @date   2024/9/3 15:12
     */
    private function mergeContents($data)
    {
        $contentsList = TestPlanModel::where(['test_plan_id' => array_column($data, 'test_plan_id')])
            ->column('test_plan_id,contents', 'test_plan_id');

        foreach ($data as &$v) {
            $v['contents'] = $contentsList[$v['test_plan_id']]['contents'];
        }

        return $data;
    }

    /**
     * 生成es查询参数
     * @param $params
     * @return array|array[]
     * <AUTHOR>
     * @date   2024/8/28 11:19
     */
    private function generateEsQueryParams($params)
    {
        return WorkItemsEsLogic::getInstance()->generateEsQueryParams($params);
    }

    /**
     * 将text类型查询条件移动至最后一个
     * @param $params
     * @return array
     * <AUTHOR>
     * @date   2024/9/10 17:27
     */
    private function textFieldMoveEnd($params)
    {
        $result = [];
        $textFieldList = [];
        foreach ($params as $key => $v) {
            if ($v['type'] == 'text') {
                $textFieldList[] = $v;
            } else {
                $result[] = $v;
            }
        }

        return array_merge($result, $textFieldList);
    }


    /**
     * 统计
     * 需求数
     * 用例数
     * 测试执行率
     * 测试通过率
     * 用例覆盖率
     * @param $planId
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * <AUTHOR>
     * @date   2024/10/18 09:38
     */
    public static function triggerStatistics($planId)
    {
        if (!self::$isTriggerStatistics){
            return;
        }

        $model = TestPlanModel::findById($planId);
        if ( ! $model) {
            return;
        }
        $result = (new PlanUseCaseLogic)->statistics($planId);
        $model->save([
            'story_count'        => $result['demand_count'],
            'use_case_count'     => $result['case_count'],
            'execution_progress' => $result['implementation_rate'],
            'test_pass_rate'     => $result['pass_rate'],
            'use_case_coverage'  => $result['use_case_coverage'],
        ]);
    }

    /**
     * 按计划类型分组获取平均执行进度
     * @param $iterationId
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/12/6 下午4:26
     */
    public function getAvgCompletenessByIterationId($iterationId)
    {

        $params = [
            ['field_name' => 'iteration_id', 'value' => $iterationId, 'type' => "term", 'operate_type' => "equal"],
        ];

        $params[] = $this->getNotDelParams();
        $where = $this->generateEsQueryParams($params);


        $params = [
            'index' => $this->esIndex,
            'body'  => [
                'query' => [
                    'bool' => $where
                ],
                'aggs'  => [
                    'group_plan_type' => [//按计划类型分组
                        'terms' => [
                            'field' => 'plan_type',
//                            'missing' => -1,
                        ],
                        'aggs'  => [
                            'avg_execution_progress' => [//平均测试执行进度
                                'avg' => [
                                    'field' => 'execution_progress'
                                ],
                            ],
                            'avg_test_pass_rate'     => [//平均测试通过率
                                'avg' => [
                                    'field' => 'test_pass_rate'
                                ],
                            ],
                        ]
                    ]

                ]

            ],
            'size'  => 0, // 每页数量
        ];


        $response = $this->esClient->search($params)->getBody();

        $resp = json_decode((string)$response, true);
//        dd($resp);

        $data = array_column($resp['aggregations']['group_plan_type']['buckets'], null, 'key');

        $result = [];
        (new EnumLogic)->getList('test_plan_type')->map(function ($v) use ($data, &$result) {
            $result[$v['enum_code']] = [
                'label'                  => $v['enum_name'],
                'value'                  => $v['enum_value'],
                'enum_code'              => $v['enum_code'],
                //(测试通过率 + 测试执行进度)/2
                'avg_execution_progress' => (float)bcdiv(
                    (string)(($data[$v['enum_value']]['avg_execution_progress']['value'] ?? 0.0) + ($data[$v['enum_value']]['avg_test_pass_rate']['value'] ?? 0.0)),
                    '2',
                    2
                ),
            ];
        });

        return $result;

    }

    /**
     * 批量删除
     * @param array $ids 测试计划ID数组
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws \Throwable
     * <AUTHOR>
     * @date 2024/3/21
     */
    public function batchDelete(array $ids)
    {
        if (empty($ids)) {
            throw new ParamsException('请选择要删除的测试计划');
        }

        $models = TestPlanModel::where('test_plan_id', 'in', $ids)->select();
        if ($models->isEmpty()) {
            return;
        }

        DBTransaction::begin();
        try {
            TestPlanModel::$autoSave = false;
            foreach ($models as $model) {
                if ($model->extends['is_delete'] === BaseModel::DELETE_YES) {
                    continue;
                }
                $model->save(['is_delete' => BaseModel::DELETE_YES]);
            }

            TestPlanModel::actualSaving();
            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 批量更新测试计划
     * @param array $params 包含test_plan_ids和field_list的参数数组
     * @return array 更新结果
     * @throws \Throwable
     */
    public function batchUpdate(array $params)
    {
        $planIds = $params['test_plan_ids'];

        // 只处理field_list格式的参数
        if (!isset($params['field_list']) || !is_array($params['field_list'])) {
            throw new ParamsException("field_list参数不能为空");
        }

        // 获取可编辑字段列表
        $allowedFields = $this->getAllowedFields();
        if (empty($allowedFields)) {
            throw new ParamsException("未找到可编辑字段配置");
        }

        $updateFields = [];
        foreach ($params['field_list'] as $field) {
            if (isset($field['field_name'])) {
                // 验证字段是否在允许的范围内
                if (!in_array($field['field_name'], $allowedFields)) {
                    continue; // 跳过不允许编辑的字段
                }

                // 处理vlaue拼写错误，同时兼容value正确拼写
                $value = $field['value'] ?? $field['vlaue'] ?? null;
                $updateFields[$field['field_name']] = $value;
            }
        }

        if (empty($planIds)) {
            throw new ParamsException("测试计划ID不能为空");
        }

        if (empty($updateFields)) {
            throw new ParamsException("更新字段不能为空");
        }

        $result = [];
        $hasSuccess = false;

        DBTransaction::begin();
        try {
            TestPlanModel::$autoSave = false;
            foreach ($planIds as $id) {
                $model = TestPlanModel::findById($id);
                if (!$model || $model->extends['is_delete'] === BaseModel::DELETE_YES) {
                    continue;
                }

                // 构建更新数据
                $updateData = [];
                foreach ($updateFields as $field => $value) {
                    // 特殊字段处理
                    switch ($field) {
                        case 'iteration_id':
                            // 检查是否可以设置迭代
                            if (!$this->isSetIteration($model)) {
                                continue 2; // 跳过这个字段的更新
                            }
                            $updateData[$field] = $value;
                            break;
                        default:
                            $updateData[$field] = $value;
                            break;
                    }
                }

                // 执行更新
                if (!empty($updateData)) {
                    $model->save($updateData);
                }

                $hasSuccess = true;
                $result[] = $id;
            }

            TestPlanModel::actualSaving();
            DBTransaction::commit();

            $msg = $hasSuccess ? "批量更新成功" : "批量更新失败";

            return [
                'msg'      => $msg,
                'data'     => [
                    'result'  => $result,
                    'success' => $hasSuccess
                ],
                'msg_type' => $hasSuccess ? 1 : 0
            ];
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 获取测试计划允许编辑的字段列表
     * @return array 允许编辑的字段名称数组
     */
    private function getAllowedFields(): array
    {
        $fieldList = FieldSubsetModel::getByKey('plan_edit');
        if (empty($fieldList)) {
            return [];
        }

        // 提取字段名称
        $allowedFields = [];
        foreach ($fieldList['field_list'] as $field) {
            if (isset($field['field_name'])) {
                $allowedFields[] = $field['field_name'];
            }
        }

        return $allowedFields;
    }

    /**
     * 格式化排序参数
     * @param  mixed  $order  排序参数，支持字符串（如 "field desc, field2 asc"）或数组格式
     * @return array Elasticsearch排序格式的数组
     * <AUTHOR>
     * @date   2024/7/24
     */
    private function formatOrderParams($order)
    {
        $result = [];

        // 空值处理
        if (empty($order)) {
            return $result;
        }

        // 字符串格式: "field desc, field2 asc"
        if (is_string($order)) {
            $orderItems = explode(',', $order);
            foreach ($orderItems as $item) {
                $item = trim($item);
                if (empty($item)) {
                    continue;
                }

                $parts = explode(' ', $item, 2);
                $field = trim($parts[0]);
                $direction = isset($parts[1]) ? strtolower(trim($parts[1])) : 'desc';

                if ($direction !== 'asc' && $direction !== 'desc') {
                    $direction = 'desc'; // 默认降序
                }

                $result[$field] = ['order' => $direction];
            }
            return $result;
        }

        // 数组格式1: ['field' => 'desc', 'field2' => 'asc']
        if (is_array($order) && ! isset($order[0])) {
            foreach ($order as $field => $direction) {
                $direction = strtolower(trim($direction));
                if ($direction !== 'asc' && $direction !== 'desc') {
                    $direction = 'desc'; // 默认降序
                }
                $result[$field] = ['order' => $direction];
            }
            return $result;
        }

        // 数组格式2: [['field' => 'field1', 'direction' => 'desc'], [...]]
        if (is_array($order) && isset($order[0]) && is_array($order[0])) {
            foreach ($order as $item) {
                if ( ! isset($item['field'])) {
                    continue;
                }

                $field = $item['field'];
                $direction = isset($item['direction']) ? strtolower(trim($item['direction'])) : 'desc';

                if ($direction !== 'asc' && $direction !== 'desc') {
                    $direction = 'desc'; // 默认降序
                }

                $result[$field] = ['order' => $direction];
            }
            return $result;
        }

        return $result;
    }

}
