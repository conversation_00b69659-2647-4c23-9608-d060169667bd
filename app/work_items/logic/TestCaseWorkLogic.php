<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/9/29 14:46
 */

namespace app\work_items\logic;

use app\work_items\model\TestCaseModel;
use app\work_items\model\TestCaseWorkModel;
use app\work_items\model\WorkItemsModel;
use basic\BaseLogic;
use exception\NotFoundException;
use exception\ParamsException;
use Throwable;
use utils\DBTransaction;

class TestCaseWorkLogic extends BaseLogic
{

    //当前id为需求id，查询用例id集合
    const TYPE_CNT_ID = 1;

    //上面取反
    const TYPE_TEST_CASE_ID = 2;


    /**
     * 需求<->测试用例  相互关联
     * @param $id  int 关联id
     * @param $idList array 被关联id集合
     * @param $relevancyType int  关联类型 TYPE_CNT_ID|TYPE_TEST_CASE_ID
     * @return void
     * @throws Throwable
     * <AUTHOR>
     * @date 2024/10/9 10:34
     */
    public function relevancy($id, $idList, $relevancyType)
    {

        [$class, $idField, $idListField] = match ($relevancyType) {
            self::TYPE_CNT_ID => [
                WorkItemsModel::class,
                'cnt_id',
                'test_case_id',
            ],
            self::TYPE_TEST_CASE_ID => [
                TestCaseModel::class,
                'test_case_id',
                'cnt_id',
            ],
            default => throw new ParamsException(),
        };


        $model = $class::with([
            'testCaseWork'
        ])->find($id);

        if (!$model) {
            throw new  NotFoundException();
        }
        try {
            DBTransaction::begin();


            $oldList = $model->testCaseWork->column($idListField);

            $removeList = array_diff($oldList, $idList);
            $newList = array_diff($idList, $oldList);

            //删除
            if ($idField == 'cnt_id') {
                $removeList && $this->del($id, $removeList);

            } else {
                $removeList && $this->del($removeList, $id);

            }

            //新增
            $newList && (new TestCaseWorkModel)->saveAll(array_map(function ($item) use ($id, $idField, $idListField) {
                return [
                    $idField => $id,
                    $idListField => $item,
                ];
            }, $newList));


            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }
    }

    /**
     * 删除关系
     * @param $cntId
     * @param $caseId
     * @return void
     * <AUTHOR>
     * @date 2024/10/10 09:28
     */
    public function del($cntId, $caseId)
    {

        try {
            DBTransaction::begin();
            $where = [];
            if (!is_null($caseId)) {
                $where['test_case_id'] = $caseId;
            }

            if (!is_null($cntId)) {
                $where['cnt_id'] = $cntId;
            }

            if (!$where) {
                throw new ParamsException();
            }

            //计划内联动删除
            (new TestPlanWorkCaseLogic)->del(null, $where['test_case_id'] ?? null, $where['cnt_id'] ?? null);

            TestCaseWorkModel::where($where)->select()->each(function (TestCaseWorkModel $model) {
                $model->delete();
            });

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }
    }


    /**
     * 获取关联数据集合
     * @param $id
     * @param $type 1-id=需求，2-id=测试用例
     * @return array
     * <AUTHOR>
     * @date 2024/9/29 17:33
     */
    public static function getIdListByType($id, $type)
    {
        return match ($type) {
            1 => TestCaseWorkModel::where(['cnt_id' => $id])->column('test_case_id'),
            2 => TestCaseWorkModel::where(['test_case_id' => $id])->column('cnt_id'),
            default => throw new ParamsException(),
        };
    }
}