<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\work_items\validate;

use app\microservice\model\MicroserviceModel;
use basic\BaseValidate;

class TestCaseWorkItemsValidate extends BaseValidate
{
    protected $rule = [
        'id|关联id' => 'require',
        'id_list|被关联id集合' => 'array',
        'relevancy_type|关联类型' => 'require',
    ];


    protected $scene = [
        'relevancy' => ['id', 'id_list', 'relevancy_type'],
    ];



}
