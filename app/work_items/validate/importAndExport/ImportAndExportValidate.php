<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\work_items\validate\importAndExport;

use basic\BaseValidate;

class ImportAndExportValidate extends BaseValidate
{
    protected $rule = [
        'file|文件' => 'require|file|fileExt:xlsx,xls|fileSize:104857600',//100MB
        'project_id|项目id' => 'require',
        'is_repeatable_titles|标题是否可重复' => 'require',
        'module_id|模块id' => 'require',
        'sub_key|字段子集标识' => 'require',

        'field_list|字段集合' => 'array',
        'searchParams|查询参数' => 'array',
    ];
    protected $message
        = [
            'file.fileSize'=>'上传文件大小不可超过100MB'
        ];


    protected $scene = [
        'getExportHeaderTemplate' => ['project_id', 'field_list', 'sub_key'],
        'export' => ['project_id', 'field_list','searchParams', 'module_id'],
        'import' => ['file', 'project_id','is_repeatable_titles','sub_key'],
    ];

}
