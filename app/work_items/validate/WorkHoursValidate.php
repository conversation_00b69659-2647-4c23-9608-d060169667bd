<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\work_items\validate;

use basic\BaseValidate;

class WorkHoursValidate extends BaseValidate
{
    protected $rule = [
        'cnt_id|工作项id' => 'require',
        'work_hours_id|工时id' => 'require',
        'type|类型' => 'require|in:1,2,3',
        'remark|描述' => 'max:200',
        'working_hours|工时' => 'require|float',
        'work_date|工作日期' => 'requireIf:type,2|date',
    ];


    protected $scene = [
        'create' => ['cnt_id', 'type', 'remark', 'working_hours', 'work_date',],
        'update' => ['work_hours_id', 'remark', 'working_hours', 'work_date',],
    ];


}
