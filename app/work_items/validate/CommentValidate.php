<?php
/**
 * Desc 迭代分类 - 验证器
 * User Long
 * Date 2024/07/19
 */

namespace app\work_items\validate;

use basic\BaseValidate;

class CommentValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|gt:0|elt:99',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'comment_id|评论id' => 'require|integer|egt:0',
        'work_items_id|所属内容id' => 'require|integer|egt:0',
        'work_items_ids|工作项ID数组' => 'require|isArray',
        'content|内容' => 'require',
        'remark|其他说明' => 'max:100',
        'reply_id|回复评论' => 'require|integer|egt:0',
        'reply_user_id|回复用户' => 'require|integer|egt:0',
        'is_top|是否置顶' => 'require|in:1,0',
        'is_fake_delete|是否假删' => 'require|in:1,0'
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
    ];

    /**
     * 新增
     * @return CommentValidate
     * User Long
     * Date 2024/8/28
     */
    public function sceneCreate(): CommentValidate
    {
        return $this->only(['work_items_id', 'content', 'remark']);
    }

    /**
     * 更新
     * @return CommentValidate
     * User Long
     * Date 2024/8/28
     */
    public function sceneUpdate(): CommentValidate
    {
        return $this->only(['comment_id', 'content', 'remark']);
    }

    /**
     * 回复
     * @return CommentValidate
     * User Long
     * Date 2024/8/28
     */
    public function sceneReply(): CommentValidate
    {
        return $this->only(['reply_id', 'content', 'remark']);
    }

    /**
     * 为多个工作项添加相同评论
     * @return CommentValidate
     * User Long
     * Date 2024/9/25
     */
    public function sceneCreateSameForMultiple(): CommentValidate
    {
        return $this->only(['work_items_ids', 'content', 'remark']);
    }
}
