<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\work_items\validate;

use app\microservice\model\MicroserviceModel;
use basic\BaseValidate;

class PlanUseCaseValidate extends BaseValidate
{
    protected $rule = [
        'test_plan_id|计划id' => 'require',
        'test_case_id|用例id' => 'require',
        'test_case_id_list|用例id集合' => 'require',
        'result|执行结果' => 'require',
        'remark|备注' => 'max:200',
        'cnt_id_list|缺陷id' => 'array',
        'test_plan_work_case_id|关系id' => 'require',
    ];


    protected $scene = [
        'execute' => ['test_plan_work_case_id', 'result', 'remark', 'cnt_id_list'],
        'batchExecute' => ['result', 'remark'],
    ];


}
