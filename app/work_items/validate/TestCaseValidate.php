<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\work_items\validate;

use app\microservice\model\MicroserviceModel;
use basic\BaseValidate;

class TestCaseValidate extends BaseValidate
{
    protected $rule = [
        'test_case_id|ID' => 'require',
        'version|版本号' => 'require',
        'project_id|项目id' => 'require',
        'title|标题' => 'require',
        'test_case_ids|测试用例ID列表' => 'require|array',
        'field_list|更新字段列表' => 'require|array',
    ];


    protected $scene = [
        'create' => ['project_id', 'title'],
        'update' => ['test_case_id',],
        'pageQuery' => ['project_id'],
        'batchUpdate' => ['test_case_ids', 'field_list'],
    ];


}
