<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\microservice\validate;

use app\microservice\model\MicroserviceModel;
use basic\BaseValidate;

class MicroserviceValidate extends BaseValidate
{
    protected $rule = [
        'microservice_id|微服务id' => 'require',
        'microservice_name|微服务名称' => 'require|max:50|checkNameUnique',
        'extends|自定义字段' => 'require',
        'version|版本号' => 'require',
    ];


    protected $scene = [
        'create' => ['microservice_name', 'extends'],
        'update' => ['microservice_id', 'microservice_name', 'extends', 'version'],
    ];


    protected $message = [
        'microservice_name.checkNameUnique' => '微服务名称重复',
    ];

    /**
     * 验证名称是否唯一
     * @param $value
     * @param $rule
     * @param $data
     * @return bool
     * <AUTHOR>
     * @date 2024/7/8 上午10:16
     */
    protected function checkNameUnique($value, $rule, $data = [])
    {
        return MicroserviceModel::checkNameUnique($data['microservice_id'] ?? '', $data['microservice_name'] ?? '');
    }


}
