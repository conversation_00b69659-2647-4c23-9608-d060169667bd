<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/7/5 上午10:33
 */

namespace app\microservice\controller;

use app\microservice\logic\MicroserviceLogic;
use basic\BaseController;
use resp\Result;
use think\App;
use think\Request;

class Microservice extends BaseController
{
    private $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new MicroserviceLogic();
    }


    /**
     * 创建
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 下午5:52
     */
    public function create(Request $request)
    {
        $param = $request->param([
            'microservice_name',
            'extends',
        ]);
        $model = $this->logic->create($param);

        return Result::success($model);
    }


    /**
     * 删除
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 下午5:52
     */
    public function delete(Request $request)
    {
        $this->logic->delete($request->post('microservice_id'));

        return Result::success();
    }


    /**
     * 更新
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 下午5:52
     */
    public function update(Request $request)
    {
        $param = $request->param([
            'microservice_name',
            'extends',
            'microservice_id',
            'version',
        ]);
        $model = $this->logic->update($param);

        return Result::success($model);
    }


    /**
     * 详情
     * @param $microserviceId
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 下午5:53
     */
    public function detail($microserviceId)
    {
        $res = $this->logic->detail($microserviceId);

        return Result::success($res);
    }


    /**
     * 分页查询
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/7/8 下午5:53
     */
    public function pageQuery(Request $request)
    {
        $param = $request->param([
            'microservice_name',
        ]);
        $data = $this->logic->pageQuery($param);

        return Result::success($data);
    }


    /**
     * 启用
     * @param $microserviceId
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 下午5:53
     */
    public function enable($microserviceId)
    {
        $this->logic->enable($microserviceId);

        return Result::success();
    }

    /**
     * 禁用
     * @param $microserviceId
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 下午5:53
     */
    public function disable($microserviceId)
    {
        $this->logic->disable($microserviceId);

        return Result::success();
    }

    /**
     * 微服务下拉框数据
     * @param Request $request
     * @return \think\response\Json
     * User Long
     * Date 2024/11/20
     */
    public function selector(Request $request)
    {
        $res = $this->logic->selector();

        return Result::success($res);
    }

    /**
     * 项目产品微服务下拉框数据
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/20
     */
    public function selectorByProduct(Request $request)
    {
        $projectId = $request->post('project_id/d', 0);

        $res = $this->logic->selectorByProduct($projectId);

        return Result::success($res);
    }
}
