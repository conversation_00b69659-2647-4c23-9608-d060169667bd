<?php
/**
 * Desc 示例 - 逻辑层
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);

namespace app\infrastructure\logic;

use app\infrastructure\model\CustomerTableConfigModel;
use app\infrastructure\validate\CustomerTableConfigValidate;
use basic\BaseLogic;
use utils\Ctx;


class CustomerTableConfigLogic extends BaseLogic
{

    /**
     * 保存
     * @param $params
     * @return CustomerTableConfigModel|array|mixed|\think\Model|null
     * <AUTHOR>
     * @date 2024/7/8 上午10:12
     */
    public function save($params)
    {

        validate(CustomerTableConfigValidate::class)->check($params);
        $model = CustomerTableConfigModel::findUserCustomerTable(Ctx::$user->userId, $params['table_unique']);

        if (!$model) {
            $model = new CustomerTableConfigModel();
        }

        $model->save($params);

        return $model;
    }


    /**
     * 获取
     * @param $tableUnique
     * @param $moduleId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/7/10 下午2:15
     */
    public function get($params)
    {
        $model = CustomerTableConfigModel::findUserCustomerTable(Ctx::$user->userId, $params['table_unique']);

        $columnsConfig = null;
        if ($params['module_id'] ?? false) {
            if (is_numeric($params['module_id'])) {
                $columnsConfig = FieldConfigLogic::getListByModuleIdGroupByFieldType($params);
            } else {
                $columnsConfig = FieldConfigLogic::getListBySubKeyGroupByFieldType(['sub_key' => $params['module_id'], 'project_id' => $params['project_id'] ?? null]);
            }
        }

        return [
            'table_unique' => $model ? $model->table_unique : null,
            'columns_config' => $columnsConfig,
            'user_fields' => $model ? $model->user_fields : null
        ];
    }

}
