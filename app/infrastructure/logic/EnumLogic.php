<?php
/**
 * Desc 示例 - 逻辑层
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);

namespace app\infrastructure\logic;

use app\infrastructure\model\EnumModel;
use basic\BaseLogic;
use exception\NotFoundException;
use app\infrastructure\validate\EnumValidate;
use think\exception\ValidateException;


class EnumLogic extends BaseLogic
{
    // 获取枚举集合
    public function getList($code): array|\think\Collection
    {
        $model = EnumModel::findByCode($code);
        if ( ! $model) {
            throw new NotFoundException();
        }

        return $model;
    }

    /**
     * 根据code查询指定配置枚举集
     * @param  string  $code
     * @return array
     * User Long
     * Date 2024/8/12
     */
    public static function selectorEnumData(string $code): array
    {
        $model = EnumModel::findByCode($code);

        if ( ! $model) {
            return [];
        }

        return $model->toArray();
    }

    /**
     * 根据code查询指定配置枚举值
     * @param $enumCode
     * @return array
     * User Long
     * Date 2024/8/13
     */
    public static function getEnumValues($enumCode): array
    {
        $roleEnum = EnumLogic::selectorEnumData($enumCode);

        return array_column($roleEnum, 'enum_value');
    }

    /**
     * 根据code查询指定配置
     * @param  string  $code
     * @return mixed
     * User Long
     * Date 2024/8/12
     */
    public static function findEnumValue(string $code): mixed
    {
        return EnumModel::findEnumValueByCode($code);
    }

    /**
     * 创建枚举
     * @param  array  $data
     * @return EnumModel
     * @throws ValidateException
     */
    public function create(array $data): EnumModel
    {
        validate(EnumValidate::class)->check($data);

        // 检查enum_code是否已存在
        if (EnumModel::where('enum_code', $data['enum_code'])->find()) {
            throw new ValidateException('枚举代码已存在');
        }

        // 检查父级层级
        if ($data['parent_id'] > 0) {
            $parent = EnumModel::findById($data['parent_id']);
            if ( ! $parent) {
                throw new ValidateException('父级枚举不存在');
            }
            if ($parent['parent_id'] > 0) {
                throw new ValidateException('最多只能创建二级枚举');
            }
        }

        $model = (new EnumModel());
        $model->save($data);
        return $model;
    }

    /**
     * 更新枚举
     * @param  int    $id
     * @param  array  $data
     * @return EnumModel|array|mixed|\think\Model
     * @throws ValidateException
     */
    public function update($id, $data)
    {
        validate(EnumValidate::class)->check($data);

        $model = EnumModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }

        // 检查enum_code是否已存在（排除当前记录）
        if (EnumModel::where('enum_code', $data['enum_code'])
            ->where('enum_id', '<>', $id)
            ->find()
        ) {
            throw new ValidateException('枚举代码已存在');
        }

        // 检查父级层级
        if ($data['parent_id'] > 0) {
            $parent = EnumModel::findById($data['parent_id']);
            if ( ! $parent) {
                throw new ValidateException('父级枚举不存在');
            }
            if ($parent['parent_id'] > 0) {
                throw new ValidateException('最多只能创建二级枚举');
            }
            // 检查是否有子枚举（如果当前是一级枚举）
            if ($model['parent_id'] == 0) {
                $hasChildren = EnumModel::where('parent_id', $id)->find();
                if ($hasChildren) {
                    throw new ValidateException('该枚举下已有子枚举，不能将其变成子枚举');
                }
            }
        }

        $model->save($data);
        return $model;
    }

    /**
     * 删除枚举
     * @param  int  $id
     * @return bool
     * @throws NotFoundException
     */
    public function delete(int $id): bool
    {
        $model = EnumModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }
        return $model->delete();
    }

    /**
     * 分页查询枚举列表,只支持二级子父级
     * @param $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function pageQuery($params): array
    {
        $query = EnumModel::status();

        // 关键词搜索
        if (!empty($params['keyword'])) {
            $query = $query->where([
                ['enum_code|enum_name|enum_value', 'like', "%{$params['keyword']}%"]
            ]);
        }

        if (isset($params['enum_type'])) {
            $query = $query->where('enum_type', (int)$params['enum_type']);
        }

        // 获取所有匹配条件的枚举
        $matchedEnums = $query->field(EnumModel::LIST_FIELDS)
            ->order('enum_id', 'desc')
            ->select()
            ->toArray();

        // 收集所有需要显示的父级ID
        $parentIds = [];
        foreach ($matchedEnums as $enum) {
            if ($enum['parent_id'] > 0) {
                $parentIds[] = $enum['parent_id'];
            }
        }

        // 如果有子枚举匹配，查询它们的父级
        if (!empty($parentIds)) {
            $parentEnums = EnumModel::status()
                ->whereIn('enum_id', array_unique($parentIds))
                ->field(EnumModel::LIST_FIELDS)
                ->select()
                ->toArray();
            
            // 将父级枚举添加到结果集中（如果还不存在）
            foreach ($parentEnums as $parent) {
                if (!in_array($parent['enum_id'], array_column($matchedEnums, 'enum_id'))) {
                    $matchedEnums[] = $parent;
                }
            }
        }

        // 分离顶级枚举和子枚举
        $topLevelEnums = [];
        $children = [];
        foreach ($matchedEnums as $enum) {
            if ($enum['parent_id'] == 0) {
                $topLevelEnums[] = $enum;
            } else {
                $children[$enum['parent_id']][] = $enum;
            }
        }

        // 手动实现分页
        ['page' => $page, 'list_rows' => $pageSize] = getPageSize();
        $total = count($topLevelEnums);
        $offset = ($page - 1) * $pageSize;
        
        // 获取当前页的顶级枚举
        $currentPageEnums = array_slice($topLevelEnums, $offset, $pageSize);

        // 将子枚举添加到对应的父枚举中
        foreach ($currentPageEnums as &$parent) {
            $parent['children'] = $children[$parent['enum_id']] ?? [];
        }

        // 构建分页结果
        $result = [
            'total' => $total,
            'per_page' => $pageSize,
            'current_page' => $page,
            'last_page' => ceil($total / $pageSize),
            'data' => $currentPageEnums
        ];

        return $result;
    }
}
