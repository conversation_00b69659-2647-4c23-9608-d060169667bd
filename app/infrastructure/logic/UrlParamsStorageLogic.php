<?php
/**
 * Desc URL参数存储 - 逻辑层
 * User
 * Date <?= date('Y/m/d') ?>
 */

declare (strict_types=1);

namespace app\infrastructure\logic;

use app\infrastructure\model\UrlParamsStorageModel;
use think\Exception;
use think\Request;

class UrlParamsStorageLogic
{

    /**
     * 自动获取当前请求参数并保存
     * @return array 包含url_params_id的数组
     * @throws Exception
     */
    static public function saveCurrentParams(): array
    {
        // 获取当前请求的所有参数
        $params = \request()->param();

        // 调用保存方法
        return self::saveParams($params);
    }

    /**
     * 保存URL参数
     * @param  array  $params  要保存的参数
     * @return array 包含url_params_id的数组
     * @throws Exception
     */
    static public function saveParams(array $params): array
    {
        // 创建新记录
        $model = new UrlParamsStorageModel();
        $model->params_data = json_encode($params, JSON_UNESCAPED_UNICODE);

        // 设置过期时间 (默认7天后过期)
        $model->expires_at = date('Y-m-d H:i:s', strtotime('+7 days'));

        // 保存记录
        $model->save();

        return [
            'url_params_id' => $model->url_params_id
        ];
    }

    /**
     * 获取URL参数
     * @param  int  $id  参数ID
     * @return array 参数数据
     * @throws Exception
     */
    public function getParams(int $id): array
    {
        // 查询记录
        $model = UrlParamsStorageModel::findById($id);

        if ( ! $model) {
            return [];
        }

//        // 检查是否过期
//        if (!empty($model->expires_at) && strtotime($model->expires_at) < time()) {
//            throw new Exception('参数记录已过期');
//        }
        // 返回参数数据
        return $model->params_data;
    }
}
