<?php
/**
 * Desc 权限相关 - 逻辑层
 * User Long
 * Date 2024/12/12
 */

namespace app\infrastructure\logic;


use components\platform\user\requests\User;
use exception\BusinessException;
use utils\Ctx;
use utils\Redis;

class PermissionsLogic
{
    // 获取类型
    const FRONT_END = 'vue'; // 前端
    const REAR_END = 'rule_path'; // 后端
    const MIDDLEWARE = 'middleware'; // 中间件使用
    const ALL = 'all'; // 所有

    /**
     * 处理权限数据
     * @param $permission
     * @param $code
     * @param $rulePath
     * @param $authSource
     * User Long
     * Date 2024/12/12
     */
    private static function processPermission($permission, &$code, &$rulePath, $authSource)
    {
        if (isset($permission['code'])) {
            $code['permissions'][$permission['code']] = [
                'code' => $permission['code'],
                'role' => $authSource
            ];
        }
        if (isset($permission['linkAddress'])) {
            $rulePath['permissions'][$permission['linkAddress']] = [
                'route_path' => $permission['linkAddress'],
                'role' => $authSource
            ];
        }

        if (isset($permission['auth_link_infos'])) {
            foreach ($permission['auth_link_infos'] as $subPermission) {
                self::processPermission($subPermission, $code, $rulePath, $authSource);
            }
        }
    }

    /**
     * 获取中台权限项
     * @param $type
     * @return array
     * User Long
     * Date 2024/12/12
     */
    private static function getCenterPermissionsData($type = '')
    {
        // 不是前端请求，优先从缓存中获取
        if ($type != self::FRONT_END) {
            $redis = Redis::getPersonalInstance()->getCache();

            if ($redis && !empty($redis['permissions'])) {
                $centerCodeData = $redis['permissions'];
                goto skip_acquire; // 跳转到 "skip_pallet:"
            }
        }

        try {
            // 获取子系统id
            $systemId = env('platform.center_sub_system_id');
            $centerCodeData = (new User(Ctx::$token))->getSubSystemUserRole($systemId)->wait();

            // 缓存中台权限
            $redisData = Redis::getPersonalInstance()->setCache(['permissions' => $centerCodeData])->send();

            $centerCodeData = $redisData['permissions'] ?? [];
        } catch (\Throwable $e) {
            if (env('ENVIRONMENT') == 'dev' || env('ENVIRONMENT') == 'test') {
                throw $e;
            }
            throw new BusinessException('查无权限，请联系客服人员');
        }

        // 添加跳过锚点
        skip_acquire:

        // 重新组合中台返回权限
        $code = $ruleInitPath = $rulePath = ['is_admin' => false, 'permissions' => []];

        foreach ($centerCodeData as $per) {
            // 管理员直接跳出循环体
            if (isset($per['is_admin']) && $per['is_admin']) {
                $code['is_admin'] = $ruleInitPath['is_admin'] = $rulePath['is_admin'] = true;
                break;
            }

            // 组合权限数据
            self::processPermission($per, $code, $rulePath, $per['auth_source']);
        }

        $code['permissions'] = array_values($code['permissions']);
        $ruleInitPath['permissions'] = $rulePath['permissions'];
        $rulePath['permissions'] = array_values($rulePath['permissions']);

        // 按需求返回权限
        return match ($type) {
            self::FRONT_END => $code,
            self::REAR_END => $rulePath,
            self::ALL => ['code' => $code, 'rule_path' => $rulePath],
            self::MIDDLEWARE => $ruleInitPath,
            default => [],
        };
    }

    /**
     * 获取后端路由权限
     * @param string $type
     * @return array
     * User Long
     * Date 2024/12/13
     */
    public static function getRulePathPermissions(string $type = self::REAR_END)
    {
        return self::getCenterPermissionsData($type);
    }

    /**
     * 获取用户 - 拥有权限内的前端编码
     * @return array
     * User Long
     * Date 2024/12/12
     */
    public static function getVuePermissions()
    {
        return self::getCenterPermissionsData(self::FRONT_END);
    }
}
