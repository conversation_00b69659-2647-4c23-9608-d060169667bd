<?php
declare (strict_types=1);

namespace app\infrastructure\logic;

use app\infrastructure\model\TemplateModel;
use app\infrastructure\validate\TemplateValidate;
use app\project\logic\ProjectInfoLogic;
use app\project\logic\ProjectTemplateLogic;
use app\project\model\ProjectTemplateModel;
use basic\BaseLogic;
use basic\BaseModel;
use exception\NotFoundException;
use exception\ParamsException;

class TemplateLogic extends BaseLogic
{
    // 重命名链接
    private const RENAME_URL = '/template/rename';

    /**
     * 校验名称是否与模板相同
     * @param          $projectInfo
     * @param  string  $templateName
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function validateProjectTemplateData($projectInfo, string $templateName = ''): void
    {
        if ($projectInfo) {
            if ($projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
                $templateId = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);

                if ($templateId) {
                    $templateNameExist = TemplateModel::findTemplateAndName($templateId, $templateName);
                    if ($templateNameExist) {
                        throw new ParamsException(ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id).'-模板名称已存在');
                    }
                }
            } else {
                $projectIds = ProjectInfoLogic::getProjectIdsByTemplateId($projectInfo->project_template_id)
                    ->where('is_template', ProjectTemplateModel::IS_TEMPLATE_NO);
                $templateNameExist = TemplateModel::status()
                    ->with([
                        'project' => function ($sql) {
                            $sql->bind(['project_name']);
                        }
                    ])
                    ->whereIn('project_id', $projectIds->column('project_id'))
                    ->select();

                if ( ! $templateNameExist->where('template_name', $templateName)->isEmpty()) {
                    throw new ParamsException('【'.implode('】、【', $templateNameExist->where('template_name', $templateName)->column('project_name')).'】-模板名称已存在');
                }
            }
        }
    }

    /**
     * 新增
     *
     * @param $params
     *
     * @return TemplateModel
     * <AUTHOR>
     * @date   2024/7/8 上午10:19
     */
    public function create($params)
    {

        validate(TemplateValidate::class)->scene('create')->check($params);

        $model = new TemplateModel();
        $model->save($params);

        return $model->toDetail();
    }

    /**
     * 删除
     * @param $id
     * @return void
     * <AUTHOR>
     * @date   2024/7/8 上午10:19
     */
    public function delete($id)
    {
        $model = TemplateModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }

        if ($model->is_default == TemplateModel::DEFAULT_YES) {
            throw new ParamsException('已启用模板不可删除！');
        }

        if ( ! $model->category->isEmpty()) {
            throw new ParamsException('应有中的模板不可删除！');
        }

        $model->is_delete = BaseModel::DELETE_YES;
        $model->save();

    }

    /**
     * 修改
     *
     * @param $params
     *
     * @return TemplateModel|array|mixed|\think\Model
     * <AUTHOR>
     * @date   2024/7/8 上午10:19
     */
    public function update($params)
    {

        validate(TemplateValidate::class)->scene('update')->check($params);
        $model = TemplateModel::findById($params['id']);
        if ( ! $model) {
            throw new NotFoundException();
        }
        $model->save($params);

        return $model->toDetail();
    }

    /**
     * 详情
     *
     * @param $id
     *
     * @return TemplateModel
     * <AUTHOR>
     * @date   2024/7/8 上午10:19
     */
    public function detail($id)
    {
        $model = TemplateModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }
        $model->getFieldList();

        return $model->toDetail();
    }

    /**
     * 默认模板详情
     * @param $params  =[
     *                 'module_id'=>'模块id',
     *                 'project_id'=>'项目id',
     *                 ]
     * @return TemplateModel
     * <AUTHOR>
     * @date   2024/7/8 上午10:19
     */
    public function defaultTempDetail($params)
    {
        validate(TemplateValidate::class)->scene('defaultTempDetail')->check($params);

        $model = TemplateModel::findByDefault($params['module_id']);
        if (TemplateModel::isRequireProjectId($params['module_id'])) {
//            $model->where(['project_id' => $params['project_id']]);

            $projectIds[] = $params['project_id'];
            $templateIds = [];
            $projectInfo = ProjectInfoLogic::getProjectData($params['project_id']);
            if ($projectInfo && $projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
                $templateIds[] = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);
            }
            $projectIds = array_merge($projectIds, $templateIds);

            $model->whereIn('project_id', $projectIds)->order('project_id ASC, is_default DESC, id desc');
        }
        $model = $model->find();
        if ( ! $model) {
            throw new NotFoundException("无默认模板！");
        }
        $model->getFieldList();

        return $model->toDetail();
    }

    /**
     * 分页查询
     * @param $params  =[
     *                 'module_id'=>'模块id',
     *                 'project_id'=>'项目id',
     *                 ]
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/7/8 上午10:19
     */
    public function pageQuery($params)
    {
        validate(TemplateValidate::class)->scene('defaultTempDetail')->check($params);

        $quire = (new TemplateModel)
            ->with(['category'])
            ->where(['is_delete' => BaseModel::DELETE_NOT, 'module_id' => $params['module_id']])
            ->field(TemplateModel::LIST_FIELDS)
            ->order('project_id ASC, is_default DESC, id desc');

        $projectId = $params['project_id'] ?? 0;
        $projectIds[] = $projectId;
        $templateIds = [];

        $projectInfo = ProjectInfoLogic::getProjectData($projectId);
        if ($projectInfo && $projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
            $templateIds[] = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);
        }
        $projectIds = array_merge($projectIds, $templateIds);

        if (TemplateModel::isRequireProjectId($params['module_id'])) {
//            $quire->where(['project_id' => $params['project_id']]);
            $quire->whereIn('project_id', $projectIds);
        }

        $result = $quire->paginate(getPageSize());

        $result->each(function ($item) use ($projectInfo, $templateIds) {
            $item['hasCategory'] = ! $item->category->isEmpty();

            $item['template_not_allowed_del'] = false; // 模板字段不允许删除，默认否
            // 项目内模板字段不允许编辑
            if (isset($projectInfo->is_template) && $projectInfo->is_template == 0 && in_array($item['project_id'], $templateIds)) {
                $item['template_not_allowed_del'] = true;
            }

            unset($item['category']);
        });

        return $result;
    }

    /**
     * 复制
     * @param $id
     * @param $project_id
     * @return TemplateModel
     * <AUTHOR>
     * @date   2024/7/8 上午10:19
     */
    public function copy($id, $project_id)
    {
        $model = TemplateModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $new = new TemplateModel();
        $new->template_name = TemplateModel::generateName($model->template_name, $model->module_id);
        validate(TemplateValidate::class)->scene('copy')->check([
            'template_name' => $new->template_name,
        ]);
        $new->template_content = $model->template_content;
        $new->module_id = $model->module_id;
        if ($project_id) {
            $new->project_id = $project_id;
        } else {
            $new->project_id = $model->project_id;
        }

        $new->save();

        return $new->toDetail();
    }

    /**
     * 启用
     * @param $id
     * @return TemplateModel
     * <AUTHOR>
     * @date   2024/7/8 上午10:19
     */
    public function enable($id)
    {
        $model = TemplateModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }
        if ($model->isDefault()) {
            return $model->toDetail();
        }
        TemplateModel::cancelAllDefault($model->module_id, $model->project_id);
        $model->is_default = TemplateModel::DEFAULT_YES;
        $model->save();

        return $model->toDetail();
    }

    /**
     * 根据模板id获取对应模板
     * @param  int  $id
     * @return TemplateModel|array|mixed|\think\Model|null
     * User Long
     * Date 2024/7/24
     */
    public function findById(int $id)
    {
        return TemplateModel::findById($id);
    }

    /**
     * 复制页面管理
     * @param  int  $oldProjectId
     * @param  int  $newProjectId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/10/14
     */
    public static function copyProjectDataByProjectId(int $oldProjectId, int $newProjectId)
    {
        $oldProjectData = TemplateModel::status()->where([
            'project_id' => $oldProjectId,
        ])->whereIn('module_id', TemplateModel::REQUIRE_PROJECT_MODULE)->select();

        foreach ($oldProjectData as $oldProjectDatum) {
            $model = new TemplateModel();
            $model->save([
                'attribution_id'   => $oldProjectDatum->id,
                'is_default'       => $oldProjectDatum->is_default,
                'template_name'    => $oldProjectDatum->template_name,
                'template_content' => $oldProjectDatum->template_content,
                'module_id'        => $oldProjectDatum->module_id,
                'project_id'       => $newProjectId
            ]);
        }
    }

    /**
     * 根据项目id查询 新旧模板id
     * @param  int  $newProjectId
     * @return array
     * User Long
     * Date 2024/10/14
     */
    public static function selectAttributionIdByProjectId(int $newProjectId)
    {
        return TemplateModel::status()->where([
            'project_id' => $newProjectId,
        ])->column('id', 'attribution_id');
    }

    /**
     * 复制至 功能增加创建页模板
     * @param  array  $params
     * @param  int    $newProjectId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/4/14
     */
    public static function copyTemplateByCategory(array $params, int $newProjectId)
    {
        $res = ['data' => [], 'is_rename' => false];

        $rename = $params['template_name'];
        $diffData = TemplateModel::status()->where([
            'project_id'    => $newProjectId,
            'template_name' => $rename,
        ])
            ->column('template_name, project_id', 'project_id');

        if (isset($diffData[$newProjectId])) {
            $res['is_rename'] = true;
            $rename = TemplateModel::generateName($diffData[$newProjectId]['template_name'], $diffData[$newProjectId]['project_id']);
        }

        $model = new TemplateModel();
        $model->save([
            'attribution_id'   => $params['attribution_id'],
            'is_default'       => $params['is_default'],
            'template_name'    => $rename,
            'template_content' => $params['template_content'],
            'module_id'        => $params['module_id'],
            'project_id'       => $newProjectId,
        ]);
        if (isset($diffData[$newProjectId])) {
            $res['data']['template_config'][] = ['id' => $model->id, 'template_name' => $rename, 'project_id' => $newProjectId, 'url' => self::RENAME_URL];
        }

        $res['id'] = $model->id;

        return $res;
    }

    /**
     * 更新模板名称
     * @param  int     $id
     * @param  string  $templateName
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function rename(int $id, string $templateName)
    {
        $model = TemplateModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $templateNameExist = TemplateModel::findTemplateAndName($model->project_id, $templateName);
        $projectInfo = ProjectInfoLogic::getProjectData($model->project_id);

        if ($templateNameExist && $templateNameExist->id != $id) {
            $name = match ($projectInfo->is_template) {
                ProjectTemplateModel::IS_TEMPLATE_YES => ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id),
                ProjectTemplateModel::IS_TEMPLATE_NO => '【'.$projectInfo->project_name.'】',
                default => '',
            };
            throw new ParamsException($name.'-模板名称已存在');
        }

        // 校验名称是否与模板相同
        $this->validateProjectTemplateData($projectInfo, $templateName);

        $model->template_name = $templateName;
        $model->save();
    }
}
