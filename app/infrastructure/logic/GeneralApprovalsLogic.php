<?php
/**
 * 通用审批逻辑层
 */

declare (strict_types=1);

namespace app\infrastructure\logic;

use app\infrastructure\model\GeneralApprovalsModel;
use app\infrastructure\service\ApprovalFactory;
use app\infrastructure\validate\GeneralApprovalsValidate;
use app\project\logic\ProjectUserLogic;
use app\project\model\IterationProcessNodeModel;
use app\project\model\ProjectModel;
use app\work_items\model\WorkCommentModel;
use app\work_items\model\WorkItemsModel;
use basic\BaseLogic;
use exception\NotFoundException;
use fsApproval\Example;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\ValidateException;
use think\Paginator;
use utils\Ctx;
use utils\DBTransaction;

class GeneralApprovalsLogic extends BaseLogic
{
    /**
     * 获取通用审批详情
     * @param int $id
     * @return array
     * @throws NotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDetail(int $id): array
    {
        $model = GeneralApprovalsModel::findById($id);
        if (!$model){
            throw new NotFoundException('审批记录不存在');
        }

        $detail = $model->toDetail();
        // 添加审批人字段
        $detail['approvers'] = $this->getApprovers($model);

        return $detail;
    }

    /**
     * 根据业务模型ID和业务类型获取通用审批
     * @param int $businessModelId
     * @param int $type
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws NotFoundException
     */
    public function getByBusinessModelIdAndType(int $businessModelId, int $type): array
    {
        $model = GeneralApprovalsModel::findByBusinessModelIdAndType($businessModelId, $type);
        if (!$model){
            throw new NotFoundException('审批记录不存在');
        }

        $detail = $model->toDetail();
        // 添加审批人字段
        $detail['approvers'] = $this->getApprovers($model);

        return $detail;
    }

    /**
     * 分页查询通用审批
     * @param array $params
     * @return Paginator
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function pageQuery(array $params): Paginator
    {

        $query = GeneralApprovalsModel::where('is_delete', GeneralApprovalsModel::DELETE_NO);
        // 业务类型筛选
        if (isset($params['type'])
            && \in_array($params['type'], [
                GeneralApprovalsModel::TYPE_ITERATION_NODE,
                GeneralApprovalsModel::TYPE_WORK_ITEMS,
                GeneralApprovalsModel::TYPE_WORK_HOURS
            ])
        ){
            $query->where('type', \intval($params['type']));
        }

        // 审批类型筛选
        if (isset($params['is_audit'])
            && \in_array($params['is_audit'], [
                GeneralApprovalsModel::AUDIT_INITIATE,
                GeneralApprovalsModel::AUDIT_AGREE,
                GeneralApprovalsModel::AUDIT_REJECT
            ])
        ){
            $query->where('is_audit', \intval($params['is_audit']));
        }

        // 业务模型ID筛选
        if (isset($params['business_model_id']) && $params['business_model_id'] > 0){
            $query->where('business_model_id', \intval($params['business_model_id']));
        }

        $paginator = $query->order('general_approvals_id', 'desc')
            ->paginate(\getPageSize());

        // 为每条记录添加审批人字段
        $items = $paginator->items();
        foreach ($items as &$item) {
            $detail = $item->toArray();
            $detail['approvers'] = $this->getApprovers($item);
            $item->setAttr('approvers', $detail['approvers']);
        }

        return $paginator;
    }

    /**
     * 创建通用审批
     * @param array $data
     * @return GeneralApprovalsModel
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws ValidateException
     * @throws Exception
     */
    public function create(array $data): GeneralApprovalsModel
    {
        // 验证数据
        \validate(GeneralApprovalsValidate::class)
            ->scene('create')
            ->check($data);

        // 检查业务类型是否支持
        if (!\in_array($data['type'], [
            GeneralApprovalsModel::TYPE_ITERATION_NODE,
            GeneralApprovalsModel::TYPE_WORK_ITEMS,
            GeneralApprovalsModel::TYPE_WORK_HOURS,
            GeneralApprovalsModel::TYPE_ITERATION_NODE_SYNCED // 添加新的类型
        ])
        ){
            throw new ValidateException('不支持的业务类型');
        }

        // 针对迭代节点修改实际结束时间的特殊验证
        if ($data['type'] == GeneralApprovalsModel::TYPE_ITERATION_NODE){
            // 确保内容字段中包含所需数据
            if (empty($data['content']) || !isset($data['content']['end_time'])){
                throw new ValidateException('迭代节点修改缺少必要的时间信息');
            }
            $startTime = IterationProcessNodeModel::findById($data['business_model_id'])->getdata('start_time');
            if (!$startTime){
                throw new ValidateException('迭代开始时间不存在');
            }
//             获取结束时间和原开始时间
//            $endTime = strtotime(date('Y-m-d H:i:s', strtotime($data['content']['end_time'])));
//            $sourceStartTime = strtotime(date('Y-m-d H:i:s', strtotime($startTime)));
//             确保结束时间不小于原开始时间
//            if ($endTime < $sourceStartTime){
//                throw new ValidateException('修改的实际结束时间不能小于原开始时间');
//            }
        }

        // 获取策略类并验证发起人权限
        $strategy = ApprovalFactory::getStrategy($data['type']);
//        if (!$strategy->validateInitiator(Ctx::$userId, $data['business_model_id'])){
//            $errorMessages = [
//                GeneralApprovalsModel::TYPE_ITERATION_NODE => '仅节点负责人才可发起',
//                GeneralApprovalsModel::TYPE_WORK_ITEMS     => '仅架构师才可发起',
//                GeneralApprovalsModel::TYPE_WORK_HOURS     => '仅架构师才可发起'
//            ];
//            throw new ValidateException($errorMessages[$data['type']] ?? '您没有权限发起此审批');
//        }

        // 检查是否已存在审批记录
        $existApproval = GeneralApprovalsModel::findByBusinessModelIdAndType($data['business_model_id'], $data['type']);

        // 对于type=2（需求变更）的审批，不限制必须结束才可发起
        // 对于其他类型，只有当存在审批记录且状态为"发起"（即审批中）时，才不允许重新发起
        if ($data['type'] != GeneralApprovalsModel::TYPE_WORK_ITEMS
            && $existApproval
            && $existApproval->is_audit == GeneralApprovalsModel::AUDIT_INITIATE
        ){
            throw new ValidateException('已存在审批中的记录，请等待审批完成后再发起');
        }


        try {
            DBTransaction::begin();

            // 创建审批记录
            $model = new GeneralApprovalsModel();
            $model->save([
                'is_audit'          => GeneralApprovalsModel::AUDIT_INITIATE,
                'business_model_id' => $data['business_model_id'],
                'type'              => $data['type'],
                'content'           => $data['content'],
            ]);

            // 如果是同步迭代节点审批类型，并且审批记录已创建（状态为INITIATE），则执行同步旧表操作
            // 这确保在非自动批准的情况下，旧表的发起记录被创建
            if ($model->type == GeneralApprovalsModel::TYPE_ITERATION_NODE_SYNCED &&
                $model->is_audit == GeneralApprovalsModel::AUDIT_INITIATE &&
                $strategy instanceof \app\infrastructure\service\approval\SyncedIterationNodeApprovalStrategy
            ){
                // 此调用已在事务内，如果失败将导致整体回滚
                $strategy->postGeneralApprovalCreationActions($model);
            }

            // 检查发起人是否也是审批人，如果是则直接审批通过
            $approvers = $strategy->getApprovers($model->general_approvals_id);
            $approverIds = array_column($approvers, 'user_id');

            // 迭代节点审批，需同步飞书
            $syncService = new Example($model->general_approvals_id);
            //获取发起人飞书用户ID
            $initiateUser = ProjectUserLogic::getFsEmployeeIdByUserIds([ Ctx::$userId ]);
            if(empty($initiateUser)){
                throw new ValidateException('未查询到发起人飞书用户ID');
            }
            // 获取审批人飞书用户ID
            $approvalUser = ProjectUserLogic::getFsEmployeeIdByUserIds($approverIds);
            if(empty($approvalUser)){
                throw new ValidateException('未查询到审批人飞书用户ID');
            }

            if ($model->type == GeneralApprovalsModel::TYPE_ITERATION_NODE || $model->type == GeneralApprovalsModel::TYPE_ITERATION_NODE_SYNCED){
                $iterationProcessNode = IterationProcessNodeModel::findById($data['business_model_id']);
                $botSummary = [
                    "审批节点：{$iterationProcessNode->node_name}",
                ];
            }

            if ($model->type == GeneralApprovalsModel::TYPE_ITERATION_NODE){
                // 修改节点实际结束时间审批
                $title = "效能平台 — 修改节点实际时间审批";
                $startTime = date('Y-m-d H:i', strtotime($data['content']['start_time']));
                $endTime = date('Y-m-d H:i', strtotime($data['content']['end_time']));
                $botSummary[1] = "申请实际时间：{$startTime} - {$endTime}";
                $actualStart = date('Y-m-d H:i', strtotime($iterationProcessNode->getData('start_time')));
                $actualEnd = date('Y-m-d H:i', strtotime($iterationProcessNode->getData('end_time')));
                $botSummary[2] = "实际时间：{$actualStart} - {$actualEnd}";
//                if (!empty($data['content']['reason_application'])){
                $botSummary[3] = "申请原因：" . (!empty($data['content']['reason_application']) ? $data['content']['reason_application'] : " --");
//                }

                $nodeId = $iterationProcessNode->iteration_process_node_id;
            } else if ($model->type == GeneralApprovalsModel::TYPE_WORK_ITEMS){
                $title = "效能平台 — 需求变更录入";
                $workItems = WorkItemsModel::findById($data['business_model_id']);
                $botSummary = [
                    "需求标题：{$workItems['extends']['title']}",
                    "变更内容：{$data['content']['changes_content']}"
                ];
                $nodeId = $workItems['cnt_id'];

            } else if ($model->type == GeneralApprovalsModel::TYPE_WORK_HOURS){
                $title = "效能平台 — 工时打回";
                $workItems = WorkItemsModel::findById($data['business_model_id']);

                // 任务标题、建议的预估工时、打回原因、预估工时
                $botSummary = [
                    0 => "任务标题：{$workItems['extends']['title']}",
                    3 => "预估工时：".($workItems['extends']['estimated_work_hours'] ?? ''),
                ];
//                if (!empty($data['content']['estimated_work_hours'])){
                $botSummary[2] = "建议的预估工时：" . ($data['content']['working_hours'] ?? '');
//                }
//                if (!empty($data['content']['hit_back_reason'])){
                $botSummary[1] = "打回原因：" . ($data['content']['hit_back_reason'] ?? "");
//                }
                $nodeId = $workItems['cnt_id'];
            } else if ($model->type == GeneralApprovalsModel::TYPE_ITERATION_NODE_SYNCED){
                $project = ProjectModel::findById($data['content']['project_id']);
                //  迭代节点审批
                $title = "效能平台 — {$iterationProcessNode->node_name}审批";
                $botSummary[2] = "所属项目：{$project->project_name}";
                if (!empty($data['content']['remark'])){
                    $botSummary[1] = "备注：{$data['content']['remark']}";
                }
                $nodeId = $iterationProcessNode->iteration_process_node_id;
            }

//            dd($botSummary,$initiateUser,$approvalUser,in_array(Ctx::$userId, $approverIds));
            $systemExampleId=0;
            //查询到发起人及审批人飞书用户ID
            if (!empty($initiateUser) && !empty($approvalUser)){
                //同步实例
                $res = $syncService->newApproval($model->general_approvals_id, $title, $initiateUser[0]);
                if (empty($res['data']['systemExampleId'])){
                    throw new Exception('同步飞书审批失败');
                }
                $systemExampleId = $res['data']['systemExampleId'];
                $model->save([ 'sync_example_id' => $systemExampleId ]);

                $auditUser = implode(',', $approvalUser) . ','.env('FS_CONFIG.bot_user', '');
                $auditUser = trim($auditUser, ',');
                //同步实例任务
                $syncService->newTask($systemExampleId, $nodeId, $title, $auditUser, array_values($botSummary));

            }

            // 当前用户是审批人之一，直接通过审批
            if (in_array(Ctx::$userId, $approverIds)){
                // 更新审批状态为同意
                $model->save([
                    'is_audit' => GeneralApprovalsModel::AUDIT_AGREE,
                ]);

                if(!empty($systemExampleId)){
                    //审批状态为同意时，同步实例为已完成
                    usleep(1000000);//休眠1秒,避免同步飞书审批bot消息状态失败
                    $syncService->syncApproval($systemExampleId, GeneralApprovalsModel::AUDIT_AGREE);
                }

                // 执行审批通过后的处理
                $strategy->handleApproved($model);
            }

            DBTransaction::commit();
            return $model;
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 审批
     * @param int $id
     * @param int $isAudit
     * @param array $content
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws NotFoundException
     * @throws ValidateException
     * @throws Exception
     */
    public function approve(int $id, int $isAudit, array $content = []): bool
    {
        // 验证审批类型
        if (!in_array($isAudit, [ GeneralApprovalsModel::AUDIT_AGREE, GeneralApprovalsModel::AUDIT_REJECT ])){
            throw new ValidateException('审批类型错误');
        }

        // 查找审批记录
        $model = GeneralApprovalsModel::findById($id);
        if (!$model){
            throw new NotFoundException('审批记录不存在');
        }

        // 检查审批状态
        if ($model->is_audit != GeneralApprovalsModel::AUDIT_INITIATE){
            throw new ValidateException('审批状态已更新');
        }

        // 验证审批人权限
        $strategy = ApprovalFactory::getStrategy($model->type);
        if (!$strategy->validateApprover(Ctx::$userId, $model->general_approvals_id)){
            $errorMessages = [
                GeneralApprovalsModel::TYPE_ITERATION_NODE => '仅“迭代leader、项目经理”进行审批',
                GeneralApprovalsModel::TYPE_WORK_ITEMS     => '仅可所选择的需求“负责人”进行审批',
                GeneralApprovalsModel::TYPE_WORK_HOURS     => '仅可任务的“处理人”进行审批'
            ];
            throw new ValidateException($errorMessages[$model->type] ?? '没有审批权限');
        }

        try {
            DBTransaction::begin();

            // 更新审批记录
            $updateData = [
                'is_audit' => $isAudit,
            ];

            // 如果有新的内容，则更新内容
            if (!empty($content)){
                $oldContent = $model->content;

                // 处理内容合并，检查重复键
                $processedContent = [];
                foreach ($content as $key => $value) {
                    // 如果键已存在于旧内容中，则添加approve_前缀
                    if (isset($oldContent[$key])){
                        $processedContent['approve_' . $key] = $value;
                    } else {
                        $processedContent[$key] = $value;
                    }
                }

                // 合并处理后的内容
                $updateData['content'] = array_merge($oldContent, $processedContent);
            }

            $model->save($updateData);

            // 根据审批结果执行相应的处理
            if ($isAudit == GeneralApprovalsModel::AUDIT_AGREE){
                $strategy->handleApproved($model);
            } else {
                $strategy->handleRejected($model);
            }

            if (!empty($model->sync_example_id)){
                // 迭代节点审批，同步飞书
                $syncService = new Example($model->general_approvals_id);
                $syncService->syncApproval($model->sync_example_id, $model->is_audit);
            }

            DBTransaction::commit();
            return true;
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 删除通用审批
     * @param int $id
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws NotFoundException
     * @throws ValidateException
     */
    public function delete(int $id): bool
    {
        // 验证数据
        validate(GeneralApprovalsValidate::class)->scene('delete')->check([ 'general_approvals_id' => $id ]);

        // 查找记录
        $model = GeneralApprovalsModel::findById($id);
        if (!$model){
            throw new NotFoundException('审批记录不存在');
        }

        // 删除记录
        return $model->save([
            'is_delete' => GeneralApprovalsModel::DELETE_YES,
        ]);
    }

    /**
     * 获取审批人信息
     * @param GeneralApprovalsModel $model 审批记录模型
     * @return array 审批人信息数组
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getApprovers(GeneralApprovalsModel $model): array
    {
        // 获取对应的策略类
        $strategy = ApprovalFactory::getStrategy($model->type);

        // 调用策略类的getApproversByAuditStatus方法
        return $strategy->getApproversByAuditStatus($model);
    }
}
