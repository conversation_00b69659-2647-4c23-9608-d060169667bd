<?php
/**
 * Desc URL参数存储 - 模型
 * User
 * Date <?= date('Y/m/d') ?>
 */

declare (strict_types=1);

namespace app\infrastructure\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "url_params_storage".
 * @property string $url_params_id  主键ID
 * @property string $params_data    参数数据（JSON格式）
 * @property string $create_by      创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at      创建时间
 * @property string $is_delete      是否删除：0未删除、1已删除
 * @property string $update_by      更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at      更新时间
 * @property string $expires_at     过期时间
 */
class UrlParamsStorageModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'url_params_id';
    protected $name = 'url_params_storage';


    protected $json = ['params_data'];
    protected $jsonAssoc = true;

    /**
     * 根据ID查找记录
     * @param  int  $id  记录ID
     * @return UrlParamsStorageModel|array|mixed|\think\Model|null
     */
    public static function findById($id)
    {
        return self::where(['url_params_id' => $id, 'is_delete' => self::DELETE_NOT])->find();
    }

    /**
     * 转换为详情数组
     * @return array
     */
    public function toDetail(): array
    {
        return [
            'url_params_id'  => $this->url_params_id,
            'params_data'    => json_decode($this->params_data, true),
            'create_by'      => $this->create_by,
            'create_by_name' => $this->create_by_name,
            'create_at'      => $this->create_at,
            'update_at'      => $this->update_at,
            'expires_at'     => $this->expires_at,
        ];
    }
}
