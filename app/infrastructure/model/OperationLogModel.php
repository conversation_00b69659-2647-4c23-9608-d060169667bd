<?php
declare (strict_types=1);

namespace app\infrastructure\model;

use basic\BaseModel;
use traits\CreateModelTrait;

/**
 * This is the model class for table "t_operation_log".
 * @property int $log_id id
 * @property int $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $operation_table 操作类型
 * @property int $table_id 关联Id
 * @property string $log_details 详情
 */
class OperationLogModel extends BaseModel
{

    use CreateModelTrait;

    protected $name = 'operation_log';

}
