<?php
/**
 * Desc 示例 - 模型
 * User
 * Date 2024/09/27
 * */

declare (strict_types=1);

namespace app\infrastructure\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "field_subset".
 * @property string $sub_id id
 * @property string $sub_key 标识
 * @property string $field_list 字段集合;json格式
 * @property string $include_custom 是否包含自定义字段;0：不是，1：是
 * @property string $remark 备注
 */
class FieldSubsetModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'sub_id';
    protected $name = 'field_subset';

    public function setFieldListAttr($value, $data)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function getFieldListAttr($value, $data)
    {

        return json_decode($value ?? '{}', true);
    }
    public function getModuleIdAttr($value, $data)
    {

        return json_decode($value ?? '{}', true);
    }

    public static function getByKey($key)
    {
        return self::where(['sub_key' => $key])->find();
    }


}
