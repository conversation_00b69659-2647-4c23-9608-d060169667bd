<?php
declare (strict_types=1);

namespace app\infrastructure\model;

use basic\BaseModel;
use traits\CreateModelTrait;

/**
 * This is the model class for table "t_customer_table_config".
 * @property int $table_config_id id
 * @property int $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $table_unique 表格唯一标识
 * @property string $user_fields 用户自定义显示字段
 */
class CustomerTableConfigModel extends BaseModel
{


    //操作日志
    use CreateModelTrait;


    const LIST_FIELDS = [
        'table_config_id',
        'table_unique',
        'user_fields',
    ];
    protected $name = 'customer_table_config';
    protected $pk = 'table_config_id';

    public static function findById($id)
    {
        return static::where(['table_config_id' => $id])->find();
    }

    public static function findUserCustomerTable($userId, $unique)
    {
        return static::where(['table_unique' => $unique, 'create_by' => $userId])->find();
    }

    public function getUserFieldsAttr($value)
    {
        return json_decode($value, true);
    }

    public function setUserFieldsAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }


}
