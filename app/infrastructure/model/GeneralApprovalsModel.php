<?php
/**
 * 通用审批模型
 */

declare (strict_types=1);

namespace app\infrastructure\model;

use basic\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use traits\CreateAndUpdateModelTrait;

/**
 * Class GeneralApprovalsModel
 * @package app\infrastructure\model
 * @property int $general_approvals_id id
 * @property int $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property int $is_delete 是否删除;1-是 0-否
 * @property int $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property int $is_audit 审批类型0默认1发起2同意3拒绝
 * @property int $business_model_id 业务模型id
 * @property int $type 业务类型,1-迭代节点修改实际结束时间t_iteration_process_node、2-需求变更t_work_items、3-工时打回t_work_items、4-同步迭代节点审批
 * @property array $content 内容
 */
class GeneralApprovalsModel extends BaseModel
{

    use CreateAndUpdateModelTrait;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'general_approvals_id';

    /**
     * 数据表名称
     * @var string
     */
    protected $table = 't_general_approvals';

    /**
     * 自动转换的字段
     * @var array
     */
    protected $json = ['content'];

    /**
     * 设置JSON数据返回数组
     * @var array
     */
    protected $jsonAssoc = true;

    /**
     * 是否删除：是
     */
    const DELETE_YES = 1;

    /**
     * 是否删除：否
     */
    const DELETE_NO = 0;

    /**
     * 审批类型：默认
     */
    const AUDIT_DEFAULT = 0;

    /**
     * 审批类型：发起
     */
    const AUDIT_INITIATE = 1;

    /**
     * 审批类型：同意
     */
    const AUDIT_AGREE = 2;

    /**
     * 审批类型：拒绝
     */
    const AUDIT_REJECT = 3;

    /**
     * 业务类型：迭代节点修改实际结束时间
     */
    const TYPE_ITERATION_NODE = 1;

    /**
     * 业务类型：需求变更
     */
    const TYPE_WORK_ITEMS = 2;

    /**
     * 业务类型：工时打回
     */
    const TYPE_WORK_HOURS = 3;

    /**
     * 业务类型：同步迭代节点审批 (旧接口发起，需同步到通用审批，并保留旧表记录)
     */
    const TYPE_ITERATION_NODE_SYNCED = 4;

    /**
     * 根据ID查找记录
     * @param  int  $id
     * @return GeneralApprovalsModel|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function findById(int $id)
    {
        return self::where('general_approvals_id', $id)
            ->where('is_delete', self::DELETE_NO)
            ->find();
    }

    /**
     * 根据业务模型ID和业务类型查找记录
     * @param  int  $businessModelId
     * @param  int  $type
     * @return GeneralApprovalsModel|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function findByBusinessModelIdAndType(int $businessModelId, int $type)
    {
        return self::where('business_model_id', $businessModelId)
            ->where('type', $type)
            ->where('is_delete', self::DELETE_NO)
            ->order('create_at', 'desc')
            ->find();
    }

    /**
     * 根据业务类型查找记录列表
     * @param  int  $type
     * @return \think\Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function findListByType(int $type)
    {
        return self::where('type', $type)
            ->where('is_delete', self::DELETE_NO)
            ->select();
    }

    /**
     * content获取器 - 将JSON字符串转为数组
     * @param $value
     * @return array|mixed
     */
    public function getContentAttr($value)
    {
        if (empty($value)) {
            return [];
        }
        
        if (is_array($value)) {
            return $value;
        }
        
        return json_decode($value, true) ?: [];
    }

    /**
     * content修改器 - 将数组转为JSON字符串
     * @param $value
     * @return false|string
     */
    public function setContentAttr($value)
    {
        if (empty($value)) {
            return '{}';
        }
        
        if (is_string($value)) {
            // 如果已经是JSON字符串，则验证其有效性
            json_decode($value);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $value;
            }
        }
        
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 转换为详情数组
     * @return array
     */
    public function toDetail(): array
    {
        return [
            'general_approvals_id' => $this->general_approvals_id,
            'is_audit'             => $this->is_audit,
            'business_model_id'    => $this->business_model_id,
            'type'                 => $this->type,
            'content'              => $this->content,
            'create_at'            => $this->create_at,
            'create_by'            => $this->create_by,
            'create_by_name'       => $this->create_by_name,
            'update_at'            => $this->update_at,
            'update_by'            => $this->update_by,
            'update_by_name'       => $this->update_by_name
        ];
    }
} 