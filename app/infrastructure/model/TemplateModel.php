<?php
declare (strict_types=1);

namespace app\infrastructure\model;

use app\project\model\ProjectCategorySettingsModel;
use app\project\model\ProjectModel;
use basic\BaseModel;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;
use traits\OperationLogTrait;

/**
 * This is the model class for table "t_client_template".
 * @property int $id id
 * @property int $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property int $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property string $is_default 是否默认模板;1-是 0-否
 * @property string $template_name 模板名称
 * @property array $template_content 模板内容
 * @property int $module_id 所属模块id
 * @property int $project_id 所属模块id
 */
class TemplateModel extends BaseModel
{
    //操作日志
    use OperationLogTrait;
    use CreateAndUpdateModelTrait;

    protected $name = 'template';

    const  DEFAULT_YES = 1; //是否默认模板;1-是
    const  DEFAULT_NOT = 0;   //是否默认模板;0-否

    //所属模块
    const MODULE_TYPE_CLIENT = 1; //终端
    const MODULE_TYPE_MICROSERVICE = 2; //微服务
    const MODULE_TYPE_PRODUCT = 3; //产品
    const MODULE_TYPE_ITERATION = 4; //迭代
    const MODULE_TYPE_REQUIREMENT = 5; //需求
    const MODULE_TYPE_TASK = 6; //任务
    const MODULE_TYPE_DEFECT = 7; //缺陷
    const MODULE_TYPE_TEST_CASE = 8; //测试用例
    const MODULE_TYPE_TEST_PLAN = 9; //测试计划

    //需要项目id的模块
    const REQUIRE_PROJECT_MODULE = [
        self::MODULE_TYPE_ITERATION,
        self::MODULE_TYPE_REQUIREMENT,
        self::MODULE_TYPE_TASK,
        self::MODULE_TYPE_DEFECT,
        self::MODULE_TYPE_TEST_CASE,
        self::MODULE_TYPE_TEST_PLAN
    ];

    const PROJECT_NOT_HAS = 0; //不属于任何项目

    const LIST_FIELDS = [
        'id',
        'project_id',
        'is_default',
        'template_name',
        'template_content',
    ];

    protected $pk = 'id';

    public function getTemplateContentAttr($value)
    {
        return json_decode($value, true);
    }

    public function setTemplateContentAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    //新增/修改需要记录日志的字段，字段名=>展示名称
    protected array $logFieldList = [
        'is_default' => '是否默认模板',
        'template_name' => '模板名称',
        //        'template_content' => '模板内容',
        //            'create_by'      => '创建人',
        //            'create_by_name' => '创建人名称',
        //            'create_at'      => '创建时间',
        //            'update_by'      => '更新人',
        //            'update_by_name' => '更新人名称',
        //            'update_at'      => '更新时间',
    ];

    public static function findById($id)
    {
        return static::where(['id' => $id, 'is_delete' => self::DELETE_NOT])->find();
    }

    /**
     * 生成复制名称
     * @param $name
     * @param $moduleId
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/7/29 上午11:11
     */
    public static function generateName($name, $moduleId)
    {

        $query = static::where([
            ['is_delete', '=', self::DELETE_NOT],
            ['module_id', '=', $moduleId],
        ]);

        return generateCopyName($name, 'template_name', $query);
    }

    public static function findByDefault($moduleId)
    {
        return static::where([
            'is_default' => self::DEFAULT_YES,
            'is_delete' => self::DELETE_NOT,
            'module_id' => $moduleId
        ]);
    }

    public static function cancelAllDefault($moduleId, $projectId)
    {
        static::where([
            'is_default' => self::DEFAULT_YES,
            'is_delete' => self::DELETE_NOT,
            'module_id' => $moduleId,
            'project_id' => $projectId,
        ])->save(['is_default' => self::DEFAULT_NOT]);
    }

    /**
     * @return string[]
     */
    public function getLogFieldList(): array
    {
        return $this->logFieldList;
    }

    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }

    /**
     * 查询指定模块下的模板名称
     * @param $moduleId
     * @param $templateName
     * @param $projectId
     * @return TemplateModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/7/19 下午4:56
     */
    public static function findModuleTemplateName($moduleId, $templateName, $projectId)
    {
        $where = [
            ['is_delete', '=', BaseModel::DELETE_NOT],
            ['template_name', '=', $templateName],
            ['module_id', '=', $moduleId],
        ];
        if (self::isRequireProjectId($moduleId)) {
            $where[] = ['project_id', '=', $projectId];
        }

        return static::where($where)->find();

    }

    public function isDefault(): bool
    {
        return $this->is_default == self::DEFAULT_YES;
    }

    /**
     * 解析template_content中所需的组件
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/7/19 下午2:17
     */
    public function getFieldList()
    {
        $fieldNameList = array_column($this->template_content, 'field_name');
        $this->fieldList = FieldConfigModel::getListByFieldName($fieldNameList, $this->module_id, $this->project_id);
    }

    /**
     * 判断模块是否含有所属项目
     * @param $moduleId
     * @return bool
     * <AUTHOR>
     * @date 2024/8/22 17:26
     */
    public static function isRequireProjectId($moduleId)
    {
        return in_array($moduleId, self::REQUIRE_PROJECT_MODULE);
    }

    /**
     * 预加载 - 类别
     * @return \think\model\relation\HasMany
     * User Long
     * Date 2024/9/2
     */
    public function category()
    {
        return $this->hasMany(ProjectCategorySettingsModel::class, 'template_id', 'id')
            ->where(['is_delete' => BaseModel::DELETE_NOT]);
    }

    /**
     * 根据 模板名称 获取当前项目下的指定模板
     * @param int $projectId
     * @param string $templateName
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findTemplateAndName(int $projectId, string $templateName): mixed
    {
        return static::status()->where([
            'project_id' => $projectId,
            'template_name' => $templateName
        ])->find();
    }

    /**
     * 预加载 - 项目表
     * @return HasOne
     */
    public function project(): HasOne
    {
        return $this->hasOne(ProjectModel::class, 'project_id', 'project_id');
    }
}
