<?php
/**
 * 工时打回审批策略
 */

declare (strict_types=1);

namespace app\infrastructure\service\approval;

use app\infrastructure\model\EnumModel;
use app\infrastructure\model\GeneralApprovalsModel;
use app\project\logic\ProjectUserLogic;
use app\work_items\logic\WorkHoursLogic;
use app\work_items\logic\WorkHoursRejectionRecordsLogic;
use app\work_items\model\WorkHoursModel;
use app\work_items\model\WorkItemsModel;
use think\Exception;

/**
 * 工时打回审批策略
 * 处理工时打回的审批
 */
class WorkHoursApprovalStrategy extends AbstractApprovalStrategy
{
    /**
     * 获取业务类型
     * @return int
     */
    public function getType(): int
    {
        return GeneralApprovalsModel::TYPE_WORK_HOURS;
    }


    /**
     * 审批通过后的处理
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return bool
     * @throws Exception
     */
    public function handleApproved(GeneralApprovalsModel $model): bool
    {
        try {
            $this->startTrans();

            if ( ! isset($model->content['working_hours'])) {
                throw new Exception('审批内容格式错误');
            }

            // 创建工时记录
            (new WorkHoursLogic())->create([
                'cnt_id'        => $model->business_model_id,
                'type'          => WorkHoursModel::TYPE_ESTIMATED,
                'working_hours' => $model->content['estimated_work_hours'] ?? $model->content['working_hours'],
            ], 1);

            // 记录工时打回信息
            (new WorkHoursRejectionRecordsLogic())->create($model);

            $this->commit();
            return true;
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 审批拒绝后的处理
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return bool
     * @throws Exception
     */
    public function handleRejected(GeneralApprovalsModel $model): bool
    {
        try {
            $this->startTrans();

            // TODO: 实现工时打回审批拒绝后的处理逻辑
            // 例如：发送通知给申请人

            $this->commit();
            return true;
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }


    /**
     * 获取审批人列表
     * @param  int  $id  主键ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getApprovers(int $id): array
    {
        $model = GeneralApprovalsModel::findById($id);

        $workItem = WorkItemsModel::findById($model->business_model_id);
        if ( ! $workItem) {
            return [];
        }

        $approvers = [];
        if ( ! empty($workItem['extends']['handler_uid'])) {
            $approvers = $this->getUserInfo($workItem['extends']['handler_uid']);
        }

        return array_unique($approvers, SORT_REGULAR);
    }

    /**
     * 验证发起人权限
     * @param  int  $userId           用户ID
     * @param  int  $businessModelId  业务模型ID
     * @return bool
     */
    public function validateInitiator(int $userId, int $businessModelId): bool
    {
        $workItem = WorkItemsModel::findById($businessModelId);
        if ( ! $workItem) {
            return false;
        }

        $businessArchitects = array_column((new ProjectUserLogic())->selectorListQuery((int)$workItem['extends']['project_id'], '', '', [EnumModel::BUSINESS_ARCHITECT]), 'user_id');
        return in_array($userId, $businessArchitects);
    }
} 