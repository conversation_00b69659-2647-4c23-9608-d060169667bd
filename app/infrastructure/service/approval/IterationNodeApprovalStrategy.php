<?php
/**
 * 迭代节点审批策略
 */

declare (strict_types=1);

namespace app\infrastructure\service\approval;

use app\infrastructure\model\EnumModel;
use app\infrastructure\model\GeneralApprovalsModel;
use app\iterate\model\IterationModel;
use app\project\logic\IterationProcessNodeLogic;
use app\project\logic\ProjectUserLogic;
use app\project\logic\IterationProcessNodeRelationLogic;
use app\project\model\IterationProcessNodeModel;
use think\Exception;

/**
 * 迭代节点审批策略
 * 处理迭代节点修改实际结束时间的审批
 */
class IterationNodeApprovalStrategy extends AbstractApprovalStrategy
{
    /**
     * 获取业务类型
     * @return int
     */
    public function getType(): int
    {
        return GeneralApprovalsModel::TYPE_ITERATION_NODE;
    }


    /**
     * 审批通过后的处理
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return bool
     * @throws Exception
     */
    public function handleApproved(GeneralApprovalsModel $model): bool
    {
        try {
            $this->startTrans();

            if ( ! isset($model->content['start_time']) || ! isset($model->content['end_time'])) {
                throw new Exception('审批内容格式错误，缺少开始时间或结束时间');
            }

            // 合并新的日期和原始时间
            $newStartTime = date('Y-m-d H:i:s', strtotime($model->content['start_time']));
            $newEndTime = date('Y-m-d H:i:s', strtotime($model->content['end_time']));

            IterationProcessNodeLogic::updateNode($model->business_model_id, [
                'start_time' => $newStartTime,
                'end_time'   => $newEndTime,
            ]);

            // 判断是否是第一个、最后一个节点，是的话要同步更改迭代的实际开始，结束时间
            $node = IterationProcessNodeModel::find($model->business_model_id);
            if ($node) {
                $iterationUpdateData = [];

                // 检查是否为第一个业务节点 (前置节点是开始节点)
                $prevRelations = IterationProcessNodeRelationLogic::getPrevRelationDataByNodeId([$node->iteration_process_node_id]);
                if ( ! $prevRelations->isEmpty()) {
                    $prevNodeIds = $prevRelations->column('process_node_id');
                    $isFirstNode = IterationProcessNodeModel::whereIn('iteration_process_node_id', $prevNodeIds)
                        ->where('row_id', IterationProcessNodeModel::START_NODE)
                        ->find();
                    if ($isFirstNode) {
                        $iterationUpdateData['start_time'] = $newStartTime;
                    }
                }

                // 检查是否为最后一个业务节点 (参考 updateEndNode 方法)
                $endNode = IterationProcessNodeModel::where([
                    'iteration_id' => $node->iteration_id,
                    'row_id'       => IterationProcessNodeModel::END_NODE
                ])->find();

                if ($endNode) {
                    $endNodePredecessorRelations = IterationProcessNodeRelationLogic::getPrevRelationDataByNodeId([$endNode->iteration_process_node_id]);
                    if ( ! $endNodePredecessorRelations->isEmpty()) {
                        $endNodePredecessorIds = $endNodePredecessorRelations->column('process_node_id');

                        // 检查当前节点是否是结束节点的前置节点之一
                        if (in_array($node->iteration_process_node_id, $endNodePredecessorIds)) {
                            $predecessorNodes = IterationProcessNodeModel::whereIn('iteration_process_node_id', $endNodePredecessorIds)->select();

                            $allPredecessorsComplete = true;
                            foreach ($predecessorNodes as $pNode) {
                                // 如果一个前置节点不是当前节点，并且状态不是已完成，则不满足条件
                                if ($pNode->iteration_process_node_id != $node->iteration_process_node_id && $pNode->status != IterationProcessNodeModel::STATUS_COMPLETED) {
                                    $allPredecessorsComplete = false;
                                    break;
                                }
                            }

                            if ($allPredecessorsComplete) {
                                $iterationUpdateData['end_time'] = $newEndTime;
                            }
                        }
                    }
                }

                if ( ! empty($iterationUpdateData)) {
                    $iteration = IterationModel::find($node->iteration_id);
                    if ($iteration) {
                        $extends = json_decode($iteration->extends, true);
                        if (isset($iterationUpdateData['start_time'])) {

                            $extends['actual_iteration_cycle'][0] = $iterationUpdateData['start_time'];
                        }
                        if (isset($iterationUpdateData['end_time'])) {
                            $extends['actual_iteration_cycle'][1] = $iterationUpdateData['end_time'];
                        }
                        $iterationUpdateData['extends'] = json_encode($extends, JSON_UNESCAPED_UNICODE);
                        $iteration->save($iterationUpdateData);
                    }
                }
            }


            $this->commit();
            return true;
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 审批拒绝后的处理
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return bool
     * @throws Exception
     */
    public function handleRejected(GeneralApprovalsModel $model): bool
    {
        try {
            $this->startTrans();

            // TODO: 实现迭代节点审批拒绝后的处理逻辑
            // 例如：发送通知给申请人

            $this->commit();
            return true;
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 获取审批人列表
     * @param  int  $id  主键ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getApprovers(int $id): array
    {
        $model = GeneralApprovalsModel::findById($id);
        // 获取迭代节点信息
        $iterationNode = IterationProcessNodeModel::findById($model->business_model_id);
        if ( ! $iterationNode) {
            return [];
        }
        // 获取迭代信息
        $iteration = IterationModel::findById($iterationNode->iteration_id);
        if ( ! $iteration) {
            return [];
        }

        $approvers = [];
        $extends = json_decode($iteration->extends ?? "{}", true);

        // 获取迭代负责人信息
        if ( ! empty($extends['iteration_leader'])) {
            $approvers = $this->getUserInfo($extends['iteration_leader']);
        }

        // 获取项目经理信息
        if ( ! empty($extends['project_manager'])) {
            $approvers = array_merge($approvers, $this->getUserInfo($extends['project_manager']));
        } else {
            $users = (new ProjectUserLogic())->selectorListQuery($iteration->project_id, '', '', [EnumModel::PROJECT_MANAGER]);
            if ($users) {
                $approvers = array_merge($approvers, $this->getUserInfo(array_column($users, 'user_id')));
            }
        }

        return array_unique($approvers, SORT_REGULAR);
    }

    /**
     * 验证发起人权限
     * @param  int  $userId           用户ID
     * @param  int  $businessModelId  业务模型ID
     * @return bool
     */
    public function validateInitiator(int $userId, int $businessModelId): bool
    {
        // 获取迭代节点信息
        $iterationNode = IterationProcessNodeModel::findById($businessModelId);
        if ( ! $iterationNode) {
            return false;
        }

        return in_array($userId, $iterationNode['node_data']['node_manger']['users'] ?? []);

    }
}
