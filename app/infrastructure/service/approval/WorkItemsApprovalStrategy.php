<?php
/**
 * 需求变更审批策略
 */

declare (strict_types=1);

namespace app\infrastructure\service\approval;

use app\infrastructure\model\GeneralApprovalsModel;
use app\work_items\logic\WorkItemsChangeRecordsLogic;
use think\Exception;

/**
 * 需求变更审批策略
 * 处理需求变更的审批
 */
class WorkItemsApprovalStrategy extends AbstractApprovalStrategy
{
    /**
     * 获取业务类型
     * @return int
     */
    public function getType(): int
    {
        return GeneralApprovalsModel::TYPE_WORK_ITEMS;
    }


    /**
     * 审批通过后的处理
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return bool
     * @throws Exception
     */
    public function handleApproved(GeneralApprovalsModel $model): bool
    {
        try {
            $this->startTrans();

            // 获取审批内容
//            $content = $model->content;
//            if (!isset($content['work_items_id']) || !isset($content['changes'])) {
//                throw new Exception('审批内容格式错误');
//            }

//            $workItemsId = $content['work_items_id'];
//            $changes = $content['changes'];

            // TODO: 实现需求变更审批通过后的处理逻辑
            // 例如：更新需求信息

            // 记录需求变更信息
            (new WorkItemsChangeRecordsLogic())->create($model);

            $this->commit();
            return true;
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 审批拒绝后的处理
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return bool
     * @throws Exception
     */
    public function handleRejected(GeneralApprovalsModel $model): bool
    {
        try {
            $this->startTrans();

            // TODO: 实现需求变更审批拒绝后的处理逻辑
            // 例如：发送通知给申请人

            $this->commit();
            return true;
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }


    /**
     * 获取审批人列表
     * @param  int  $id  主键ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getApprovers(int $id): array
    {
        $model = GeneralApprovalsModel::findById($id);

        $approvers = [];
        $content = $model->content ?? [];
        // 获取项目经理信息
        if (!empty($content['handler'])) {
            $approvers = array_merge($this->getUserInfo($content['handler']));
        }

        return array_unique($approvers, SORT_REGULAR);
    }
} 