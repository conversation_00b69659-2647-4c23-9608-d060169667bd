<?php
/**
 * 审批策略抽象类
 */

declare (strict_types=1);

namespace app\infrastructure\service\approval;

use app\infrastructure\model\GeneralApprovalsModel;
use app\infrastructure\service\ApprovalStrategy;
use app\project\model\ProjectUserModel;
use utils\DBTransaction;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 审批策略抽象类
 * 实现一些通用方法
 */
abstract class AbstractApprovalStrategy implements ApprovalStrategy
{
    /**
     * 获取业务类型
     * @return int
     */
    abstract public function getType(): int;


    /**
     * 开始事务
     * @return void
     */
    protected function startTrans(): void
    {
        DBTransaction::begin();
    }

    /**
     * 提交事务
     * @return void
     */
    protected function commit(): void
    {
        DBTransaction::commit();
    }

    /**
     * 回滚事务
     * @return void
     */
    protected function rollback(): void
    {
        DBTransaction::rollback();
    }


    /**
     * 审批通过后的处理
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return bool
     */
    abstract public function handleApproved(GeneralApprovalsModel $model): bool;

    /**
     * 审批拒绝后的处理
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return bool
     */
    abstract public function handleRejected(GeneralApprovalsModel $model): bool;

    /**
     * 获取审批人列表
     * @param  int  $id  主键ID
     * @return array 审批人信息数组
     */
    abstract public function getApprovers(int $id): array;

    /**
     * 根据审批状态获取审批人
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return array 审批人信息数组
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getApproversByAuditStatus(GeneralApprovalsModel $model): array
    {
        // 如果审批已通过或已拒绝，直接返回修改人作为审批人
        if ($model->is_audit == GeneralApprovalsModel::AUDIT_AGREE
            || $model->is_audit == GeneralApprovalsModel::AUDIT_REJECT
        ) {
            // 返回创建人信息

            return $this->getUserInfo($model->update_by);
        }

        // 对于发起状态的审批，调用子类实现的getApprovers方法
        return $this->getApprovers($model->general_approvals_id);
    }


    /**
     * 验证审批人权限
     * @param  int  $userId  用户ID
     * @param  int  $id      主键id
     * @return bool
     */
    public function validateApprover(int $userId, int $id): bool
    {
//        验证用户是否是项目管理员或迭代负责人
        $users = $this->getApprovers($id);
        return in_array($userId, array_column($users, 'user_id'));
    }

    /**
     * 获取用户信息
     * @param  int|array  $userIds  用户ID或用户ID数组
     * @return array 用户信息数组
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getUserInfo($userIds): array
    {
        // 如果是单个用户ID,转换为数组
        if ( ! is_array($userIds)) {
            $userIds = [$userIds];
        }

        // 批量获取用户信息
        $users = ProjectUserModel::findUsersByIds($userIds);
        $userMap = [];
        foreach ($users as $user) {
            $userMap[$user->user_id] = [
                'user_id'   => $user->user_id,
                'user_name' => $user->user_name,
            ];
        }

        $result = [];
        foreach ($userIds as $userId) {
            $result[] = $userMap[$userId] ?? [
                'user_id'   => $userId,
                'user_name' => '未知用户',
            ];
        }


        // 如果原始输入是单个ID,返回单个用户信息
        return $result;
    }

    /**
     * 验证发起人权限
     * @param  int  $userId           用户ID
     * @param  int  $businessModelId  业务模型ID
     * @return bool
     */
    public function validateInitiator(int $userId, int $businessModelId): bool
    {
        // 默认实现：任何用户都可以发起审批
        // 子类可以根据具体业务需求重写此方法
        return true;
    }
} 