<?php
declare(strict_types=1);

namespace app\infrastructure\service\approval;

use app\infrastructure\model\GeneralApprovalsModel;
use app\project\logic\IterationProcessNodeLogic;
use app\project\logic\IterationProcessNodeAuditLogic; // 用于操作旧审批表
use app\project\model\IterationProcessNodeModel;
use app\project\model\IterationModel;
use app\project\logic\ProjectUserLogic;
use app\infrastructure\model\EnumModel;
use utils\Ctx;
use basic\BaseLogic; // 用于 isNodeButton

class SyncedIterationNodeApprovalStrategy extends AbstractApprovalStrategy
{
    public function getType(): int
    {
        return GeneralApprovalsModel::TYPE_ITERATION_NODE_SYNCED;
    }

    /**
     * 在通用审批记录创建（状态为INITIATE）之后，执行与旧审批“发起”相关的操作。
     * 此方法应由 GeneralApprovalsLogic 在创建 TYPE_ITERATION_NODE_SYNCED 审批后调用。
     * @param GeneralApprovalsModel $model 通用审批模型实例
     * @return void
     * @throws \Throwable
     */
    public function postGeneralApprovalCreationActions(GeneralApprovalsModel $model): void
    {
        $iterationProcessNodeId = $model->business_model_id;
        $content = $model->content ?? [];
        // 确保从 content 中能正确获取 remark, projectId 等必要信息
        $remark = $content['remark'] ?? ''; 
        // $projectId = $content['project_id'] ?? null; // 如果需要 projectId

        // 1. 在旧审批表 t_iteration_process_node_audit 中创建“发起”记录
        IterationProcessNodeAuditLogic::initiate($iterationProcessNodeId, $remark);

        // 2. 更新迭代节点的 is_audit_status 为审批中
        IterationProcessNodeLogic::updateNode($iterationProcessNodeId, [
            'is_audit_status' => IterationProcessNodeModel::IS_AUDIT_STATUS_PROCEED,
        ]);
    }

    public function handleApproved(GeneralApprovalsModel $model): bool
    {
        $iterationProcessNodeId = $model->business_model_id;
        $content = $model->content ?? [];
        $remark = $content['approve_remark'] ?? $content['remark'] ?? '';

        $this->ensureOldInitiateRecordExists($model);

        // 1. 在旧审批表 t_iteration_process_node_audit 中创建“通过”记录
        IterationProcessNodeAuditLogic::pass($iterationProcessNodeId, $remark);

        // 2. 更新迭代节点的 is_audit_status 为通过，并根据需要更新时间等
        // 注意：这里的更新逻辑需要严格对齐旧的 IterationCatalogLogic::approve 方法在通过时的行为
        // 可能需要从 $model->content 获取更多用于更新节点时间和状态的参数
        IterationProcessNodeLogic::updateNode($iterationProcessNodeId, [
            'is_audit_status' => IterationProcessNodeModel::IS_AUDIT_STATUS_PASS,
            // 其他需要更新的字段，例如：
            // 'start_time' => $content['actual_start_time'] ?? null, // 示例
            // 'end_time' => $content['actual_end_time'] ?? null,     // 示例
        ]);
        
        // 如果旧逻辑中审批通过会触发其他操作（如更新迭代整体状态），也应在此处触发

        return true; 
    }

    public function handleRejected(GeneralApprovalsModel $model): bool
    {
        $iterationProcessNodeId = $model->business_model_id;
        $content = $model->content ?? [];
        $remark = $content['approve_remark'] ?? $content['remark'] ?? $model->content['content'] ?? '';


        $this->ensureOldInitiateRecordExists($model);

        // 1. 在旧审批表 t_iteration_process_node_audit 中创建“拒绝”记录
        IterationProcessNodeAuditLogic::turnDown($iterationProcessNodeId, $remark);

        // 2. 更新迭代节点的 is_audit_status 为拒绝
        IterationProcessNodeLogic::updateNode($iterationProcessNodeId, [
            'is_audit_status' => IterationProcessNodeModel::IS_AUDIT_STATUS_TURN_DOWN,
        ]);

        // 3. 如果旧逻辑包含，调用 IterationProcessNodeTipLogic::turnDown()
        \app\project\logic\IterationProcessNodeTipLogic::turnDown($iterationProcessNodeId); // 确认此类和方法存在且适用 (基于旧 IterationCatalogLogic::approve)

        return true;
    }
    
    /**
     * 确保旧的审批发起记录存在，特别是在发起即审批通过的场景。
     */
    private function ensureOldInitiateRecordExists(GeneralApprovalsModel $generalApprovalModel): void
    {
        $iterationProcessNodeId = $generalApprovalModel->business_model_id;
        
        $existingAudit = \app\project\model\IterationProcessNodeAuditModel::where([
            'iteration_process_node_id' => $iterationProcessNodeId,
            'is_audit'                  => \app\project\model\IterationProcessNodeAuditModel::INITIATE,
        ])->order('iteration_process_node_audit_id', 'desc')->find();

        if (!$existingAudit) {
             $content = $generalApprovalModel->content ?? [];
             $remark = $content['remark'] ?? ($generalApprovalModel->content['content'] ?? '发起审批');
             IterationProcessNodeAuditLogic::initiate($iterationProcessNodeId, $remark);
             
             $node = IterationProcessNodeModel::findById($iterationProcessNodeId);
             if ($node && $node->is_audit_status != IterationProcessNodeModel::IS_AUDIT_STATUS_PROCEED) {
                 IterationProcessNodeLogic::updateNode($iterationProcessNodeId, [
                    'is_audit_status' => IterationProcessNodeModel::IS_AUDIT_STATUS_PROCEED,
                 ]);
             }
        }
    }

    public function getApprovers(int $id): array
    {
        $approvalModel = GeneralApprovalsModel::findById($id);
        if (!$approvalModel) return [];
        $iterationProcessNodeId = $approvalModel->business_model_id;
        $nodeModel = IterationProcessNodeModel::findById($iterationProcessNodeId);
        if (!$nodeModel) return [];
        
        // 从通用审批的 content 中获取 project_id，如果旧接口调用时已存入
        $content = $approvalModel->content ?? [];
        $projectId = $content['project_id'] ?? null;

        if (!$projectId) {
            // 如果 content 中没有 project_id，尝试从节点关联的迭代中获取
            $iterationModel = IterationModel::findById($nodeModel->iteration_id);
            if (!$iterationModel) return [];
            $projectId = (int)$iterationModel->project_id;
        } else {
            $projectId = (int)$projectId;
        }

        $auditRoles = $nodeModel->node_data['node_setting']['audit']['roles'] ?? [];
        $nodeUsers = $nodeModel->node_data['node_setting']['audit']['users'] ?? [];
        $defaultRoles = [EnumModel::PROJECT_MANAGER, EnumModel::ITERATION_LEADER];
        $effectiveRoles = !empty($auditRoles) ? $auditRoles : $defaultRoles;

        $allowedUserIds = [];
        if (!empty($nodeUsers)) {
            $allowedUserIds = array_merge($allowedUserIds, $nodeUsers);
        }
        $roleUsers = (new ProjectUserLogic())->selectorListQuery($projectId, '', '', $effectiveRoles);
        if ($roleUsers) {
            $allowedUserIds = array_merge($allowedUserIds, array_column($roleUsers, 'user_id'));
        }
        
        $approvers = [];
        if(!empty($allowedUserIds)){
            $uniqueUserIds = array_unique($allowedUserIds);
            // 调用父类的 getUserInfo 来格式化用户信息
            $approvers = parent::getUserInfo($uniqueUserIds);
        }
        return $approvers;
    }

    public function validateInitiator(int $userId, int $businessModelId): bool
    {
        // $businessModelId is $iterationProcessNodeId
        $iterationNodeModel = IterationProcessNodeModel::findById($businessModelId);
        if ( ! $iterationNodeModel) {
            // It's better to throw specific exceptions from strategy if GeneralApprovalsLogic can catch them
            // For now, returning false will lead to a generic "no permission" or "validation failed" in GeneralApprovalsLogic
            // throw new \exception\NotFoundException('迭代节点不存在');
            return false;
        }

        if ($iterationNodeModel->status < IterationProcessNodeModel::STATUS_UNDER_WAY) {
            // throw new \exception\BusinessException('请先开启节点！');
            return false;
        }

        $error_msg = '';
        // 获取未完成的任务id集合
        // Ensure WorkItemsLogic is use'd or use full namespace
        $cntIds = \app\work_items\logic\WorkItemsLogic::getIterationNodeTaskIdByNodeId($businessModelId, false, true);
        if ($cntIds) {
            $error_msg .= '任务';
        }

        if ($iterationNodeModel->is_auto_status == IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN) {
            $error_msg .= ($error_msg ? '、' : '').'自动化检验';
        }

        if ($error_msg) {
            // throw new \exception\BusinessException($error_msg.' 未完成');
            // Or set a more specific error message that GeneralApprovalsLogic can surface
            return false;
        }

        // Permission check (original logic)
        $iterationModel = IterationModel::findById($iterationNodeModel->iteration_id);
        if (!$iterationModel) return false; // Should not happen if iterationNodeModel exists
        $projectId = (int)$iterationModel->project_id;
        
        $nodeMangerUsers = $iterationNodeModel->node_data['node_manger']['users'] ?? [];
        // BaseLogic::isNodeButton 默认角色是项目经理和迭代leader
        if (!BaseLogic::isNodeButton($projectId, $nodeMangerUsers, [EnumModel::PROJECT_MANAGER, EnumModel::ITERATION_LEADER], $userId)) {
             // Pass Ctx::$userId to isNodeButton if it's not already implicitly using it.
             // Assuming isNodeButton needs the current user ID passed explicitly or uses Ctx internally.
             // The BaseLogic::isNodeButton already uses Ctx::$userId internally.
            return false;
        }
        
        return true;
    }
}