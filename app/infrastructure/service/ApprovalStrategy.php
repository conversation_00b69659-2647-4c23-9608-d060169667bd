<?php
/**
 * 审批策略接口
 */

declare (strict_types=1);

namespace app\infrastructure\service;

use app\infrastructure\model\GeneralApprovalsModel;

/**
 * 审批策略接口
 * 不同业务类型的审批逻辑实现此接口
 */
interface ApprovalStrategy
{
    /**
     * 获取业务类型
     * @return int
     */
    public function getType(): int;

    /**
     * 验证审批人权限
     * @param int $userId 用户ID
     * @param int $id 主键id
     * @return bool
     */
    public function validateApprover(int $userId, int $id): bool;

    /**
     * 审批通过后的处理
     * @param GeneralApprovalsModel $model 审批模型
     * @return bool
     */
    public function handleApproved(GeneralApprovalsModel $model): bool;

    /**
     * 审批拒绝后的处理
     * @param GeneralApprovalsModel $model 审批模型
     * @return bool
     */
    public function handleRejected(GeneralApprovalsModel $model): bool;

    /**
     * 获取审批人列表
     * @param int $id 主键ID
     * @return array 审批人信息数组
     */
    public function getApprovers(int $id): array;

    /**
     * 验证发起人权限
     * @param int $userId 用户ID
     * @param int $businessModelId 业务模型ID
     * @return bool
     */
    public function validateInitiator(int $userId, int $businessModelId): bool;

    /**
     * 根据审批状态获取审批人
     * @param GeneralApprovalsModel $model 审批模型
     * @return array 审批人信息数组
     */
    public function getApproversByAuditStatus(GeneralApprovalsModel $model): array;
}