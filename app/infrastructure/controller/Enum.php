<?php
/**
 * Desc 示例 - 控制器
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);


namespace app\infrastructure\controller;

use app\infrastructure\logic\EnumLogic;
use basic\BaseController;
use resp\Result;
use think\App;

class Enum extends BaseController
{
    private EnumLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new EnumLogic();
    }

    /**
     * 获取
     * @param $code
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 上午10:15
     */
    public function get($code)
    {
        $res = $this->logic->getList($code);

        return Result::success($res);
    }

    /**
     * 创建枚举
     * @return \think\response\Json
     */
    public function create()
    {
        $data = $this->request->post();
        $result = $this->logic->create($data);
        return Result::success($result);
    }

    /**
     * 更新枚举
     * @return \think\response\Json
     */
    public function update()
    {
        $id = $this->request->post('enum_id');
        $data = $this->request->post();
        $result = $this->logic->update($id, $data);
        return Result::success($result);
    }

    /**
     * 删除枚举
     * @return \think\response\Json
     */
    public function delete()
    {
        $id = $this->request->post('enum_id');
        $result = $this->logic->delete($id);
        return Result::success($result);
    }

    /**
     * 分页查询枚举列表
     * @return \think\response\Json
     */
    public function pageQuery()
    {
        $params = $this->request->get();

        $result = $this->logic->pageQuery($params);
        return Result::success($result);
    }
}
