<?php
/**
 * Desc 示例 - 控制器
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);


namespace app\infrastructure\controller;

use app\infrastructure\logic\CustomerTableConfigLogic;
use basic\BaseController;
use resp\Result;
use think\App;
use think\Request;

class CustomerTableConfig extends BaseController
{
    private CustomerTableConfigLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new CustomerTableConfigLogic();
    }


    /**
     * 保存
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 上午10:15
     */
    public function save(Request $request)
    {
        $param = $request->param([
            'table_unique',
            'user_fields',
        ]);
        $model = $this->logic->save($param);

        return Result::success($model->toDetail());
    }


    /**
     * 获取
     * @param $tableUnique
     * @param $moduleId
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/7/8 上午10:15
     */
    public function get(Request $request)
    {
        $params = $request->post([
            'table_unique',
            'module_id',
            'project_id',
        ]);

//        $this->setCache();
        $res = $this->logic->get($params);

        return Result::success($res);
    }

    private function setCache()
    {
        header('Cache-Control: max-age=60'); // 缓存60秒
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 60) . ' GMT'); // 设置到期时间

        $currentTime = '1725934880';
        // 使用 ETag 进行缓存验证
        $etag = md5($currentTime);
        // 设置 ETag 头部
        header('ETag: "' . $etag . '"');

        if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && trim(ltrim($_SERVER['HTTP_IF_NONE_MATCH'], "W/"), '"') == $etag) {
            // 如果 ETag 匹配，则返回 304 Not Modified
            header('HTTP/1.1 304 Not Modified');
            exit;
        }
    }

}
