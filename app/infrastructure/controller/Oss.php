<?php

namespace app\infrastructure\controller;

use app\Request;
use resp\Result;
use think\facade\Env;
use utils\OssClient;

class Oss
{
    /**
     * 获取oss 服务端签名
     * @return \think\response\Json
     */
    public function policy()
    {
        $data = (new OssClient())->getUploadPolicy(strtolower(Env::get('environment')) . '/' . date('Y-m-d'));

        return Result::success($data);
    }

    public function sign(Request $request)
    {
        $url = (new OssClient())->accessSign($request->get('path'));
        header("Location: $url", true, 302);
        exit;
    }

}
