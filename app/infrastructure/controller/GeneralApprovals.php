<?php
/**
 * 通用审批控制器
 */

declare (strict_types=1);

namespace app\infrastructure\controller;

use app\infrastructure\logic\GeneralApprovalsLogic;
use app\infrastructure\validate\GeneralApprovalsValidate;
use basic\BaseController;
use exception\NotFoundException;
use resp\Result;
use think\App;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\ValidateException;

class GeneralApprovals extends BaseController
{
    /**
     * 通用审批逻辑
     * @var GeneralApprovalsLogic
     */
    private $generalApprovalsLogic;

    /**
     * 构造函数
     * @param  App  $app
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->generalApprovalsLogic = new GeneralApprovalsLogic();
    }

    /**
     * 获取通用审批详情
     * @return \think\Response
     */
    public function getDetail()
    {
        $id = $this->request->get('id');
        
        // 验证参数
        validate(GeneralApprovalsValidate::class)
            ->scene('detail')
            ->check(['general_approvals_id' => $id]);

        // 获取详情
        $data = $this->generalApprovalsLogic->getDetail(intval($id));
        return Result::success($data);
    }

    /**
     * 根据业务模型ID和业务类型获取通用审批
     * @return \think\Response
     */
    public function getByBusinessModelIdAndType()
    {
        $data = $this->request->get();

        // 验证参数
        validate(GeneralApprovalsValidate::class)
            ->scene('byBusinessModel')
            ->check($data);

        // 获取详情
        $result = $this->generalApprovalsLogic->getByBusinessModelIdAndType(
            intval($data['business_model_id']),
            intval($data['type'])
        );
        return Result::success($result);
    }

    /**
     * 分页查询通用审批
     * @return \think\Response
     */
    public function pageQuery()
    {
        $params = $this->request->get();
        $data = $this->generalApprovalsLogic->pageQuery($params);
        return Result::success($data);
    }

    /**
     * 创建通用审批
     * @return \think\Response
     */
    public function create()
    {
        $data = $this->request->post();
        $model = $this->generalApprovalsLogic->create($data);
        return Result::success($model);
    }

    /**
     * 审批
     * @return \think\Response
     */
    public function approve()
    {
        $data = $this->request->post();

        // 验证参数
        validate(GeneralApprovalsValidate::class)
            ->scene('approve')
            ->check($data);

        $id = intval($data['general_approvals_id']);
        $isAudit = intval($data['is_audit']);
        $content = isset($data['content']) ? $data['content'] : [];

        $result = $this->generalApprovalsLogic->approve($id, $isAudit, $content);
        return Result::success($result);
    }

    /**
     * 删除通用审批
     * @return \think\Response
     */
    public function delete()
    {
        $data = $this->request->post();

        // 验证参数
        validate(GeneralApprovalsValidate::class)
            ->scene('delete')
            ->check($data);

        $id = intval($data['general_approvals_id']);
        $result = $this->generalApprovalsLogic->delete($id);
        return Result::success($result);
    }
} 