<?php
/**
 * Desc 权限相关
 * User Long
 * Date 2024/12/12
 */

namespace app\infrastructure\controller;

use app\infrastructure\logic\PermissionsLogic;
use resp\Result;

class Permissions
{
    private PermissionsLogic $logic;

    public function __construct()
    {
        $this->logic = new PermissionsLogic();
    }

    /**
     * 获取用户 - 拥有权限内的前端编码
     * @return \think\response\Json
     * User Long
     * Date 2024/12/12
     */
    public function permissions()
    {
        return Result::success(PermissionsLogic::getVuePermissions());
    }
}
