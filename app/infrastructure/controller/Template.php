<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/7/5 上午10:33
 */

namespace app\infrastructure\controller;

use app\infrastructure\logic\TemplateLogic;
use basic\BaseController;
use resp\Result;
use think\App;
use think\Request;
use think\response\Json;

class Template extends BaseController
{
    private $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->logic = new TemplateLogic();
    }


    /**
     * 新增
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/7/8 下午5:53
     */
    public function create(Request $request)
    {
        $param = $request->post([
            'module_id',
            'template_name',
            'template_content',
            'project_id',
        ]);
        $model = $this->logic->create($param);

        return Result::success($model);
    }

    /**
     * 删除
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/7/8 下午5:53
     */
    public function delete(Request $request)
    {
        $this->logic->delete($request->post('id'));

        return Result::success();
    }

    /**
     * 修改
     * @param  Request  $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/7/8 下午5:53
     */
    public function update(Request $request)
    {
        $param = $request->param([
            'id',
            'template_name',
            'template_content',
        ]);
        $model = $this->logic->update($param);

        return Result::success($model);
    }


    /**
     * 详情
     * @param $id
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/7/8 下午5:53
     */
    public function detail($id)
    {
        $res = $this->logic->detail($id);

        return Result::success($res);
    }

    /**
     * 默认模板详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/7/10 下午3:10
     */
    public function defaultTempDetail(Request $request)
    {
        $params = $request->get([
            'module_id',
            'project_id',
        ]);
        $res = $this->logic->defaultTempDetail($params);

        return Result::success($res);
    }

    /**
     * 分页查询
     * @param  Request  $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/7/8 下午5:54
     */
    public function pageQuery(Request $request)
    {
        $params = $request->get([
            'module_id',
            'project_id',
        ]);

        $data = $this->logic->pageQuery($params);

        return Result::success($data);
    }

    /**
     * 复制
     * @param $id
     * @param $project_id
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/7/8 下午5:54
     */
    public function copy($id, $project_id = 0)
    {
        $res = $this->logic->copy($id, $project_id);

        return Result::success($res);
    }

    /**
     * 启用
     * @param $id
     * @return \think\response\Json
     * <AUTHOR>
     * @date   2024/7/8 下午5:54
     */
    public function enable($id)
    {
        $res = $this->logic->enable($id);

        return Result::success($res);
    }

    /**
     * 更新模板名称
     * @return Json
     * User Long
     * Date 2025/4/14
     */
    public function rename()
    {
        $id = $this->request->post('id/d');
        $templateName = $this->request->post('template_name/s');

        $this->logic->rename($id, $templateName);

        return Result::success();
    }
}
