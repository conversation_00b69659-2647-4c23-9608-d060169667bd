<?php
/**
 * Desc 示例 - 控制器
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);


namespace app\infrastructure\controller;

use app\infrastructure\logic\FieldConfigLogic;
use basic\BaseController;
use resp\Result;
use think\App;
use think\Request;
use think\response\Json;

class FieldConfig extends BaseController
{
    private $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->logic = new FieldConfigLogic();
    }

    /**
     * 新增
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 上午10:14
     */
    public function create(Request $request)
    {
        $param = $request->param([
            'field_label',
            'field_component',
            'remark',
            'module_id',
            'field_sort',
            'field_type',
            'project_id',
        ]);
        $model = $this->logic->create($param);

        return Result::success($model);
    }

    /**
     * 删除
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 上午10:14
     */
    public function delete(Request $request)
    {
        $this->logic->delete($request->post('field_id'));

        return Result::success();
    }

    /**
     * 修改
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 上午10:14
     */
    public function update(Request $request)
    {
        $param = $request->param([
            'field_id',
            'field_label',
            'field_component',
            'remark',
            'field_sort',
            'field_type',
        ]);
        $model = $this->logic->update($param);

        return Result::success($model->toDetail());
    }


    /**
     * 详情
     * @param $fieldId
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 上午10:14
     */
    public function detail($fieldId)
    {
        $res = $this->logic->detail($fieldId);

        return Result::success($res);
    }

    /**
     * 分页查询
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 上午10:14
     */
    public function pageQuery(Request $request)
    {
        $param = $request->get([
            'module_id',
            'project_id',
        ]);
        $data = $this->logic->pageQuery($param);

        return Result::success($data);
    }

    /**
     * 列表无分页&按照field_type分组
     * @param Request $request
     *
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/10 上午10:22
     */
    public function getListGroupByModuleId(Request $request)
    {
        $params = $request->get([
            'module_id',
            'allow_setting',
            'project_id',
            'isSearch',
        ]);
        $data = $this->logic::getListByModuleIdGroupByFieldType($params);

        return Result::success($data);
    }

    public function getListBySubKey(Request $request)
    {
        $params = $request->get([
            'project_id',
            'sub_key',
            'is_search',
        ]);
        $data = $this->logic::getListBySubKeyGroupByFieldType($params);

        return Result::success($data);
    }

    /**
     * 根据fieldName获取
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/15 上午10:50
     */
    public function getListByFieldName(Request $request)
    {
        $param = $request->param([
            'module_id',
            'project_id',
            'field_name',
        ]);
        $data = $this->logic->getListByFieldName($param);

        return Result::success($data);
    }

    /**
     * 获取指定模块的固定字段
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/19 下午2:16
     */
    public function getFixedList(Request $request)
    {
        $data = $this->logic->getFixedList($request->get('module_id'));

        return Result::success($data);
    }

    /**
     * 获取需求、任务、缺陷、以及其中的部分公共字段的汇总
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/11/7 14:46
     */
    public function getDemandTaskFlawSummaryFieldList(Request $request)
    {
        $params = $request->post([
            'allow_setting',
            'project_id',
            'isSearch',
        ]);

        $data = $this->logic::getDemandTaskFlawSummaryFieldList($params);

        return Result::success($data);
    }

    /**
     * 需求、任务、缺陷字段合并去重
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/11/7 14:46
     */
    public function getDemandTaskFlawSummaryDeduplicationFieldList(Request $request)
    {
        $params = $request->post([
            'allow_setting',
            'project_id',
            'isSearch',
        ]);
        $data = $this->logic::getDemandTaskFlawSummaryDeduplicationFieldList($params);

        return Result::success($data);
    }

    /**
     * 获取复制至项目/模板数据
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/4/12
     */
    public function getCopyToSelector()
    {
        $fieldId = $this->request->post('field_id/d');

        return Result::success($this->logic->getCopyToSelector($fieldId));
    }

    /**
     * 自定义字段复制至项目/模板
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/4/12
     */
    public function fieldCopyTo()
    {
        $fieldId = $this->request->post('field_id/d');
        $copyToIds = $this->request->post('copy_to_ids/a');
        $isTemplate = $this->request->post('is_template/d');

        return Result::success($this->logic->categoryCopyTo($fieldId, $copyToIds, $isTemplate));
    }

    /**
     * 更新字段名称
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/4/14
     */
    public function rename(): Json
    {
        $fieldId = $this->request->post('field_id/d');
        $fieldLabel = $this->request->post('field_label/s');

        $this->logic->rename($fieldId, $fieldLabel);

        return Result::success();
    }
}
