<?php
/**
 * Desc URL参数存储 - 控制器
 * User
 * Date <?= date('Y/m/d') ?>
 */

declare (strict_types = 1);

namespace app\infrastructure\controller;

use app\infrastructure\logic\UrlParamsStorageLogic;
use app\infrastructure\validate\UrlParamsStorageValidate;
use basic\BaseController;
use resp\Result;
use resp\StatusCode;
use think\App;
use think\Exception;
use think\Request;
use think\response\Json;

class UrlParamsStorage extends BaseController
{
    /**
     * @var UrlParamsStorageLogic
     */
    protected $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new UrlParamsStorageLogic();
    }

    /**
     * 根据唯一标识获取URL参数
     * @param  int  $id  唯一标识
     * @return Json
     * @throws Exception
     */
    public function get($id = 0)
    {
        // 参数验证
        validate(UrlParamsStorageValidate::class)
            ->scene('get')
            ->check(['url_params_id' => $id]);

        // 调用逻辑层获取参数
        $result = $this->logic->getParams((int)$id);

        // 返回参数数据
        return Result::success($result);
    }
}
