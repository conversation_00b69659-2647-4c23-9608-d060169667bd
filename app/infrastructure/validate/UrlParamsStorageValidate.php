<?php
/**
 * Desc URL参数存储 - 验证器
 * User
 * Date <?= date('Y/m/d') ?>
 */

namespace app\infrastructure\validate;

use basic\BaseValidate;

class UrlParamsStorageValidate extends BaseValidate
{
    protected $rule = [
        'url_params_id|参数ID' => 'require|integer|gt:0',
        'params_data|参数数据' => 'require',
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [];

    /**
     * 获取参数场景
     * @return UrlParamsStorageValidate
     */
    public function sceneGet()
    {
        return $this->only(['url_params_id']);
    }

    /**
     * 保存参数场景
     * @return UrlParamsStorageValidate
     */
    public function sceneSave()
    {
        return $this->only(['params_data']);
    }
}
