<?php

declare (strict_types=1);

namespace app\infrastructure\validate;

use think\Validate;

class EnumValidate extends Validate
{
    protected $rule = [
        'enum_code'  => 'require|max:20',
        'enum_name'  => 'require|max:20',
        'enum_value' => 'max:200',
        'enum_type'  => 'require|integer|in:1,2',
        'parent_id'  => 'require|integer|egt:0',
    ];

    protected $message = [
        'enum_code.require'  => '枚举编码不能为空',
        'enum_code.max'      => '枚举编码最大长度为20',
        'enum_name.require'  => '枚举名称不能为空',
        'enum_name.max'      => '枚举名称最大长度为20',
        'enum_value.max'     => '枚举值最大长度为200',
        'enum_type.require'  => '枚举类型不能为空',
        'enum_type.integer'  => '枚举类型必须为整数',
        'enum_type.in'       => '枚举类型值无效',
        'parent_id.require'  => '父级ID不能为空',
        'parent_id.integer'  => '父级ID必须为整数',
        'parent_id.egt'      => '父级ID必须大于等于0',
    ];
} 