<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\infrastructure\validate;

use app\infrastructure\logic\FieldConfigLogic;
use app\infrastructure\model\FieldConfigModel;
use app\project\logic\ProjectInfoLogic;
use basic\BaseValidate;
use exception\NotFoundException;
use exception\ParamsException;

class FieldValidate extends BaseValidate
{
    protected $rule
        = [
            'field_id'        => 'require',
            'field_label'     => 'require|max:50|checkNameUnique',
            'field_component' => 'require',
            'remark'          => 'max:1000',
            'module_id'       => 'require|integer',
            'fieldNameList'   => 'require|array',
            'project_id'      => 'requireCallback:checkHasProjectId',

            'sub_key|子集标识' => 'require',
            //            'field_sort'      => '',
        ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message
        = [
            'field_id.require'            => 'field_id 必须传值',
            'field_label.require'         => '字段名称 必须传值',
            'field_label.max'             => '字段名称 最长支持50字符',
            'field_label.checkNameUnique' => '字段名称 存在同名字段',
            'field_component.require'     => '组件内容 必须传值',
            'module_id.require'           => '归属模块 必须传值',
            'remark.max'                  => '备注 最长支持1000字符',
        ];

    protected $scene
        = [
            'create'                            => ['field_label', 'field_component', 'module_id', 'remark', 'project_id'],
            'update'                            => ['field_id', 'field_label', 'field_component', 'remark'],
            'getListByFieldName'                => ['fieldNameList'],
            'getListByModuleIdGroupByFieldType' => ['module_id', 'project_id'],
            'getListBySubKeyGroupByFieldType'   => ['sub_key'],
        ];

    /**
     * 验证名称是否唯一
     * @param $value
     * @param $rule
     * @param $data
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/7/8 上午10:16
     */
    protected function checkNameUnique($value, $rule, $data = [])
    {
        if ( ! empty($data['field_id'])) {
            $model = FieldConfigModel::findById($data['field_id']);
            if ( ! $model) {
                throw new NotFoundException();
            }
            $data['module_id'] = $model['module_id'];
            $data['project_id'] = $model['project_id'];
        }


        $model = FieldConfigModel::findModuleFieldLabel($data['module_id'] ?? '', $data['field_label'], $data['project_id'] ?? '');
        // 校验名称是否与模板相同
        (new FieldConfigLogic())->validateProjectFieldData(ProjectInfoLogic::getProjectData($data['project_id'] ?? ''), $data['field_label']);

        if ( ! empty($data['field_id'])) {
            return ! $model || ($model->field_id == $data['field_id']);
        } else {
            return ! $model;
        }
    }

    /**
     * 检查所属项目id
     * @param $value
     * @param $data
     * @return false
     * <AUTHOR>
     * @date   2024/8/22 15:21
     */
    protected function checkHasProjectId($value, $data = [])
    {
        if ( ! FieldConfigModel::isRequireProjectId($data['module_id'])) {
            return false;
        }
        if (empty($data['project_id'])) {
            throw new ParamsException('项目id必填(当所属模块是4-迭代，5-需求，6-任务，7-缺陷时，8-测试用例，9-测试计划)');
        }

        return false;
    }

}
