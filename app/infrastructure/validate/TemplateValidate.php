<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\infrastructure\validate;

use app\infrastructure\logic\TemplateLogic;
use app\infrastructure\model\TemplateModel;
use app\project\logic\ProjectInfoLogic;
use basic\BaseValidate;
use exception\NotFoundException;
use exception\ParamsException;

class TemplateValidate extends BaseValidate
{
    protected $rule = [
        'id' => 'require|integer',
        'module_id|模块id' => 'require',
        'template_name' => 'require|max:50|checkNameUnique',
        'template_content' => 'require',
        'project_id' => 'requireCallback:checkHasProjectId|number',
    ];


    protected $scene = [
        'create' => ['template_name', 'template_content', 'module_id','project_id'],
        'update' => ['id', 'template_name', 'template_content',],
        'copy' => ['template_name'],
        'defaultTempDetail' => ['module_id','project_id'],
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
        'id.require' => 'id 必须传值',
        'template_name.require' => '模板名称 必须传值',
        'template_content.require' => '模板内容 必须传值',
        'template_name.max' => '模板名称 最长支持50字符',
        'template_name.checkNameUnique' => '模板名称 存在同名模版',
    ];

    /**
     * 验证名称是否唯一
     * @param $value
     * @param $rule
     * @param $data
     * @return bool
     * <AUTHOR>
     * @date 2024/7/8 上午10:15
     */
    protected function checkNameUnique($value, $rule, $data = [])
    {
        if (!empty($data['id'])) {
            $model = TemplateModel::findById($data['id']);
            if (!$model) {
                throw new NotFoundException();
            }

            $data['module_id'] = $model['module_id'];
            $data['project_id'] = $model['project_id'];
        }

        $model = TemplateModel::findModuleTemplateName($data['module_id'] ?? '', $data['template_name'], $data['project_id'] ?? '');

        // 校验名称是否与模板相同
        (new TemplateLogic())->validateProjectTemplateData(ProjectInfoLogic::getProjectData($data['project_id'] ?? ''), $data['template_name']);

        if (!empty($data['id'])) {
            return !$model || ($model->id == $data['id']);
        } else {
            return !$model;
        }

    }

    /**
     * 检查所属项目id
     * @param $value
     * @param $data
     * @return false
     * <AUTHOR>
     * @date 2024/8/22 15:21
     */
    protected function checkHasProjectId($value, $data = [])
    {
        if (!TemplateModel::isRequireProjectId($data['module_id'])) {
            return false;
        }
        if (empty($data['project_id'])) {
            throw new ParamsException('项目id必填(当所属模块是4-迭代，5-需求，6-任务，7-缺陷时，8-测试用例，9-测试计划)');
        }

        return false;
    }


}
