<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\infrastructure\validate;

use basic\BaseValidate;

class CustomerTableConfigValidate extends BaseValidate
{
    protected $rule = [
        'table_unique' => 'require',
        'user_fields' => 'require',

    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
        'table_unique.require' => '表格唯一标识 必须传值',
        'user_fields.require' => '表头内容 必须传值',
    ];


}
