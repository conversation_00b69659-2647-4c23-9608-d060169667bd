<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\console\input\Argument;
use app\project\scheduled_tasks\BugStatistics;
use utils\Log;

class RegenerateBugStatisticsCommand extends Command
{
    protected function configure()
    {
        $this->setName('bug:regenerate-statistics')
            ->addArgument('startDate', Argument::REQUIRED, 'Start date (Y-m-d)')
            ->addArgument('endDate', Argument::REQUIRED, 'End date (Y-m-d)')
            ->setDescription('Regenerate bug statistics for a given date range');
    }

    protected function execute(Input $input, Output $output)
    {
        $startDate = $input->getArgument('startDate');
        $endDate = $input->getArgument('endDate');

        // 简单的日期格式校验
        if ( ! $this->validateDate($startDate) || ! $this->validateDate($endDate)) {
            $output->writeln('<error>Invalid date format. Please use Y-m-d format.</error>');
            Log::instance(Log::PLATFORM)->error('RegenerateBugStatisticsCommand: Invalid date format provided. StartDate: ' . $startDate . ', EndDate: ' . $endDate);
            return 1; // 返回非0值表示错误
        }
        
        if (strtotime($startDate) > strtotime($endDate)) {
            $output->writeln('<error>Start date cannot be later than end date.</error>');
            Log::instance(Log::PLATFORM)->error('RegenerateBugStatisticsCommand: Start date is later than end date. StartDate: ' . $startDate . ', EndDate: ' . $endDate);
            return 1;
        }

        $output->writeln('Starting bug statistics regeneration for date range: ' . $startDate . ' to ' . $endDate);
        Log::instance(Log::PLATFORM)->info('RegenerateBugStatisticsCommand started for date range: ' . $startDate . ' to ' . $endDate);

        try {
            BugStatistics::regenerateStatisticsByDateRange($startDate, $endDate);
            $output->writeln('<info>Successfully regenerated bug statistics for the specified date range.</info>');
            Log::instance(Log::PLATFORM)->info('RegenerateBugStatisticsCommand completed successfully for date range: ' . $startDate . ' to ' . $endDate);
        } catch (\Throwable $e) {
            $output->writeln('<error>Error during bug statistics regeneration: ' . $e->getMessage() . '</error>');
            $output->writeln('Error details: ' . $e->getFile() . ' on line ' . $e->getLine());
            Log::instance(Log::PLATFORM)->error('RegenerateBugStatisticsCommand failed for date range: ' . $startDate . ' to ' . $endDate . '. Error: ' . $e->getMessage() . ' at ' . $e->getFile() . ':' . $e->getLine());
            Log::instance(Log::PLATFORM)->error((string)$e);
            return 1; // 返回非0值表示错误
        }
        return 0; // 返回0表示成功
    }

    private function validateDate(string $date, string $format = 'Y-m-d'): bool
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
}