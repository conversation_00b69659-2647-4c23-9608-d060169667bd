<?php
declare (strict_types=1);

namespace app\client\model;

use app\infrastructure\model\FieldConfigModel;
use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;
use traits\OperationLogTrait;
use traits\OptimLockTrait;

/**
 * This is the model class for table "t_client".
 * @property int $client_id id
 * @property int $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property int $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property string $client_name 终端名称
 * @property array $extends 自定义字段
 * @property int $is_enable 自定义字段
 * @property int $version 版本号
 */
class ClientModel extends BaseModel
{
    //操作日志
    use OperationLogTrait;
    use CreateAndUpdateModelTrait;
    use OptimLockTrait;


    protected $name = 'client';

    const LIST_FIELDS = [
        'client_id',
        'client_name',
        'extends',
        'is_enable',
        'version',
        'create_at',
        'create_by',
        'create_by_name',
    ];


    protected $pk = 'client_id';
    protected array $logFieldList = [
        'client_name' => '终端名称',
        'extends' => [
            'type' => 'json',
        ],
        'is_enable' => [
            'type' => "enum",
            'field_label' => '状态',
            'values' => [
                self::ENABLE_YES => '开启',
                self::ENABLE_NOT => '关闭',
            ]
        ],

    ];

    public static function findById($id)
    {
        return static::where(['client_id' => $id, 'is_delete' => self::DELETE_NOT])->find();
    }


    public static function checkNameUnique($id, $clientName)
    {
        $where = [
            ['is_delete', '=', BaseModel::DELETE_NOT],
            ['client_name', '=', $clientName],
        ];
        if ($id) {
            $where[] = ['client_id', '<>', $id];
        }

        return static::where($where)->count() == 0;
    }

    public function getExtendsAttr($value)
    {
        return json_decode($value, true);
    }

    public function setExtendsAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * @return string[]
     */
    public function getLogFieldList(): array
    {
        return $this->logFieldList;
    }


    //验证名称是否唯一

    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }

    public function isEnable(): bool
    {
        return $this->is_enable == ClientModel::ENABLE_YES;
    }

    /**
     * 解析extends中所需的组件
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/7/19 下午2:17
     */
    public function getFieldList()
    {
        $fieldNameList = array_column($this->extends, 'field_name');
        $this->fieldList = FieldConfigModel::getListByFieldName($fieldNameList,FieldConfigModel::MODULE_TYPE_CLIENT);
    }

}
