<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\client\validate;

use app\client\model\ClientModel;
use basic\BaseValidate;

class ClientValidate extends BaseValidate
{
    protected $rule = [
        'client_id' => 'require',
        'client_name' => 'require|max:50|checkNameUnique',
        'extends' => 'require',
        'version' => 'require',
    ];


    protected $scene = [
        'create' => ['client_name', 'extends'],
        'update' => ['client_id', 'client_name', 'extends', 'version'],
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
        'client_id.require' => 'client_id 必须传值',
        'client_name.require' => '终端名称 必须传值',
        'extends.require' => '自定义字段 必须传值',
        'version.require' => '版本号 必须传值',
        'client_name.max' => '终端名称 最长支持50字符',
        'client_name.checkNameUnique' => '终端名称 存在同名',
    ];

    /**
     * 验证名称是否唯一
     * @param $value
     * @param $rule
     * @param $data
     * @return bool
     * <AUTHOR>
     * @date 2024/7/8 上午10:16
     */
    protected function checkNameUnique($value, $rule, $data = [])
    {
        return ClientModel::checkNameUnique($data['client_id'] ?? '', $data['client_name'] ?? '');
    }


}
