<?php
declare (strict_types=1);

namespace app\client\logic;

use app\client\model\ClientModel;
use app\client\validate\ClientValidate;
use app\product\logic\ProductClientLogic;
use app\project\logic\ProjectInfoLogic;
use basic\BaseModel;
use exception\NotFoundException;
use basic\BaseLogic;

class ClientLogic extends BaseLogic
{
    /**
     * 新增
     * @param $params
     * @return ClientModel
     * <AUTHOR>
     * @date 2024/7/8 上午10:18
     */
    public function create($params)
    {
        validate(ClientValidate::class)->scene('create')->check($params);
        $model = new ClientModel();
        $model->save($params);

        return $model->toDetail();

    }


    /**
     * 删除
     * @param $clientId
     * @return void
     * <AUTHOR>
     * @date 2024/7/8 上午10:18
     */
    public function delete($clientId)
    {
        $model = ClientModel::findById($clientId);
        if (!$model) {
            throw new NotFoundException();
        }
        $model->is_delete = BaseModel::DELETE_YES;
        $model->save();

        // 解除终端与产品关联
        ProductClientLogic::removeClientBindByClientIds([$clientId]);
    }

    /**
     * 修改
     * @param $params
     * @return ClientModel|array|mixed|\think\Model
     * <AUTHOR>
     * @date 2024/7/8 上午10:18
     */
    public function update($params)
    {

        validate(ClientValidate::class)->scene('update')->check($params);
        $model = ClientModel::findById($params['client_id']);
        if (!$model) {
            throw new NotFoundException();
        }
        $model->save($params);

        return $model->toDetail();

    }


    /**
     * 详情
     * @param $id
     * @return ClientModel
     * <AUTHOR>
     * @date 2024/7/8 上午10:18
     */
    public function detail($id)
    {
        $model = ClientModel::findById($id);
        if (!$model) {
            throw new NotFoundException();
        }
        $model->getFieldList();

        return $model->toDetail();
    }

    /**
     * 分页查询
     * @param $params
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/7/8 上午10:18
     */
    public function pageQuery($params)
    {
        $where = [];
        if ($params['client_name'] ?? '') {
            $where[] = ['client_name', 'like', "%{$params['client_name']}%"];
        }
        return (new ClientModel)
            ->where(['is_delete' => BaseModel::DELETE_NOT])
            ->where($where)
            ->field(ClientModel::LIST_FIELDS)->order('is_enable DESC, client_id desc')
            ->paginate(getPageSize());

    }

    /**
     *  启用
     * @param $clientId
     * @return ClientModel|void
     * <AUTHOR>
     * @date 2024/7/8 上午9:53
     */
    public function enable($clientId)
    {
        $model = ClientModel::findById($clientId);
        if (!$model) {
            throw new NotFoundException();
        }
        if ($model->isEnable()) {
            return;
        }

        $model->is_enable = ClientModel::ENABLE_YES;
        $model->save();

    }

    /**
     * 禁用
     * @param $clientId
     * @return ClientModel|void
     * <AUTHOR>
     * @date 2024/7/8 上午9:54
     */
    public function disable($clientId)
    {
        $model = ClientModel::findById($clientId);
        if (!$model) {
            throw new NotFoundException();
        }
        if (!$model->isEnable()) {
            return;
        }
        $model->is_enable = ClientModel::ENABLE_NOT;
        $model->save();
    }

    /**
     * 根据终端名称模糊获取终端id集合
     * @param string $clientName 终端名称
     * @return ClientModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public static function getClientIdByClientName(string $clientName)
    {
        $clientInfo = ClientModel::status()->where('client_name', 'like', "%$clientName%")->select();

        if ($clientInfo->isEmpty()) {
            return [];
        }

        return $clientInfo->column('client_id');
    }

    /**
     * 根据终端id集查询数据
     * @param array $clientIds
     * @return ClientModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/7/25
     */
    public static function selectInfoByClientIds(array $clientIds, int $is_enable = null)
    {
        $model = ClientModel::status()->whereIn('client_id', $clientIds);

        if ($is_enable !== null) $model->where(['is_enable' => $is_enable]);

        return $model->select();
    }

    /**
     * 终端下拉框数据
     * @return array
     * User Long
     * Date 2024/7/23
     */
    public function selector()
    {
        return ClientModel::status()->order('client_id DESC')->where(['is_enable' => BaseModel::ENABLE_YES])->column('client_name as label, client_id as value');
    }

    /**
     * 终端下拉框数据
     * @param int $projectId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/20
     */
    public function selectorByProduct(int $projectId)
    {
        // 项目id为空就返回所有数据
        if (empty($projectId)) {
            return $this->selector();
        }

        $projectModel = ProjectInfoLogic::getProjectData($projectId);

        if (!$projectModel) {
            return [];
        }

        return ProductClientLogic::getClient($projectModel->product_id, BaseModel::ENABLE_YES);
    }
}
