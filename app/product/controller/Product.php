<?php
/**
 * Desc 产品管理 - 控制器
 * User Long
 * Date 2024/07/23
 */

declare (strict_types=1);


namespace app\product\controller;

use app\product\logic\ProductLogic;
use resp\Result;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Request;
use think\response\Json;
use Throwable;

class Product
{
    private ProductLogic $logic;

    public function __construct()
    {
        $this->logic = new ProductLogic();
    }

    /**
     * 创建新产品并关联客户端与微服务
     * @param Request $request
     * @return Json
     * @throws Throwable
     * User Long
     * Date 2024/7/23
     */
    public function create(Request $request): Json
    {
        $params = $request->post([
            'product_name',
            'extends',
            'client_ids',
            'microservice_ids'
        ]);

        $this->logic->create($params);

        return Result::success();
    }

    /**
     * 根据产品id集删除产品
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public function delete(Request $request): Json
    {
        $productIds = $request->post('product_ids/a');

        $this->logic->delete($productIds);

        return Result::success();
    }

    /**
     * 根据产品id更新数据
     * @param Request $request
     * @return Json
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public function update(Request $request): Json
    {
        $params = $request->post([
            'product_id',
            'product_name',
            'extends',
            'client_ids',
            'microservice_ids'
        ]);

        $this->logic->update($params['product_id'], $params);

        return Result::success();
    }

    /**
     * 产品详情
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public function detail(Request $request): Json
    {
        $productId = $request->get('product_id/d');

        $res = $this->logic->detail((int)$productId);

        return Result::success($res);
    }

    /**
     * 产品分页数据
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public function pageQuery(Request $request): Json
    {
        $productName = $request->get('product_name/s', '');
        $clientName = $request->get('client_name/s', '');
        $microserviceName = $request->get('microservice_name/s', '');

        $res = $this->logic->pageQuery($productName, $clientName, $microserviceName);

        return Result::success($res);
    }

    /**
     * 启用
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/20
     */
    public function enable(Request $request): Json
    {
        $productIds = $request->post('product_ids/a');

        $this->logic->enable($productIds);

        return Result::success();
    }

    /**
     * 禁用
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/20
     */
    public function disable(Request $request): Json
    {
        $productIds = $request->post('product_ids/a');

        $this->logic->disable($productIds);

        return Result::success();
    }

    /**
     * 产品下拉数据
     * @return Json
     * User Long
     * Date 2024/10/8
     */
    public function getProductSelector()
    {
        return Result::success($this->logic->getProductSelector());
    }

}
