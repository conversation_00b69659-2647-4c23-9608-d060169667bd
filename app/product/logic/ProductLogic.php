<?php
/**
 * Desc 产品管理 - 逻辑层
 * User Long
 * Date 2024/07/23
 */
declare (strict_types=1);

namespace app\product\logic;

use app\client\logic\ClientLogic;
use app\microservice\logic\MicroserviceLogic;
use app\product\model\ProductModel;
use app\product\validate\ProductValidate;
use basic\BaseLogic;
use basic\BaseModel;
use exception\NotFoundException;
use exception\ParamsException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\Paginator;
use Throwable;

class ProductLogic extends BaseLogic
{
    /**
     * 根据产品名称模糊获取项目id
     * @param $productName
     * @return array
     * User Long
     * Date 2024/10/11
     */
    public static function getProductIdColumnProductName($productName): array
    {
        return ProductModel::status()->where('product_name', 'like', "%$productName%")->column('product_id');
    }

    /**
     * 获取产品名称
     * @param $productId
     * @return mixed
     * User Long
     * Date 2024/10/11
     */
    public static function getProductNameByProductId($productId): mixed
    {
        return ProductModel::status()->where(['product_id' => $productId])->value('product_name');
    }

    /**
     * 创建新产品并关联客户端与微服务
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/7/23
     */
    public function create(array $params): void
    {
        // 验证器校验
        validate(ProductValidate::class)->scene('create')->check($params);

        // 检查产品名称是否已存在
        $productNameExist = ProductModel::findProductAndName($params['product_name']);
        if ($productNameExist) {
            throw new ParamsException('产品名称不可相同');
        }

        try {
            Db::startTrans();

            // 创建新的产品模型实例
            $productModel = new ProductModel();
            // 保存产品信息至数据库
            $productInfo = $productModel->create([
                'product_name' => $params['product_name'],
                'product_no' => time(),
                'extends' => $params['extends']
            ]);

            // 关联产品与客户端
            ProductClientLogic::saveClientData((int)$productInfo->product_id, $params['client_ids']);

            // 关联产品与微服务
            ProductMicroserviceLogic::saveMicroserviceData((int)$productInfo->product_id, $params['microservice_ids']);

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 根据产品id集删除产品
     * @param array $productIds 产品id集
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public function delete(array $productIds): void
    {
        $models = ProductModel::selectByProductIds($productIds);

        foreach ($models as $model) {
            $model->save(['is_delete' => BaseModel::DELETE_YES]);
        }
    }

    /**
     * 根据产品id更新数据
     * @param $productId
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/7/22
     */
    public function update($productId, array $params): void
    {
        // 验证器校验
        validate(ProductValidate::class)->scene('update')->check($params);

        $model = ProductModel::findByProductId($productId);
        if (!$model) {
            throw new NotFoundException();
        }
        $productNameExist = ProductModel::findProductAndName($params['product_name']);
        if ($productNameExist && $productNameExist->product_id != $productId) {
            throw new ParamsException('产品名称已存在');
        }

        try {
            Db::startTrans();

            // 解除关联产品与终端
            ProductClientLogic::removeClientDataByProductId($productId);

            // 解除关联产品与客户端
            ProductMicroserviceLogic::removeMicroserviceDataByProductId($productId);

            // 保存产品信息至数据库
            $model->save([
                'product_name' => $params['product_name'],
                'extends' => $params['extends']
            ]);

            // 关联产品与客户端
            ProductClientLogic::saveClientData((int)$productId, $params['client_ids']);

            // 关联产品与微服务
            ProductMicroserviceLogic::saveMicroserviceData((int)$productId, $params['microservice_ids']);

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 产品详情
     * @param int $productId 产品id
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public function detail(int $productId): array
    {
        $model = ProductModel::findByProductId($productId);

        if (!$model) {
            throw new NotFoundException();
        }
        $model->getFieldList();

        $res = $model->toDetail()->toArray();

        // 终端数据
        $res['client_info'] = ProductClientLogic::getClient($res['product_id']);

        // 微服务数据
        $res['microservice_info'] = ProductMicroserviceLogic::getMicroservice($res['product_id']);

        $res['client_names'] = $res['client_info']
            ? implode('、', array_column($res['client_info'], 'label')) : '';

        $res['microservice_names'] = $res['microservice_info']
            ? implode('、', array_column($res['microservice_info'], 'label')) : '';


        return $res;
    }

    /**
     * 获取查询类型
     * @param string $clientName 终端名称
     * @param string $microserviceName 微服务名称
     * @return string
     * User Long
     * Date 2024/7/31
     */
    private function getSearchType(string $clientName = '', string $microserviceName = ''): string
    {
        if ($clientName && $microserviceName) {
            $searchType = 'all';
        } elseif ($clientName) {
            $searchType = 'client';
        } elseif ($microserviceName) {
            $searchType = 'microservice';
        } else {
            $searchType = '';
        }

        return $searchType;
    }

    /**
     * 终端名称查询
     * @param string $clientName
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/31
     */
    private function getSearchClientProductIds(string $clientName = ''): array
    {
        if (!$clientName) {
            return [];
        }

        $clientIds = ClientLogic::getClientIdByClientName($clientName);
        if (!$clientIds) {
            return [];
        }

        return array_unique(ProductClientLogic::getProductIdByClientId(array_unique($clientIds)));
    }

    /**
     * 微服务名称查询
     * @param string $microserviceName
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/31
     */
    private function getSearchMicroserviceProductIds(string $microserviceName = ''): array
    {
        if (!$microserviceName) {
            return [];
        }

        $microserviceIds = MicroserviceLogic::getMicroserviceIdByMicroserviceName($microserviceName);
        if (!$microserviceIds) {
            return [];
        }

        return array_unique(ProductMicroserviceLogic::getProductIdByMicroserviceId(array_unique($microserviceIds)));
    }

    /**
     * 产品分页数据
     * @param string $productName
     * @param string $clientName
     * @param string $microserviceName
     * @return Paginator
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public function pageQuery(string $productName = '', string $clientName = '', string $microserviceName = ''): Paginator
    {
        $hidden = ['id', 'is_delete', 'update_by', 'update_by_name', 'update_at', 'product_id'];
        $where = [];

        // 产品名称查询
        if ($productName) {
            $where[] = ['product_name', 'like', "%$productName%"];
        }

        // 终端名称查询
        $clientProductIds = $this->getSearchClientProductIds($clientName);

        // 微服务名称查询
        $microserviceProductIds = $this->getSearchMicroserviceProductIds($microserviceName);

        // 根据查询类型，进行条件组合
        switch ($this->getSearchType($clientName, $microserviceName)) {
            case 'all':
                $where[] = ['product_id', 'in', array_unique(array_intersect($clientProductIds, $microserviceProductIds))];
                break;
            case 'client':
                $where[] = ['product_id', 'in', $clientProductIds];
                break;
            case 'microservice':
                $where[] = ['product_id', 'in', $microserviceProductIds];
                break;
        }

        return ProductModel::status()
            ->field(ProductModel::LIST_FIELDS)
            ->with([
                'client' => function ($sql) use ($hidden) {
                    $sql->with(['clientDetail' => function ($sql) {
                        $sql->bind(['client_name', 'is_enable']);
                    }])->hidden($hidden);
                },
                'microservice' => function ($sql) use ($hidden) {
                    $sql->with(['microserviceDetail' => function ($sql) {
                        $sql->bind(['microservice_name', 'is_enable']);
                    }])->hidden($hidden);
                }
            ])
            ->where($where)
            ->order('is_enable DESC, product_id desc')
            ->paginate(getPageSize());
    }

    /**
     * 分类 - 启用
     * @param array $productIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException User Long
     * Date 2024/7/20
     */
    public function enable(array $productIds): void
    {
        $data = ProductModel::selectByProductIds($productIds);

        foreach ($data as $model) {
            $model->save(['is_enable' => BaseModel::ENABLE_YES]);
        }

    }

    /**
     * 分类 - 禁用
     * @param array $productIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException User Long
     * Date 2024/7/20
     */
    public function disable(array $productIds): void
    {
        $data = ProductModel::selectByProductIds($productIds);

        foreach ($data as $datum) {
            $datum->save(['is_enable' => $datum::ENABLE_NOT]);
        }

    }

    /**
     * 获取产品下拉数据
     * @return array
     * User Long
     * Date 2024/10/10
     */
    public function getProductSelector() :array
    {
        return ProductModel::status()->where(['is_enable' => BaseModel::ENABLE_YES])->column('product_name as label, product_id as value');
    }
}
