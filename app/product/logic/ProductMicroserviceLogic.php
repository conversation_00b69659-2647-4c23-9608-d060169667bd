<?php
/**
 * Desc 产品微服务关联 - 逻辑层
 * User Long
 * Date 2024/7/27
 */

namespace app\product\logic;

use app\microservice\logic\MicroserviceLogic;
use app\product\model\ProductMicroserviceModel;
use basic\BaseLogic;
use basic\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class ProductMicroserviceLogic extends BaseLogic
{
    /**
     * 组装微服务储存数据
     * @param int $productId 产品id
     * @param array $microserviceIds 微服务id集
     * User Long
     * Date 2024/7/27
     */
    public static function saveMicroserviceData(int $productId, array $microserviceIds): void
    {
        foreach ($microserviceIds as $microserviceId) {
            $model = new ProductMicroserviceModel();
            $model->product_id = $productId;
            $model->microservice_id = $microserviceId;
            $model->save();
        }
    }

    /**
     * 解除关联产品与微服务
     * @param int $productId 产品id
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/27
     */
    public static function removeMicroserviceDataByProductId(int $productId): void
    {
        $productMicroserviceData = ProductMicroserviceModel::selectMicroserviceByProductId($productId);
        foreach ($productMicroserviceData as $productMicroserviceDatum) {
            $productMicroserviceDatum->save(['is_delete' => BaseModel::DELETE_YES]);
        }
    }

    /**
     * 解除关联微服务与产品
     * @param array $microserviceIds 微服务id集
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/30
     */
    public static function removeMicroserviceBindByMicroserviceId(array $microserviceIds): void
    {
        $productMicroserviceData = ProductMicroserviceModel::selectMicroserviceByMicroserviceIds($microserviceIds);
        foreach ($productMicroserviceData as $productMicroserviceDatum) {
            $productMicroserviceDatum->save(['is_delete' => BaseModel::DELETE_YES]);
        }
    }

    /**
     * 根据项目id 获取微服务
     * @param int $productId 产品id
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/27
     */
    public static function getMicroservice(int $productId, int $is_enable = null): array
    {
        $microserviceIds = ProductMicroserviceModel::selectMicroserviceByProductId($productId)->column('microservice_id');
        if (!$microserviceIds) {
            return [];
        }

        $microserviceInfo = MicroserviceLogic::selectInfoByClientIds($microserviceIds, $is_enable);
        $resp = [];
        foreach ($microserviceInfo as $item) {
            $sel['label'] = $item['microservice_name'];
            $sel['value'] = $item['microservice_id'];

            if ($is_enable === null) {
                $sel['extends'] = $item['extends'];
                $sel['is_enable'] = $item['is_enable'];
            }

            $resp[] = $sel;
        }

        return $resp;
    }

    /**
     * 根据微服务id集合获取产品id集合
     * @param array $microserviceIds
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/31
     */
    public static function getProductIdByMicroserviceId(array $microserviceIds): array
    {
        $productClientInfo = ProductMicroserviceModel::selectMicroserviceByMicroserviceIds($microserviceIds);

        if ($productClientInfo->isEmpty()) {
            return [];
        }

        return $productClientInfo->column('product_id');
    }
}
