<?php
/**
 * Desc 产品终端关联 - 逻辑层
 * User Long
 * Date 2024/7/27
 */

namespace app\product\logic;

use app\client\logic\ClientLogic;
use app\product\model\ProductClientModel;
use basic\BaseLogic;
use basic\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class ProductClientLogic extends BaseLogic
{
    /**
     * 组装终端储存数据
     * @param int $productId 产品id
     * @param array $clientIds 终端id集
     * User Long
     * Date 2024/7/27
     */
    public static function saveClientData(int $productId, array $clientIds): void
    {
        foreach ($clientIds as $clientId) {
            $model = new ProductClientModel();
            $model->product_id = $productId;
            $model->client_id = $clientId;
            $model->save();
        }
    }

    /**
     * 解除关联产品与终端
     * @param int $productId 产品id
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/27
     */
    public static function removeClientDataByProductId(int $productId): void
    {
        $productClientData = ProductClientModel::selectClientByProductId($productId);
        foreach ($productClientData as $productClientDatum) {
            $productClientDatum->save(['is_delete' => BaseModel::DELETE_YES]);
        }
    }

    /**
     * 解除关联终端与产品
     * @param array $clientIds 终端id集
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/30
     */
    public static function removeClientBindByClientIds(array $clientIds): void
    {
        $productClientData = ProductClientModel::selectClientByClientIds($clientIds);
        foreach ($productClientData as $productClientDatum) {
            $productClientDatum->save(['is_delete' => BaseModel::DELETE_YES]);
        }
    }

    /**
     * 根据产品Id 获取终端
     * @param int $productId 产品id
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/27
     */
    public static function getClient(int $productId, int $is_enable = null): array
    {
        $clientIds = ProductClientModel::selectClientByProductId($productId)->column('client_id');
        if (!$clientIds) {
            return [];
        }
        $clientInfo = ClientLogic::selectInfoByClientIds($clientIds, $is_enable);
        $resp = [];
        foreach ($clientInfo as $item) {
            $sel['label'] = $item['client_name'];
            $sel['value'] = $item['client_id'];

            if ($is_enable === null) {
                $sel['extends'] = $item['extends'];
                $sel['is_enable'] = $item['is_enable'];
            }

            $resp[] = $sel;
        }

        return $resp;
    }

    /**
     * 根据终端id集合获取产品id集合
     * @param array $clientIds
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/27
     */
    public static function getProductIdByClientId(array $clientIds): array
    {
        $productClientInfo = ProductClientModel::selectClientByClientIds($clientIds);

        if ($productClientInfo->isEmpty()) {
            return [];
        }

        return $productClientInfo->column('product_id');
    }
}
