<?php
/**
 * Desc 产品管理 - 验证器
 * User Long
 * Date 2024/07/23
 */

namespace app\product\validate;

use basic\BaseValidate;

class ProductValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|egt:0|elt:9999',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'product_id|产品id' => 'require|integer|egt:0',
        'product_name|产品名称' => 'require|max:50',
        'extends|自定义字段' => 'isArray',
        'client_ids|终端' => 'isArray',
        'microservice_ids|微服务' => 'isArray'
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
        'product_name.max' => '产品名称不可超过50个字符'
    ];

    /**
     * 新增
     * @return ProductValidate
     * User Long
     * Date 2024/7/23
     */
    public function sceneCreate(): ProductValidate
    {
        return $this->only(['product_name', 'extends', 'client_ids', 'microservice_ids']);
    }

    /**
     * 更新
     * @return ProductValidate
     * User Long
     * Date 2024/7/23
     */
    public function sceneUpdate(): ProductValidate
    {
        return $this->only(['product_id', 'product_name', 'extends', 'client_ids', 'microservice_ids']);
    }

    /**
     * 分页
     * @return ProductValidate
     * User Long
     * Date 2024/7/20
     */
    public function scenePageQuery(): ProductValidate
    {
        return $this->only(['project_id']);
    }
}
