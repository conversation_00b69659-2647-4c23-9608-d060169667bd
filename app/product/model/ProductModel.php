<?php
/**
 * Desc 产品管理 - 模型
 * User Long
 * Date 2024/07/23
 */

declare (strict_types=1);

namespace app\product\model;

use app\infrastructure\model\FieldConfigModel;
use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;
use think\model\relation\HasMany;
use traits\CreateAndUpdateModelTrait;
use traits\OperationLogTrait;

/**
 * This is the model class for table "product".
 * @property string $product_id id
 * @property string $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property string $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property string $product_name 产品名称
 * @property string $product_no 产品编号
 * @property string $extends 自定义字段
 * @property string $version
 */
class ProductModel extends BaseModel
{
    use CreateAndUpdateModelTrait;
    use OperationLogTrait;

    protected $pk = 'product_id';
    protected $name = 'product';

    //新增/修改需要记录日志的字段，字段名=>展示名称
    protected array $logFieldList = [
        'product_name' => '产品名称',
//        'category_en_name' => '类别英文名称',
//        'icon' => '图标',
//        'page_id' => '创建页id',
//        'flow_status_id' => '工作流id',
//        'project_id' => '项目Id',
        'is_delete' => [
            'type' => 'enum',
            'field_label' => '是否删除',
            'values' => [
                self::DELETE_YES => '是',
                self::DELETE_NOT => '否'
            ]
        ],
        'is_enable' => [
            'type' => 'enum',
            'field_label' => '是否禁用',
            'values' => [
                self::ENABLE_YES => '是',
                self::ENABLE_NOT => '否'
            ]
        ]

    ];

    const LIST_FIELDS = 'product_id, product_name, is_enable, extends, create_at, create_by_name';

    /**
     * 解析json
     * @param $value
     * @return mixed
     * User Long
     * Date 2024/7/23
     */
    public function getExtendsAttr($value): mixed
    {
        if (!$value) return [];

        $extends = json_decode($value, true);

        if (isset($extends['fieldData'])) {
            $extends['fieldData'] = $this->resetExtendsAttr($extends['fieldData']);
        } elseif (isset($extends[0]['field_component'])) {
            $extends = $this->resetExtendsAttr($extends);
        }

        return $extends;
    }

    /**
     * 进行json
     * @param $value
     * @return false|string
     * User Long
     * Date 2024/7/23
     */
    public function setExtendsAttr($value): bool|string
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 根据 产品名称 获取当前项目下的指定产品
     * @param string $productName 产品名称
     * @return ProductModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public static function findProductAndName(string $productName): mixed
    {
        return static::status()->where([
            'product_name' => $productName
        ])->find();
    }

    /**
     * 根据id集查询数据
     * @param array $productIds 产品id集
     * @return ProductModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public static function selectByProductIds(array $productIds): array|Collection
    {
        return static::status()
            ->whereIn('product_id', $productIds)
            ->select();
    }

    /**
     * 根据id查询产品数据
     * @param int $productId 产品id
     * @return ProductModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public static function findByProductId(int $productId): mixed
    {
        return static::status()->where(['product_id' => $productId])->find();
    }

    /**
     * 预加载 - 产品终端关系
     * @return HasMany
     * User Long
     * Date 2024/7/23
     */
    public function client(): HasMany
    {
        return $this->hasMany(ProductClientModel::class, 'product_id', 'product_id')->where(['is_delete' => self::DELETE_NOT]);
    }

    /**
     * 预加载 - 产品微服务关系
     * @return HasMany
     * User Long
     * Date 2024/7/23
     */
    public function microservice(): HasMany
    {
        return $this->hasMany(ProductMicroserviceModel::class, 'product_id', 'product_id')->where(['is_delete' => self::DELETE_NOT]);
    }

    public function getLogFieldList(): array
    {
        return $this->logFieldList;
    }

    /**
     * 解析extends中所需的组件
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date 2024/7/19 下午2:17
     */
    public function getFieldList(): void
    {
        $fieldNameList = array_column($this->extends, 'field_name');
        $this->fieldList = FieldConfigModel::getListByFieldName($fieldNameList,FieldConfigModel::MODULE_TYPE_PRODUCT);
    }

    /**
     * 详情页处理
     * @return ProductModel
     * User Long
     * Date 2024/8/10
     */
    public function toDetail(): ProductModel
    {
        return $this->hidden(['is_delete', 'version', 'product_no']);
    }
}
