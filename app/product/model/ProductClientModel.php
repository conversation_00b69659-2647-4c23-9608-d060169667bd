<?php
/**
* Desc 产品终端关系 - 模型
* User Long
* Date 2024/07/23
*/

declare (strict_types = 1);

namespace app\product\model;

use app\client\model\ClientModel;
use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "product_client".
* @property string $id id
* @property string $product_id 产品id
* @property string $client_id 终端id
*/
class ProductClientModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'product_client';

    /**
     * 根据产品id查询关联终端
     * @param int $productId 产品id
     * @return ProductClientModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public static function selectClientByProductId(int $productId): array|Collection
    {
        return static::status()->where([
            'product_id' => $productId,
        ])->select();
    }

    /**
     * 根据终端id集查询关联产品
     * @param array $clientIds
     * @return ProductClientModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public static function selectClientByClientIds(array $clientIds): array|Collection
    {
        return static::status()->whereIn( 'client_id', $clientIds)->select();
    }

    /**
     * 预加载 - 终端详情
     * @return HasOne
     * User Long
     * Date 2024/7/23
     */
    public function clientDetail(): HasOne
    {
        return $this->hasOne(ClientModel::class, 'client_id', 'client_id')->where(['is_delete' => self::DELETE_NOT]);
    }
}
