<?php
/**
* Desc 产品微服务关系 - 模型
* User Long
* Date 2024/07/23
*/

declare (strict_types = 1);

namespace app\product\model;

use app\microservice\model\MicroserviceModel;
use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "product_microservice".
* @property string $id id
* @property string $product_id 产品id
* @property string $microservice_id 终端id
*/
class ProductMicroserviceModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'product_microservice';

    /**
     * 根据产品id查询关联微服务
     * @param int $productId 产品id
     * @return ProductClientModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/23
     */
    public static function selectMicroserviceByProductId(int $productId): array|Collection
    {
        return static::status()->where([
            'product_id' => $productId,
        ])->select();
    }

    /**
     * 根据微服务id集查询关联产品
     * @param array $microserviceIds
     * @return ProductMicroserviceModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/30
     */
    public static function selectMicroserviceByMicroserviceIds(array $microserviceIds): array|Collection
    {
        return static::status()->whereIn( 'microservice_id', $microserviceIds)->select();
    }

    /**
     * 预加载 - 微服务详情
     * @return HasOne
     * User Long
     * Date 2024/7/23
     */
    public function microserviceDetail(): HasOne
    {
        return $this->hasOne(MicroserviceModel::class, 'microservice_id', 'microservice_id')->where(['is_delete' => self::DELETE_NOT]);
    }
}
