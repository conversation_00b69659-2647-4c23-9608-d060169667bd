<?php
/**
 * Desc 工作流状态库 - 逻辑层
 * User Long
 * Date 2024/7/27
 */

namespace app\iterate\logic;

use app\iterate\model\FlowStatusEnumModel;
use app\iterate\validate\FlowStatusEnumValidate;
use basic\BaseLogic;
use basic\BaseModel;
use exception\BusinessException;
use exception\NotFoundException;
use exception\ParamsException;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\Model;
use Throwable;

class FlowStatusEnumLogic extends BaseLogic
{
    private int $enumMaxNum = 50; // 状态库数量最大限制：单项目最多可添加50个状态
    private int $statusEnumType; // 状态库类型 1=>迭代 2=>需求 3=>任务 4=>缺陷

    public function __construct(int $statusEnumType = BaseModel::SETTING_TYPE_ITERATION)
    {
        // 工作库仅支持 迭代、需求、任务、缺陷
        if ($statusEnumType > BaseModel::SETTING_TYPE_DEFECT) {
            throw new ParamsException('所属类型不在预设访问内，请联系客服！');
        }

        $this->statusEnumType = $statusEnumType;
    }

    /**
     * 状态库新增状态
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/7
     */
    public function create(array $params): void
    {
        // 验证器校验
        validate(FlowStatusEnumValidate::class)->scene('create')->check($params);

        if (FlowStatusEnumModel::countProjectEnum((int)$params['project_id'], $this->statusEnumType) >= $this->enumMaxNum) {
            throw new ParamsException('仅限添加' . $this->enumMaxNum . '个状态');
        }

        // 检查产品名称是否已存在
        $enumNameExist = FlowStatusEnumModel::findEnumAndName((int)$params['project_id'], $this->statusEnumType, (string)$params['name']);
        if ($enumNameExist) {
            throw new ParamsException('名称不可相同');
        }

        $model = new FlowStatusEnumModel();
        $params['status_enum_type'] = $this->statusEnumType;

        $model->save($params);
    }

    /**
     * 根据状态库id集删除状态
     * @param array $statusEnumIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/8/7
     */
    public function delete(array $statusEnumIds): void
    {
        $models = FlowStatusEnumModel::selectByStatusEnumIds($this->statusEnumType, $statusEnumIds);

        try {
            Db::startTrans();

            foreach ($models as $model) {
                if ($model->is_permanent) {
                    throw new BusinessException('常驻状态不允许删除');
                }
                $model->save(['is_delete' => BaseModel::DELETE_YES]);
            }

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }

    }

    /**
     * 根据状态库id更新数据
     * @param $statusEnumId
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/7
     */
    public function update($statusEnumId, array $params): void
    {
        // 验证器校验
        validate(FlowStatusEnumValidate::class)->scene('update')->check($params);

        $model = FlowStatusEnumModel::findByStatusEnumId($this->statusEnumType, $statusEnumId);
        if (!$model) {
            throw new NotFoundException();
        }
        if ($model->name != $params['name'] && $model->is_permanent)  throw new BusinessException('常驻状态不允许编辑');

        $enumNameExist = FlowStatusEnumModel::findEnumAndName($model->project_id, $this->statusEnumType, (string)$params['name']);
        if ($enumNameExist && $enumNameExist->status_enum_id != $statusEnumId) {
            throw new ParamsException('名称已存在');
        }

        $model->save($params);
    }

    /**
     * 分页数据
     * @param int $projectId
     * @return FlowStatusEnumModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/8
     */
    public function listQuery(int $projectId): array|Collection
    {
        return FlowStatusEnumModel::status()
            ->with(['flow_status_text_detail' => function($sql) {
                $sql->field('status_enum_id, status_text_id, flow_status_id')
                    ->with(['flow_status_detail' => function($sql) {
                        $sql->bind(['status_flow_name']);
                }])->hidden(['status_enum_id', 'status_text_id']);
            }])
            ->where(['project_id' => $projectId, 'status_enum_type' => $this->statusEnumType])
            ->order('create_at DESC')
            ->hidden(FlowStatusEnumModel::HIDDEN_FIELD)
            ->select();
    }

    /**
     * 根据状态库id合集查询未删除的数量
     * @param int $statusEnumType
     * @param array $enumIds
     * @return int
     * @throws DbException
     * User Long
     * Date 2024/8/24
     */
    public static function countByEnumIds(int $statusEnumType, array $enumIds): int
    {
        return FlowStatusEnumModel::countByEnumIds($statusEnumType, $enumIds);
    }

    /**
     * 根据状态id 查询需求状态数据
     * @param int $enumId
     * @return FlowStatusEnumModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/3
     */
    public static function findDemandByEnumId(int $enumId): mixed
    {
        return FlowStatusEnumModel::findById($enumId);
    }

    /**
     * 状态下拉数据
     * @param int $projectId
     * @return FlowStatusEnumModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date 2024/9/9 14:53
     */
    public function statusSelector(int $projectId)
    {
        return FlowStatusEnumModel::status()
            ->where(['project_id' => $projectId, 'status_enum_type' => $this->statusEnumType])
            ->order('create_at DESC')
            ->field(['status_enum_id value', 'name as label', 'colour'])
            ->select();
    }

    /**
     * 复制状态库（废弃）
     * @param int $oldProjectId
     * @param int $newProjectId
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/14
     */
    public static function copyProjectDataByProjectId(int $oldProjectId, int $newProjectId)
    {
        return;
        $oldProjectData = FlowStatusEnumModel::status()->where([
            'project_id' => $oldProjectId,
        ])->select();

        foreach ($oldProjectData as $oldProjectDatum) {
            $model = new FlowStatusEnumModel();
            $model->save([
                'attribution_id' => $oldProjectDatum->status_enum_id,
                'name' => $oldProjectDatum->name,
                'colour' => $oldProjectDatum->colour,
                'is_permanent' => $oldProjectDatum->is_permanent,
                'status_enum_type' => $oldProjectDatum->status_enum_type,

                'project_id' => $newProjectId
            ]);
        }
    }

    /**
     * 根据项目id查询 新旧状态库id
     * @return array
     * User Long
     * Date 2024/10/15
     */
    public static function selectAttributionIdByProjectId()
    {
        return FlowStatusEnumModel::status()->where([
            'project_id' => FlowStatusEnumModel::DEFAULT_PROJECT_ID,
        ])->column('status_enum_id', 'attribution_id');
    }
}
