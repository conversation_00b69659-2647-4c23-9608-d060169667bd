<?php
/**
 * Desc 迭代流程节点关系 逻辑
 * User Long
 * Date 2024/11/25
 */

namespace app\iterate\logic;

use app\iterate\model\ProcessNodeRelationModel;

class FlowProcessNodeRelationLogic
{
    /**
     * 复制项目数据 - 流程节点关系
     * @param array $flowProcessNodeData
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/25
     */
    public static function copyProjectDataByOldNodeId(array $flowProcessNodeData)
    {
        $oldNodeIds = array_keys($flowProcessNodeData);
        $oldProjectData = ProcessNodeRelationModel::whereIn('process_node_id', $oldNodeIds)->select();

        foreach ($oldProjectData as $oldProjectDatum) {
            // 复制类别
            $flowStatusNodeRelationModel = new ProcessNodeRelationModel();
            $flowStatusNodeRelationModel->save([
                'process_node_id' => $flowProcessNodeData[$oldProjectDatum->process_node_id] ?? $oldProjectDatum->process_node_id,
                'next_node_id' => $flowProcessNodeData[$oldProjectDatum->next_node_id] ?? $oldProjectDatum->next_node_id
            ]);
        }
    }
}
