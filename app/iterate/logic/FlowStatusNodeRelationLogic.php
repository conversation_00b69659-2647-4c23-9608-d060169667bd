<?php
/**
 * Desc 工作流状态与节点关系 - 逻辑层
 * User Long
 * Date 2024/7/27
 */

namespace app\iterate\logic;

use app\iterate\model\FlowStatusNodeRelationModel;
use basic\BaseLogic;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class FlowStatusNodeRelationLogic extends BaseLogic
{
    /**
     * 保存状态与节点关系
     * @param int $flowStatusId 状态流程id
     * @param int $statusTextId 状态描述id
     * @param int $processNodeId 流程节点id
     * User Long
     * Date 2024/8/9
     */
    public static function saveFlowStatusTextData(int $flowStatusId, int $statusTextId, int $processNodeId): void
    {
        $model = new FlowStatusNodeRelationModel();
        $model->flow_status_id = $flowStatusId;
        $model->status_text_id = $statusTextId;
        $model->process_node_id = $processNodeId;
        $model->save();
    }

    /**
     * 删除状态与节点关系（真删）
     * @param int $flowStatusId
     * User Long
     * Date 2024/8/10
     */
    public static function destroyFlowStatusTextData(int $flowStatusId): void
    {
        $model = new FlowStatusNodeRelationModel();
        $model->destroy(['flow_status_id' => $flowStatusId], true);
    }

    /**
     * 复制 状态与节点关系
     * @param array $oldFlowStatusIds
     * @param array $flowStatusData
     * @param array $flowStatusTextData
     * @param array $flowProcessNodeData
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/15
     */
    public static function copyProjectDataByOldFlowStatusId(array $oldFlowStatusIds, array $flowStatusData, array $flowStatusTextData, array $flowProcessNodeData)
    {
        $oldProjectData = FlowStatusNodeRelationModel::whereIn('flow_status_id', $oldFlowStatusIds)->select();

        foreach ($oldProjectData as $oldProjectDatum) {
            // 复制类别
            $flowStatusNodeRelationModel = new FlowStatusNodeRelationModel();
            $flowStatusNodeRelationModel->save([
                'flow_status_id' => $flowStatusData[$oldProjectDatum->flow_status_id] ?? $oldProjectDatum->flow_status_id,
                'status_text_id' => $flowStatusTextData[$oldProjectDatum->status_text_id] ?? $oldProjectDatum->status_text_id,
                'process_node_id' => $flowProcessNodeData[$oldProjectDatum->process_node_id] ?? $oldProjectDatum->process_node_id
            ]);
        }
    }

    /**
     * 根据状态id查询状态与节点关系
     * @param int $flowStatusId
     * @return FlowStatusNodeRelationModel[]|array|\think\Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/9
     */
    public static function selectRelationDataByStatusId(int $flowStatusId)
    {
        return FlowStatusNodeRelationModel::where(['flow_status_id' => $flowStatusId])->select();
    }
}
