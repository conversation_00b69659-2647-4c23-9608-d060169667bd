<?php
/**
 * Desc 工作流程 自动化数据处理
 * User Long
 * Date 2024/11/7
 */

namespace app\iterate\logic;

use app\infrastructure\logic\FieldConfigLogic;
use app\infrastructure\model\FieldConfigModel;

class FlowProcessAutoLogic
{
    const STATUS_ENUM = 'status_enum'; //状态
    const PRESENT_NODE = 'present_node'; //当前节点
    const ITERATION_PROGRESS = 'iteration_progress'; //迭代进度
    const TEST_PLAN_COMPLETION_PROGRESS = 'test_plan_completion_progress'; //测试计划完成进度
    const SELF_TEST_PLAN_COMPLETION_PROGRESS = 'self_test_plan_completion_progress'; //开发自测计划完成进度
    const AUTO_COMPLETION_PROGRESS = 'auto_completion_progress'; //自动化完成进度
    const DEMAND_NUMBER = 'demand_number'; //需求数
    const DEMAND_INCOMPLETE_NUMBER = 'demand_incomplete_number'; //需求未完成数量
    const DEMAND_COMPLETE_NUMBER = 'demand_complete_number'; //需求已完成数量
    const DEFECT_NUMBER = 'defect_number'; //缺陷数
    const DEFECT_INCOMPLETE_NUMBER = 'defect_incomplete_number'; //缺陷未完成数量
    const DEFECT_COMPLETE_NUMBER = 'defect_complete_number'; //缺陷已完成数量
    const TASK_NUMBER = 'task_number'; //任务数
    const TASK_INCOMPLETE_NUMBER = 'task_incomplete_number'; //任务未完成数量
    const TASK_COMPLETE_NUMBER = 'task_complete_number'; //任务已完成数量

    // 使用的配置
    const USED_BY_CONFIG = [
        self::PRESENT_NODE,
        self::ITERATION_PROGRESS,
        self::TEST_PLAN_COMPLETION_PROGRESS,
        self::SELF_TEST_PLAN_COMPLETION_PROGRESS,
        self::AUTO_COMPLETION_PROGRESS,
        self::DEMAND_NUMBER,
        self::DEMAND_INCOMPLETE_NUMBER,
        self::DEMAND_COMPLETE_NUMBER,
        self::DEFECT_NUMBER,
        self::DEFECT_INCOMPLETE_NUMBER,
        self::DEFECT_COMPLETE_NUMBER,
        self::TASK_NUMBER,
        self::TASK_INCOMPLETE_NUMBER,
        self::TASK_COMPLETE_NUMBER,
    ];

    // 比较符
    const EQ = ['label' => '等于', 'value' => 'eq'];
    const NEQ = ['label' => '不等于', 'value' => 'neq'];
    const IN = ['label' => '包含', 'value' => 'in'];
    const NOTIN = ['label' => '不包含', 'value' => 'notin'];
    const GT = ['label' => '大于', 'value' => 'gt'];
    const EGT = ['label' => '小于', 'value' => 'egt'];

    // 状态
    private array $statusEnum = [];
//    private array $presentNode = [self::EQ, self::NEQ, self::IN, self::NOTIN];
    private array $presentNode = [self::EQ, self::NEQ]; // 2024-12-26 关闭当前节点的包含选项
    private array $iterationProgress = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $testPlanCompletionProgress = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $selfTestPlanCompletionProgress = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $autoCompletionProgress = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $demandNumber = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $demandIncompleteNumber = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $demandCompleteNumber = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $defectNumber = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $defectIncompleteNumber = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $defectCompleteNumber = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $taskNumber = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $taskIncompleteNumber = [self::EQ, self::NEQ, self::GT, self::EGT];
    private array $taskCompleteNumber = [self::EQ, self::NEQ, self::GT, self::EGT];

    /**
     * 获取配置
     * @return array
     * User Long
     * Date 2024/11/7
     */
    public function config()
    {
        $config = FieldConfigLogic::columnFieldComponentByFieldName(FieldConfigModel::MODULE_TYPE_ITERATION, self::USED_BY_CONFIG);

        foreach ($config as &$value) {
            $value['op'] = match ($value['field_name']) {
                self::STATUS_ENUM => $this->statusEnum,
                self::PRESENT_NODE => $this->presentNode,
                self::ITERATION_PROGRESS => $this->iterationProgress,
                self::TEST_PLAN_COMPLETION_PROGRESS => $this->testPlanCompletionProgress,
                self::SELF_TEST_PLAN_COMPLETION_PROGRESS => $this->selfTestPlanCompletionProgress,
                self::AUTO_COMPLETION_PROGRESS => $this->autoCompletionProgress,
                self::DEMAND_NUMBER => $this->demandNumber,
                self::DEMAND_INCOMPLETE_NUMBER => $this->demandIncompleteNumber,
                self::DEMAND_COMPLETE_NUMBER => $this->demandCompleteNumber,
                self::DEFECT_NUMBER => $this->defectNumber,
                self::DEFECT_INCOMPLETE_NUMBER => $this->defectIncompleteNumber,
                self::DEFECT_COMPLETE_NUMBER => $this->defectCompleteNumber,
                self::TASK_NUMBER => $this->taskNumber,
                self::TASK_INCOMPLETE_NUMBER => $this->taskIncompleteNumber,
                self::TASK_COMPLETE_NUMBER => $this->taskCompleteNumber,
                default => [],
            };
            $value['field_component'] = json_decode($value['field_component'], true);
        }
        unset($value);

        return $config;
    }
}
