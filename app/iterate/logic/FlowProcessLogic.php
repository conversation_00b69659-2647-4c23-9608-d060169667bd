<?php
declare (strict_types=1);

namespace app\iterate\logic;

use app\iterate\model\FlowProcessModel;
use app\iterate\model\FlowProcessNodeModel;
use app\iterate\model\ProcessNodeRelationModel;
use app\iterate\validate\FlowProcessValidate;
use app\microservice\model\MicroserviceModel;
use app\project\logic\IterationProcessNodeLogic;
use app\project\logic\IterationProcessNodeRelationLogic;
use app\project\logic\ProjectInfoLogic;
use app\project\logic\ProjectTemplateLogic;
use app\project\model\ProjectTemplateModel;
use basic\BaseLogic;
use basic\BaseModel;
use exception\BusinessException;
use exception\NotFoundException;
use exception\ParamsException;
use think\facade\Db;

class FlowProcessLogic extends BaseLogic
{
    // 重命名链接
    private const RENAME_URL = '/iterate/flowProcess/rename';

    /**
     * 校验名称是否与模板相同
     * @param          $projectInfo
     * @param  string  $flowProcessName
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function validateProjectFlowProcessData($projectInfo, string $flowProcessName = ''): void
    {
        if ($projectInfo) {
            if ($projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
                $templateId = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);

                if ($templateId) {
                    $flowProcessNameExist = FlowProcessModel::findStatusFlowAndName($templateId, $flowProcessName);
                    if ($flowProcessNameExist) {
                        throw new ParamsException(ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id).'-工作流程名称已存在');
                    }
                }
            } else {
                $projectIds = ProjectInfoLogic::getProjectIdsByTemplateId($projectInfo->project_template_id)
                    ->where('is_template', ProjectTemplateModel::IS_TEMPLATE_NO);
                $flowProcessNameExist = FlowProcessModel::status()
                    ->with([
                        'project' => function ($sql) {
                            $sql->bind(['project_name']);
                        }
                    ])
                    ->whereIn('project_id', $projectIds->column('project_id'))
                    ->select();

                if ( ! $flowProcessNameExist->where('flow_process_name', $flowProcessName)->isEmpty()) {
                    throw new ParamsException('【'.implode('】、【', $flowProcessNameExist->where('flow_process_name', $flowProcessName)->column('project_name')).'】-工作流程名称已存在');
                }
            }
        }
    }

    /**
     * 新增
     * @param $params
     * @return FlowProcessModel
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/8/7 下午3:13
     */
    public function create($params)
    {
        validate(FlowProcessValidate::class)->scene('create')->check($params);

        if (FlowProcessModel::countByProjectId($params['project_id']) >= 10) {
            throw new ParamsException("最多创建十个工作流程！");
        }

        try {
            Db::startTrans();
            $model = new FlowProcessModel();
            $model->save($params);

            //节点
            $nodeList = $this->generateNodeList($params['node_list'], $model->flow_process_id);
            $nodeList = (new FlowProcessNodeModel)->saveAll($nodeList);

            //节点关系
            $relationList = $this->generateNodeRelationList($nodeList, $params['node_list']);
            (new ProcessNodeRelationModel())->saveAll($relationList);

            Db::commit();

            return $model->toDetail();
        } catch (\Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 修改
     * @param $params
     * @return MicroserviceModel|array|mixed|\think\Model
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function update($params)
    {
        validate(FlowProcessValidate::class)->scene('update')->check($params);

        $model = FlowProcessModel::findById($params['flow_process_id']);
        if ( ! $model) {
            throw new NotFoundException();
        }

        try {
            Db::startTrans();

            $model = FlowProcessModel::getDataAndNodeRelation($params['flow_process_id']);
            $model->save($params);

            //节点
            $nodeList = $this->generateNodeList($params['node_list'], $model->flow_process_id);
            $nodeList = (new FlowProcessNodeModel)->saveAll($nodeList);

            //删除旧节点
            FlowProcessNodeModel::update(['is_delete' => BaseModel::DELETE_YES], [
                ['process_node_id', 'not in', $nodeList->column('process_node_id')],
                ['flow_process_id', '=', $model->flow_process_id],
            ]);


            //节点关系
            $oldRelationList = [];
            foreach ($model->nodeList as $v) {
                foreach ($v['nextNodeList'] as $s) {
                    $key = $s['process_node_id'].'-'.$s['next_node_id'];
                    $oldRelationList[$key] = $s['id'];
                }
            }

            $relationList = [];
            foreach ($this->generateNodeRelationList($nodeList, $params['node_list']) as $v) {
                $key = $v['process_node_id'].'-'.$v['next_node_id'];
                $relationList[$key] = $v;
            }

            //新增
            (new ProcessNodeRelationModel())->saveAll(array_diff_key($relationList, $oldRelationList));
            //删除
            ProcessNodeRelationModel::destroy(array_values(array_diff_key($oldRelationList, $relationList)));

            Db::commit();

            return $model->toDetail();
        } catch (\Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 生成节点
     * @param $nodeList
     * @param $flowProcessId
     * @return array
     * <AUTHOR>
     * @date   2024/8/8 下午2:26
     */
    private function generateNodeList($nodeList, $flowProcessId)
    {
        $nodeModelList = [];
        foreach ($nodeList as $v) {
            $node = [
                'row_id'          => $v['node']['row_id'],
                'node_name'       => $v['node']['node_name'],
                'node_data'       => $v['setting_config'],
                'flow_process_id' => $flowProcessId,
            ];
            if ($v['node']['process_node_id'] ?? '') {
                $node['process_node_id'] = $v['node']['process_node_id'];
            }
            $nodeModelList[] = $node;
        }

        return $nodeModelList;
    }

    /**
     * 生成节点关系
     * @param $nodeList
     * @param $rowIdList
     * @return array
     * <AUTHOR>
     * @date   2024/8/8 下午2:26
     */
    private function generateNodeRelationList($nodeList, $rowIdList)
    {
        $nodeList = array_column($nodeList->toArray(), null, 'row_id');
        $rowIdList = array_column(array_column($rowIdList, 'node'), null, 'row_id');
        $relationList = [];
        foreach ($nodeList as $q) {
            foreach ($rowIdList[$q['row_id']]['next_node'] as $s) {
                if ( ! isset($nodeList[$s])) {
                    throw new ParamsException("row_id:{$q['row_id']}的后置节点row_id:{$s}不存在");
                }
                $relation = [
                    'process_node_id' => $q['process_node_id'],
                    'next_node_id'    => $nodeList[$s]['process_node_id'],
                ];
                $relationList[] = $relation;
            }
        }

        return $relationList;
    }


    /**
     * 删除
     * @param $flowProcessId
     * @return void
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function delete($flowProcessId)
    {
        $model = FlowProcessModel::findById($flowProcessId);
        if ( ! $model) {
            throw new NotFoundException();
        }
        if ( ! $model->flowStatusList->isEmpty()) {
            throw new ParamsException('应用中的模板不可删除！');
        }
        $model->is_delete = BaseModel::DELETE_YES;
        $model->save();
    }

    /**
     * 详情
     * @param $flowProcessId
     * @return array
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function detail($flowProcessId)
    {
        $model = FlowProcessModel::findById($flowProcessId);
        if ( ! $model) {
            throw new NotFoundException();
        }
        $model = FlowProcessModel::getDataAndNodeRelation($flowProcessId);

        return [
            'flow_process_id'      => $model->flow_process_id,
            'flow_process_name'    => $model->flow_process_name,
            'flow_process_desc'    => $model->flow_process_desc,
            'project_id'           => $model->project_id,
            'version'              => $model->version,
            'bug_liquidation_time' => date('H:i', strtotime($model->bug_liquidation_time)),
            'node_list'            => $this->formatNodeList($model->nodeList),
        ];
    }

    /**
     * 格式化nodeList供响应
     * @param $nodeList
     * @return array
     * <AUTHOR>
     * @date   2024/8/8 下午4:37
     */
    private function formatNodeList($nodeList)
    {
        $result = [];
        foreach ($nodeList as $v) {
            $result[] = [
                'node'           => [
                    'process_node_id' => $v['process_node_id'],
                    'node_name'       => $v['node_name'],
                    'row_id'          => $v['row_id'],
                    'prev_node'       => array_column(array_column($v->prevNodeList->toArray(), 'prevNode'), 'row_id'),
                    'next_node'       => array_column(array_column($v->nextNodeList->toArray(), 'nextNode'), 'row_id'),
                ],
                'setting_config' => $v['node_data'],
            ];
        }

        return $result;
    }


    /**
     * 列表
     * @param $params
     * @return array|FlowProcessModel[]|\think\Collection
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function pageQuery($params)
    {
        $projectIds[] = $params['project_id'];
        $templateIds = [];

        $projectInfo = ProjectInfoLogic::getProjectData($params['project_id']);
        if ($projectInfo && $projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
            $templateIds[] = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);
        }
        $projectIds = array_merge($projectIds, $templateIds);

        return FlowProcessModel::status()
            ->with(['flowStatusList'])
//            ->where(['project_id' => $params['project_id']])
            ->whereIn('project_id', $projectIds)
            ->field(FlowProcessModel::LIST_FIELDS)->order('flow_process_id desc')
            ->order('project_id ASC')
            ->select()->each(function ($v) use ($projectInfo, $templateIds) {
                //未被应用，才可删除
                $v['canBeDeleted'] = $v['flowStatusList']->isEmpty();

                $v['template_not_allowed_del'] = false; // 模板字段不允许删除，默认否
                // 项目内模板字段不允许编辑
                if (isset($projectInfo->is_template) && $projectInfo->is_template == 0 && in_array($v['project_id'], $templateIds)) {
                    $v['template_not_allowed_del'] = true;
                }

                unset($v['flowStatusList']);
            });
    }

    /**
     * 复制
     * @param $flowProcessId
     * @param $project_id
     * @return FlowProcessModel
     * <AUTHOR>
     * @date   2024/7/8 上午10:19
     */
    public function copy($flowProcessId, $project_id)
    {
        $detail = $this->detail($flowProcessId);
        $detail['flow_process_name'] = FlowProcessModel::generateName($detail['flow_process_name'], $detail['project_id']);
        unset($detail['flow_process_id']);
        foreach ($detail['node_list'] as &$v) {
            unset($v['node']['process_node_id']);
        }
        if ($project_id) {
            $detail['project_id'] = $project_id;
        }
        return $this->create($detail);
    }

    /**
     * 流程下拉数据
     * @param $projectId
     * @return array
     * <AUTHOR>
     * @date   2024/8/8 下午5:09
     */
    public function selector($projectId)
    {
        $projectIds[] = $projectId;
        $templateIds = [];

        $projectInfo = ProjectInfoLogic::getProjectData($projectId);
        if ($projectInfo && $projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
            $templateIds[] = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);
        }
        $projectIds = array_merge($projectIds, $templateIds);

        //        return FlowProcessModel::status()
////            ->where(['project_id' => $projectId])
//            ->whereIn('project_id', $projectIds)
//            ->order('project_id ASC, flow_process_id DESC')
//            ->column('flow_process_name as label, flow_process_id as value');

        return FlowProcessModel::status()
//            ->where(['project_id' => $projectId])
            ->whereIn('project_id', $projectIds)
            ->order('project_id ASC, flow_process_id DESC')
            ->field('flow_process_name as label, flow_process_id as value, project_id')
            ->select()
            ->each(function ($item) use ($projectInfo, $templateIds) {
                $item['template_not_allowed_del'] = false; // 模板字段不允许删除，默认否
                // 项目内模板字段不允许编辑
                if (isset($projectInfo->is_template) && $projectInfo->is_template == 0 && in_array($item['project_id'], $templateIds)) {
                    $item['template_not_allowed_del'] = true;
                }
            });
    }

    /**
     * 流程下拉数据
     * @param $flowProcessId
     * @return array
     * <AUTHOR>
     * @date   2024/8/8 下午5:09
     */
    public function nodeSelector($flowProcessId)
    {
        return FlowProcessNodeModel::selector($flowProcessId);
    }

    /**
     * 根据节点id集查询节点信息
     * @param $processNodeIds
     * @return FlowProcessNodeModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/8/8
     */
    public static function selectProcessNodeByIds($processNodeIds)
    {
        return FlowProcessNodeModel::selectProcessNodeByIds($processNodeIds);
    }

    /**
     * 根据工作流程id查询节点信息
     * @param $flowProcessId
     * @return FlowProcessNodeModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/8/10
     */
    public static function findProcessNodeById($flowProcessId)
    {
        return FlowProcessModel::findProcessNodeById($flowProcessId);
    }

    /**
     * 根据工作流程id查询流程信息
     * @param $flowProcessId
     * @return FlowProcessModel|array|mixed|\think\Model|null
     * User Long
     * Date 2024/8/10
     */
    public static function findProcessById($flowProcessId)
    {
        return FlowProcessModel::findById($flowProcessId);
    }

    /**
     * 复制工作流程
     * @param  int  $oldProjectId
     * @param  int  $newProjectId
     * @param  int  $flowProcessId  // 复制至 功能增加, 默认不使用
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     *                              User Long
     *                              Date 2025/4/12
     */
    public static function copyProjectDataByProjectId(int $oldProjectId, int $newProjectId, int $flowProcessId = 0)
    {
        $oldProjectData = FlowProcessModel::status()->with(['nodeList']);

        if ($flowProcessId) {
            $oldProjectData = $oldProjectData->where('flow_process_id', $flowProcessId);
        } else {
            $oldProjectData = $oldProjectData->where([
                'project_id' => $oldProjectId,
            ]);
        }

        $oldProjectData = $oldProjectData->select();

        $newFlowProcess = ['data' => [], 'is_rename' => false];

        if ($flowProcessId) {
            $diffData = FlowProcessModel::status()->where([
                'project_id'        => $newProjectId,
                'flow_process_name' => $oldProjectData->first()->flow_process_name
            ])->column('flow_process_name, project_id', 'project_id');
        }

        foreach ($oldProjectData as $oldProjectDatum) {
            $rename = $oldProjectDatum->flow_process_name;
            if (isset($diffData) && isset($diffData[$newProjectId])) {
                if ( ! $newFlowProcess['is_rename']) {
                    $newFlowProcess['is_rename'] = true;
                }
                $rename = FlowProcessModel::generateName($diffData[$newProjectId]['flow_process_name'], $diffData[$newProjectId]['project_id']);
            }
            // 复制工作流程
            $flowProcessModel = new FlowProcessModel();
            $flowProcessModel->save([
                'attribution_id'    => $oldProjectDatum->flow_process_id,
                'flow_process_name' => $rename,
                'flow_process_desc' => $oldProjectDatum->flow_process_desc,
                'version'           => $oldProjectDatum->version,
                'project_id'        => $newProjectId
            ]);
            $newFlowProcess['flow_process_id'] = $flowProcessModel->flow_process_id;
            $newFlowProcess['attribution_id'] = $oldProjectDatum->flow_process_id;

            if (isset($diffData) && isset($diffData[$newProjectId])) {
                $newFlowProcess['data']['flow_process'][] = [
                    'flow_process_id'   => $flowProcessModel->flow_process_id,
                    'flow_process_name' => $rename,
                    'project_id'        => $newProjectId,
                    'url'               => self::RENAME_URL
                ];
            }

            foreach ($oldProjectDatum->nodeList as $item) {
                // 复制工作流程节点
                $flowProcessNodeModel = new FlowProcessNodeModel();
                $flowProcessNodeModel->save([
                    'attribution_id'  => $item->process_node_id,
                    'flow_process_id' => $flowProcessModel->flow_process_id,
                    'node_name'       => $item->node_name,
                    'node_data'       => $item->node_data,
                    'row_id'          => $item->row_id
                ]);
            }
        }

        return $newFlowProcess;
    }

    /**
     * 根据项目id查询 新旧工作流程id
     * @param  int  $projectId
     * @return array
     * User Long
     * Date 2024/10/15
     */
    public static function selectAttributionIdByProjectId(int $projectId)
    {
        $model = FlowProcessModel::status()->where([
            'project_id' => $projectId,
        ]);

        return $model->column('flow_process_id', 'attribution_id');
    }


    /**
     * 根据项目id查询 工作流程id
     * @param  int  $projectId
     * @return array
     * User Long
     * Date 2024/10/15
     */
    public static function selectFlowProcessIdByProjectId(int $projectId)
    {
        $model = FlowProcessModel::status()->where([
            'project_id' => $projectId,
        ]);

        return $model->column('flow_process_id');
    }

    /**
     * 根据流程id查询 节点id（key 为归属来源id）
     * @param  array  $flowProcessIds
     * @return array
     * User Long
     * Date 2024/10/15
     */
    public static function selectAttributionIdByFlowProcessIds(array $flowProcessIds)
    {
        return FlowProcessNodeModel::status()->whereIn('flow_process_id', $flowProcessIds)->column('process_node_id', 'attribution_id');
    }

    /**
     * 复制流程模板（迭代创建用）
     * @param  int    $projectId
     * @param  int    $iterationId
     * @param  int    $flowProcessId
     * @param  int    $flowStatusId
     * @param  array  $extends
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/9
     */
    public static function copyProcessTemplate(int $projectId, int $iterationId, int $flowProcessId, int $flowStatusId, array $extends)
    {
        // 获取指定工作流程的所有节点
        $nodeModel = FlowProcessNodeModel::status()->where(['flow_process_id' => $flowProcessId])->select();

        if ($nodeModel->isEmpty()) {
            throw new BusinessException('请先设置工作流程');
        }

        // 获取指定状态的所有节点关系
        $flowStatusNodeModel = FlowStatusNodeRelationLogic::selectRelationDataByStatusId($flowStatusId);

        // 获取指定节点的所有关系
        $nodeRelationModel = ProcessNodeRelationModel::where(['process_node_id' => $nodeModel->column('process_node_id')])->select();

        // 复制工作流程节点数据到新的迭代中
        IterationProcessNodeLogic::copyNodeData($nodeModel, $flowStatusNodeModel, $iterationId, $projectId, $extends);

        // 获取新迭代中的节点数据
        $iterationNodeData = IterationProcessNodeLogic::selectRelationDataByStatusId($iterationId);

        // 复制节点关系数据到新的迭代中
        IterationProcessNodeRelationLogic::copyNodeData($nodeRelationModel, $iterationNodeData);
    }

    /**
     * 根据流程节点id查询row_id
     * @param  array  $flowProcessNodeIds
     * @return mixed
     * User Long
     * Date 2024/11/12
     */
    public static function getRowIdByFlowProcessNodeIds(array $flowProcessNodeIds)
    {
        return FlowProcessNodeModel::whereIn('process_node_id', $flowProcessNodeIds)->column('row_id', 'process_node_id');
    }

    /**
     * 更新工作流程图名称
     * @param  int     $flowProcessId
     * @param  string  $flowProcessName
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function rename(int $flowProcessId, string $flowProcessName)
    {
        $model = FlowProcessModel::findById($flowProcessId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $flowProcessNameExist = FlowProcessModel::findStatusFlowAndName($model->project_id, $flowProcessName);
        $projectInfo = ProjectInfoLogic::getProjectData($model->project_id);

        if ($flowProcessNameExist && $flowProcessNameExist->flow_process_id != $flowProcessId) {
            $name = match ($projectInfo->is_template) {
                ProjectTemplateModel::IS_TEMPLATE_YES => ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id),
                ProjectTemplateModel::IS_TEMPLATE_NO => '【'.$projectInfo->project_name.'】',
                default => '',
            };
            throw new ParamsException($name.'-工作流程名称已存在');
        }

        // 校验名称是否与模板相同
        $this->validateProjectFlowProcessData($projectInfo, $flowProcessName);

        $model->flow_process_name = $flowProcessName;
        $model->save();
    }
}
