<?php
declare (strict_types=1);

namespace app\iterate\logic;

use app\infrastructure\logic\FieldConfigLogic;
use app\infrastructure\model\EnumModel;
use app\infrastructure\model\FieldConfigModel;
use app\iterate\model\FlowStatusEnumModel;
use app\iterate\model\IterationModel;
use app\iterate\validate\IterationImportExportValidate;
use app\project\logic\IterationCatalogLogic;
use app\project\model\ProjectModel;
use basic\BaseLogic;
use basic\BaseModel;
use excel_utils\Hanlder;
use exception\ParamsException;
use field_utils\Transfer;
use think\Exception;
use think\facade\Filesystem;
use think\File;
use think\Validate;
use utils\DBTransaction;
use utils\Log;
use PhpOffice\PhpSpreadsheet\Shared\Date as PhpSpreadsheetDate;

class IterationImportExportLogic extends BaseLogic
{
    protected int $moduleId;
    protected IterationCatalogLogic $iterationCatalogLogic;
    protected FieldConfigLogic $fieldConfigLogic;
    protected FlowStatusTextLogic $flowStatusLogic; // 假设状态名称到ID的逻辑在这里
    protected Transfer $transfer;
    protected string $moduleLabel;

    // protected $settingType; // 判断迭代模块是否需要

    public function __construct(
        IterationCatalogLogic $iterationCatalogLogic,
        FieldConfigLogic $fieldConfigLogic,
        FlowStatusTextLogic $flowStatusLogic,
        Transfer $transfer
    ) {
        $this->moduleId = FieldConfigModel::MODULE_TYPE_ITERATION;
        $this->iterationCatalogLogic = $iterationCatalogLogic;
        $this->fieldConfigLogic = $fieldConfigLogic;
        $this->flowStatusLogic = $flowStatusLogic;
        $this->transfer = $transfer;
        $this->moduleLabel = $this->getModuleLabel();
        // $this->settingType = $this->getSettingType(); // 如果需要，确定其值
    }

    // 基于计划的占位符方法 - 将在后续步骤中实现
    public function getLogic(): IterationCatalogLogic
    {
        return $this->iterationCatalogLogic;
    }

    public function getModel(): IterationModel
    {
        // 虽然逻辑层使用 IterationCatalogLogic，但可能仍需要访问模型
        return new IterationModel();
    }

    public function getModuleLabel(): string
    {
        return "迭代";
    }

    // public function getSettingType() { /* 如果需要，确定其值 */ }

    public function getImportValidate(string $subKey): Validate
    {
        // 使用全局的 validate() 辅助函数，假设 IterationImportExportValidate 构造函数使用了依赖注入
        $scene = $this->isCreate($subKey) ? 'create' : 'update';
        return validate(IterationImportExportValidate::class)->scene($scene);
    }

    public function isCreate(string $subKey): bool
    {
        return str_ends_with($subKey, '_add'); // 假设子键格式类似于 'iteration_output_add'
    }

    /**
     * 格式化导入数据
     * @param  string  $subKey    'iteration_output_add' or 'iteration_output_update'
     * @param  int     $projectId
     * @param  array   $dataList  Raw data from Excel, first row is header
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function formatImportData(string $subKey, int $projectId, array $dataList): array
    {
        if (empty($dataList)) {
            return [
                'fieldList'       => [],
                'head'            => [],
                'headColumnIndex' => [],
                'nameToLabel'     => [],
                'dataList'        => [],
                'ignoredColumns'  => [],
            ];
        }

        // 获取迭代模块（ID 4）的字段配置
        // subKey（例如 'iteration_output_add'）帮助 FieldConfigLogic 确定要返回的字段集（例如，用于创建或更新）
        $fieldConfigs = $this->fieldConfigLogic->getListBySubKey($subKey, $projectId, $this->moduleId)->column(null, 'field_label');

        $nameToLabel = []; // 字段名 => 字段标签
        $labelToName = []; // 字段标签 => 字段名
        foreach ($fieldConfigs as $label => $config) {
            $nameToLabel[$config['field_name']] = $label;
            $labelToName[$label] = $config['field_name'];
        }
        // 为更新场景添加 ID，因为它不是标准的字段配置，但是一个常见的表头
        if ( ! $this->isCreate($subKey)) {
            $nameToLabel['iteration_id'] = 'ID'; // 假设 'ID' 是 iteration_id 的表头
            $labelToName['ID'] = 'iteration_id';
            // 如果需要，为了保持一致性，将 'ID' 添加到 fieldConfigs 的模拟数据中，或者单独处理
            if ( ! isset($fieldConfigs['ID'])) {
                $fieldConfigs['ID'] = ['field_name' => 'iteration_id', 'field_label' => 'ID', 'field_type' => FieldConfigModel::FIELD_TYPE_SYSTEM]; // 用于处理的模拟数据
            }
        }


        $head = array_shift($dataList); // 第一行是表头

        $head = array_map(function ($s) {
            if (is_string($s)) {
                return trim($s);
            }
            return $s;
        }, $head); // 去除表头中的空格

        $validHead = [];
        $headColumnIndex = []; // 字段名 => 列索引
        $ignoredColumns = [];
        $processedHeadLabels = []; // 字段标签 => 字段名 (用于有效列)


        foreach ($head as $columnIndex => $headerLabel) {
            if (isset($labelToName[$headerLabel])) {
                $fieldName = $labelToName[$headerLabel];
                if (isset($headColumnIndex[$fieldName])) {
                    continue;
                }
                $validHead[] = $headerLabel; // 存储原始标签
                $headColumnIndex[$fieldName] = $columnIndex;
                $processedHeadLabels[$headerLabel] = $fieldName;
            } else {
                $ignoredColumns[] = $headerLabel;
            }
        }

        $formattedDataList = [];
        foreach ($dataList as $row) {
            $formattedRow = [];
            // 确保行的元素数量与原始（未过滤的）表头相同
            // 如有必要，用 null 填充，尽管 ExcelHandler 通常确保行长度一致
            $paddedRow = array_pad($row, count($head), null);
            foreach ($processedHeadLabels as $label => $fieldName) {
                $columnIndex = -1;
                // 查找此有效表头标签的原始列索引
                foreach ($head as $idx => $originalHeaderLabel) {
                    if ($originalHeaderLabel === $label && isset($labelToName[$originalHeaderLabel]) && $labelToName[$originalHeaderLabel] === $fieldName) {
                        $columnIndex = $idx;
                        break;
                    }
                }
                if ($columnIndex !== -1 && isset($paddedRow[$columnIndex])) {
                    $formattedRow[$fieldName] = $paddedRow[$columnIndex];
                } else {
                    $formattedRow[$fieldName] = null; // 或某些默认值
                }
            }
            $formattedDataList[] = $formattedRow;
        }

        // fieldList 应该是 field_label => config 以与原始 ImportAndExportLogic 保持一致
        // 但我们的 $fieldConfigs 已经是 field_label => config
        // 对于返回，我们需要 field_name => config 以便在 import() 中更容易访问
        $finalFieldList = [];
        foreach ($fieldConfigs as $label => $config) {
            if (isset($labelToName[$label])) {
                $finalFieldList[$labelToName[$label]] = $config;
            }
        }


        return [
            'fieldList'       => $finalFieldList, // 字段名 => 配置
            'head'            => $validHead,          // 有效的表头标签
            'headColumnIndex' => $headColumnIndex,    // 字段名 => 原始Excel中的列索引
            'nameToLabel'     => $nameToLabel,        // 字段名 => 字段标签
            'dataList'        => $formattedDataList,  // 以字段名为键的数据行
            'ignoredColumns'  => $ignoredColumns,
        ];
    }

    /**
     * 获取导出表头模板
     * @param  array  $params  需要 'sub_key', 'project_id', 'field_list' (字段名数组)
     * @return array
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getExportHeaderTemplate(array $params): array
    {
        // 验证参数 - 目前是一个简单的验证
        if (empty($params['sub_key']) || empty($params['project_id']) || empty($params['field_list'])) {
            throw new Exception("参数 'sub_key', 'project_id', 'field_list' 不能为空");
        }
        if ( ! is_array($params['field_list'])) {
            throw new Exception("'field_list' 必须是数组");
        }

        // 获取给定 sub_key 和 project_id 的字段配置
        // FieldConfigLogic::getListBySubKey 需要 module_id 作为第三个参数
        $fieldConfigs = $this->fieldConfigLogic->getListBySubKey($params['sub_key'], (int)$params['project_id'], $this->moduleId)
            ->column(null, 'field_name'); // 按字段名索引
        $allowedFieldNames = array_keys($fieldConfigs);
        $requestedFieldNames = $params['field_list'];

        // 过滤请求的字段，只包括允许/配置的字段
        $headerFieldNames = array_intersect($requestedFieldNames, $allowedFieldNames);
        $headerRow = [];
        foreach ($headerFieldNames as $fieldName) {
            if (isset($fieldConfigs[$fieldName])) {
                $headerRow[] = $fieldConfigs[$fieldName]['field_label'];
            }
        }

//        // 如果是更新模板，则将 ID 列作为第一列添加
//        if ( ! $this->isCreate($params['sub_key'])) {
//            array_unshift($headerRow, 'ID');
//        }

        $result = [$headerRow]; // Excel 的数据是行的数组
        $project = ProjectModel::findById((int)$params['project_id']);
        if ( ! $project) {
            throw new Exception("项目不存在");
        }

        $templateType = $this->isCreate($params['sub_key']) ? '导入新建模板' : '导入更新模板';

        return [
            'data'     => $result,
            'fileName' => "{$project->project_name}_{$this->moduleLabel}_{$templateType}_".date("YmdHis"),
        ];
    }

    /**
     * 导出数据
     * @param  array  $params  需要 'project_id', 'field_list' (字段名数组), 'searchParams' (用于筛选的数组)
     * @return array
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function export(array $params): array
    {
        set_time_limit(0);
        ini_set('memory_limit', '1024M');

        if (empty($params['project_id']) || empty($params['field_list'])) {
            throw new Exception("参数 'project_id', 'field_list' 不能为空");
        }
        if ( ! is_array($params['field_list'])) {
            throw new Exception("'field_list' 必须是数组");
        }

        $projectId = (int)$params['project_id'];
        $requestedFieldNames = $params['field_list'];
        $searchParams = $params['searchParams'] ?? [];

        $project = ProjectModel::findById($projectId);
        if ( ! $project) {
            throw new Exception("项目不存在");
        }

        $fieldConfigs = $this->fieldConfigLogic->getListByFieldName([
            'field_name' => $requestedFieldNames,
            'module_id'  => $this->moduleId,
            'project_id' => $projectId
        ])
            ->column(null, 'field_name');

        $querySelectFields = $requestedFieldNames;
        if ( ! in_array('iteration_id', $querySelectFields)) {
            $querySelectFields[] = 'iteration_id';
        }
        if ( ! in_array('extends', $querySelectFields) && count(array_filter($fieldConfigs, fn($fc) => $fc['field_type'] == FieldConfigModel::FIELD_TYPE_CUSTOMIZE)) > 0) {
            $querySelectFields[] = 'extends';
        }
        // Ensure status_text_id is selected if status_text_name is requested for export
        $statusTextNameRequested = false;
        foreach ($requestedFieldNames as $fn) {
            if (isset($fieldConfigs[$fn]) && $fieldConfigs[$fn]['field_label'] === '状态') { // Assuming '状态' is the label for status_text_name
                $statusTextNameRequested = true;
                break;
            }
            if ($fn === 'status_text_name') { // Direct request by field_name
                $statusTextNameRequested = true;
                break;
            }
        }
        if ($statusTextNameRequested && ! in_array('status_text_id', $querySelectFields)) {
            $querySelectFields[] = 'status_text_id';
        }
        $querySelectFields = array_unique($querySelectFields);

        $headerRow = [];
        $finalExportProcessingOrder = []; // Stores field_name or special keys like 'status_text_name'

        foreach ($requestedFieldNames as $fieldName) {
            if ( ! isset($fieldConfigs[$fieldName]) && $fieldName !== 'status_text_name') {
                continue; // Skip fields that are not configured for export
            }

            if ($fieldName === 'iteration_id') {
                $headerRow[] = 'ID';
                $finalExportProcessingOrder[] = 'iteration_id';
            } elseif (isset($fieldConfigs[$fieldName])) {
                $headerRow[] = $fieldConfigs[$fieldName]['field_label'];
                $finalExportProcessingOrder[] = $fieldName;
            } elseif ($fieldName === 'status_text_name' && isset($fieldConfigs['status_text_id'])) {
                $headerRow[] = $fieldConfigs['status_text_id']['field_label']; // Or a generic "Status"
                $finalExportProcessingOrder[] = 'status_text_name'; // Special key for processing
            }
        }

        if (empty($headerRow)) {
            throw new Exception("没有有效的导出字段被选中或配置");
        }

        $result = [$headerRow];
        $query = $this->getModel()->where('project_id', $projectId)->where('is_delete', BaseModel::DELETE_NOT);

        if ( ! empty($searchParams)) {
            $directIterationFields = $this->getModel()->getTableFields();
            foreach ($searchParams as $search) {
                if (isset($search['field_name'], $search['value']) && $search['value'] !== '') {
                    $sField = $search['field_name'];
                    $sValue = $search['value'];
                    $sOp = $search['operate_type'] ?? '=';
                    if (in_array($sField, $directIterationFields)) {
                        if (is_array($sValue) && in_array(strtolower($sOp), ['in', 'not in', 'between'])) {
                            $query = $query->where($sField, $sOp, $sValue);
                        } else {
                            if ( ! is_array($sValue)) {
                                $query = $query->where($sField, $sOp, $sValue);
                            }
                        }
                    }
                }
            }
        }

//        $iterationDataList = $query->field($querySelectFields)->select()->toArray();
        $iterationDataList = $query->select()->toArray();

        foreach ($iterationDataList as $iteration) {
            $itemRow = [];
            $extendsData = isset($iteration['extends']) ? ((is_array($iteration['extends']) ? $iteration['extends'] : json_decode($iteration['extends'], true)) ?: []) : [];

            foreach ($finalExportProcessingOrder as $processingKey) {
                $fieldValue = null;
                // Determine the actual field config to use for formatting
                $currentFieldConfig = $fieldConfigs[$processingKey] ?? null;
                if ($processingKey === 'iteration_id') {
                    $currentFieldConfig = ['field_name' => 'iteration_id', 'field_label' => 'ID', 'field_type' => FieldConfigModel::FIELD_TYPE_SYSTEM];
                } elseif ($processingKey === 'status_text_name' && isset($fieldConfigs['status_text_id'])) {
                    $currentFieldConfig = $fieldConfigs['status_text_id']; // Use status_text_id config for context
                }

                if ($processingKey === 'iteration_id') {
                    $fieldValue = $iteration['iteration_id'] ?? null;
                } elseif ($processingKey === 'status_enum_id') {
                    $statusId = $iteration['status_text_id'] ?? ($extendsData['status_text_id'] ?? null);
                    if ($statusId !== null) {
                        $statusTextModel = $this->flowStatusLogic->findDataByFlowTextId((int)$statusId);
                        if ($statusTextModel) {
                            $statusEnumModel = FlowStatusEnumModel::findById((int)$statusTextModel->status_enum_id);
                        }
                        $fieldValue = $statusEnumModel->name ?? null; // Export the name
                    }
                } elseif (isset($iteration[$processingKey])) {
                    $fieldValue = $iteration[$processingKey];
                } elseif (isset($extendsData[$processingKey])) {
                    $fieldValue = $extendsData[$processingKey];
                }

                if ($currentFieldConfig) {
                    try {
                        $isMultiple = $currentFieldConfig['field_component']['props']['multiple'] ?? $currentFieldConfig['field_component']['multiple'] ?? false;
                        if ($isMultiple && is_array($fieldValue)) {
                            $formattedValues = [];
                            foreach ($fieldValue as $singleValue) {
                                $formattedValues[] = $this->exportFormat($this->transfer, $currentFieldConfig, $singleValue);
                            }
                            $fieldValue = implode('|', $formattedValues);
                        } else {
                            $fieldValue = $this->exportFormat($this->transfer, $currentFieldConfig, $fieldValue);
                        }
                    } catch (\Throwable $e) {
                        $fieldValue = "错误: ".$e->getMessage();
                        Log::instance(Log::PLATFORM)->error("字段 {$processingKey} 导出格式化错误: ".$e->getMessage());
                    }
                }
                $itemRow[] = ($fieldValue === null || $fieldValue === '') ? '' : $fieldValue;
            }
            $result[] = $itemRow;
        }

        return [
            'data'     => $result,
            'fileName' => "{$project->project_name}_{$this->moduleLabel}_数据导出_".date("YmdHis"),
        ];
    }

    /**
     * 导入数据
     * @param  array  $params  需要 'sub_key', 'project_id', 'file' (UploadedFile 或路径), 'is_repeatable_titles' (布尔值, 可选)
     * @return array
     * @throws Exception
     * @throws \Throwable
     */
    public function import(array $params): array
    {
        set_time_limit(0);
        ini_set('memory_limit', '2048M'); // 增加导入的内存限制

        if (empty($params['file']) && (empty($_FILES['file']['tmp_name']) && empty($params['file_path']))) {
            throw new Exception("文件读取失败，请提供 'file' (UploadedFile) 或 'file_path' (string)");
        }
        if (empty($params['sub_key']) || empty($params['project_id'])) {
            throw new Exception("参数 'sub_key', 'project_id' 不能为空");
        }

        $isCreate = $this->isCreate($params['sub_key']);
        $rawExcelData = Hanlder::readFile($params['file']);
        if (count($rawExcelData) < 2) { // 必须有表头 + 至少一行数据
            throw new Exception("导入文件至少需要包含表头和一行数据");
        }

        $projectId = (int)$params['project_id'];
        $subKey = $params['sub_key'];
        $allowRepeatTitles = (bool)($params['is_repeatable_titles'] ?? true);

        $formattedImport = $this->formatImportData($subKey, $projectId, $rawExcelData);
        $excelDataList = $formattedImport['dataList'];
        $fieldConfigsByName = $formattedImport['fieldList'];
        $headerLabels = $formattedImport['head'];
        $ignoredColumns = $formattedImport['ignoredColumns'];
        $nameToLabelMap = $formattedImport['nameToLabel'];
        $headColumnIndex = $formattedImport['headColumnIndex'];

        if (empty($excelDataList)) {
            throw new Exception("文件中没有有效数据可导入");
        }

        // 验证表头的必需字段
        if ($isCreate && ! isset($headColumnIndex['iteration_name'])) {
            throw new Exception("文件无迭代名称表头，不可导入");
        }
        if ( ! $isCreate && ! isset($headColumnIndex['iteration_id'])) { // 检查 'iteration_id'，它映射到 'ID'
            throw new Exception("导入更新时，文件中必须包含 'ID' 列");
        }

        if ($isCreate && ! isset($headColumnIndex['project_category_settings_id'])) { // 检查 'iteration_id'，它映射到 'ID'
            throw new Exception("文件无迭代类别表头，不可导入");
        }


//        $validator = $this->getImportValidate($subKey);
//        if (method_exists($validator, 'loadCustomFieldRules')) {
//            $validator->loadCustomFieldRules($projectId, $subKey, $headerLabels);
//        }


        $successCount = 0;
        $errorRows = [];
        $processedRowDataForErrorFile = []; // 存储原始Excel行数据以供错误报告

        try {
            foreach ($excelDataList as $rowIndex => $rowData) {
                $itemParams = ['project_id' => $projectId];
                $itemExtends = [];
                $rowErrors = [];
                $originalRowForErrorReport = $excelDataList[$rowIndex] ?? []; // +1 跳过 rawExcelData 中的表头
                foreach ($rowData as $fieldName => $excelValue) {

                    $fieldConfig = $fieldConfigsByName[$fieldName] ?? null;

                    if ($excelValue === null || $excelValue === '' || (is_string($excelValue) && trim($excelValue) === '--')) {
                        if ($fieldConfig && ($fieldConfig['field_component']['componentType'] ?? '') === 'Editor') {
                            $itemParams[$fieldName] = '';
                        }
                        continue;
                    }

                    try {

//                        if ($fieldName=='iteration_leader'){
                        $transformedValue = $this->importFormat($this->transfer, $fieldConfig ?: ['field_name' => $fieldName, 'field_label' => ($nameToLabelMap[$fieldName] ?? $fieldName)], $excelValue);
//                            dd($transformedValue);
//                        }

                        if ($fieldName === 'iteration_id') {
                            $itemParams[$fieldName] = (int)$transformedValue;
                        } elseif ($fieldName === 'status_text_name') {
                            if ( ! empty($transformedValue)) {
                                $statusTextModel = null;
                                if ($statusTextModel && isset($statusTextModel->status_text_id)) {
                                    $itemParams['status_text_id'] = $statusTextModel->status_text_id;
                                    if (isset($statusTextModel->status_enum_id)) {
                                        $itemParams['status_enum_id'] = $statusTextModel->status_enum_id;
                                    }
                                    if (isset($statusTextModel->flow_status_id)) {
                                        $itemParams['flow_status_id'] = $statusTextModel->flow_status_id;
                                    }
                                } elseif ( ! empty($transformedValue)) {
                                    $rowErrors[] = "无效的状态名称: {$transformedValue}";
                                }
                            }
                            // If $transformedValue is empty, do nothing, let IterationCatalogLogic handle default status.
                            // 如果 $transformedValue 为空，则不执行任何操作，让 IterationCatalogLogic 处理默认状态。
                        } elseif ($fieldConfig && $fieldConfig['field_type'] == FieldConfigModel::FIELD_TYPE_CUSTOMIZE) {
                            $itemExtends[$fieldName] = $transformedValue;
                        } else {
                            $itemParams[$fieldName] = $transformedValue;
                        }
                    } catch (\Throwable $e) {
//                        dd((string)$e);
                        $rowErrors[] = ($nameToLabelMap[$fieldName] ?? $fieldName).": ".$e->getMessage();
                    }
                }

                if ( ! empty($itemExtends)) {
                    $itemParams['extends'] = array_merge($itemParams['extends'] ?? [], $itemExtends);
                }

                // Basic validation using the validator for core fields
                // Scene is already set in getImportValidate
                // 使用验证器对核心字段进行基本验证
                // 场景已在 getImportValidate 中设置
//                dd($itemParams,$rowIndex);

//                try {
//                    $validator->check($itemParams);
//                }catch (\Throwable $s){
//                    $rowErrors = array_merge($rowErrors, [$s->getMessage()]);
//                }


//                dd($itemParams);
                if (empty($rowErrors)) {
                    try {
                        if ($isCreate && ! $allowRepeatTitles && ! empty($itemParams['iteration_name'])) {
                            $existingIteration = $this->getModel()->where('project_id', $projectId)
                                ->where('iteration_name', $itemParams['iteration_name'])
                                ->where('is_delete', BaseModel::DELETE_NOT)
                                ->find();
                            if ($existingIteration) {
                                throw new Exception("迭代名称 '{$itemParams['iteration_name']}' 已存在");
                            }
                        }

                        if ( ! $isCreate && ! empty($itemParams['iteration_id'])) {
                            $iterationToUpdate = $this->getModel()->where('iteration_id', $itemParams['iteration_id'])
                                ->where('project_id', $projectId)
                                ->where('is_delete', BaseModel::DELETE_NOT)
                                ->find();
                            if ( ! $iterationToUpdate) {
                                throw new Exception("ID 为 '{$itemParams['iteration_id']}' 的迭代不存在或不属于当前项目");
                            }
                        } elseif ( ! $isCreate && empty($itemParams['iteration_id'])) {
                            throw new Exception("更新操作必须提供迭代ID");
                        }
                        //处理预估开始时间、预估结束时间
                        if ( ! empty($itemParams['estimate_iteration_cycle'])) {
                            $estimate_iteration_cycle = explode('~', $itemParams['estimate_iteration_cycle']);
                            $itemParams['estimate_iteration_cycle'] = $estimate_iteration_cycle;
                            $itemParams['estimate_start_time'] = trim($estimate_iteration_cycle[0]) ?: '1971-01-01 00:00:00';
                            $itemParams['estimate_end_time'] = trim($estimate_iteration_cycle[1]) ?: '1971-01-01 00:00:00';
                        }

                        //默认迭代图标
                        $itemParams['iteration_icon'] = getSystemEnumLibrary(EnumModel::ITERATION_ICON)[0]['value'] ?? throw new ParamsException();
                        $itemParams['extends'] = array_merge($itemParams['extends'],$itemParams);
                        unset($itemParams['extends']['extends']);
                        if ($isCreate) {
//                            dd(1,$itemParams);
                            $this->getLogic()->create($itemParams);
                        } else {
                            $this->getLogic()->update($itemParams['iteration_id'], $itemParams);
                        }
                        $successCount++;
                    } catch (\Throwable $e) {
//                        dd((string)$e);
                        $rowErrors[] = "保存失败: ".$e->getMessage();
                    }
                }

                if ( ! empty($rowErrors)) {
                    $currentErrorRow = $originalRowForErrorReport; // Use original Excel data for error row // 使用原始Excel数据作为错误行
                    $currentErrorRow['error'] = implode(' | ', $rowErrors);
                    $errorRows[] = array_values($currentErrorRow);
                }
            }

            if ( ! empty($errorRows)) {
                array_unshift($errorRows, array_merge($headerLabels, ['错误原因']));

                $errorFile = Hanlder::outputFile($errorRows, 'iteration_import_errors_'.time());

                // 生成临时文件
                $tmpFilePath = tempnam(sys_get_temp_dir(), 'upload_').".xlsx"; // 创建临时文件
                file_put_contents($tmpFilePath, $errorFile->getContent());         // 写入内容

                // 构造 think\File 对象
                $file = new File($tmpFilePath, false);
                $fileUrl = Filesystem::disk('public')->putFile('import_error_file', $file);
                $fileUrl = '/upload/'.$fileUrl;
                unlink($tmpFilePath);
                if (file_exists($tmpFilePath)) { // Ensure temp file is deleted // 确保临时文件被删除
                    unlink($tmpFilePath);
                }


                return [
                    'total'           => count($excelDataList),
                    'success_total'   => $successCount,
                    'fail_total'      => count($excelDataList) - $successCount,
                    'ignored_columns' => $ignoredColumns,
                    'fail_file_url'   => $fileUrl,
                ];
            }

        } catch (\Throwable $e) {
//            dd((string)$e);
            Log::instance(Log::PLATFORM)->error("迭代导入失败: ".$e->getMessage()."\n".$e->getTraceAsString());
            throw $e;
        }

        return [
            'total'           => count($excelDataList),
            'success_total'   => $successCount,
            'fail_total'      => 0,
            'ignored_columns' => $ignoredColumns,
            'fail_file_url'   => '',
        ];
    }

    /**
     * 导出格式化辅助函数
     * @param  Transfer  $transfer  转换器实例
     * @param  array     $field     字段配置 (来自 FieldConfigModel)
     * @param  mixed     $value     来自模型或 'extends' 的原始值
     * @return mixed|string
     */
    public function exportFormat(Transfer $transfer, $field, $value)
    {
        if ($value === null || $value === '') {
            return '';
        }

        $parseKey = $field['field_name'] ?? ($field['field_label'] ?? '');
        if (empty($parseKey)) {
            return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) : (string)$value;
        }

        // 如果需要，对迭代字段进行特定处理，否则依赖 Transfer
        // 示例：如果在此处格式化 status_text_id（尽管它在 export() 中直接处理）
        // if ($field['field_name'] === 'status_text_id') {
        //     $statusTextModel = $this->flowStatusLogic->findDataByFlowTextId((int)$value);
        //     return $statusTextModel->status_text ?? (string)$value;
        // }

        try {
            // 如果 Transfer 是这样设计的，则对系统字段使用 field_name，对自定义字段使用 field_label
            // 原始的 ImportAndExportLogic 对系统字段使用 $field['field_name']，对自定义字段使用 $field['field_label']。
            // 假设 Transfer 可以处理系统字段的 $field['field_name'] 和自定义字段的 $field['field_label']。
            // 为了简单和与原始逻辑一致，我们可能需要传递正确的键。
            // 提供的 $field 数组应包含 'field_name' 和 'field_label'。
            // Transfer::parse 通常期望使用其配置的键（通常是自定义字段的 field_label，系统字段的 field_name）。

            $keyForTransfer = $field['field_name']; // 默认为 field_name，如果 Transfer 期望自定义字段使用标签，则进行调整

            $newValue = $transfer->parse($keyForTransfer, $value);

            $componentType = $field['field_component']['componentType'] ?? null;
            if ($componentType === "Casader" && is_array($newValue)) {
                return implode('/', $newValue);
            }

            // 处理 $newValue 可能来自 Transfer 的数组的情况（例如用户选择器）
            if (is_array($newValue)) {
                // 如果是带有 'label' 的关联数组，则取 label。否则，连接。
                if (isset($newValue['label'])) {
                    return $newValue['label'];
                }
                // 检查它是否是此类关联数组的列表（多选用户）
                if (count($newValue) > 0 && isset($newValue[0]['label'])) {
                    return implode('|', array_column($newValue, 'label'));
                }
                if (in_array($field['field_name'], ['actual_iteration_cycle', 'estimate_iteration_cycle'])) {
                    return implode(' ~ ', $newValue);
                }
                return json_encode($newValue, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            }

            return $newValue;

        } catch (\Throwable $e) {
            Log::instance(Log::PLATFORM)->error("字段 '{$parseKey}' 导出格式化错误: ".$e->getMessage());
            return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) : (string)$value;
        }
    }

    /**
     * 导入格式化辅助函数
     * @param  Transfer  $transfer  转换器实例
     * @param  array     $field     字段配置 (或带有 'field_name', 'field_label', 'component_type', 'field_component' 的模拟数组)
     * @param  mixed     $value     来自Excel的原始值
     * @return mixed
     * @throws Exception
     */
    public function importFormat(Transfer $transfer, $field, $value)
    {
        if ($value === null || (is_string($value) && trim($value) === '') || (is_string($value) && trim($value) === '--')) {
            // 对于大多数字段，null 或空字符串是可以的。
            // 特定组件可能需要不同的处理（例如，编辑器需要空字符串而不是 null）。
            // 这通常在主 import() 方法中调用 importFormat 之前处理。
            // 但是，如果空字符串应明确为数据库的 null：
            if (($field['field_component']['componentType'] ?? '') !== 'Editor' && (is_string($value) && trim($value) === '')) {
                return null;
            }
            return $value; // 按原样返回（空字符串或 null）
        }

        $parseKey = $field['field_label'] ?? ($field['field_name'] ?? ''); // Transfer 通常对自定义字段使用 field_label
        if (empty($parseKey)) {
            return $value; // 没有键无法解析
        }

        $componentType = $field['field_component']['componentType'] ?? null;
        $fieldName = $field['field_name'] ?? '';
        try {
            // 处理多选值，假设它们由 '|' 或 ';' 分隔
            $isMultiple = $field['field_component']['props']['multiple'] ?? $field['field_component']['multiple'] ?? false;
            if ($isMultiple && is_string($value)) {
                $separator = str_contains($value, '|') ? '|' : (str_contains($value, ';') ? ';' : null);
                if ($separator) {
                    $values = explode($separator, $value);
                    $transformedValues = [];
                    foreach ($values as $singleValue) {
                        $transformedValues[] = $this->parseSingleValueForImport($transfer, $field, $componentType, $parseKey, trim($singleValue));
                    }
                    return $transformedValues; // 对于多选字段，返回一个数组
                } else {
                    return [$this->parseSingleValueForImport($transfer, $field, $componentType, $parseKey, trim($value))]; // 对于多选字段，返回一个数组
                }
            }
            return $this->parseSingleValueForImport($transfer, $field, $componentType, $parseKey, $value);

        } catch (\Throwable $e) {
            Log::instance(Log::PLATFORM)->error("字段 '{$parseKey}' 值 '{$value}' 导入格式化错误: ".$e->getMessage());
            throw new Exception($parseKey.'不存在');
        }
    }

    /**
     * 用于 importFormat 的辅助函数，用于解析单个值，适用于单选和多选项目。
     */
    private function parseSingleValueForImport(Transfer $transfer, $field, ?string $componentType, string $parseKey, $value)
    {
        switch (true) {
        case $componentType === "Casader":
            return $transfer->parse($parseKey, explode('/', (string)$value));

        case isset($field['field_component']['type']) && $field['field_component']['type'] == 'date':
            $dateFormat = $field['field_component']['format'] ?? 'YYYY-MM-DD HH:mm:ss';
            $phpDateFormat = match ($dateFormat) {
                'YYYY-MM-DD HH:mm:ss' => 'Y-m-d H:i:s',
                'YYYY-MM-DD' => 'Y-m-d',
                default => 'Y-m-d H:i:s',
            };
            $timestamp = is_numeric($value) ? (int)$value : strtotime((string)$value); // 处理来自Excel的潜在数字时间戳
            if ($timestamp === false || $timestamp < 0) { // strtotime 失败时返回 false，Excel 日期是正数
                // 尝试解析是否为 Excel 日期序列号（自 1900-01-00 以来的天数，但由于 Lotus 1-2-3 的错误，在 PHP 中为 1899-12-30）
                if (is_numeric($value) && $value > 0 && $value < 300000) { // Excel 日期数字的启发式方法
                    // (值 - 25569) * 86400 = Unix 时间戳
                    // PHP 的 DateTime 可以使用 createFromFormat 直接处理 Excel 时间戳
                    try {
                        // 使用别名类名
                        $dateTime = PhpSpreadsheetDate::excelToDateTimeObject($value);
                        return $dateTime->format($phpDateFormat);
                    } catch (\Exception $ex) {
                        throw new Exception("日期/时间值 '{$value}' 格式无法解析为 '{$dateFormat}'");
                    }
                }
                throw new Exception("日期/时间值 '{$value}' 格式无法解析为 '{$dateFormat}'");
            }
            return date($phpDateFormat, $timestamp);

            // 对于 iteration_id、status_text_name 等字段，此处不需要通过 Transfer 进行转换。
            // 它们要么是直接值，要么在主 import() 方法中查找/处理。
        case $field['field_name'] === 'iteration_id':
            return (int)$value;
        case $field['field_name'] === 'status_text_name':
            return (string)$value; // 按原样返回，稍后在 import() 中查找

        default:
            // 默认使用 field_label（或 field_name，如果 Transfer 以此为键）通过 Transfer::parse 解析
            return $transfer->parse($parseKey, $value);
        }
    }
}
