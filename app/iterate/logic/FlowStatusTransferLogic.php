<?php
/**
 * Desc 状态流转 - 逻辑层
 * User Long
 * Date 2024/8/26
 */

namespace app\iterate\logic;

use app\iterate\model\FlowStatusTransferModel;
use basic\BaseLogic;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class FlowStatusTransferLogic extends BaseLogic
{
    /**
     * 保存状态流转关系
     * @param int $flowStatusId 状态流程id
     * @param int $flowTextId 状态描述id
     * @param int $statusEnumId 状态id
     * @param int $targetStatusEnumId 流转的状态id
     * @param array $extends 自定义字段
     * User Long
     * Date 2024/8/26
     */
    public static function saveFlowStatusTransferData(int $flowStatusId,int $flowTextId, int $statusEnumId, int $targetStatusEnumId, array $extends): void
    {
        $model = new FlowStatusTransferModel();
        $model->flow_status_id = $flowStatusId;
        $model->status_text_id = $flowTextId;
        $model->status_enum_id = $statusEnumId;
        $model->target_status_enum_id = $targetStatusEnumId;
        $model->extends = $extends;
        $model->save();
    }

    /**
     * 删除状态流转关系（真删）
     * @param int $flowStatusId 状态流程id
     * User Long
     * Date 2024/8/26
     */
    public static function destroyFlowStatusTransferData(int $flowStatusId): void
    {
        $model = new FlowStatusTransferModel();
        $model->destroy(['flow_status_id' => $flowStatusId], true);
    }

    /**
     * 根据状态描述id获取可流转至的状态数据
     * @param int $statusTextId
     * @return FlowStatusTransferModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/3
     */
    public static function getTargetStatusEnumByStatusTextId(int $statusTextId): Collection|array
    {
        return (new FlowStatusTransferModel())->where([
            'status_text_id' => $statusTextId
        ])
            ->field('id, target_status_enum_id, extends')
            ->with(['enumDetail' => function($sql) {
                $sql->bind(['target_status_enum_name' => 'name', 'colour']);
            }])
            ->select();
    }

    /**
     * 复制状态流转
     * @param array $oldFlowStatusIds
     * @param array $flowStatusData
     * @param array $flowStatusTextData
     * @param array $statusEnumData
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/15
     */
    public static function copyProjectDataByProjectId(array $oldFlowStatusIds, array $flowStatusData, array $flowStatusTextData, array $statusEnumData)
    {
        $oldProjectData = FlowStatusTransferModel::whereIn('flow_status_id', $oldFlowStatusIds)->select();

        foreach ($oldProjectData as $oldProjectDatum) {
            $flowStatusTransferModel = new FlowStatusTransferModel();
            $flowStatusTransferModel->save([
                'flow_status_id' => $flowStatusData[$oldProjectDatum->flow_status_id] ?? $oldProjectDatum->flow_status_id,
                'status_text_id' => $flowStatusTextData[$oldProjectDatum->status_text_id] ?? $oldProjectDatum->status_text_id,
                'status_enum_id' => $statusEnumData[$oldProjectDatum->status_enum_id] ?? $oldProjectDatum->status_enum_id,
                'target_status_enum_id' => $statusEnumData[$oldProjectDatum->target_status_enum_id] ?? $oldProjectDatum->target_status_enum_id,
                'extends' => $oldProjectDatum->extends
            ]);
        }
    }
}
