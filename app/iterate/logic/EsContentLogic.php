<?php
declare (strict_types=1);

namespace app\iterate\logic;

use Elastic\Elasticsearch\ClientInterface;
use think\facade\Env;

class EsContentLogic
{
    private $client;
    private $index;

    public function __construct(ClientInterface $client)
    {
        $this->client = $client;
        $this->index = Env::get('es.content_index');
    }

    public function create()
    {
        $params = [
            'index' => $this->index,
            'id' => 2,
            'body' => [

                'cnt_id' => 2,
                'create_by' => 123,
                'create_by_name' => '曾海洋2',
                'create_at' => '2024-08-20 12:00:00',
                'is_delete' => 0,
                'update_by' => 123,
                'update_by_name' => '曾海洋',
                'update_at' => '2024-08-20 12:00:00',
                'cnt_type' => '1',
                'title' => '迭代设置—类别管理可与页面、字段、流程进行绑定',
                'category_id' => 10,
                'project_id' => 100,
                'iteration_id' => 200,
                'parent_id' => 0,
                'priority' => 1,
                'type_id' => 300,
                'handler' => ['邓凯龙'],
                'handler_uid' => [101],
                'developer' => ['韦顺隆', '袁志凡'],
                'developer_uid' => [102, 103],
                'tester' => [],
                'tester_uid' => [],
                'cc_user' => [],
                'cc_uid' => [],
                'estimate_start_time' => "",
                'estimate_end_time' => "",
                'finish_time' => '2024-08-22 12:00:00',
                'estimated_work_hours' => 40,
                'actual_work_hours' => 35,
                'remaining_work' => 5,
                'exceeding_working_hours' => 0,
                'speed_of_progress' => 75,

            ]

        ];


        $response = $this->client->index($params)->getBody();

        dd(json_decode((string)$response, true));
        dd($response);
    }

    public function update()
    {
        $params = [
            'index' => $this->index,
            'id' => 2,
            'body' => [
                'doc' => [
                    'cnt_id' => 2,
                    'create_by' => 123,
                    'create_by_name' => '曾海洋',
                    'create_at' => '2024-08-20 12:00:00',
                    'is_delete' => 0,
                    'update_by' => 123,
                    'update_by_name' => '曾海洋',
                    'update_at' => '2024-08-20 12:00:00',
                    'cnt_type' => '1',
                    'title' => '迭代设置—类别管理可与页面、字段、流程进行绑定222',
                    'category_id' => 10,
                    'project_id' => 100,
                    'iteration_id' => 200,
                    'parent_id' => 0,
                    'priority' => 1,
                    'type_id' => 300,
                    'handler' => ['邓凯龙'],
                    'handler_uid' => [101],
                    'developer' => ['韦顺隆', '袁志凡'],
                    'developer_uid' => [102, 103],
                    'tester' => [],
                    'tester_uid' => [],
                    'cc_user' => [],
                    'cc_uid' => [],
                    'estimate_start_time' => "",
                    'estimate_end_time' => "",
                    'finish_time' => '2024-08-22 12:00:00',
                    'estimated_work_hours' => 40,
                    'actual_work_hours' => 35,
                    'remaining_work' => 5,
                    'exceeding_working_hours' => 0,
                    'speed_of_progress' => 75,

                ]
            ]
        ];


        $response = $this->client->update($params)->getBody();
        dd(json_decode((string)$response, true));


    }

    public function search($params, $page = 1, $size = 10)
    {
        $where = [];
        if (!empty($params['title'])) {
            $where[] = ['match' => ['title' => $params['title']]];
        }
        if (!empty($params['developer_uid'])) { // 需要是数组
            $where[] = ['terms' => ['developer_uid' => $params['developer_uid']]];
        }

        if (!empty($params['create_by'])) { // 需要是数组
            $where[] = ['term' => ['create_by' => $params['create_by']]];
        }


        $params = [
            'index' => $this->index,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $where
                    ]
                ]
            ],
            'from' => ($page - 1) * $size, // 起始位置
            'size' => $size // 每页数量
        ];


        $response = $this->client->search($params)->getBody();
        $resp = json_decode((string)$response, true);
        $total = $resp['hits']['total']['value'];
        $data = $resp['hits']['hits'];
        var_dump($total);
        var_dump($data);
        die;
    }

}
