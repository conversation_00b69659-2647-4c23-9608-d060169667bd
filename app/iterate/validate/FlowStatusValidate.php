<?php
/**
 * Desc 产品管理 - 验证器
 * User Long
 * Date 2024/07/23
 */

namespace app\iterate\validate;

use basic\BaseValidate;

class FlowStatusValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|egt:0|elt:9999',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'project_id|项目id' => 'require|integer|egt:0',
        'status_flow_name|工作流名称' => 'require|max:20',
        'flow_process_id|工作流程id' => 'require|integer|egt:0',
        'status_flow_desc|工作流描述' => 'max:200',
        'flow_status_collection|工作流状态' => 'isNotEmptyArray',
        'modify_relevance_demand|关联内容数据集合' => 'isArray',

        'status_text_id|状态描述id' => 'require|integer|egt:0',
        'status_enum_id|状态' => 'require|integer|gt:0',
        'status_type|状态类型' => 'require|in:1,2,3',
        'process_node_ids|相关节点' => 'isArray',
        'target_status_enum_id|流转至的状态描述id' => 'require|integer|gt:0',
        'extends|配置字段' => 'isArray',
        'work_item_ids|关联内容id集合' => 'isNotEmptyArray'
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
        'status_type.require' => '状态类型 未填写',
        'flow_status_collection.isNotEmptyArray' => '请设置工作流状态设置'
    ];

    /**
     * 新增 - 迭代
     * @return FlowStatusValidate
     * User Long
     * Date 2024/8/8
     */
    public function sceneCreateIteration(): FlowStatusValidate
    {
        return $this->only(['project_id', 'status_flow_name', 'flow_process_id', 'status_flow_desc', 'flow_status_collection']);
    }

    /**
     * 更新 - 迭代
     * @return FlowStatusValidate
     * User Long
     * Date 2024/8/9
     */
    public function sceneUpdateIteration(): FlowStatusValidate
    {
        return $this->only(['flow_status_id', 'status_flow_name', 'status_flow_desc', 'flow_status_collection']);
    }

    /**
     * 校验 工作流状态设置
     * @return FlowStatusValidate
     * User Long
     * Date 2024/8/9
     */
    public function sceneFlowStatusCollection(): FlowStatusValidate
    {
        return $this->only(['status_enum_id', 'status_type', 'process_node_ids']);
    }

    /**
     * 校验 流转数据
     * @return FlowStatusValidate
     * User Long
     * Date 2024/8/26
     */
    public function sceneFlowStatusTransfer(): FlowStatusValidate
    {
        return $this->only(['target_status_text_id', 'extends']);
    }

    /**
     * 新增 - 需求
     * @return FlowStatusValidate
     * User Long
     * Date 2024/8/8
     */
    public function sceneCreateWorkItem(): FlowStatusValidate
    {
        return $this->only(['project_id', 'status_flow_name', 'status_flow_desc', 'flow_status_collection']);
    }

    /**
     * 更新 - 需求
     * @return FlowStatusValidate
     * User Long
     * Date 2024/8/9
     */
    public function sceneUpdateWorkItem(): FlowStatusValidate
    {
        return $this->only(['flow_status_id', 'status_flow_name', 'status_flow_desc', 'flow_status_collection']);
    }

    /**
     * 新增 - 需求
     * @return FlowStatusValidate
     * User Long
     * Date 2024/8/8
     */
    public function sceneCreate(): FlowStatusValidate
    {
        return $this->only(['project_id', 'status_flow_name', 'status_flow_desc', 'flow_status_collection']);
    }

    /**
     * 更新 - 需求
     * @return FlowStatusValidate
     * User Long
     * Date 2024/8/9
     */
    public function sceneUpdate(): FlowStatusValidate
    {
        return $this->only(['flow_status_id', 'status_flow_name', 'status_flow_desc', 'flow_status_collection']);
    }

    /**
     * 校验 相关联需求状态数据
     * @return FlowStatusValidate
     * User Long
     * Date 2024/8/9
     */
    public function sceneModifyRelevanceWorkItem(): FlowStatusValidate
    {
        return $this->only(['target_status_enum_id', 'work_item_ids']);
    }
}
