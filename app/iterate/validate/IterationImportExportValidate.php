<?php
declare (strict_types=1);

namespace app\iterate\validate;

use app\infrastructure\logic\FieldConfigLogic;
use app\infrastructure\model\FieldConfigModel;
use app\iterate\logic\FlowStatusTextLogic;
use basic\BaseValidate;
// use think\facade\App; // Removed facade

class IterationImportExportValidate extends BaseValidate
{
    protected FieldConfigLogic $fieldConfigLogic;
    protected FlowStatusTextLogic $flowStatusLogic;

    // 基础规则
    protected $rule = [
        'iteration_id'               => 'require|integer|gt:0',
        'project_id'                 => 'require|integer|gt:0',
        'iteration_name'             => 'require|max:255',
        'project_category_settings_id' => 'require|integer|gt:0', // 创建时需要，用于关联流程模板
        'estimate_start_time'        => 'date',
        'estimate_end_time'          => 'date|after:estimate_start_time',
        'start_time'                 => 'date',
        'end_time'                   => 'date|after:start_time',
        // 'status_text_name'           => 'require|checkStatusTextName', // 状态名称验证将在 Logic 中处理
        // 动态添加自定义字段规则...
    ];

    // 提示信息
    protected $message = [
        'iteration_id.require'                 => '迭代ID不能为空',
        'iteration_id.integer'                 => '迭代ID必须是整数',
        'iteration_id.gt'                      => '迭代ID无效',
        'project_id.require'                   => '项目ID不能为空',
        'project_id.integer'                   => '项目ID必须是整数',
        'project_id.gt'                        => '项目ID无效',
        'iteration_name.require'               => '迭代名称不能为空',
        'iteration_name.max'                   => '迭代名称不能超过255个字符',
        'project_category_settings_id.require' => '类别不存在',
        'estimate_start_time.date'             => '预估开始时间格式无效',
        'estimate_end_time.date'               => '预估结束时间格式无效',
        'estimate_end_time.after'              => '预估结束时间必须晚于预估开始时间',
        'start_time.date'                      => '实际开始时间格式无效',
        'end_time.date'                        => '实际结束时间格式无效',
        'end_time.after'                       => '实际结束时间必须晚于实际开始时间',
        // 'status_text_name.require'             => '状态名称不能为空', // 状态名称验证将在 Logic 中处理
        // 'status_text_name.checkStatusTextName' => '无效的状态名称',
    ];

    // 创建场景
    protected $scene = [
        'create' => ['project_id', 'iteration_name', 'project_category_settings_id', 'estimate_start_time', 'estimate_end_time', 'start_time', 'end_time'], // 移除 status_text_name
        'update' => ['iteration_id', 'project_id', 'iteration_name', 'estimate_start_time', 'estimate_end_time', 'start_time', 'end_time'], // 移除 status_text_name
    ];

    public function __construct()
    {
        parent::__construct(); // Call parent constructor if needed
        $this->fieldConfigLogic = new FieldConfigLogic();
        $this->flowStatusLogic = new FlowStatusTextLogic();
    }

    /**
     * 动态添加自定义字段验证规则
     * @param int $projectId
     * @param string $subKey 'iteration_output_add' or 'iteration_output_update'
     * @param array $header Excel表头 (字段标签)
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function loadCustomFieldRules(int $projectId, string $subKey, array $header)
    {
        // Use injected dependency
        $fieldList = $this->fieldConfigLogic->getListBySubKey($subKey, $projectId)
            ->whereIn('field_label', $header)
            ->column(null, 'field_name');

        foreach ($fieldList as $fieldName => $field) {
            // 跳过基础字段，只处理自定义字段
            if ($field['field_type'] == FieldConfigModel::FIELD_TYPE_SYSTEM) {
                 continue;
            }

            $rules = [];
            $messages = [];

            // 示例：添加必填规则 (需要根据实际字段配置决定)
            // if ($field['is_required']) { // 假设字段配置中有是否必填信息
            //     $rules[] = 'require';
            //     $messages[$fieldName . '.require'] = $field['field_label'] . '不能为空';
            // }

            // 示例：添加类型规则 (需要解析 field_component)
            // $componentType = $field['field_component']['componentType'] ?? '';
            // if ($componentType == 'InputNumber') {
            //     $rules[] = 'number';
            //     $messages[$fieldName . '.number'] = $field['field_label'] . '必须是数字';
            // } elseif ($componentType == 'DatePicker') {
            //     $rules[] = 'date';
            //     $messages[$fieldName . '.date'] = $field['field_label'] . '必须是日期格式';
            // }

            // 示例：添加选项验证 (如下拉框、单选框)
            // if (in_array($componentType, ['Select', 'Radio'])) {
            //     $options = array_column($field['field_component']['props']['options'] ?? [], 'label');
            //     if ($options) {
            //         $rules[] = 'in:' . implode(',', $options);
            //         $messages[$fieldName . '.in'] = $field['field_label'] . '的值无效';
            //     }
            // }

            // 示例：添加多选选项验证
            // if (in_array($componentType, ['Checkbox', 'MultipleSelect'])) {
            //     // 多选验证比较复杂，可能需要自定义验证方法
            //     // 验证每个值是否在选项内
            // }


            if ($rules) {
                $this->rule[$fieldName] = implode('|', $rules);
                $this->message = array_merge($this->message, $messages);
                // Fields with rules will be automatically validated if present in the data and scene
            }
        }
    }

    // 移除了 checkStatusTextName 方法

    // 可能需要一个辅助方法来获取当前行数据对应的 flow_status_id
    // protected function getCurrentFlowStatusId($data) { ... }

}
