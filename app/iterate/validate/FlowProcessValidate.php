<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\iterate\validate;

use app\iterate\logic\FlowProcessLogic;
use app\iterate\model\FlowProcessModel;
use app\project\logic\ProjectInfoLogic;
use basic\BaseValidate;
use exception\NotFoundException;

class FlowProcessValidate extends BaseValidate
{
    protected $rule = [
        'flow_process_id|id' => 'require',
        'flow_process_name|流程名称' => 'require|max:50|checkNameUnique',
        'flow_process_desc|流程说明' => 'max:500',
        'project_id|项目Id' => 'require',
        'node_list|节点集合' => 'require',
        'version|版本号' => 'require',
        'bug_liquidation_time|bug清算时间' => 'dateFormat:H:i'
    ];


    protected $scene = [
        'create' => ['flow_process_name', 'flow_process_desc', 'project_id', 'node_list', 'bug_liquidation_time'],
        'update' => ['flow_process_id', 'flow_process_name', 'flow_process_desc', 'node_list', 'bug_liquidation_time', 'version'],
    ];


    protected $message = [
        'flow_process_name.checkNameUnique' => '流程名称重复',
    ];

    /**
     * 验证名称是否唯一
     * @param $value
     * @param $rule
     * @param $data
     * @return bool
     * <AUTHOR>
     * @date 2024/7/8 上午10:15
     */
    protected function checkNameUnique($value, $rule, $data = [])
    {
        if (!empty($data['flow_process_id'])) {
            $model = FlowProcessModel::findById($data['flow_process_id']);
            if (!$model) {
                throw new NotFoundException();
            }
            $data['project_id'] = $model['project_id'];
        }


        $model = FlowProcessModel::findProjectProcessName($data['project_id'] ?? '', $data['flow_process_name']);
        // 校验名称是否与模板相同
        (new FlowProcessLogic())->validateProjectFlowProcessData(ProjectInfoLogic::getProjectData($data['project_id'] ?? ''), $data['flow_process_name']);

        if (!empty($data['flow_process_id'])) {
            return !$model || ($model->flow_process_id == $data['flow_process_id']);
        } else {
            return !$model;
        }

    }


}
