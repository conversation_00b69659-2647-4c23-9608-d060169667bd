<?php
/**
 * Desc 产品管理 - 验证器
 * User Long
 * Date 2024/07/23
 */

namespace app\iterate\validate;

use basic\BaseValidate;

class FlowStatusEnumValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|egt:0|elt:9999',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
//        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'product_id|产品id' => 'require|integer|egt:0',
        'status_enum_id|状态库id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:20',
        'colour|颜色' => 'max:15'
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
    ];

    /**
     * 新增
     * @return FlowStatusEnumValidate
     * User Long
     * Date 2024/8/7
     */
    public function sceneCreate(): FlowStatusEnumValidate
    {
        return $this->only(['project_id', 'name', 'colour']);
    }

    /**
     * 更新
     * @return FlowStatusEnumValidate
     * User Long
     * Date 2024/8/7
     */
    public function sceneUpdate(): FlowStatusEnumValidate
    {
        return $this->only(['status_enum_id', 'name', 'colour']);
    }
}
