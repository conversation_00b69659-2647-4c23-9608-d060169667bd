<?php
/**
* Desc 工作流状态与节点关系 - 模型
* User Long
* Date 2024/08/08*/

declare (strict_types = 1);

namespace app\iterate\model;

use basic\BaseModel;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "flow_status_node_relation".
* @property string $id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $flow_status_id 状态流程id
* @property string $status_text_id 状态描述id
* @property string $process_node_id 流程节点id
*/
class FlowStatusNodeRelationModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'flow_status_node_relation';


    /**
     * 预加载 - 流程节点模板
     * @return HasOne
     * User Long
     * Date 2024/7/24
     */
    public function flowProcessNode(): HasOne
    {
        return $this->hasOne(FlowProcessNodeModel::class, 'process_node_id', 'process_node_id')->where(['is_delete' => self::DELETE_NOT]);
    }

    /**
     * 获取器 - 获取状态类型
     * @param $value
     * @param $data
     * @return string
     * User Long
     * Date 2024/8/10
     */
    public function getProcessNodeNameAttr($value, $data): string
    {
        return $value ?? '';
    }
}
