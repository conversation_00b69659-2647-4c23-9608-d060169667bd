<?php
/**
* Desc 状态流转 - 模型
* User Long
* Date 2024/08/26*/

declare (strict_types = 1);

namespace app\iterate\model;

use basic\BaseModel;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "flow_status_transfer".
* @property string $id id
* @property string $flow_status_id 状态流程id
* @property string $status_text_id 状态描述id
* @property string $status_enum_id 状态id
* @property string $target_status_enum_id 流转到的状态id
* @property string $extends 自定义字段
*/
class FlowStatusTransferModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'flow_status_transfer';

    public function getExtendsAttr($value)
    {
        $extends = json_decode($value, true);

        if (isset($extends['fieldData'])) {
            $extends['fieldData'] = $this->resetExtendsAttr($extends['fieldData']);
        }

        return $extends;
    }

    public function setExtendsAttr($value): bool|string
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 预加载 - 状态库
     * @return HasOne
     * User Long
     * Date 2024/8/26
     */
    public function enumDetail(): HasOne
    {
        return $this->hasOne(FlowStatusEnumModel::class, 'status_enum_id', 'target_status_enum_id')->where(['is_delete' => self::DELETE_NOT]);
    }

    /**
     * 预加载 - 状态库表
     * @return HasOne
     * User Long
     * Date 2024/9/3
     */
    public function statusEnum(): HasOne
    {
        return $this->hasOne(FlowStatusEnumModel::class, 'status_enum_id', 'status_enum_id');
    }
}
