<?php
/**
* Desc 示例 - 模型
* User
* Date 2024/11/07
* */

declare (strict_types = 1);

namespace app\iterate\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "iteration".
* @property string $iteration_id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $project_id 项目Id
* @property string $iteration_name 迭代名称
* @property string $extends 自定义字段
* @property string $estimate_start_time 预估开始时间
* @property string $estimate_end_time 预估结束时间
* @property string $start_time 开始时间
* @property string $end_time 结束时间
* @property string $flow_status_id 状态流程id
* @property string $status_text_id 当前状态
* @property string $status_enum_id 状态库Id
* @property string $sort 排序，默认正序
*/
class IterationModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'iteration_id';
    protected $name = 'iteration';

    public static function findById($id)
    {
        return static::where(['iteration_id' => $id, 'is_delete' => self::DELETE_NOT])->find();
    }

}
