<?php
/**
 * Desc 示例 - 模型
 * User
 * Date 2024/08/07*/

declare (strict_types=1);

namespace app\iterate\model;

use app\project\model\ProjectModel;
use basic\BaseModel;
use think\db\Query;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;
use traits\OptimLockTrait;

/**
 * This is the model class for table "flow_process".
 * @property string $flow_process_id id
 * @property string $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property string $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property string $project_id 项目Id
 * @property string $flow_process_name 流程名称
 * @property string $flow_process_desc 流程说明
 * @property string $version 版本
 * @property string $bug_liquidation_time bug清算时间 '00:00:00'
 */
class FlowProcessModel extends BaseModel
{
    use CreateAndUpdateModelTrait;
    use OptimLockTrait;

    protected $pk = 'flow_process_id';
    protected $name = 'flow_process';


    const LIST_FIELDS = [
        'flow_process_id',
        'project_id',
        'flow_process_name',
        'flow_process_desc',
        'version',
    ];

    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }

    public static function findById($id)
    {
        return static::status()->find($id);
    }

    public function nodeList()
    {
        return $this->hasMany(FlowProcessNodeModel::class, 'flow_process_id')->where(['is_delete' => self::DELETE_NOT]);
    }

    public function flowStatusList()
    {
        return $this->hasMany(FlowStatusModel::class, 'flow_process_id')->where(['is_delete' => self::DELETE_NOT]);
    }

    /**
     * 查询指定项目下的流程数量
     * @param $projectId
     * @return int
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/8/8 上午10:47
     */
    public static function countByProjectId($projectId)
    {
        return self::status()->where(['project_id' => $projectId])->count();
    }

    /**
     * 查询指定项目下的流程名称
     * @param $projectId
     * @param $flowProcessName
     * @return array|static|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/8/8 上午10:47
     */
    public static function findProjectProcessName($projectId, $flowProcessName)
    {
        $where = [
            ['flow_process_name', '=', $flowProcessName],
            ['project_id', '=', $projectId],
        ];

        return static::status()->where($where)->find();

    }

    public static function generateName($name, $projectId)
    {
        $query = static::status()->where([
            ['project_id', '=', $projectId]
        ]);

        return generateCopyName($name, 'flow_process_name', $query);
    }

    public static function getDataAndNodeRelation($flowProcessId)
    {
        return static::status()
            ->with(['nodeList' => function (Query $query) {
                $query->with([
                    'nextNodeList' => function (Query $query2) {
                        $query2->with(['nextNode']);
                    },
                    'prevNodeList' => function (Query $query3) {
                        $query3->with(['prevNode']);
                    }
                ]);
            }])->find($flowProcessId);
    }

    /**
     * 根据工作流程id查询信息
     * @param $flowProcessId
     * @return FlowProcessNodeModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/8/10
     */
    public static function findProcessNodeById($flowProcessId)
    {
        return static::status()->where(['flow_process_id' => $flowProcessId])->find();
    }

    /**
     * 根据 工作流程名称 获取当前项目下的指定工作流程
     * @param int $projectId
     * @param string $flowProcessName
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findStatusFlowAndName(int $projectId, string $flowProcessName): mixed
    {
        return static::status()->where([
            'project_id' => $projectId,
            'flow_process_name' => $flowProcessName
        ])->find();
    }

    /**
     * 预加载 - 项目表
     * @return HasOne
     */
    public function project(): HasOne
    {
        return $this->hasOne(ProjectModel::class, 'project_id', 'project_id');
    }
}
