<?php
/**
* Desc 工作流状态库 - 模型
* User Long
* Date 2024/08/07*/

declare (strict_types = 1);

namespace app\iterate\model;

use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;
use think\model\relation\HasMany;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "flow_status_enum".
* @property string $status_enum_id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $name 名称
* @property string $project_id 项目id
* @property string $colour 颜色
 * @property string $status_enum_status 状态库枚举0默认1新2接受/处理3已解决4已验证5重新打开6已拒绝7挂起8已关闭
 * @property string $status_enum_type 类型1-迭代,2-需求
*/
class FlowStatusEnumModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'status_enum_id';
    protected $name = 'flow_status_enum';

    const HIDDEN_FIELD = ['create_by', 'create_by_name', 'create_at', 'update_by', 'update_by_name', 'update_at', 'is_delete'];

    // 状态库取消项目区分，默认为0
    const DEFAULT_PROJECT_ID = 0;

    // 状态库枚举0默认1新2接受/处理3已解决4已验证5重新打开6已拒绝7挂起8已关闭
    const ENUM_STATUS_DEFAULT = 0;
    const ENUM_STATUS_NEW = 1;
    const ENUM_STATUS_ACCEPT = 2;
    const ENUM_STATUS_SOLVE = 3;
    const ENUM_STATUS_VERIFY = 4;
    const ENUM_STATUS_AGAIN = 5;
    const ENUM_STATUS_REFUSE = 6;
    const ENUM_STATUS_PENDING = 7;
    const ENUM_STATUS_CLOSE = 8;

    /**
     * 根据 名称 获取当前项目下的指定状态库数据
     * @param int $projectId
     * @param int $statusEnumType
     * @param string $name
     * @return FlowStatusEnumModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public static function findEnumAndName(int $projectId, int $statusEnumType, string $name): mixed
    {
        return static::status()->where([
            'project_id' => $projectId,
            'status_enum_type' => $statusEnumType,
            'name' => $name
        ])->find();
    }

    /**
     * 统计项目下的状态库数据个数
     * @param int $projectId
     * @param int $statusEnumType
     * @return int
     * @throws DbException
     * User Long
     * Date 2024/8/24
     */
    public static function countProjectEnum(int $projectId, int $statusEnumType): int
    {
        return static::status()->where(['project_id' => $projectId, 'status_enum_type' => $statusEnumType])->count();
    }

    /**
     * 根据id集查询数据
     * @param int $statusEnumType
     * @param array $statusEnumIds
     * @return FlowStatusEnumModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public static function selectByStatusEnumIds(int $statusEnumType, array $statusEnumIds): array|Collection
    {
        return static::status()
            ->where(['status_enum_type' => $statusEnumType])
            ->whereIn('status_enum_id', $statusEnumIds)
            ->select();
    }

    /**
     * 根据 id 类型 查询状态库数据
     * @param int $statusEnumType
     * @param int $statusEnumId
     * @return FlowStatusEnumModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public static function findByStatusEnumId(int $statusEnumType, int $statusEnumId): mixed
    {
        return static::status()->where(['status_enum_type' => $statusEnumType, 'status_enum_id' => $statusEnumId])->find();
    }

    /**
     * 根据id查询状态库数据
     * @param int $statusEnumId
     * @return FlowStatusEnumModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/3
     */
    public static function findById(int $statusEnumId): mixed
    {
        return (new static())->where(['status_enum_id' => $statusEnumId])->find();
    }

    public static function findListById(array|int $statusEnumId)
    {
        return (new static())->where(['status_enum_id' => $statusEnumId])->select();
    }


    /**
     * 预加载 - 状态描述
     * @return HasMany
     * User Long
     * Date 2024/8/8
     */
    public function flowStatusTextDetail(): HasMany
    {
        return $this->hasMany(FlowStatusTextModel::class, 'status_enum_id', 'status_enum_id')->where(['is_delete' => self::DELETE_NOT]);
    }

    /**
     * 根据状态库id合集查询未删除的数量
     * @param int $statusEnumType
     * @param array $enumIds
     * @return int
     * @throws DbException
     * User Long
     * Date 2024/8/24
     */
    public static function countByEnumIds(int $statusEnumType, array $enumIds): int
    {
        return self::status()->where(['status_enum_type' => $statusEnumType])->whereIn('status_enum_id',  $enumIds)->count();
    }


    /**
     * 获取未处理的缺陷状态id集
     * @return array
     * @throws DbException
     * User Long
     * Date 2024/8/24
     */
    public static function getUnhandledDefectStatusCollectionByProjectId()
    {
        return self::status()->where([
            ['status_enum_type', '=', 4],
            ['status_enum_status', 'in', [
                self::ENUM_STATUS_NEW,
                self::ENUM_STATUS_ACCEPT,
                self::ENUM_STATUS_AGAIN,
            ]],
        ])
            ->column('status_enum_id');
    }
}
