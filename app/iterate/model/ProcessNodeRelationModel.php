<?php
/**
* Desc 示例 - 模型
* User
* Date 2024/08/07*/

declare (strict_types = 1);

namespace app\iterate\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "process_node_relation".
* @property string $id id
* @property string $process_node_id 当前Id;指process_node_id
* @property string $next_node_id 后置;指process_node_id
*/
class ProcessNodeRelationModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'process_node_relation';

    public function prevNode()
    {
        return $this->hasOne(FlowProcessNodeModel::class, 'process_node_id', 'process_node_id');
    }
    public function nextNode()
    {
        return $this->hasOne(FlowProcessNodeModel::class, 'process_node_id','next_node_id');
    }


}
