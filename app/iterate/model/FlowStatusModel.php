<?php
/**
* Desc 工作流 - 模型
* User Long
* Date 2024/08/07*/

declare (strict_types = 1);

namespace app\iterate\model;

use app\project\model\ProjectCategorySettingsModel;
use app\project\model\ProjectModel;
use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;
use think\model\relation\HasMany;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "flow_status".
* @property string $flow_status_id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $flow_process_id 迭代流程id
* @property string $status_flow_name 工作流名称
* @property string $status_flow_desc 描述
* @property string $project_id 项目id
* @property string $flow_status_type 类型1-迭代,2-需求
*/
class FlowStatusModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'flow_status_id';
    protected $name = 'flow_status';

    // 状态类型;1-开始,2-过程,3-结束
    const START_STATUS_TYPE = 1;
    const PROCESS_STATUS_TYPE = 2;
    const END_STATUS_TYPE = 3;

    const LIST_FIELDS = 'flow_status_id, status_flow_name, project_id';
    const SELECTOR_FIELDS = 'flow_status_id, status_flow_name, flow_process_id, project_id';

    /**
     * 根据 工作流名称 获取当前项目下的指定工作流
     * @param int $projectId
     * @param int $flowStatusType
     * @param string $statusFlowName
     * @return FlowStatusModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public static function findStatusFlowAndName(int $projectId, int $flowStatusType, string $statusFlowName): mixed
    {
        return static::status()->where([
            'project_id' => $projectId,
            'flow_status_type' => $flowStatusType,
            'status_flow_name' => $statusFlowName
        ])->find();
    }

    /**
     * 保存状态流程
     * @param int $projectId 项目id
     * @param string $statusFlowName 工作流名称
     * @param int $flowProcessId 迭代流程id
     * @param string $statusFlowDesc 描述
     * @param int $flowStatusType 类型1-迭代,2-需求
     * @return int
     * User Long
     * Date 2024/8/9
     */
    public static function saveFlowStatusData(int $projectId, string $statusFlowName, int $flowProcessId, string $statusFlowDesc, int $flowStatusType): int
    {
        // 保存 状态流程
        $flowStatusModel = new static();
        $flowStatusModel->project_id = $projectId;
        $flowStatusModel->status_flow_name = $statusFlowName;
        $flowStatusModel->flow_process_id = $flowProcessId;
        $flowStatusModel->status_flow_desc = $statusFlowDesc;
        $flowStatusModel->flow_status_type = $flowStatusType;
        $flowStatusModel->save();

        return (int)$flowStatusModel->flow_status_id;
    }

    /**
     * 根据id集查询数据
     * @param int $flowStatusType
     * @param array $flowStatusIds
     * @return FlowStatusModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public static function selectByFlowStatusIds(int $flowStatusType, array $flowStatusIds): Collection|array
    {
        return static::status()
            ->where(['flow_status_type' => $flowStatusType])
            ->whereIn('flow_status_id', $flowStatusIds)
            ->select();
    }

    /**
     * 根据id查询工作流数据
     * @param int $flowStatusType
     * @param int $statusId
     * @return FlowStatusModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/7
     */
    public static function findByStatusId(int $flowStatusType, int $statusId): mixed
    {
        return static::status()->where(['flow_status_type' => $flowStatusType, 'flow_status_id' => $statusId])->find();
    }

    /**
     * 统计项目下的工作流数据个数
     * @param int $projectId
     * @param int $flowStatusType
     * @return int
     * @throws DbException
     * User Long
     * Date 2024/8/24
     */
    public static function countProjectStatus(int $projectId, int $flowStatusType): int
    {
        return static::status()->where(['project_id' => $projectId, 'flow_status_type' => $flowStatusType])->count();
    }

    /**
     * 详情页处理
     * @return FlowStatusModel
     * User Long
     * Date 2024/8/10
     */
    public function toDetail(): FlowStatusModel
    {
        return $this->hidden(['is_delete']);
    }

    /**
     * 预加载 - 工作流程
     * @return HasOne
     * User Long
     * Date 2024/8/22
     */
    public function flowProcess(): HasOne
    {
        return $this->hasOne(FlowProcessModel::class, 'flow_process_id', 'flow_process_id');
    }

    /**
     * 预加载 - 状态库表
     * @return HasOne
     * User Long
     * Date 2024/9/2
     */
    public function statusEnum(): HasOne
    {
        return $this->hasOne(FlowStatusEnumModel::class, 'status_enum_id', 'status_enum_id');
    }

    /**
     * 预加载 - 状态描述
     * @return HasMany
     * User Long
     * Date 2024/9/2
     */
    public function statusText(): HasMany
    {
        return $this->hasMany(FlowStatusTextModel::class, 'flow_status_id', 'flow_status_id');
    }

    /**
     * 修改器 - 避免工作流名称返回null
     * @param $value
     * @return string
     * User Long
     * Date 2024/8/22
     */
    protected function getStatusFlowNameAttr($value): string
    {
        return $value ?? '';
    }

    /**
     * 修改器 - 避免工作流程名称返回null
     * @param $value
     * @return string
     * User Long
     * Date 2024/8/22
     */
    protected function getFlowProcessNameAttr($value): string
    {
        return $value ?? '';
    }

    /**
     * 生成复制名称
     * @param $name
     * @param $projectId
     * @return string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/30
     */
    public static function generateName($name, $projectId): string
    {
        $query = static::status()->where(['project_id' => $projectId]);

        return generateCopyName($name, 'status_flow_name', $query);
    }

    /**
     * 预加载 - 项目类别管理表
     * @return HasOne
     * User Long
     * Date 2024/9/4
     */
    public function categorySetting(): HasOne
    {
        return $this->hasOne(ProjectCategorySettingsModel::class, 'flow_status_id', 'flow_status_id');
    }

    /**
     * 预加载 - 项目表
     * @return HasOne
     */
    public function project(): HasOne
    {
        return $this->hasOne(ProjectModel::class, 'project_id', 'project_id');
    }
}
