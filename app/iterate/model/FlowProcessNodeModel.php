<?php
/**
 * Desc 示例 - 模型
 * User
 * Date 2024/08/07*/

declare (strict_types=1);

namespace app\iterate\model;

use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "flow_process_node".
 * @property string $process_node_id id
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property string $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property string $flow_process_id 流程Id
 * @property string $node_name 节点名称
 * @property string $node_data 节点设置
 * @property string $row_id 节点标识
 */
class FlowProcessNodeModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'process_node_id';
    protected $name = 'flow_process_node';


    public function getNodeDataAttr($value)
    {
        return json_decode($value ?? '', true);
    }


    public function setNodeDataAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function nextNodeList()
    {
        return $this->hasMany(ProcessNodeRelationModel::class, 'process_node_id');
    }

    public function prevNodeList()
    {
        return $this->hasMany(ProcessNodeRelationModel::class, 'next_node_id', 'process_node_id');
    }


    public static function findById($id)
    {
        return static::status()->find($id);
    }

    public static function selector($flowProcessId)
    {
        return FlowProcessNodeModel::status()
            ->where(['flow_process_id' => $flowProcessId])
            ->order('process_node_id DESC')->column('node_name as label, process_node_id as value');
    }

    /**
     * 根据节点id集查询节点信息
     * @param $processNodeIds
     * @return FlowProcessNodeModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/8
     */
    public static function selectProcessNodeByIds($processNodeIds)
    {
        return static::status()->whereIn('process_node_id', $processNodeIds)->select();
    }
}
