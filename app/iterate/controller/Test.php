<?php

namespace app\iterate\controller;

use app\iterate\logic\EsContentLogic;
use utils\EsClientFactory;

class Test
{
    public function create()
    {

        $es = new EsContentLogic(EsClientFactory::getInstance());
        //  $es->create();
        // $es->update();


        $search = [
            "title" => "迭代",
            "developer_uid" => [102],
            "create_by" => 123,
            "extends.f18" => [
                "value" => "hello"
            ]
        ];
        $es->search($search);
    }

}
