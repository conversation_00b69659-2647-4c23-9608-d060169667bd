<?php

namespace app\iterate\controller;

use app\iterate\model\IterationModel;
use app\work_items\logic\WorkItemsEsLogic;
use basic\BaseController;
use think\App;
use utils\Ctx;

class <PERSON>a extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);

    }
    public function fillEstimateTime()
    {
        $startDate = $this->request->param('start_date');
        $endDate = $this->request->param('end_date');


        $query = IterationModel::where('is_delete', 0)
            ->where(function ($query) {
                $query->whereNull('estimate_start_time')
                    ->whereOr('estimate_start_time', '=', '1971-01-01 00:00:00')
                    ->whereOr(function ($query) {
                        $query->whereNull('estimate_end_time')
                            ->whereOr('estimate_end_time', '=', '1971-01-01 00:00:00');
                    });
            });
//        dd($query->select()->where('iteration_id', 4014));
        if ($startDate && $endDate) {
            $query->whereBetween('create_at', [$startDate, $endDate]);
        }

        $iterations = $query->select();

        $esLogic = WorkItemsEsLogic::getInstance();
        $esClient = invade($esLogic)->esClient;
        $esIndex = invade($esLogic)->esIndex;
//        dd($iterations->column('iteration_id'));
//        $iterations = [$iterations->last()];
        foreach ($iterations as $iteration) {
            $params = [
                'index' => $esIndex,
                'body'  => [
                    'query' => [
                        'bool' => [
                            'filter' => [
                                ['term' => ['iteration_id' => $iteration->iteration_id]],
                                ['term' => ['is_delete' => 0]],
                            ]
                        ]
                    ],
                    'aggs'  => [
                        'min_create_time' => [
                            'min' => ['field' => 'create_at']
                        ],
                        'max_create_time' => [
                            'max' => ['field' => 'create_at']
                        ],
                        'max_update_time' => [
                            'max' => ['field' => 'update_at']
                        ]
                    ],
                    'size'  => 0
                ]
            ];

            $response = $esClient->search($params)->asArray();
//            dd($response);

            $aggregations = $response['aggregations'] ?? null;
            if ( ! $aggregations) {
                continue;
            }

            $minCreateTime = $aggregations['min_create_time']['value_as_string'] ?? null;
            $maxCreateTime = $aggregations['max_create_time']['value_as_string'] ?? null;
            $maxUpdateTime = $aggregations['max_update_time']['value_as_string'] ?? null;

            if ($minCreateTime) {
                // 从创建时间和更新时间中选择最早和最晚的时间
                $allTimes = array_filter([$minCreateTime, $maxCreateTime, $maxUpdateTime]);

                if (!empty($allTimes)) {
                    $minTime = min($allTimes);
                    $maxTime = max($allTimes);

                    $extends = json_decode($iteration->extends, true) ?: [];
                    $extends['estimate_iteration_cycle'] = [
                        date('Y-m-d', strtotime($minTime)),
                        date('Y-m-d', strtotime($maxTime)),
                    ];
                    $iteration->estimate_start_time = $minTime;
                    $iteration->estimate_end_time = $maxTime;
                    $iteration->extends = json_encode($extends, JSON_UNESCAPED_UNICODE);
                    $iteration->save();
                }
            }

        }

        return json(['code' => 0, 'msg' => 'success']);
    }
}