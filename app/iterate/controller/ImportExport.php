<?php
declare (strict_types=1);

namespace app\iterate\controller;

use app\infrastructure\logic\FieldConfigLogic;
use app\infrastructure\model\FieldConfigModel;
use app\iterate\logic\FlowStatusTextLogic;
use app\iterate\logic\IterationImportExportLogic;
use app\project\logic\IterationCatalogLogic;
use basic\BaseController;
use excel_utils\Hanlder;
use field_utils\Transfer;
use resp\Result;
use think\App;
use think\facade\Request;
use think\response\File;
use think\response\Json;
use utils\Log;


class ImportExport extends BaseController
{
    protected IterationImportExportLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new IterationImportExportLogic(
            $app->make(IterationCatalogLogic::class),
            $app->make(FieldConfigLogic::class),
            $app->make(FlowStatusTextLogic::class),
            new Transfer(FieldConfigModel::MODULE_TYPE_ITERATION,\request()->param('project_id'))
        );
    }

    /**
     * 获取迭代导入模板
     * @return File
     */
    public function getImportTemplate()
    {
        $result = $this->logic->getExportHeaderTemplate(Request::param());
        return Hanlder::outputFile($result['data'], $result['fileName']);
    }

    /**
     * 导出迭代数据
     * @return File
     */
    public function exportData()
    {
        $result = $this->logic->export(Request::param());
        return Hanlder::outputFile($result['data'], $result['fileName']);

    }

    /**
     * 导入迭代数据
     * @return Json
     */
    public function importData()
    {
        $params = Request::param();
        $file = Request::file('file');

        $params['file'] = $file;

        try {
            $result = $this->logic->import($params);
            return Result::success($result);
        } catch (\Throwable $e) {
            Log::instance(Log::PLATFORM)->error("迭代导入接口错误: ".$e->getMessage()."\n".$e->getTraceAsString());
            Log::instance(Log::PLATFORM)->error((string)$e);
            throw $e;
        }
    }
}
