<?php
/**
 * Desc 工作流程 自动化
 * User Long
 * Date 2024/11/7
 */

namespace app\iterate\controller;

use app\iterate\logic\FlowProcessAutoLogic;
use resp\Result;
use think\App;

class FlowProcessAuto
{
    private FlowProcessAutoLogic $logic;

    public function __construct()
    {
        $this->logic = new FlowProcessAutoLogic();
    }

    /**
     * 获取自动化配置
     * @return \think\response\Json
     * User Long
     * Date 2024/11/7
     */
    public function config()
    {
        return Result::success($this->logic->config());
    }
}
