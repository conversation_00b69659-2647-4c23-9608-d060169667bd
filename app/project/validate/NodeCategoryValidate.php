<?php

declare (strict_types=1);

namespace app\project\validate;

use think\Validate;

class NodeCategoryValidate extends Validate
{
    protected $rule = [
        'category_name'  => 'require|max:20',
        'priority'       => 'integer|egt:0',
        'sort'           => 'integer|egt:0',
//        'can_be_deleted' => 'require|in:0,1',
    ];

    protected $message = [
        'category_name.require'  => '分类名称不能为空',
        'category_name.max'      => '分类名称最大长度为20',
        'priority.integer'       => '优先级必须为整数',
        'priority.egt'           => '优先级必须大于等于0',
        'sort.integer'           => '排序必须为整数',
        'sort.egt'               => '排序必须大于等于0',
        'can_be_deleted.require' => '是否可删除不能为空',
        'can_be_deleted.in'      => '是否可删除值无效',
    ];
} 