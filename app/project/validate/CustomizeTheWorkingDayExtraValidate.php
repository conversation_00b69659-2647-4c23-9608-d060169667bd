<?php

declare (strict_types=1);

namespace app\project\validate;

use app\project\model\CustomizeTheWorkingDayExtraModel;
use think\Validate;

class CustomizeTheWorkingDayExtraValidate extends Validate
{
    protected $rule
        = [
            'project_id' => 'require|integer',
            'date'       => 'require|date',
            'type'       => 'require|in:1,2',
            'sync_to_all_projects' => 'in:0,1',
            'is_sync_to_all_projects' => 'in:0,1',
        ];

    protected $message
        = [
            'project_id.require' => '项目ID不能为空',
            'project_id.integer' => '项目ID必须为整数',
            'project_id.gt'      => '项目ID必须大于0',
            'date.require'       => '日期不能为空',
            'date.date'          => '日期格式不正确',
            'type.require'       => '类型不能为空',
            'type.in'            => '类型值无效，必须是1(休息日)或2(工作日)',
            'sync_to_all_projects.in' => '同步到所有项目参数只能是0或1',
            'is_sync_to_all_projects.in' => '同步到所有项目参数只能是0或1',
        ];
    protected $scene
        = [
            'update' => ['date', 'type', 'sync_to_all_projects'],
        ];
} 