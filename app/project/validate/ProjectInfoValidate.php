<?php
/**
 * Desc 项目成员 - 验证器
 * User Long
 * Date 2024/08/12
 */

namespace app\project\validate;

use basic\BaseValidate;

class ProjectInfoValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|egt:0|elt:9999',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'project_id|项目id' => 'require|integer|egt:0',
        'project_name|项目名称' => 'require|max:20',
        'project_icon|项目图标' => 'require|max:255',
        'project_manager_user_ids|项目经理' => 'require|isArray',
        'product_id|产品' => 'require|integer|egt:0',
        'template_id|项目模板' => 'require|integer|egt:0',
        'project_remark|项目描述' => 'max:200',
        'is_follow_up|是否需要跟进' => 'require|in:0,1',
        'demand_ids|需求' => 'isArray',
        'demand_user_ids|需求跟进人' => 'isArray',
        'defect_ids|缺陷' => 'isArray',
        'defect_user_ids|缺陷跟进人' => 'isArray',
        'task_ids|任务' => 'isArray',
        'task_user_ids|任务跟进人' => 'isArray'
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
    ];

    /**
     * 新增
     * @return ProjectInfoValidate
     * User Long
     * Date 2024/10/10
     */
    public function sceneCreate(): ProjectInfoValidate
    {
        return $this->only(['project_name', 'project_icon', 'project_manager_user_ids', 'product_id', 'template_id', 'project_remark']);
    }

    /**
     * 更新
     * @return ProjectInfoValidate
     * User Long
     * Date 2024/10/10
     */
    public function sceneUpdate(): ProjectInfoValidate
    {
        return $this->only(['project_id', 'project_name', 'project_icon', 'project_manager_user_ids', 'product_id', 'project_remark']);
    }

    /**
     * 退出项目用户（需要交接）
     * @return ProjectInfoValidate
     * User Long
     * Date 2024/12/10
     */
    public function sceneQuitProjectUser(): ProjectInfoValidate
    {
        return $this->only([
            'project_id', 'is_follow_up', 'demand_ids', 'demand_user_ids',
            'defect_ids', 'defect_user_ids', 'task_ids', 'task_user_ids'
        ]);
    }
}
