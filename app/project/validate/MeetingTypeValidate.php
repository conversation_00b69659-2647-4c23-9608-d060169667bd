<?php

declare (strict_types=1);

namespace app\project\validate;

use think\Validate;

class MeetingTypeValidate extends Validate
{
    protected $rule = [
        'type_name'      => 'require|max:20',
        'sort'           => 'integer|egt:0',
//        'can_be_deleted' => 'require|in:0,1',
    ];

    protected $message = [
        'type_name.require'      => '类型名称不能为空',
        'type_name.max'          => '类型名称最大长度为20',
        'sort.integer'           => '排序必须为整数',
        'sort.egt'               => '排序必须大于等于0',
        'can_be_deleted.require' => '是否可删除不能为空',
        'can_be_deleted.in'      => '是否可删除值无效',
    ];
} 