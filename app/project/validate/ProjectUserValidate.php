<?php
/**
 * Desc 项目成员 - 验证器
 * User Long
 * Date 2024/08/12
 */

namespace app\project\validate;

use basic\BaseValidate;

class ProjectUserValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|egt:0|elt:9999',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'project_id|项目id' => 'require|integer|egt:0',
        'user_ids|用户id集' => 'require|isArray',
        'is_empty|是否清空待办' => 'require|in:1,0',

    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
        'user_ids.require' => '请选择对应的成员',
        'user_id.require' => '请选择对应的成员',
        'is_empty.in' => '请选择是否清空待办',
    ];

    /**
     * 新增
     * @return ProjectUserValidate
     * User Long
     * Date 2024/8/12
     */
    public function sceneCreate(): ProjectUserValidate
    {
        return $this->only(['project_id', 'user_ids']);
    }

    /**
     * 移出项目成员
     * @return ProjectUserValidate
     * User Long
     * Date 2024/12/7
     */
    public function sceneRemoveUser(): ProjectUserValidate
    {
        return $this->only(['project_id', 'user_id', 'is_empty']);
    }
}
