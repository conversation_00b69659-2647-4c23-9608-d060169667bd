<?php
/**
 * Desc 迭代目录 - 验证器
 * User Long
 * Date 2024/11/9
 */

namespace app\project\validate;

use basic\BaseValidate;

class IterationCatalogValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|egt:0|elt:9999',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'project_id|项目id' => 'require|integer|egt:0',
        'iteration_id|迭代id' => 'require|integer|egt:0',
        'iteration_name|迭代名称' => 'require|max:200',
        'iteration_icon|迭代图标' => 'require|max:255',
        'project_category_settings_id|类别id' => 'require|integer|egt:0',
        'extends|配置字段' => 'isArray',
        'estimate_start_time|预计开始时间' => 'date',
        'estimate_end_time|预计结束时间' => 'date',
        'start_time|开始时间' => 'date',
        'end_time|结束时间' => 'date'
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
    ];

    /**
     * 新增
     * @return IterationCatalogValidate
     * User Long
     * Date 2024/11/9
     */
    public function sceneCreate(): iterationCatalogValidate
    {
        return $this->only([
            'project_id', 'project_category_settings_id', 'iteration_name', 'iteration_icon', 'extends',
            'estimate_start_time', 'estimate_end_time', 'start_time', 'end_time'
        ]);
    }

    /**
     * 更新
     * @return IterationCatalogValidate
     * User Long
     * Date 2024/11/11
     */
    public function sceneUpdate(): iterationCatalogValidate
    {
        return $this->only([
            'iteration_id', 'iteration_name', 'iteration_icon', 'extends',
            'estimate_start_time', 'estimate_end_time'
        ]);
    }

    /**
     * 更新迭代时间
     * @return IterationCatalogValidate
     * User Long
     * Date 2024/11/14
     */
    public function sceneUpdateIterativeTime(): iterationCatalogValidate
    {
        return $this->only([
            'iteration_id', 'estimate_start_time', 'estimate_end_time'
        ]);
    }
}
