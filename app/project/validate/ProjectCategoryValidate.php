<?php
/**
 * Desc 迭代分类 - 验证器
 * User Long
 * Date 2024/07/19
 */

namespace app\project\validate;

use basic\BaseValidate;

class ProjectCategoryValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|gt:0|elt:99',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'project_id|项目id' => 'require|integer|egt:0',
        'category_id|id' => 'require|integer|egt:0',
        'category_ids|id集' => 'require|isArray',
        'category_name|名称' => 'require|max:100',
        'remark|分类说明' => 'max:200',
        'pid|上级id' => 'require|integer|egt:0'
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
    ];

    /**
     * 新增
     * @return ProjectCategoryValidate
     * User Long
     * Date 2024/8/27
     */
    public function sceneCreate(): ProjectCategoryValidate
    {
        return $this->only(['project_id', 'category_name', 'category_en_name', 'pid', 'remark', 'sort']);
    }

    /**
     * 更新
     * @return ProjectCategoryValidate
     * User Long
     * Date 2024/8/27
     */
    public function sceneUpdate(): ProjectCategoryValidate
    {
        return $this->only(['category_id', 'category_name', 'pid', 'remark', 'sort']);
    }

    /**
     * 更新排序
     * @return ProjectCategoryValidate
     * User Long
     * Date 2024/8/27
     */
    public function sceneUpdateSort(): ProjectCategoryValidate
    {
        return $this->only(['category_id', 'sort']);
    }
}
