<?php
/**
 * Desc 标签管理 - 验证器
 * User Long
 * Date 2025/03/10
 */

namespace app\project\validate;

use basic\BaseValidate;

class TagLibraryValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'integer|egt:0|elt:9999',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'tag_code|标签code' => 'require|max:30',
        'tag_name|名称' => 'require|max:20',
        'priority|优先级' => 'integer|egt:0|elt:9999',
        'group_type|分组类型' => 'integer|in:1,2'
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
        'product_name.max' => '产品名称不可超过50个字符'
    ];

    /**
     * 新增
     * @return TagLibraryValidate
     * User Long
     * Date 2025/3/10
     */
    public function sceneCreate(): TagLibraryValidate
    {
        return $this->only(['tag_name', 'priority', 'sort', 'group_type']);
    }

    /**
     * 更新
     * @return TagLibraryValidate
     * User Long
     * Date 2025/3/10
     */
    public function sceneUpdate(): TagLibraryValidate
    {
        return $this->only(['tag_code', 'tag_name', 'priority', 'sort', 'group_type']);
    }

    /**
     * 分页
     * @return TagLibraryValidate
     * User Long
     * Date 2025/3/10
     */
    public function scenePageQuery(): TagLibraryValidate
    {
        return $this->only(['project_id']);
    }

    /**
     * 排序
     * @return TagLibraryValidate
     * User Long
     * Date 2025/3/20
     */
    public function sceneUpdateSort(): TagLibraryValidate
    {
        return $this->only(['tag_code', 'sort']);
    }
}
