<?php
/**
 * Desc 迭代分类 - 验证器
 * User Long
 * Date 2024/07/19
 */

namespace app\project\validate;

use basic\BaseValidate;

class ProjectCategorySettingsValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|egt:0|elt:99',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'category_id|类别id' => 'require|integer|egt:0',
        'category_ids|类别id集' => 'require|isArray',
        'project_id|项目id' => 'require|integer|egt:0',
        'category_name|类别名称' => 'require|max:20',
        'category_en_name|图标设置' => 'require|max:10|regex:english',
        'icon|图标' => 'require|max:20',
        'template_id|创建页id' => 'require|integer|egt:0',
        'flow_status_id|工作流id' => 'require|integer|egt:0',
        'flow_process_id|工作流程id' => 'require|integer|egt:0'
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
        'category_en_name.regex' => '图标设置 传值仅支持英文'
    ];

    /**
     * 新增 - 迭代
     * @return ProjectCategorySettingsValidate
     * User Long
     * Date 2024/7/20
     */
    public function sceneCreateIteration(): ProjectCategorySettingsValidate
    {
        return $this->only(['project_id', 'category_name', 'category_en_name', 'icon', 'template_id', 'flow_status_id', 'flow_process_id', 'sort']);
    }

    /**
     * 更新 - 迭代
     * @return ProjectCategorySettingsValidate
     * User Long
     * Date 2024/7/20
     */
    public function sceneUpdateIteration(): ProjectCategorySettingsValidate
    {
        return $this->only(['category_id', 'category_name', 'category_en_name', 'icon', 'template_id', 'flow_status_id', 'flow_process_id', 'sort']);
    }

    /**
     * 更新排序 - 迭代
     * @return ProjectCategorySettingsValidate
     * User Long
     * Date 2024/8/23
     */
    public function sceneUpdateSort(): ProjectCategorySettingsValidate
    {
        return $this->only(['category_id', 'sort']);
    }

    /**
     * 新增 - 需求
     * @return ProjectCategorySettingsValidate
     * User Long
     * Date 2024/7/20
     */
    public function sceneCreateWorkItem(): ProjectCategorySettingsValidate
    {
        return $this->only(['project_id', 'category_name', 'category_en_name', 'icon', 'template_id', 'flow_status_id', 'sort']);
    }

    /**
     * 更新- 需求
     * @return ProjectCategorySettingsValidate
     * User Long
     * Date 2024/7/20
     */
    public function sceneUpdateWorkItem(): ProjectCategorySettingsValidate
    {
        return $this->only(['category_id', 'category_name', 'category_en_name', 'icon', 'template_id', 'flow_status_id', 'sort']);
    }

    /**
     * 新增 - 任务
     * @return ProjectCategorySettingsValidate
     * User Long
     * Date 2024/7/20
     */
    public function sceneCreateTask(): ProjectCategorySettingsValidate
    {
        return $this->only(['project_id', 'category_name', 'category_en_name', 'icon', 'template_id', 'sort']);
    }

    /**
     * 更新- 任务
     * @return ProjectCategorySettingsValidate
     * User Long
     * Date 2024/7/20
     */
    public function sceneUpdateTask(): ProjectCategorySettingsValidate
    {
        return $this->only(['category_id', 'category_name', 'category_en_name', 'icon', 'template_id', 'sort']);
    }

    /**
     * 新增 - 通用
     * @return ProjectCategorySettingsValidate
     * User Long
     * Date 2024/7/20
     */
    public function sceneCreate(): ProjectCategorySettingsValidate
    {
        return $this->only(['project_id', 'category_name', 'category_en_name', 'icon', 'template_id', 'sort']);
    }

    /**
     * 更新- 通用
     * @return ProjectCategorySettingsValidate
     * User Long
     * Date 2024/7/20
     */
    public function sceneUpdate(): ProjectCategorySettingsValidate
    {
        return $this->only(['category_id', 'category_name', 'category_en_name', 'icon', 'template_id', 'sort']);
    }
}
