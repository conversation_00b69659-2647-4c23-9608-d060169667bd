<?php
/**
 * 日清bug统计规则设置验证器
 */

namespace app\project\validate;

use think\Validate;

class NissinBugStatisticsRulesValidate extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'project_id' => 'require|integer',
        'time' => ['require', 'regex' => '/^([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/'],
        'statistics' => 'require|in:1,2',
        'enable' => 'require|in:0,1',
        'nissin_bug_statistics_rules_id' => 'require|integer|gt:0',
        'sync_to_all_projects' => 'in:0,1'
    ];

    /**
     * 错误信息
     * @var array
     */
    protected $message = [
        'project_id.require' => '项目ID不能为空',
        'project_id.integer' => '项目ID必须为整数',
        'project_id.gt' => '项目ID必须大于0',
        'time.require' => '执行时间不能为空',
        'time.regex' => '执行时间格式不正确，应为HH:MM:SS格式',
        'statistics.require' => '统计日期不能为空',
        'statistics.in' => '统计日期只能是1或2',
        'enable.require' => '是否启用不能为空',
        'enable.in' => '是否启用只能是0或1',
        'nissin_bug_statistics_rules_id.require' => 'ID不能为空',
        'nissin_bug_statistics_rules_id.integer' => 'ID必须为整数',
        'nissin_bug_statistics_rules_id.gt' => 'ID必须大于0',
        'sync_to_all_projects.in' => '同步到所有项目参数只能是0或1'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'create' => ['project_id', 'time', 'statistics', 'enable'],
        'update' => ['nissin_bug_statistics_rules_id', 'time', 'statistics', 'enable'],
        'delete' => ['nissin_bug_statistics_rules_id'],
        'detail' => ['nissin_bug_statistics_rules_id'],
        'byProject' => ['project_id']
    ];
} 