<?php

declare (strict_types=1);

namespace app\project\validate;

use think\Validate;

class CustomizeTheWorkingDayValidate extends Validate
{
    protected $rule = [
        'project_id'           => 'require|integer',
        'weekday'              => 'require|integer|between:0,127',
//        'work_hours'           => 'require|integer|between:1,24',
        'skip_public_holidays' => 'require|in:0,1',
        'sync_to_all_projects' => 'in:0,1'
    ];

    protected $message = [
        'project_id.require'           => '项目ID不能为空',
        'project_id.integer'           => '项目ID必须为整数',
        'project_id.gt'                => '项目ID必须大于0',
        'weekday.require'              => '工作日设置不能为空',
        'weekday.integer'              => '工作日设置必须为整数',
        'weekday.between'              => '工作日设置必须在0-127之间',
        'work_hours.require'           => '一天工时不能为空',
        'work_hours.integer'           => '一天工时必须为整数',
        'work_hours.between'           => '一天工时必须在1-24之间',
        'skip_public_holidays.require' => '是否跳过法定节假日不能为空',
        'skip_public_holidays.in'      => '是否跳过法定节假日值无效',
        'sync_to_all_projects.in'      => '同步到所有项目参数只能是0或1'
    ];
} 