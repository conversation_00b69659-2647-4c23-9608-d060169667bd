<?php
/**
 * Desc 项目模板 - 验证器
 * User Long
 * Date 2024/07/19
 */

namespace app\project\validate;

use basic\BaseValidate;

class ProjectTemplateValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|gt:0|elt:99',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'project_template_id|模板id' => 'require|integer|egt:0',
        'project_id|项目id' => 'require|integer|egt:0',
        'project_template_name|模板名称' => 'require|max:30',
        'remark|描述' => 'max:200',
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
    ];

    /**
     * 新增
     * @return ProjectTemplateValidate
     * User Long
     * Date 2024/10/9
     */
    public function sceneCreate(): ProjectTemplateValidate
    {
        return $this->only(['project_id', 'project_template_name', 'remark']);
    }

    /**
     * 更新
     * @return ProjectTemplateValidate
     * User Long
     * Date 2024/10/9
     */
    public function sceneUpdate(): ProjectTemplateValidate
    {
        return $this->only(['project_template_id', 'project_template_name', 'remark', 'sort']);
    }

    /**
     * 更新排序
     * @return ProjectTemplateValidate
     * User Long
     * Date 2024/10/9
     */
    public function sceneUpdateSort(): ProjectTemplateValidate
    {
        return $this->only(['project_template_id', 'sort']);
    }
}
