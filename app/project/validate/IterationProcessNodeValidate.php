<?php
/**
 * Desc 迭代目录 - 验证器
 * User Long
 * Date 2024/11/9
 */

namespace app\project\validate;

use basic\BaseValidate;

class IterationProcessNodeValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|egt:0|elt:9999',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'iteration_process_node_id|节点id' => 'require|integer|egt:0',
        'extends|配置字段' => 'isArray',
        'estimate_start_time|预估开始时间' => 'date',
        'estimate_end_time|预估结束时间' => 'date'
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
    ];

    public function sceneUpdateNode(): IterationProcessNodeValidate
    {
        return $this->only([
            'iteration_process_node_id',
            'extends',
            'estimate_start_time',
            'estimate_end_time'
        ]);
    }
}
