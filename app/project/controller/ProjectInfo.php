<?php
/**
 * Desc 项目管理
 * User Long
 * Date 2024/9/20
 */

namespace app\project\controller;

use app\project\logic\ProjectFavoriteLogic;
use app\project\logic\ProjectInfoLogic;
use resp\Result;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Request;
use think\response\Json;
use Throwable;

class ProjectInfo
{
    private ProjectInfoLogic $logic;

    public function __construct()
    {
        $this->logic = new ProjectInfoLogic();
    }

    /**
     * 项目icon
     * @return Json
     * User Long
     * Date 2024/10/8
     */
    public function getIconPath()
    {
        return Result::success($this->logic->getIconPath());
    }

    /**
     * 项目状态下拉数据
     * @return Json
     * User Long
     * Date 2024/10/11
     */
    public function getProjectStatus()
    {
        return Result::success($this->logic->getProjectStatus());
    }

    /**
     * 项目下拉数据
     * @return Json
     * User Long
     * Date 2024/10/8
     */
    public function getProjectSelector()
    {
        return Result::success($this->logic->getProjectSelector());
    }

    /**
     * 项目左侧列表
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function abbreviate()
    {
        return Result::success($this->logic->abbreviate());
    }

    /**
     * 新增
     * @param Request $request
     * @return Json
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function create(Request $request): Json
    {
        $params = $request->post([
            'project_name',
            'project_icon',
            'project_manager_user_ids',
            'product_id',
            'template_id',
            'project_remark'
        ]);

        $this->logic->create($params);

        return Result::success();
    }

    /**
     * 删除
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function delete(Request $request): Json
    {
        $projectIds = $request->post('project_ids/a');

        $this->logic->delete($projectIds);

        return Result::success();
    }

    /**
     * 更新
     * @param Request $request
     * @return Json
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function update(Request $request): Json
    {
        $params = $request->post([
            'project_id',
            'project_name',
            'project_icon',
            'project_manager_user_ids',
            'product_id',
            'project_remark'
        ]);

        $this->logic->update($params['project_id'], $params);

        return Result::success();
    }

    /**
     * 详情
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function detail(Request $request): Json
    {
        $projectId = $request->get('project_id/d');

        return Result::success($this->logic->detail((int)$projectId));
    }

    /**
     * 列表
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function listQuery(Request $request): Json
    {
        $projectName = $request->get('project_name/s', '');
        $productName = $request->get('product_name/s', '');
        $projectStatus = $request->get('project_status/d', 0);

        $res = $this->logic->listQuery($projectName, $productName, $projectStatus);

        return Result::success($res);
    }

    /**
     * 启用
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function enable(Request $request): Json
    {
        $projectIds = $request->post('project_ids/a');

        $this->logic->enable($projectIds);

        return Result::success();
    }

    /**
     * 禁用
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function disable(Request $request): Json
    {
        $projectIds = $request->post('project_ids/a');

        $this->logic->disable($projectIds);

        return Result::success();
    }

    /**
     * 收藏
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/11
     */
    public function like(Request $request): Json
    {
        $projectIds = $request->post('project_ids/a');

        ProjectFavoriteLogic::like($projectIds);

        return Result::success();
    }

    /**
     * 取消收藏
     * @param Request $request
     * @return Json
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function dislike(Request $request): Json
    {
        $projectIds = $request->post('project_ids/a');

        ProjectFavoriteLogic::dislike($projectIds);

        return Result::success();
    }

    /**
     * 查询需要退出项目的用户信息
     * @param Request $request
     * @return Json
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/12/9
     */
    public function getExitProjectInfo(Request $request): Json
    {
        $projectId = $request->get('project_id/d');

        return Result::success($this->logic->exitProjectInfo($projectId));
    }

    /**
     * 退出项目用户（无需交接）
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/12/9
     */
    public function directQuitProjectUser(Request $request): Json
    {
        $projectId = $request->post('project_id/d');

        $this->logic->directQuitProjectUser($projectId);

        return Result::success();
    }

    /**
     * 退出项目用户（需交接）
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/12/10
     */
    public function quitProjectUser(Request $request): Json
    {
        $params = $request->post([
            'project_id',
            'is_follow_up',
            'demand_ids',
            'demand_user_ids',
            'defect_ids',
            'defect_user_ids',
            'task_ids',
            'task_user_ids',
        ]);

        $this->logic->quitProjectUser($params);

        return Result::success();
    }

    /**
     * 更改项目是否为模板（不对外使用）
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/1/3
     */
    public function upIsTemp(Request $request): Json
    {
        $projectId = $request->post('project_id/d', 0);
        $isTemplate = $request->post('is_template/d', 0);

        $this->logic->upIsTemp($projectId, $isTemplate);

        return Result::success();
    }
}
