<?php
/**
 * 日清bug统计规则设置控制器
 */

declare (strict_types=1);

namespace app\project\controller;

use app\project\logic\NissinBugStatisticsRulesLogic;
use app\project\validate\NissinBugStatisticsRulesValidate;
use basic\BaseController;
use resp\Result;
use exception\NotFoundException;
use think\App;
use think\exception\ValidateException;

class NissinBugStatisticsRules extends BaseController
{
    /**
     * 日清bug统计规则设置逻辑
     * @var NissinBugStatisticsRulesLogic
     */
    private $nissinBugStatisticsRulesLogic;

    /**
     * 构造函数
     * @param  App  $app
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->nissinBugStatisticsRulesLogic = new NissinBugStatisticsRulesLogic();
    }

    /**
     * 获取日清bug统计规则详情
     * @return \think\Response
     */
    public function getDetail()
    {
        $id = $this->request->get('id');
        
        // 验证参数
        validate(NissinBugStatisticsRulesValidate::class)
            ->scene('detail')
            ->check(['nissin_bug_statistics_rules_id' => $id]);

        // 获取详情
        $data = $this->nissinBugStatisticsRulesLogic->getDetail(intval($id));
        return Result::success($data);
    }

    /**
     * 根据项目ID获取日清bug统计规则
     * @return \think\Response
     */
    public function getByProjectId()
    {
        $projectId = $this->request->get('projectId');
        
        // 验证参数
        validate(NissinBugStatisticsRulesValidate::class)
            ->scene('byProject')
            ->check(['project_id' => $projectId]);

        // 获取详情
        $data = $this->nissinBugStatisticsRulesLogic->getByProjectId(intval($projectId));
        return Result::success($data);
    }

    /**
     * 分页查询日清bug统计规则
     * @return \think\Response
     */
    public function pageQuery()
    {
        $params = $this->request->get();
        $data = $this->nissinBugStatisticsRulesLogic->pageQuery($params);
        return Result::success($data);
    }

    /**
     * 创建日清bug统计规则
     * @return \think\Response
     */
    public function create()
    {
        $data = $this->request->post();
        $model = $this->nissinBugStatisticsRulesLogic->create($data);
        return Result::success($model);
    }

    /**
     * 更新日清bug统计规则
     * @return \think\Response
     */
    public function update()
    {
        $data = $this->request->post();

        // 验证ID参数
        validate(NissinBugStatisticsRulesValidate::class)
            ->scene('update')
            ->check($data);

        $id = intval($data['nissin_bug_statistics_rules_id']);
        unset($data['nissin_bug_statistics_rules_id']);

        $result = $this->nissinBugStatisticsRulesLogic->update($id, $data);
        return Result::success($result);
    }

    /**
     * 删除日清bug统计规则
     * @return \think\Response
     */
    public function delete()
    {
        $data = $this->request->post();

        // 验证ID参数
        validate(NissinBugStatisticsRulesValidate::class)
            ->scene('delete')
            ->check($data);

        $id = intval($data['nissin_bug_statistics_rules_id']);
        $result = $this->nissinBugStatisticsRulesLogic->delete($id);
        return Result::success($result);
    }

    /**
     * 启用日清bug统计规则
     * @return \think\Response
     */
    public function enable()
    {
        $data = $this->request->post();

        // 验证ID参数
        validate(NissinBugStatisticsRulesValidate::class)
            ->scene('detail')
            ->check($data);

        $id = intval($data['nissin_bug_statistics_rules_id']);
        // 获取是否同步到所有项目参数，默认为0
        $syncToAllProjects = isset($data['sync_to_all_projects']) ? intval($data['sync_to_all_projects']) : 0;
        $result = $this->nissinBugStatisticsRulesLogic->setEnable($id, 1, $syncToAllProjects);
        return Result::success($result);
    }

    /**
     * 禁用日清bug统计规则
     * @return \think\Response
     */
    public function disable()
    {
        $data = $this->request->post();

        // 验证ID参数
        validate(NissinBugStatisticsRulesValidate::class)
            ->scene('detail')
            ->check($data);

        $id = intval($data['nissin_bug_statistics_rules_id']);
        // 获取是否同步到所有项目参数，默认为0
        $syncToAllProjects = isset($data['sync_to_all_projects']) ? intval($data['sync_to_all_projects']) : 0;
        $result = $this->nissinBugStatisticsRulesLogic->setEnable($id, 0, $syncToAllProjects);
        return Result::success($result);
    }
} 