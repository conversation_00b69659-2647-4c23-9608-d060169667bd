<?php
/**
 * Desc 需求工作流（状态流） - 控制器
 * User Long
 * Date 2024/08/24
 */

declare (strict_types=1);


namespace app\project\controller;

use app\iterate\logic\FlowStatusLogic;
use basic\BaseController;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use resp\Result;
use think\App;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\response\Json;
use Throwable;

class FlowStatus extends BaseController
{
    private FlowStatusLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new FlowStatusLogic($this->getSettingType());
    }

    /**
     * 新增工作流
     * @return Json
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/9
     */
    public function create(): Json
    {
        $params = $this->request->post([
            'project_id',
            'status_flow_name',
            'flow_process_id',
            'status_flow_desc',
            'flow_status_collection'
        ]);

        $this->logic->create($params);

        return Result::success();
    }

    /**
     * 根据id集删除工作流
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/8/9
     */
    public function delete(): Json
    {
        $flowStatusIds = $this->request->post('flow_status_ids/a');

        $this->logic->delete($flowStatusIds);

        return Result::success();
    }

    /**
     * 根据id更新数据
     * @return Json
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/31
     */
    public function update(): Json
    {
        $params = $this->request->post([
            'flow_status_id',
            'status_flow_name',
            'status_flow_desc',
            'flow_status_collection',
            'modify_relevance_demand'
        ]);

        $this->logic->update($params['flow_status_id'], $params);

        return Result::success();
    }

    /**
     * 迭代分类详情
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/20
     */
    public function detail(): Json
    {
        $flowStatusId = $this->request->get('flow_status_id/d');

        $res = $this->logic->detail((int)$flowStatusId);

        return Result::success($res);
    }

    /**
     * 列表（无分页）
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/10
     */
    public function listQuery(): Json
    {
        $projectId = $this->request->get('project_id/d');

        $res = $this->logic->listQuery((int)$projectId);

        return Result::success($res);
    }

    /**
     * 获取状态类型枚举数据
     * @return Json
     * User Long
     * Date 2024/8/12
     */
    public function selectorStatusType(): Json
    {
        $res = $this->logic->selectorStatusType();

        return Result::success($res);
    }

    /**
     * 获取工作流下拉数据
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public function selectorFlowStatus(): Json
    {
        $projectId = $this->request->get('project_id/d');

        $res = $this->logic->selectorFlowStatus((int)$projectId);

        return Result::success($res);
    }

    /**
     * 获取相关需求id集合
     * @return Json
     * User Long
     * Date 2024/8/29
     * @throws ClientResponseException
     * @throws ServerResponseException
     */
    public function getRelevanceDemand(): Json
    {
        $flowStatusId = $this->request->get('flow_status_id/d');
        $statusEnumId = $this->request->get('status_enum_id/d');

        $res = $this->logic->getRelevanceDemand((int)$flowStatusId, (int)$statusEnumId);

        return Result::success($res);
    }

    /**
     * 复制工作流
     * @return Json
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/30
     */
    public function copy(): Json
    {
        $flowStatusId = $this->request->get('flow_status_id/d');
        $project_id = $this->request->get('project_id/d');

        $this->logic->copy((int)$flowStatusId, $project_id ?? 0);

        return Result::success();
    }

    /**
     * 获取可流转至的状态数据
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/2
     */
    public function getTargetStatusEnum(): Json
    {
        $flowStatusId = $this->request->get('flow_status_id/d');
        $statusEnumId = $this->request->get('status_enum_id/d');
        $cntId = $this->request->get('cnt_id/d');

        $res = $this->logic->getTargetStatusEnum((int)$flowStatusId, (int)$statusEnumId, (int)$cntId);

        return Result::success($res);
    }

    /**
     * 更新工作流名称
     * @return Json
     * User Long
     * Date 2025/4/14
     */
    public function rename()
    {
        $flowStatusId = $this->request->post('flow_status_id/d');
        $statusFlowName = $this->request->post('status_flow_name/s');

        $this->logic->rename($flowStatusId, $statusFlowName);

        return Result::success();
    }
}
