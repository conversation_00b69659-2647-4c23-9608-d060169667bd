<?php
/**
 * Desc 会议类型 - 控制器
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);

namespace app\project\controller;

use app\project\logic\TagLibraryLogic;
use app\project\model\TagLibraryModel;
use basic\BaseController;
use resp\Result;
use think\App;

class MeetingType extends BaseController
{
    private TagLibraryLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new TagLibraryLogic(TagLibraryModel::GROUP_TYPE_CONFERENCE);
    }

    /**
     * 获取会议类型列表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function selector()
    {
        $res = $this->logic->getSelector();
        return Result::success($res);
    }

    /**
     * 创建会议类型
     * @return \think\response\Json
     */
    public function create()
    {
        $data = $this->request->post();

        $result = $this->logic->create($data);
        return Result::success($result);
    }

    /**
     * 更新会议类型
     * @return \think\response\Json
     */
    public function update()
    {
        $tagCode = $this->request->post('tag_code');
        $data = $this->request->post();

        $result = $this->logic->update($tagCode, $data);
        return Result::success($result);
    }

    /**
     * 删除会议类型
     * @return \think\response\Json
     */
    public function delete()
    {
        $tagCodes = $this->request->post('tag_codes');

        $result = $this->logic->delete($tagCodes);
        return Result::success($result);
    }

    /**
     * 分页查询会议类型列表
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     */
    public function pageQuery()
    {
        $params = $this->request->get();

        $result = $this->logic->pageQuery($params);
        return Result::success($result);
    }

    /**
     * 获取会议类型详情
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getDetail()
    {
        $tagCode = $this->request->get('tag_code');

        $result = $this->logic->getDetail($tagCode);
        return Result::success($result);
    }

    /**
     * 更新数据排序
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/3/20
     */
    public function updateSort()
    {
        $sortData = $this->request->post('sort_data');

        $this->logic->updateSort($sortData);

        return Result::success();
    }
}
