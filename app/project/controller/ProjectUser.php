<?php
/**
 * Desc 项目成员 - 控制器
 * User Long
 * Date 2024/08/12
 */

declare (strict_types=1);


namespace app\project\controller;

use app\project\logic\ProjectUserLogic;
use resp\Result;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Request;
use think\response\Json;
use Throwable;

class ProjectUser
{
    private ProjectUserLogic $logic;

    public function __construct()
    {
        $this->logic = new ProjectUserLogic();
    }

    /**
     * 新增项目成员
     * @param Request $request
     * @return Json
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/12
     */
    public function create(Request $request): Json
    {
        $projectId = $request->post('project_id/d', 0);
        $userIds = $request->post('user_ids/a', []);

        $this->logic->create($projectId, $userIds);

        return Result::success();
    }

    /**
     * 项目用户列表
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/12
     */
    public function pageQuery(Request $request): Json
    {
        $projectId = $request->get('project_id/d', 0);
        $keyword = $request->get('keyword/s', '');
        $projectRole = $request->get('project_role/s', '');

        $res = $this->logic->pageQuery($projectId, $keyword, $projectRole);

        return Result::success($res);
    }

    /**
     * 用户下拉数据（中台数据）
     * @param Request $request
     * @return Json
     * User Long
     * Date 2024/8/12
     */
    public function selectorPageQuery(Request $request): Json
    {
        $keyword = $request->get('keyword/s', '');
        $projectRole = $request->get('project_role/s', '');
        $projectId = $request->get('project_id/d', 0);

        $res = $this->logic->selectorPageQuery($keyword, $projectRole, $projectId);

        return Result::success($res);
    }

    /**
     * 项目用户下拉数据(无分页)
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/3
     */
    public function selectorListQuery(Request $request): Json
    {
        $projectId = convertProjectId($request->post('project_id'));
        $keyword = $request->post('keyword/s', '');
        $projectRole = $request->post('project_role/s', '');
        $projectRoleList = $request->post('project_role_list/a', []);
        $scenario = $request->post('scenario/d', 0);
        $map = $request->post('map/s', '');

        $res = $this->logic->selectorListQuery(
            $projectId,
            $keyword,
            $projectRole,
            $projectRoleList,
            $scenario,
            $map
        );

        return Result::success($res);
    }

    /**
     * 移出项目成员
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/12/7
     */
    public function removeUser(Request $request): Json
    {
        $projectId = $request->post('project_id/d', 0);
        $userId = $request->post('user_id/d', 0);
        $isEmpty = $request->post('is_empty/d', 0);

        $this->logic->removeUser($projectId, $userId, $isEmpty);

        return Result::success();
    }

    /**
     * 更新用户名拼音字段
     * 检查并为所有记录更新中文名称的首拼和全拼字段
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function updateNamePinyin(): Json
    {
        $updateCount = $this->logic->updateUserNamePinyin();

        return Result::success(['update_count' => $updateCount]);
    }
}
