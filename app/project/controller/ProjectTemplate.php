<?php
/**
 * Desc 项目模板 - 控制器
 * User Long
 * Date 2024/10/9
 */

declare (strict_types=1);


namespace app\project\controller;

use app\project\logic\ProjectTemplateLogic;
use resp\Result;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Request;
use think\response\Json;
use Throwable;

class ProjectTemplate
{
    private ProjectTemplateLogic $logic;

    public function __construct()
    {
        $this->logic = new ProjectTemplateLogic();
    }

    /**
     * 新增
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/26
     */
    public function create(Request $request)
    {
        $params = $request->post([
            'project_template_name',
            'project_id',
            'remark'
        ]);
        $oldProjectId = $params['project_id'] ?? 0;

        $this->logic->create((int)$oldProjectId, $params);

        return Result::success();
    }

    /**
     * 根据id集删除
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/9
     */
    public function delete(Request $request)
    {
        $projectTemplateIds = $request->post('project_template_ids/a');

        $this->logic->delete($projectTemplateIds);

        return Result::success();
    }

    /**
     * 根据id更新数据
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/9
     */
    public function update(Request $request): Json
    {
        $params = $request->post([
            'project_template_id',
            'project_template_name',
            'remark',
            'sort',
            'sort_data' => []
        ]);

        $this->logic->update((int)$params['project_template_id'], $params);

        return Result::success();
    }

    /**
     * 详情
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/9
     */
    public function detail(Request $request): Json
    {
        $projectTemplateId = $request->get('project_template_id/d');

        $res = $this->logic->detail((int)$projectTemplateId);

        return Result::success($res);
    }

    /**
     * 列表数据
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/9
     */
    public function listQuery(): Json
    {
        $res = $this->logic->listQuery();

        return Result::success($res);
    }

    /**
     * 更新排序
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/9
     */
    public function updateSort(Request $request): Json
    {
        $sortData = $request->post('sort_data/a', []);

        $this->logic->updateSort($sortData);

        return Result::success();
    }

    /**
     * 模板下拉数据
     * @return Json
     * User Long
     * Date 2024/10/8
     */
    public function getTemplateSelector(): Json
    {
        return Result::success($this->logic->getTemplateSelector());
    }

}
