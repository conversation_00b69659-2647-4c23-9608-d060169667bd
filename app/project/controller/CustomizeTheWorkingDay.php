<?php
/**
 * Desc 自定义工作日 - 控制器
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);

namespace app\project\controller;

use app\project\logic\CustomizeTheWorkingDayLogic;
use basic\BaseController;
use resp\Result;
use think\App;

class CustomizeTheWorkingDay extends BaseController
{
    private CustomizeTheWorkingDayLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new CustomizeTheWorkingDayLogic();
    }

    /**
     * 获取自定义工作日详情
     * @return \think\response\Json
     * @throws \exception\NotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getDetail()
    {
        $id = $this->request->get('id');
        $result = $this->logic->getDetail((int)$id);
        return Result::success($result);
    }

    /**
     * 根据项目ID获取自定义工作日
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getByProjectId()
    {
        $projectId = $this->request->get('projectId');
        $result = $this->logic->getByProjectId((int)$projectId);
        return Result::success($result);
    }

    /**
     * 创建或更新自定义工作日
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\ValidateException
     */
    public function createOrUpdate()
    {
        $data = $this->request->post();
        
        // 获取是否同步到所有项目参数，默认为0
        if (!isset($data['sync_to_all_projects'])) {
            $data['sync_to_all_projects'] = 0;
        } else {
            $data['sync_to_all_projects'] = intval($data['sync_to_all_projects']);
        }
        
        $result = $this->logic->createOrUpdate($data);
        return Result::success($result);
    }

    /**
     * 删除自定义工作日
     * @return \think\response\Json
     * @throws \exception\NotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function delete()
    {
        $id = $this->request->post('customize_the_working_day_id');
        $result = $this->logic->delete($id);
        return Result::success($result);
    }
} 