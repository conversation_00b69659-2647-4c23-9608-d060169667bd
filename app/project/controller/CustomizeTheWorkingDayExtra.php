<?php
/**
 * Desc 自定义工作日额外调整 - 控制器
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);

namespace app\project\controller;

use app\project\logic\CustomizeTheWorkingDayExtraLogic;
use basic\BaseController;
use exception\ParamsException;
use resp\Result;
use think\App;

class CustomizeTheWorkingDayExtra extends BaseController
{
    private CustomizeTheWorkingDayExtraLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new CustomizeTheWorkingDayExtraLogic();
    }

    /**
     * 获取自定义工作日额外调整详情
     * @return \think\response\Json
     * @throws \exception\NotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getDetail()
    {
        $id = $this->request->get('id');
        $result = $this->logic->getDetail((int)$id);
        return Result::success($result);
    }

    /**
     * 根据项目ID和年份获取自定义工作日额外调整列表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getListByProjectId()
    {
        $projectId = $this->request->get('projectId');
        $year = $this->request->get('year');
        $result = $this->logic->getListByProjectId((int)$projectId, (int)$year);
        return Result::success($result);
    }

    /**
     * 分页查询自定义工作日额外调整列表
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     */
    public function pageQuery()
    {
        $params = $this->request->get();
        $result = $this->logic->pageQuery($params);
        return Result::success($result);
    }

    /**
     * 创建自定义工作日额外调整
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\ValidateException
     */
    public function create()
    {
        $data = $this->request->post();
        
        // 获取是否同步到所有项目参数，默认为0
        if (!isset($data['sync_to_all_projects'])) {
            $data['sync_to_all_projects'] = 0;
        } else {
            $data['sync_to_all_projects'] = intval($data['sync_to_all_projects']);
        }
        
        $result = $this->logic->createOrUpdate($data);
        return Result::success($result);
    }

    /**
     * 更新自定义工作日额外调整
     * @return \think\response\Json
     * @throws \exception\NotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\ValidateException
     */
    public function update()
    {
        $id = $this->request->post('customize_the_working_day_extra_id');
        $data = $this->request->post();
        
        // 获取是否同步到所有项目参数，默认为0
        if (!isset($data['sync_to_all_projects'])) {
            $data['sync_to_all_projects'] = 0;
        } else {
            $data['sync_to_all_projects'] = intval($data['sync_to_all_projects']);
        }
        
        $result = $this->logic->createOrUpdate($id, $data);
        return Result::success($result);
    }

    /**
     * 删除自定义工作日额外调整
     * @return \think\response\Json
     * @throws \exception\NotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function delete()
    {
        $id = $this->request->post('customize_the_working_day_extra_id');
        $result = $this->logic->delete($id);
        return Result::success($result);
    }

    /**
     * 批量删除自定义工作日额外调整
     * @return \think\response\Json
     */
    public function batchDelete()
    {
        $ids = $this->request->post('ids');
        if (!is_array($ids)) {
            throw new ParamsException();
        }
        $result = $this->logic->batchDelete($ids);
        return Result::success($result);
    }
} 