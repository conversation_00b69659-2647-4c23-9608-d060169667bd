<?php
/**
 * Desc 需求状态库 - 控制器
 * User Long
 * Date 2024/08/24
 */

declare (strict_types=1);


namespace app\project\controller;

use app\iterate\logic\FlowStatusEnumLogic;
use app\iterate\model\FlowStatusEnumModel;
use basic\BaseController;
use resp\Result;
use think\App;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\response\Json;

class FlowStatusEnum extends BaseController
{
    private FlowStatusEnumLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);



        //修改所有的parent_id=0
        $oldGet= $this->request->get();
        $oldPost=$this->request->post();
        if (array_key_exists('project_id', $oldGet)) {
            $oldGet['project_id'] = FlowStatusEnumModel::DEFAULT_PROJECT_ID;
        }
        if (array_key_exists('project_id', $oldPost)) {
            $oldPost['project_id'] = FlowStatusEnumModel::DEFAULT_PROJECT_ID;
        }
        $this->request = request()->withGet($oldGet)->withPost($oldPost)->setRoute([]);

        $this->logic = new FlowStatusEnumLogic($this->getSettingType());
    }

    /**
     * 状态库新增状态
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/7、
     */
    public function create(): Json
    {
        $params = $this->request->post([
            'project_id',
            'name',
            'colour'
        ]);

        $this->logic->create($params);

        return Result::success();
    }

    /**
     * 根据状态库id集删除状态
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \Throwable
     * User Long
     * Date 2024/7/20
     */
    public function delete(): Json
    {
        $statusEnumIds = $this->request->post('status_enum_ids/a');

        $this->logic->delete($statusEnumIds);

        return Result::success();
    }

    /**
     * 根据状态库id更新数据
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/7
     */
    public function update(): Json
    {
        $params = $this->request->post([
            'status_enum_id',
            'name',
            'colour'
        ]);

        $this->logic->update($params['status_enum_id'], $params);

        return Result::success();
    }

    /**
     * 迭代分类分页
     * @return Json
     * @throws DbException
     * User Long
     * Date 2024/7/20
     */
    public function listQuery(): Json
    {
        $projectId = $this->request->get('project_id/d');

        $res = $this->logic->listQuery((int)$projectId);

        return Result::success($res);
    }

    /**
     * 状态库下拉数据
     * @return Json
     * @throws DbException
     * User Long
     * Date 2024/7/20
     */
    public function selectorEnumStatus(): Json
    {
        $projectId = $this->request->post('project_id/d');

        $res = $this->logic->statusSelector((int)$projectId);

        return Result::success($res);
    }
}
