<?php
/**
 * Desc 需求类别 - 控制器
 * User Long
 * Date 2024/08/24
 */

declare (strict_types=1);


namespace app\project\controller;

use app\project\logic\ProjectCategorySettingsLogic;
use basic\BaseController;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\MissingParameterException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use resp\Result;
use think\App;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\response\Json;
use Throwable;

class Category extends BaseController
{
    private ProjectCategorySettingsLogic $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->logic = new ProjectCategorySettingsLogic($this->getSettingType());
    }

    /**
     * 新增类别
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public function create(): Json
    {
        $params = $this->request->post([
            'project_id',
            'category_name',
            'category_en_name',
            'icon',
            'template_id',
            'flow_status_id',
            'flow_process_id',
            'sort' => 1
        ]);

        $this->logic->create($params);

        return Result::success();
    }

    /**
     * 根据类别id集删除
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/8/24
     */
    public function delete(): Json
    {
        $categoryIds = $this->request->post('category_ids/a');

        $this->logic->delete($categoryIds);

        return Result::success();
    }

    /**
     * 根据类别id更新数据
     * @return Json
     * @throws ClientResponseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws MissingParameterException
     * @throws ModelNotFoundException
     * @throws ServerResponseException
     * @throws Throwable
     * User Long
     * Date 2024/8/24
     */
    public function update(): Json
    {
        $params = $this->request->post([
            'category_id',
            'category_name',
            'category_en_name',
            'icon',
            'template_id',
            'flow_status_id',
            'flow_process_id',
            'sort'
        ]);

        $this->logic->update($params['category_id'], $params);

        return Result::success();
    }

    /**
     * 类别详情
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public function detail(): Json
    {
        $categoryId = $this->request->get('category_id/d');

        $res = $this->logic->detail((int)$categoryId);

        return Result::success($res);
    }

    /**
     * 类别分页
     * @return Json
     * @throws DbException
     * User Long
     * Date 2024/8/24
     */
    public function listQuery(): Json
    {
        $projectId = $this->request->get('project_id/d');

        $res = $this->logic->listQuery((int)$projectId);

        return Result::success($res);
    }

    /**
     * 类别 - 启用
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public function enable(): Json
    {
        $categoryIds = $this->request->post('category_ids/a');

        $this->logic->enable($categoryIds);

        return Result::success();
    }

    /**
     * 类别 - 禁用
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public function disable(): Json
    {
        $categoryIds = $this->request->post('category_ids/a');

        $this->logic->disable($categoryIds);

        return Result::success();
    }

    /**
     * 类别 - 更新排序
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/24
     */
    public function updateSort(): Json
    {
        $sortData = $this->request->post('sort_data/a', []);

        $this->logic->updateSort($sortData);

        return Result::success();
    }

    /**
     * 类别下拉数据
     * @return Json
     * User Long
     * Date 2024/8/28
     */
    public function selector(): Json
    {
        $projectId = convertProjectId($this->request->post('project_id'));
        $scenario = $this->request->post('scenario/d', 0);

        $res = $this->logic->selector($projectId,$scenario);

        return Result::success($res);
    }

    /**
     * 需求类别下拉数据（带工作流状态）
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/2
     */
    public function selectorAndFlowStatus(): Json
    {
        $projectId = $this->request->post('project_id');
        $scenario = $this->request->post('scenario/d', 0);

        $res = $this->logic->selectorAndFlowStatus($projectId,$scenario);

        return Result::success($res);
    }

    /**
     * 获取相关需求id集合
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @return Json
     * User Long
     * Date 2024/9/4
     */
    public function getRelevanceDemand(): Json
    {
        $categorySettingId = $this->request->get('category_setting_id/d');

        $res = $this->logic->getRelevanceDemand((int)$categorySettingId);

        return Result::success($res);
    }

    /**
     * 修改相关需求
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws Throwable
     * User Long
     * Date 2024/9/4
     */
    public function updateRelevanceDemand(): Json
    {
        $workItemIds = $this->request->post('work_item_ids/a');
        $targetCategorySettingId = $this->request->post('target_category_setting_id/d');

        $this->logic->updateRelevanceDemand($workItemIds, (int)$targetCategorySettingId);

        return Result::success();
    }

    /**
     * 获取复制至项目/模板数据
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/11
     */
    public function getCopyToSelector(): Json
    {
        $categoryId = $this->request->post('category_id/d');

        return Result::success($this->logic->getCopyToSelector($categoryId));
    }

    /**
     * 数据复制至项目/模板
     * @return Json
     * @throws Throwable
     * User Long
     * Date 2025/4/12
     */
    public function categoryCopyTo(): Json
    {
        $categoryId = $this->request->post('category_id/d');
        $copyToIds = $this->request->post('copy_to_ids/a');
        $isTemplate = $this->request->post('is_template/d');

        return Result::success($this->logic->categoryCopyTo($categoryId, $copyToIds, $isTemplate));
    }

    /**
     * 更新类别名称
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/14
     */
    public function rename(): Json
    {
        $categoryId = $this->request->post('category_id/d');
        $categoryName = $this->request->post('category_name/s', '');
        $categoryEnName = $this->request->post('category_en_name/s', '');

        $this->logic->rename($categoryId, $categoryName, $categoryEnName);

        return Result::success();
    }
}
