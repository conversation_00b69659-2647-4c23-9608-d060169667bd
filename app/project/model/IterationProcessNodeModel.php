<?php
/**
 * Desc 迭代流程节点 - 模型
 * User Long
 * Date 2024/11/07
 * */

declare (strict_types=1);

namespace app\project\model;

use app\iterate\model\FlowProcessNodeModel;
use basic\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use traits\CreateAndUpdateModelTrait;
use utils\DBTransaction;

/**
 * This is the model class for table "iteration_process_node".
 * @property string $iteration_process_node_id id
 * @property string $create_by                 创建人
 * @property string $create_by_name            创建人名称
 * @property string $create_at                 创建时间
 * @property string $is_delete                 是否删除;1-是 0-否
 * @property string $update_by                 更新人
 * @property string $update_by_name            更新人名称
 * @property string $update_at                 更新时间
 * @property string $iteration_id              迭代id
 * @property string $node_name                 节点名称
 * @property string $node_data                 节点数据
 * @property string $process_node_id           流程Id
 * @property string $status                    节点状态;1-未开始,2-进行中,3-已完成
 * @property string $status_text_id            流转状态描述id
 * @property string $is_audit_status           审批是否通过0默认1通过2不通过3审批中
 * @property string $is_auto_status            自动化流程是否通过0默认1通过2不通过（获取详情时校验更新）
 * @property string $estimate_start_time       预估开始时间
 * @property string $estimate_end_time         预估结束时间
 * @property string $start_time                实际开始时间
 * @property string $end_time                  实际结束时间
 */
class IterationProcessNodeModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'iteration_process_node_id';
    protected $name = 'iteration_process_node';

    const HIDDEN_FIELD
        = [
            'create_by', 'create_by_name', 'create_at', 'update_by', 'update_by_name', 'update_at',
            'is_delete', 'node_data'
        ];

    // 节点状态;1-未开始,2-进行中,3-已完成
    const STATUS_NOT_START = 1;
    const STATUS_UNDER_WAY = 2;
    const STATUS_COMPLETED = 3;
    const STATUS_TEXT
        = [
            self::STATUS_NOT_START => '未开始',
            self::STATUS_UNDER_WAY => '进行中',
            self::STATUS_COMPLETED => '已完成'
        ];

    // 审批是否通过0默认1通过2不通过
    const IS_AUDIT_STATUS_ACQUIESCE = 0;
    const IS_AUDIT_STATUS_PASS = 1;
    const IS_AUDIT_STATUS_TURN_DOWN = 2;
    const IS_AUDIT_STATUS_PROCEED = 3;
    const IS_AUDIT_STATUS_TEXT
        = [
            self::IS_AUDIT_STATUS_ACQUIESCE => '默认',
            self::IS_AUDIT_STATUS_PASS      => '审批通过',
            self::IS_AUDIT_STATUS_TURN_DOWN => '审批未通过',
            self::IS_AUDIT_STATUS_PROCEED   => '审批中'
        ];

    // 自动化流程是否通过0默认1通过2不通过（获取详情时校验更新）
    const IS_AUTO_STATUS_ACQUIESCE = 0;
    const IS_AUTO_STATUS_PASS = 1;
    const IS_AUTO_STATUS_TURN_DOWN = 2;
    const IS_AUTO_STATUS_TEXT
        = [
            self::IS_AUTO_STATUS_ACQUIESCE => '默认',
            self::IS_AUTO_STATUS_PASS      => '校验通过',
            self::IS_AUTO_STATUS_TURN_DOWN => '校验未通过'
        ];

    // 流程图节点，开始节点 = 1，结束节点 = 9999
    const START_NODE = 1;
    const END_NODE = 9999;

    /**
     * 获取节点数据
     * @param $value
     * @return mixed
     * User Long
     * Date 2024/11/12
     */
    public function getNodeDataAttr($value)
    {
        return json_decode($value ?? '', true);
    }


    /**
     * 设置节点数据
     * @param $value
     * @return false|string
     * User Long
     * Date 2024/11/12
     */
    public function setNodeDataAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 获取器 - 修改默认预估迭代周期开始时间为空
     * @param $value
     * @return string
     * User Long
     * Date 2024/7/20
     */
    protected function getEstimateStartTimeAttr($value)
    {
        if (!$value) {
            return BaseModel::DEFAULT_TIME;
        }

        return ($value === BaseModel::DEFAULT_TIME) ? '' : date('Y-m-d', strtotime($value));
    }

    /**
     * 获取器 - 修改默认预估迭代周期结束时间为空
     * @param $value
     * @return string
     * User Long
     * Date 2024/7/20
     */
    protected function getEstimateEndTimeAttr($value)
    {
        if (!$value) {
            return BaseModel::DEFAULT_TIME;
        }

        return ($value === BaseModel::DEFAULT_TIME) ? '' : date('Y-m-d', strtotime($value));
    }

    /**
     * 获取器 - 修改默认实际迭代周期开始时间为空
     * @param $value
     * @param $data
     * @return string
     * User Long
     * Date 2024/7/20
     */
    protected function getStartTimeAttr($value, $data)
    {
        if (!$value) {
            return BaseModel::DEFAULT_TIME;
        }

        return ($value === BaseModel::DEFAULT_TIME) ? '' : date($this->isTimeConvert($data) ? 'Y-m-d H:i' : 'Y-m-d', strtotime($value));
    }

    /**
     * 获取器 - 修改默认实际迭代周期结束时间为空
     * @param $value
     * @param $data
     * @return string
     * User Long
     * Date 2024/7/20
     */
    protected function getEndTimeAttr($value, $data)
    {
        if (!$value) {
            return BaseModel::DEFAULT_TIME;
        }

        return ($value === BaseModel::DEFAULT_TIME) ? '' : date($this->isTimeConvert($data) ? 'Y-m-d H:i' : 'Y-m-d', strtotime($value));
    }

    /**
     * 判断是否转换开始结束时间
     * @param $data
     * @return bool
     * User Long
     * Date 2025/2/13
     */
    private function isTimeConvert($data)
    {
        if (isset($data['_is_time_convert']) && $data['_is_time_convert'] == 1) {
            return true;
        }

        return false;
    }

    /**
     * 获取器 - 状态描述
     * @param $value
     * @param $data
     * @return string
     * User Long
     * Date 2024/11/15
     */
    protected function getStatusTextAttr($value, $data)
    {
        return self::STATUS_TEXT[$data['status']] ?? '';
    }

    /**
     * 根据 id 查询数据
     * @param  int  $iterationProcessNodeId
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/13
     */
    public static function findById(int $iterationProcessNodeId): mixed
    {
        return static::status()->where(['iteration_process_node_id' => $iterationProcessNodeId])->find();
    }

    /**
     * 根据 id 批量查询数据
     * @param  array  $iterationProcessNodeId
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User 袁志凡
     * Date 2024/11/15
     */
    public static function findByIdList(array $iterationProcessNodeId): mixed
    {
        return static::status()->where(['iteration_process_node_id' => $iterationProcessNodeId])->field([
            'iteration_process_node_id',
            'node_name',
        ])->select();
    }

    /**
     * 详情页处理
     * @return IterationProcessNodeModel
     * User Long
     * Date 2024/11/13
     */
    public function toDetail()
    {
        return $this->hidden([
            'is_delete', 'create_by', 'create_by_name', 'create_at', 'update_by',
            'update_by_name', 'update_at', 'row_id', 'process_node_id', 'status_text_id'
        ]);
    }

    /**
     * 关联流程节点
     * @return \think\model\relation\HasOne
     * User Long
     * Date 2024/11/13
     */
    public function flowProcessNode()
    {
        return $this->hasOne(FlowProcessNodeModel::class, 'process_node_id', 'process_node_id');
    }

    /**
     * 关联下一节点
     * @return \think\model\relation\HasMany
     * User Long
     * Date 2024/11/12
     */
    public function nextNodeList()
    {
        return $this->hasMany(IterationProcessNodeRelationModel::class, 'process_node_id', 'iteration_process_node_id');
    }

    /**
     * 关联上一节点
     * @return \think\model\relation\HasMany
     * User Long
     * Date 2024/11/12
     */
    public function prevNodeList()
    {
        return $this->hasMany(IterationProcessNodeRelationModel::class, 'next_node_id', 'iteration_process_node_id');
    }

    /**
     * 关联提示语
     * @return \think\model\relation\HasMany
     * User Long
     * Date 2024/11/14
     */
    public function tip()
    {
        return $this->hasMany(IterationProcessNodeTipModel::class, 'iteration_process_node_id', 'iteration_process_node_id');
    }

    /**
     * 关联迭代
     * @return \think\model\relation\BelongsTo
     * User Claude
     * Date 2024/03/21
     */
    public function iteration()
    {
        return $this->belongsTo(IterationModel::class, 'iteration_id', 'iteration_id');
    }


    /**
     * 重写保存方法
     * @param  array        $data      数据
     * @param  string|null  $sequence  是否允许覆盖
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function save(array $data = [], string $sequence = null): bool
    {
        // 检测 end_time 字段是否发生变化
        $isEndTimeChanged = false;
        if (isset($data['end_time'])) {
            $isEndTimeChanged = $this->end_time != $data['end_time'];
        }

        // 开启事务
        DBTransaction::begin();
        try {
            // 调用父类保存方法
            $result = parent::save($data, $sequence);

            // 如果 end_time 发生变化，记录信息
            if ($isEndTimeChanged && $result) {
                $this->recordEndInfo();
            }

            // 提交事务
            DBTransaction::commit();
            return $result;
        } catch (\Exception $e) {
            // 回滚事务
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 记录节点结束信息
     * @param  array  $data  额外数据
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function recordEndInfo(array $data = [])
    {
        // 获取迭代信息
        $iteration = $this->iteration()->find();
        if ( ! $iteration) {
            throw new \Exception('迭代信息不存在');
        }

        // 获取项目信息
        $project = $iteration->project()->find();
        if ( ! $project) {
            throw new \Exception('项目信息不存在');
        }

        // 获取流程节点信息
        $flowProcessNode = $this->flowProcessNode()->find();
        if ( ! $flowProcessNode) {
            throw new \Exception('流程节点信息不存在');
        }

        // 准备记录数据
        $recordData = [
            'project_id'                => $project->project_id,
            'iteration_id'              => $iteration->iteration_id,
            'iteration_process_node_id' => $this->iteration_process_node_id,
            'node_stage'                => $flowProcessNode->node_data['node_setting']['node_stage'] ?? '',
            'estimate_start_time'       => $this->estimate_start_time ? $this->estimate_start_time : BaseModel::DEFAULT_TIME,
            'estimate_end_time'         => $this->estimate_end_time ? $this->estimate_end_time : BaseModel::DEFAULT_TIME,
            'actual_start_time'         => $this->start_time,
            'actual_end_time'           => $this->end_time,
//            'node_owner_id'             => $this->create_by,//错误
            'node_owner_id'             => 0,//错误
//            'node_owner_name'           => $this->create_by_name,//错误
            'node_owner_name'           => '',//错误
            'is_delayed'                => strtotime($this->end_time) > strtotime($this->estimate_end_time) ? 1 : 0,
        ];

        // 合并额外数据
        $recordData = array_merge($recordData, $data);

        // 查询是否已存在记录
        $existRecord = IterationProcessNodeEndRecordModel::where('iteration_process_node_id', $this->iteration_process_node_id)
            ->where('is_delete', BaseModel::DELETE_NOT)
            ->find();

        // 开启事务
        DBTransaction::begin();
        try {
            // 存在记录则更新，不存在则创建
            if ($existRecord) {
                // 更新记录
                $existRecord->save($recordData);
            } else {
                // 创建记录
                IterationProcessNodeEndRecordModel::create($recordData);
            }

            // 提交事务
            DBTransaction::commit();
        } catch (\Exception $e) {
            // 回滚事务
            DBTransaction::rollback();
            throw $e;
        }
    }


}
