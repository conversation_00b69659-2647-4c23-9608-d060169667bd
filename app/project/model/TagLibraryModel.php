<?php
/**
* Desc 标签库表 - 模型
* User Long
* Date 2025/03/10
* */

declare (strict_types = 1);

namespace app\project\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "ag_library".
* @property string $tag_id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $tag_code 标签code
* @property string $tag_name 标签名称
* @property string $sort 排序，默认正序
* @property string $priority 优先级，默认正序
* @property string $group_type 分组类型;1-阶段分类参数、2-会议参数
* @property string $can_be_deleted 是否可删除0可1否
*/
class TagLibraryModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'tag_id';
    protected $name = 'tag_library';

    const GROUP_TYPE_PHASE = 1; // 阶段分类参数
    const GROUP_TYPE_CONFERENCE = 2; // 会议参数

    const LIST_FIELDS = [
        'tag_code',
        'tag_name',
        'sort',
        'priority',
        'can_be_deleted',
        'create_at',
        'create_by',
        'create_by_name',
        'update_at',
        'update_by',
        'update_by_name',
    ];

    /**
     * 根据code查找会议类型
     * @param int $groupType
     * @param string $tagCode
     * @return TagLibraryModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/3/11
     */
    public static function findByTagCode(int $groupType, string $tagCode)
    {
        return static::status()->where(['group_type' => $groupType, 'tag_code' => $tagCode])->find();
    }

    /**
     * 根据code查找会议类型
     * @param int $groupType
     * @param array $tagCodes
     * @return TagLibraryModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/3/11
     */
    public static function selectByTagCode(int $groupType, array $tagCodes)
    {
        return static::status()->where(['group_type' => $groupType])->whereIn('tag_code', $tagCodes)->select();
    }

    /**
     * 获取详情
     * @return array
     */
    public function toDetail(): array
    {
        return $this->visible(self::LIST_FIELDS)->toArray();
    }

    /**
     * 根据标签名称查询数据
     * @param int $groupType
     * @param string $tagName
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/3/11
     */
    public static function findTagAndName(int $groupType, string $tagName): mixed
    {
        return static::status()->where([
            'group_type' => $groupType,
            'tag_name' => $tagName
        ])->find();
    }

    /**
     * 根据标签类型查询数据
     * @param int $groupType
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/3/11
     */
    public static function selectTagByGroupType(int $groupType): mixed
    {
        return static::status()->where([
            'group_type' => $groupType
        ])->select();
    }
}
