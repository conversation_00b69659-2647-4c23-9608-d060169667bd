<?php
declare (strict_types=1);

namespace app\project\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "t_customize_the_working_day".
 * @property int $customize_the_working_day_id id
 * @property int $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property int $is_delete 是否删除;1-是 0-否
 * @property int $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property int $project_id 项目id
 * @property int $weekday 工作日设置，以右7位二进制位表示一周中的7天,1是、0否
 * @property int $work_hours 一天工时
 * @property int $skip_public_holidays 是否跳过法定节假日,1是、0否
 */
class CustomizeTheWorkingDayModel extends BaseModel
{
    use CreateAndUpdateModelTrait;
    protected $name = 'customize_the_working_day';
    protected $pk = 'customize_the_working_day_id';

    const LIST_FIELDS = [
        'customize_the_working_day_id',
        'project_id',
        'weekday',
        'work_hours',
        'skip_public_holidays',
        'create_at',
        'create_by',
        'create_by_name',
        'update_at',
        'update_by',
        'update_by_name',
    ];

    /**
     * 根据ID查找自定义工作日
     * @param int $id
     * @return CustomizeTheWorkingDayModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findById(int $id)
    {
        return static::status()->where(['customize_the_working_day_id' => $id])->find();
    }

    /**
     * 根据项目ID查找自定义工作日
     * @param int $projectId
     * @return CustomizeTheWorkingDayModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findByProjectId(int $projectId)
    {
        return static::status()->where(['project_id' => $projectId])->find();
    }

    /**
     * 获取详情
     * @return array
     */
    public function toDetail(): array
    {
        return $this->visible(self::LIST_FIELDS)->toArray();
    }
} 