<?php
/**
* Desc 迭代过程节点关系 - 模型
* User Long
* Date 2024/11/07
* */

declare (strict_types = 1);

namespace app\project\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "iteration_process_node_relation".
* @property string $id id
* @property string $process_node_id 当前节点id;指iteration_process_node_id
* @property string $next_node_id 后置;指iteration_process_node_id
*/
class IterationProcessNodeRelationModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'iteration_process_node_relation';

    /**
     * 关联上一节点
     * @return \think\model\relation\HasOne
     * User Long
     * Date 2024/11/12
     */
    public function prevNode()
    {
        return $this->hasOne(IterationProcessNodeModel::class, 'iteration_process_node_id', 'process_node_id');
    }

    /**
     * 关联下一节点
     * @return \think\model\relation\HasOne
     * User Long
     * Date 2024/11/12
     */
    public function nextNode()
    {
        return $this->hasOne(IterationProcessNodeModel::class, 'iteration_process_node_id','next_node_id');
    }

}
