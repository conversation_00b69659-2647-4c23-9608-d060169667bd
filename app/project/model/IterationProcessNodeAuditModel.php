<?php
/**
 * Desc 迭代节点审批表 - 模型
 * User Long
 * Date 2024/11/07
 * */

declare (strict_types=1);

namespace app\project\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "iteration_process_node_audit".
 * @property string $iteration_process_node_audit_id id
 * @property string $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property string $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property string $is_audit 审批类型0默认1发起2通过3拒绝
 * @property string $iteration_process_node_id 关联节点id
 * @property string $remark 备注
 */
class IterationProcessNodeAuditModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'iteration_process_node_audit_id';
    protected $name = 'iteration_process_node_audit';

    // 审批类型0默认1发起2通过3拒绝
    const INITIATE = 1; // 发起
    const PASS = 2; // 通过
    const TURN_DOWN = 3; // 拒绝
    const ACQUIESCE = 0; // 默认

}
