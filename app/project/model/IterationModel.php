<?php
/**
* Desc 迭代 - 模型
* User Long
* Date 2024/11/07
* */

declare (strict_types = 1);

namespace app\project\model;

use app\iterate\model\FlowStatusEnumModel;
use app\iterate\model\FlowStatusTextModel;
use basic\BaseModel;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "iteration".
* @property string $iteration_id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $project_id 项目Id
* @property string $iteration_name 迭代名称
* @property string $iteration_icon 迭代图标
* @property string $extends 自定义字段
* @property string $estimate_start_time 预估开始时间
* @property string $estimate_end_time 预估结束时间
* @property string $start_time 开始时间
* @property string $end_time 结束时间
* @property string $flow_process_id 工作流程id
* @property string $flow_status_id 状态流程id
* @property string $status_text_id 当前状态
* @property string $status_enum_id 状态库Id
* @property string $sort 排序，默认正序
*/
class IterationModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'iteration_id';
    protected $name = 'iteration';

    const HIDDEN_FIELD = [
        'update_by', 'update_by_name', 'update_at', 'is_delete', 'sort'
    ];

    public function getExtendsAttr($value)
    {
        if (gettype($value) == 'string') {
            $extends = json_decode($value, true);

            if (isset($extends['schemasData'])) {
                $extends['schemasData'] = $this->resetExtendsAttr($extends['schemasData']);
            }

        } elseif (gettype($value) == 'object') {
            $extends = json_decode(json_encode($value, JSON_UNESCAPED_UNICODE), true);

            if (isset($extends['schemasData'])) {
                $extends['schemasData'] = $this->resetExtendsAttr($extends['schemasData']);
            }
        } else {
            return $value;
        }

        return $extends;
    }

    public function setExtendsAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 获取器 - 修改默认预估迭代周期开始时间为空
     * @param $value
     * @return string
     * User Long
     * Date 2024/7/20
     */
    protected function getEstimateStartTimeAttr($value)
    {
        if (!$value) {
            return BaseModel::DEFAULT_TIME;
        }

        return ($value === BaseModel::DEFAULT_TIME) ? '' : date('Y-m-d', strtotime($value));
    }

    /**
     * 获取器 - 修改默认预估迭代周期结束时间为空
     * @param $value
     * @return string
     * User Long
     * Date 2024/7/20
     */
    protected function getEstimateEndTimeAttr($value)
    {
        if (!$value) {
            return BaseModel::DEFAULT_TIME;
        }

        return ($value === BaseModel::DEFAULT_TIME) ? '' : date('Y-m-d', strtotime($value));
    }

    /**
     * 获取器 - 修改默认实际迭代周期开始时间为空
     * @param $value
     * @return string
     * User Long
     * Date 2024/7/20
     */
    protected function getStartTimeAttr($value)
    {
        if (!$value) {
            return BaseModel::DEFAULT_TIME;
        }

        return ($value === BaseModel::DEFAULT_TIME) ? '' : date('Y-m-d', strtotime($value));
    }

    /**
     * 获取器 - 修改默认实际迭代周期结束时间为空
     * @param $value
     * @return string
     * User Long
     * Date 2024/11/14
     */
    protected function getEndTimeAttr($value)
    {
        if (!$value) {
            return BaseModel::DEFAULT_TIME;
        }

        return ($value === BaseModel::DEFAULT_TIME) ? '' : date('Y-m-d', strtotime($value));
    }

    /**
     * 预加载 - 状态库
     * @return HasOne
     * User Long
     * Date 2024/11/18
     */
    public function statusText(): HasOne
    {
        return $this->hasOne(FlowStatusTextModel::class, 'status_text_id', 'status_text_id');
    }

    /**
     * 预加载 - 标签库
     * @return HasOne
     * User Long
     * Date 2024/11/11
     */
    public function statusEnum(): HasOne
    {
        return $this->hasOne(FlowStatusEnumModel::class, 'status_enum_id', 'status_enum_id');
    }

    /**
     * 根据id集查询数据
     * @param array $iterationIds
     * @return IterationModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/11
     */
    public static function selectByIds(array $iterationIds)
    {
        return static::status()->whereIn('iteration_id', $iterationIds)->select();
    }

    /**
     * 根据 id 查询数据
     * @param int $iterationId
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/11
     */
    public static function findById(int $iterationId): mixed
    {
        return static::status()->where(['iteration_id' => $iterationId])->find();
    }

    /**
     * 详情页处理
     * @return IterationModel
     * User Long
     * Date 2024/11/11
     */
    public function toDetail()
    {
        return $this->hidden(['is_delete', 'sort']);
    }

    /**
     * 根据 迭代名称 获取当前项目下的指定迭代
     * @param int $projectId
     * @param string $iteration_name
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/18
     */
    public static function findIterationAndName(int $projectId, string $iteration_name): mixed
    {
        return static::status()->where([
            'project_id' => $projectId,
            'iteration_name' => $iteration_name
        ])->find();
    }

    /**
     * 关联项目
     * @return \think\model\relation\BelongsTo
     * User Claude
     * Date 2024/03/21
     */
    public function project()
    {
        return $this->belongsTo(ProjectModel::class, 'project_id', 'project_id');
    }
}
