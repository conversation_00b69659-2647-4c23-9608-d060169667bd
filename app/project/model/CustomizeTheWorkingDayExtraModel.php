<?php
declare (strict_types=1);

namespace app\project\model;

use basic\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "t_customize_the_working_day_extra".
 * @property int $customize_the_working_day_extra_id id
 * @property int $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property int $is_delete 是否删除;1-是 0-否
 * @property int $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property int $project_id 项目id
 * @property string $date 日期
 * @property int $type 1、休息日,2、工作日
 * @property int $is_sync_to_all_projects 是否同步至其他项目
 */
class CustomizeTheWorkingDayExtraModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $name = 'customize_the_working_day_extra';
    protected $pk = 'customize_the_working_day_extra_id';

    // 类型：休息日
    const TYPE_REST_DAY = 1;
    // 类型：工作日
    const TYPE_WORK_DAY = 2;
    
    // 是否同步至其他项目：否
    const SYNC_TO_ALL_PROJECTS_NO = 0;
    // 是否同步至其他项目：是
    const SYNC_TO_ALL_PROJECTS_YES = 1;

    const LIST_FIELDS = [
        'customize_the_working_day_extra_id',
        'project_id',
        'date',
        'type',
        'is_sync_to_all_projects',
        'create_at',
        'create_by',
        'create_by_name',
        'update_at',
        'update_by',
        'update_by_name',
    ];

    /**
     * 根据ID查找自定义工作日额外调整
     * @param int $id
     * @return CustomizeTheWorkingDayExtraModel|array|mixed|\think\Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function findById(int $id)
    {
        return static::status()->where(['customize_the_working_day_extra_id' => $id])->find();
    }

    /**
     * 根据项目ID查找自定义工作日额外调整列表
     * @param int $projectId
     * @param string|null $startDate
     * @param string|null $endDate
     * @return \think\Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function findByProjectId(int $projectId, ?string $startDate = null, ?string $endDate = null)
    {
        $query = static::status()->where(['project_id' => $projectId]);
        
        if ($startDate) {
            $query->where('date', '>=', $startDate);
        }
        if ($endDate) {
            $query->where('date', '<=', $endDate);
        }
        
        return $query->select();
    }

    /**
     * 根据项目ID和日期查找自定义工作日额外调整
     * @param int $projectId
     * @param string $date
     * @return CustomizeTheWorkingDayExtraModel|array|mixed|\think\Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function findByProjectIdAndDate(int $projectId, string $date)
    {
        return static::status()->where(['project_id' => $projectId, 'date' => $date])->find();
    }

    /**
     * 获取详情
     * @return array
     */
    public function toDetail(): array
    {
        return $this->visible(self::LIST_FIELDS)->toArray();
    }
} 