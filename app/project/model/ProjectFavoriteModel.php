<?php
/**
* Desc 项目收藏表 - 模型
* User Long
* Date 2024/10/09
* */

declare (strict_types = 1);

namespace app\project\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "project_favorite".
* @property string $id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $project_id 项目id
*/
class ProjectFavoriteModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'project_favorite';

    /**
     * 根据id查询数据
     * @param int $userId
     * @param int $projectId
     * @return ProjectFavoriteModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public static function findById(int $userId, int $projectId)
    {
        return static::status()->where(['create_by' => $userId, 'project_id' => $projectId])->find();
    }

    /**
     * 根据id集查询数据
     * @param int $userId
     * @param array $projectIds
     * @return ProjectFavoriteModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public static function selectByIds(int $userId, array $projectIds)
    {
        return static::status()->where(['create_by' => $userId])->whereIn('project_id', $projectIds)->select();
    }

}
