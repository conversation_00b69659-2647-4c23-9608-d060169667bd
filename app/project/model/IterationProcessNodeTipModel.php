<?php
/**
* Desc 迭代节点提醒 - 模型
* User Long
* Date 2024/11/07
* */

declare (strict_types = 1);

namespace app\project\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "iteration_process_node_tip".
* @property string $iteration_process_node_tip_id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $iteration_process_node_id 关联节点id
* @property string $title 标题
* @property string $content 内容
*/
class IterationProcessNodeTipModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'iteration_process_node_tip_id';
    protected $name = 'iteration_process_node_tip';



}
