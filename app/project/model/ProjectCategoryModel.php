<?php
/**
* Desc 项目分类管理 - 模型
* User Long
* Date 2024/08/27
* */

declare (strict_types = 1);

namespace app\project\model;

use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "project_category".
* @property string $project_category_id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $project_id 项目id
* @property string $category_name 名称
* @property string $pid 父级id
* @property string $remark 备注
* @property string $project_category_type 分类所属 2->需求分类,5->测试用例分类
*/
class ProjectCategoryModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'project_category_id';
    protected $name = 'project_category';

    const PROJECT_CATEGORY_TYPE_TEST_CASE = 5;

    // 是否允许删除1允许0禁止
    const YES_ALLOW_DELETE = 1;
    const NOT_ALLOW_DELETE = 0;

    const HIDDEN_FIELD = ['create_by', 'create_by_name', 'create_at', 'update_by', 'update_by_name', 'update_at', 'is_delete', 'project_category_type'];


    /**
     * 根据 迭代分类id 查询分类
     * @param int $projectCategoryType
     * @param int $categoryId
     * @return ProjectCategoryModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/27
     */
    public static function findByCategoryId(int $projectCategoryType, int $categoryId): mixed
    {
        return static::status()->where(['project_category_id' => $categoryId, 'project_category_type' => $projectCategoryType])->find();
    }


    /**
     * 通过名称和pid查询指定分类
     * @param  int  $projectCategoryType
     * @param  int  $projectId
     * @param       $name
     * @param       $pid
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/12/17 上午10:44
     */
    public static function findByCategoryNameAndPid(int $projectCategoryType, int $projectId,$name,$pid): mixed
    {
        return static::status()->where([
            'project_id'            => $projectId,
            'project_category_type' => $projectCategoryType,
            'category_name'         => $name,
            'pid'                   => $pid,
        ])->find();
    }

    /**
     * 根据id集查询数据
     * @param int $projectCategoryType
     * @param array $categoryIds
     * @return ProjectCategoryModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/27
     */
    public static function selectByCategoryIds(int $projectCategoryType, array $categoryIds): Collection|array
    {
        return static::status()->where(['project_category_type' => $projectCategoryType])->whereIn('project_category_id', $categoryIds)->select();
    }

    /**
     * 详情页处理
     * @return ProjectCategoryModel
     * User Long
     * Date 2024/8/27
     */
    public function toDetail(): ProjectCategoryModel
    {
        return $this->hidden(['is_delete', 'project_category_type']);
    }

    public function getLogFieldList(): array
    {
        return $this->logFieldList;
    }
}
