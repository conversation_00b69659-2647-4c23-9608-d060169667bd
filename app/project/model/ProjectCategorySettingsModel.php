<?php
/**
 * Desc 迭代分类 - 模型
 * User Long
 * Date 2024/07/19*/

declare (strict_types=1);

namespace app\project\model;

use app\infrastructure\model\TemplateModel;
use app\iterate\model\FlowProcessModel;
use app\iterate\model\FlowStatusModel;
use app\iterate\model\FlowStatusTextModel;
use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;
use think\model\relation\HasMany;
use think\model\relation\HasOne;
use think\Paginator;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "iteration_category".
 * @property string $project_category_settings_id id
 * @property string $attribution_id 归属来源id,默认0
 * @property string $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property string $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property string $category_name 类别名称
 * @property string $category_en_name 类别英文名称
 * @property string $icon 图标
 * @property string $page_id 创建页id
 * @property string $flow_status_id 工作流id
 * @property string $flow_process_id 工作流程id
 * @property string $project_id 项目Id
 * @property string $is_enable 是否开启;1-是 0-否
 * @property string $sort 排序，默认正序
 * @property string $project_category_settings_type 分类所属 1 迭代 2 需求 3 任务 4 缺陷 5 测试用例 6 测试计划
 */
class ProjectCategorySettingsModel extends BaseModel
{
//    use OperationLogTrait;
    use CreateAndUpdateModelTrait;

    protected $pk = 'project_category_settings_id';
    protected $name = 'project_category_settings';

    // 是否开启;1-是 0-否
    const IS_ENABLE = 1;
    const IS_CLOSE = 0;


    const HIDDEN_FIELD = ['create_by', 'create_by_name', 'create_at', 'update_by', 'update_by_name', 'update_at', 'is_delete'];

    //新增/修改需要记录日志的字段，字段名=>展示名称
    protected array $logFieldList = [
        'category_name' => '类别名称',
        'category_en_name' => '类别英文名称',
        'icon' => '图标',
        'page_id' => '创建页id',
        'flow_status_id' => '工作流id',
        'project_id' => '项目Id',
        'is_delete' => [
            'type' => 'enum',
            'field_label' => '是否删除',
            'values' => [
                self::DELETE_YES => '是',
                self::DELETE_NOT => '否'
            ]
        ],
        'is_enable' => [
            'type' => 'enum',
            'field_label' => '是否禁用',
            'values' => [
                self::ENABLE_YES => '是',
                self::ENABLE_NOT => '否'
            ]
        ]
    ];

    /**
     * 根据 项目id 获取迭代分类数据
     * @param int $projectId
     * @param int $projectCategorySettingsType
     * @return ProjectCategorySettingsModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/23
     */
    public static function getIterationCategoryByProjectId(int $projectId, int $projectCategorySettingsType): array|Collection
    {
        return static::status()->where(['project_id' => $projectId, 'project_category_settings_type' => $projectCategorySettingsType])->select();
    }

    /**
     * 根据项目id，分类名称 获取当前项目下的指定类别
     * @param int $projectId 项目Id
     * @param int $projectCategorySettingsType
     * @param string $categoryName 分类名称
     * @return ProjectCategorySettingsModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/23
     */
    public static function findCategoryByProjectAndName(int $projectId, int $projectCategorySettingsType, string $categoryName): mixed
    {
        return static::status()->where([
            'project_id' => $projectId,
            'project_category_settings_type' => $projectCategorySettingsType,
            'category_name' => $categoryName
        ])->find();
    }

    /**
     * 根据项目id，分类英文名称 获取当前项目下的指定分类
     * @param int $projectId 项目Id
     * @param int $projectCategorySettingsType
     * @param string $categoryEnName 分类名称
     * @return ProjectCategorySettingsModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/23
     */
    public static function findCategoryByProjectAndEnName(int $projectId, int $projectCategorySettingsType, string $categoryEnName): mixed
    {
        return static::status()->where([
            'project_id' => $projectId,
            'project_category_settings_type' => $projectCategorySettingsType,
            'category_en_name' => $categoryEnName
        ])->find();
    }

    /**
     * 统计项目下的分类个数
     * @param int $projectId 项目Id
     * @param int $projectCategorySettingsType
     * @return int
     * @throws DbException
     * User Long
     * Date 2024/8/23
     */
    public static function countProjectCategory(int $projectId, int $projectCategorySettingsType): int
    {
        return static::status()->where(['project_id' => $projectId, 'project_category_settings_type' => $projectCategorySettingsType])->count();
    }

    /**
     * 根据 迭代分类id 查询分类
     * @param int $categoryId
     * @param int $projectCategorySettingsType
     * @return ProjectCategorySettingsModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/23
     */
    public static function findByCategoryId(int $projectCategorySettingsType, int $categoryId): mixed
    {
        return static::status()->where(['project_category_settings_id' => $categoryId, 'project_category_settings_type' => $projectCategorySettingsType])->find();
    }

    /**
     * 根据 归属来源id,项目id 查询分类
     * @param int $projectCategorySettingsType
     * @param int $categoryId
     * @param int $projectId
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/1/6
     */
    public static function findByAttributionId(int $projectCategorySettingsType, int $categoryId, int $projectId): mixed
    {
        return static::status()->where(['attribution_id' => $categoryId, 'project_category_settings_type' => $projectCategorySettingsType, 'project_id' => $projectId])->find();
    }

    /**
     * 已开启的
     * @param int $projectCategorySettingsType
     * @param int $categoryId
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date 2024/9/12 14:28
     */
    public static function findEnabledByCategoryId(int $projectCategorySettingsType, int $categoryId): mixed
    {
        return static::status()->where(['is_enable' => self::ENABLE_YES, 'project_category_settings_id' => $categoryId, 'project_category_settings_type' => $projectCategorySettingsType])->find();
    }

    /**
     * 根据 项目id 获取迭代分类分页数据
     * @param int $projectId
     * @param int $projectCategorySettingsType
     * @return Paginator
     * @throws DbException
     * User Long
     * Date 2024/8/23
     */
    public static function pageIterationCategoryByProjectId(int $projectId, int $projectCategorySettingsType): Paginator
    {
        return static::status()->where(['project_id' => $projectId, 'project_category_settings_type' => $projectCategorySettingsType])->paginate(getPageSize());
    }

    /**
     * 根据id集查询数据
     * @param int $projectCategorySettingsType 分类所属
     * @param array $categoryIds
     * @return ProjectCategorySettingsModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/23
     */
    public static function selectByCategoryIds(int $projectCategorySettingsType, array $categoryIds): array|Collection
    {
        return static::status()->where(['project_category_settings_type' => $projectCategorySettingsType])->whereIn('project_category_settings_id', $categoryIds)->select();
    }

    public static function findById(int $targetCategoryId)
    {
        return static::status()->where(['is_enable' => self::ENABLE_YES, 'project_category_settings_id' => $targetCategoryId])->find();

    }

    /**
     * 预加载 - 项目表
     * @return HasOne
     */
    public function project(): HasOne
    {
        return $this->hasOne(ProjectModel::class, 'project_id', 'project_id');
    }

    /**
     * 预加载 - 模板表
     * @return HasOne
     * User Long
     * Date 2024/7/24
     */
    public function template(): HasOne
    {
        return $this->hasOne(TemplateModel::class, 'id', 'template_id');
    }

    /**
     * 预加载 - 工作流表
     * @return HasOne
     * User Long
     * Date 2024/8/22
     */
    public function flowStatus(): HasOne
    {
        return $this->hasOne(FlowStatusModel::class, 'flow_status_id', 'flow_status_id');
    }

    /**
     * 预加载 - 工作流描述表
     * @return HasMany
     * User Long
     * Date 2024/9/2
     */
    public function flowStatusText(): HasMany
    {
        return $this->hasMany(FlowStatusTextModel::class, 'flow_status_id', 'flow_status_id');
    }

    /**
     * 预加载 - 工作流程表
     * @return HasOne
     * User Long
     * Date 2024/8/22
     */
    public function flowProcess(): HasOne
    {
        return $this->hasOne(FlowProcessModel::class, 'flow_process_id', 'flow_process_id');
    }

    public function getLogFieldList(): array
    {
        return $this->logFieldList;
    }

    /**
     * 生成复制名称(中文)
     * @param $name
     * @param $projectId
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/4/14
     */
    public static function generateName($name, $projectId): string
    {
        $query = static::status()->where(['project_id' => $projectId]);

        return generateCopyName($name, 'category_name', $query);
    }

    /**
     * 生成复制名称(英文)
     * @param $name
     * @param $projectId
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/4/14
     */
    public static function generateEnName($name, $projectId): string
    {
        $query = static::status()->where(['project_id' => $projectId]);

        return generateCopyName($name, 'category_en_name', $query);
    }
}
