<?php
/**
* Desc 项目模板表 - 模型
* User Long
* Date 2024/10/09
* */

declare (strict_types = 1);

namespace app\project\model;

use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "project_template".
* @property string $project_template_id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $project_template_name 模板名称
* @property string $project_id 项目id
* @property string $is_allow_delete 是否允许删除1允许0禁止
* @property string $remark 备注
* @property string $sort 排序，默认正序
*/
class ProjectTemplateModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'project_template_id';
    protected $name = 'project_template';

    const HIDDEN_FIELD = ['update_by', 'update_by_name', 'update_at', 'is_delete'];

    // 是否模板，1是 0否
    const IS_TEMPLATE_YES = 1;
    const IS_TEMPLATE_NO = 0;

    /**
     * 统计模板数量
     * @return int
     * @throws DbException
     * User Long
     * Date 2024/10/9
     */
    public static function countProjectTemplate(): int
    {
        return static::status()->count();
    }

    /**
     * 根据 名称 获取当前模板下的指定数据
     * @param string $projectTemplateName
     * @return mixed
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/9
     */
    public static function findEnumAndName(string $projectTemplateName): mixed
    {
        return static::status()->where([
            'project_template_name' => $projectTemplateName
        ])->find();
    }

    /**
     * 根据id集查询数据
     * @param array $projectTemplateIds
     * @return ProjectTemplateModel[]|array|Collection
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/9
     */
    public static function selectByIds(array $projectTemplateIds)
    {
        return static::status()->whereIn('project_template_id', $projectTemplateIds)->select();
    }

    /**
     * 根据 id 类型 查询数据
     * @param int $projectTemplateId
     * @return ProjectTemplateModel|array|mixed|Model|null
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/9
     */
    public static function findById(int $projectTemplateId)
    {
        return static::status()->where(['project_template_id' => $projectTemplateId])->find();
    }

    /**
     * 详情页处理
     * @return ProjectTemplateModel
     * User Long
     * Date 2024/10/9
     */
    public function toDetail()
    {
        return $this->hidden(['is_delete']);
    }

    /**
     * 预加载 - 项目表
     * @return HasOne
     * User Long
     * Date 2024/9/2
     */
    public function project(): HasOne
    {
        return $this->hasOne(ProjectModel::class, 'project_id', 'project_id');
    }

    /**
     * 预加载 - 项目表(一对多)
     * @return \think\model\relation\HasMany
     */
    public function projectMany(): \think\model\relation\HasMany
    {
        return $this->hasMany(ProjectModel::class, 'project_template_id', 'project_template_id');
    }
}
