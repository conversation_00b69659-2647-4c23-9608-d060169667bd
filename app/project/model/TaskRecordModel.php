<?php
declare (strict_types=1);

namespace app\project\model;

use basic\BaseModel;

/**
 * 任务记录表模型
 * Class TaskRecordModel
 * @package app\project\model
 * 
 * @property int $id 主键ID
 * @property int $project_id 项目ID
 * @property string $date 统计日期
 * @property string $time 定时任务执行时间
 * @property int $statistics 统计日期：1当天、2昨天
 * @property int $count 缺陷数量
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @property int $is_delete 是否删除：0未删除、1已删除
 */
class TaskRecordModel extends BaseModel
{
    // 设置表名
    protected $name = 'task_record';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 软删除常量
    const DELETE_NO = 0;  // 未删除
    const DELETE_YES = 1; // 已删除
    
    // 统计日期常量
    const STATISTICS_TODAY = 1;     // 当天
    const STATISTICS_YESTERDAY = 2; // 昨天
    
    /**
     * 根据项目ID和日期查询记录
     * @param int $projectId 项目ID
     * @param string $date 日期
     * @return TaskRecordModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findByProjectIdAndDate(int $projectId, string $date)
    {
        return self::where([
            'project_id' => $projectId,
            'date' => $date,
            'is_delete' => self::DELETE_NO
        ])->find();
    }
    
    /**
     * 根据项目ID查询记录列表
     * @param int $projectId 项目ID
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findListByProjectId(int $projectId)
    {
        return self::where([
            'project_id' => $projectId,
            'is_delete' => self::DELETE_NO
        ])->order('date', 'desc')->select();
    }
    
    /**
     * 根据日期查询记录列表
     * @param string $date 日期
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findListByDate(string $date)
    {
        return self::where([
            'date' => $date,
            'is_delete' => self::DELETE_NO
        ])->select();
    }
    
    /**
     * 根据ID查询记录
     * @param int $id ID
     * @return TaskRecordModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findById(int $id)
    {
        return self::where([
            'id' => $id,
            'is_delete' => self::DELETE_NO
        ])->find();
    }
} 