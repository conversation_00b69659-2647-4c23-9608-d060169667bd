<?php
declare (strict_types=1);

namespace app\project\model;

use basic\BaseModel;

/**
 * 缺陷统计表模型
 * Class BugStatisticsModel
 * @package app\project\model
 * 
 * @property int $id 主键ID
 * @property int $project_id 项目ID
 * @property int $iteration_id 迭代ID
 * @property int $user_id 人员ID
 * @property string $date 统计日期
 * @property int $statistics 统计日期：1当天、2昨天
 * @property string $time bug日期截止时间
 * @property int $count 缺陷数量
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @property int $is_delete 是否删除：0未删除、1已删除
 */
class BugStatisticsModel extends BaseModel
{
    // 设置表名
    protected $name = 'bug_statistics';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 软删除常量
    const DELETE_NO = 0;  // 未删除
    const DELETE_YES = 1; // 已删除
    
    // 统计日期常量
    const STATISTICS_TODAY = 1;     // 当天
    const STATISTICS_YESTERDAY = 2; // 昨天
    
    /**
     * 根据项目ID和日期查询记录
     * @param int $projectId 项目ID
     * @param string $date 日期
     * @return BugStatisticsModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findByProjectIdAndDate(int $projectId, string $date)
    {
        return self::where([
            'project_id' => $projectId,
            'date' => $date,
            'is_delete' => self::DELETE_NO
        ])->find();
    }
    
    /**
     * 根据项目ID、迭代ID和日期查询记录
     * @param int $projectId 项目ID
     * @param int $iterationId 迭代ID
     * @param string $date 日期
     * @return BugStatisticsModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findByProjectIterationAndDate(int $projectId, int $iterationId, string $date)
    {
        return self::where([
            'project_id' => $projectId,
            'iteration_id' => $iterationId,
            'date' => $date,
            'is_delete' => self::DELETE_NO
        ])->find();
    }
    
    /**
     * 根据项目ID、用户ID和日期查询记录
     * @param int $projectId 项目ID
     * @param int $userId 用户ID
     * @param string $date 日期
     * @return BugStatisticsModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findByProjectUserAndDate(int $projectId, int $userId, string $date)
    {
        return self::where([
            'project_id' => $projectId,
            'user_id' => $userId,
            'date' => $date,
            'is_delete' => self::DELETE_NO
        ])->find();
    }
    
    /**
     * 根据项目ID查询记录列表
     * @param int $projectId 项目ID
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findListByProjectId(int $projectId)
    {
        return self::where([
            'project_id' => $projectId,
            'is_delete' => self::DELETE_NO
        ])->order('date', 'desc')->select();
    }
    
    /**
     * 根据迭代ID查询记录列表
     * @param int $iterationId 迭代ID
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findListByIterationId(int $iterationId)
    {
        return self::where([
            'iteration_id' => $iterationId,
            'is_delete' => self::DELETE_NO
        ])->order('date', 'desc')->select();
    }
    
    /**
     * 根据用户ID查询记录列表
     * @param int $userId 用户ID
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findListByUserId(int $userId)
    {
        return self::where([
            'user_id' => $userId,
            'is_delete' => self::DELETE_NO
        ])->order('date', 'desc')->select();
    }
    
    /**
     * 根据日期查询记录列表
     * @param string $date 日期
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findListByDate(string $date)
    {
        return self::where([
            'date' => $date,
            'is_delete' => self::DELETE_NO
        ])->select();
    }
    
    /**
     * 根据ID查询记录
     * @param int $id ID
     * @return BugStatisticsModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findById(int $id)
    {
        return self::where([
            'id' => $id,
            'is_delete' => self::DELETE_NO
        ])->find();
    }
} 