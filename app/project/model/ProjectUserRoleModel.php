<?php
/**
* Desc 成员角色 - 模型
* User Long
* Date 2024/12/05
* */

declare (strict_types = 1);

namespace app\project\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "project_user_role".
* @property string $id id
* @property string $user_id 用户Id
* @property string $project_role 项目角色code(数据来源t_enum->enum_value)
* @property string $project_name 角色名
*/
class ProjectUserRoleModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'project_user_role';



}
