<?php
/**
 * Desc 项目成员 - 模型
 * User Long
 * Date 2024/08/07*/

declare (strict_types=1);

namespace app\project\model;

use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\model\relation\HasMany;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "project_user".
 * @property string $id id
 * @property string $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是,0-否
 * @property string $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property string $project_id 项目Id
 * @property string $user_id 用户Id
 * @property string $user_name 名称
 * @property string $name_pinyin_short 中文名称首拼
 * @property string $name_pinyin_full 中文名称全拼
 * @property string $en_user_name 英文名称
 * @property string $avatar 头像
 * @property string $position_name 主岗位名
 * @property string $project_role 项目角色;1-项目经理,2-产品经理,3-UI,4-前端开发,5-后端开发,6-测试  #2024-12-5 废弃
 * @property string $phone 手机号码
 */
class ProjectUserModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'project_user';

    /**
     * 预加载 - 成员角色
     * @return HasMany
     * User Long
     * Date 2024/10/11
     */
    public function role(): HasMany
    {
        return $this->HasMany(ProjectUserRoleModel::class, 'user_id', 'user_id');
    }

    /**
     * 搜索器 - 项目角色
     * @param $query
     * @param $value
     * @return void|Collection
     * User Long
     * Date 2024/12/6
     */
    public function searchProjectRoleAttr($query, $value)
    {
        if (empty($value)) return;

        $ProjectUserRoleModel = new ProjectUserRoleModel();

        if (is_array($value)) {
            $ProjectUserRoleModel = $ProjectUserRoleModel->whereIn('project_role', $value);
        } else {
            $ProjectUserRoleModel = $ProjectUserRoleModel->where(['project_role' => $value]);
        }
        $projectUserId = $ProjectUserRoleModel->column('user_id');

        return $query->whereIn('user_id', $projectUserId);
    }

    /**
     * 根据 项目id 获取 用户数据
     * @param int|array $projectId
     * @param string|array $projectRole
     * @param string $keyword
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/10
     */
    public static function selectUserInfoByProjectId(int|array $projectId = 0, string|array $projectRole = '', string $keyword = ''): mixed
    {
        $model = static::status()->alias('pu')->with(['role']);

        // 使用子查询关联最新的记录
        $subQueryModel = self::status();
        if ($projectId) {
            if (is_array($projectId)) {
                $subQueryModel = $subQueryModel->whereIn('project_id', $projectId);
            } else {
                $subQueryModel = $subQueryModel->where(['project_id' => $projectId]);
            }
        }
        // buildSql() 方法会生成子查询的SQL语句
        $subQuery = $subQueryModel->fieldRaw('max(id) as max_id')->group('user_id')->buildSql();
        // 使用标准的 join 方法，将生成的SQL作为表名进行关联
        $model->join([$subQuery => 'sub'], 'pu.id = sub.max_id');

        if ($keyword) {
            $model = $model->where('pu.user_name|pu.en_user_name|pu.name_pinyin_short|pu.name_pinyin_full', 'like', "%$keyword%");
        }

        $model = $model->withSearch(['project_role'], [
            'project_role' => $projectRole ?? '',
        ]);

        return $model->select();
    }

    public static function selectUserInfoAll()
    {
        return new Collection((new ProjectUserModel)->select()->column(null, 'user_id'));
    }


    /**
     * 根据项目id 与用户id 获取用户信息
     * @param int $projectId
     * @param array $userIds
     * @return ProjectUserModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/13
     */
    public static function selectUserInfoByProjectIdAndUserIds(int $projectId, array $userIds): Collection|array
    {
        return static::status()->with(['role'])->where(['project_id' => $projectId])->whereIn('user_id', $userIds)->select();
    }

    /**
     * 查询项目用户
     * @param int $projectId
     * @param int $userId
     * @return ProjectUserModel|array|mixed|\think\Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/12/7
     */
    public static function findProjectUserById(int $projectId, int $userId)
    {
        return static::status()->where(['project_id' => $projectId, 'user_id' => $userId])->find();
    }


    /**
     * 查询项目用户
     * @param int $projectId
     * @param int $userId
     * @return ProjectUserModel|array|mixed|\think\Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/12/7
     */
    public static function findUserById(int $userId)
    {
        return static::status()->where(['user_id' => $userId])->find();
    }

    /**
     * 查询项目用户
     * @param  array  $userIds
     * @return ProjectUserModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function findUsersByIds(array $userIds)
    {
        return static::status()
            ->where(['user_id' => $userIds])
            ->group('user_id')
            ->select();
    }

    /**
     * 根据用户id 查询项目用户集合
     * @param array|int $userId
     * @param $projectId
     * @return Collection|array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/2/28
     */
    public static function selectListById(array|int $userId, $projectId = null, $isGroup = []): Collection|array
    {
        $where = [
            'user_id' => $userId,
        ];

        if ($projectId) {
            $where['project_id'] = $projectId;
        }

        $model = (new static())::status()->where($where);

        if ($isGroup) {
            $model = $model->group($isGroup);
        }

        return $model->select();
    }

//    /**
//     * 获取用户集合，不过滤已删除的
//     * @param  array|int  $userId
//     * @param             $projectId
//     * @return Collection|array
//     * @throws DataNotFoundException
//     * @throws DbException
//     * @throws ModelNotFoundException
//     */
//    public static function findListNotDelById(array|int $userId, $projectId = null): Collection|array
//    {
//
//        $where = [
//            'user_id' => $userId,
//        ];
//
//        if ($projectId) {
//            $where['project_id'] = $projectId;
//        }
//
//        return (new static())::where($where)->select();
//    }

}
