<?php
/**
 * Desc 迭代流程节点结束记录 - 模型
 * User Claude
 * Date 2024/03/21
 */

declare (strict_types = 1);

namespace app\project\model;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "iteration_process_node_end_record".
 * @property int $id 主键ID
 * @property int $project_id 所属项目ID
 * @property int $iteration_id 迭代ID
 * @property int $iteration_process_node_id 迭代节点ID
 * @property string $node_stage 节点阶段
 * @property string $estimate_start_time 预估开始时间
 * @property string $estimate_end_time 预估结束时间
 * @property string $actual_start_time 实际开始时间
 * @property string $actual_end_time 实际结束时间
 * @property int $node_owner_id 节点负责人ID
 * @property string $node_owner_name 节点负责人姓名
 * @property int|null $group_leader_id 组长ID
 * @property string|null $group_leader_name 组长姓名
 * @property int $is_delayed 是否延期：0-否 1-是
 * @property int $create_by 创建人ID
 * @property string $create_by_name 创建人姓名
 * @property string $create_at 创建时间
 * @property int|null $update_by 更新人ID
 * @property string|null $update_by_name 更新人姓名
 * @property string|null $update_at 更新时间
 * @property int $is_delete 是否删除：0-否 1-是
 */
class IterationProcessNodeEndRecordModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'id';
    protected $name = 'iteration_process_node_end_record';

    const HIDDEN_FIELD = [
        'create_by', 'create_by_name', 'create_at', 'update_by', 'update_by_name', 'update_at', 'is_delete'
    ];

    /**
     * 关联项目
     * @return \think\model\relation\BelongsTo
     */
    public function project()
    {
        return $this->belongsTo(ProjectModel::class, 'project_id', 'project_id');
    }

    /**
     * 关联迭代
     * @return \think\model\relation\BelongsTo
     */
    public function iteration()
    {
        return $this->belongsTo(IterationModel::class, 'iteration_id', 'iteration_id');
    }

    /**
     * 关联迭代流程节点
     * @return \think\model\relation\BelongsTo
     */
    public function iterationProcessNode()
    {
        return $this->belongsTo(IterationProcessNodeModel::class, 'iteration_process_node_id', 'iteration_process_node_id');
    }

    /**
     * 关联节点负责人
     * @return \think\model\relation\BelongsTo
     */
    public function nodeOwner()
    {
        return $this->belongsTo(UserModel::class, 'node_owner_id', 'user_id');
    }

    /**
     * 关联组长
     * @return \think\model\relation\BelongsTo
     */
    public function groupLeader()
    {
        return $this->belongsTo(UserModel::class, 'group_leader_id', 'user_id');
    }

    /**
     * 获取器 - 是否延期
     * @param $value
     * @param $data
     * @return string
     */
    protected function getIsDelayedTextAttr($value, $data)
    {
        return $data['is_delayed'] ? '是' : '否';
    }

    /**
     * 创建结束记录
     * @param array $data
     * @return bool
     */
    public static function createEndRecord(array $data)
    {
        // 计算是否延期
        $data['is_delayed'] = strtotime($data['actual_end_time']) > strtotime($data['estimate_end_time']) ? 1 : 0;

        return static::create($data);
    }
}