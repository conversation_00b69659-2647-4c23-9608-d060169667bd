<?php
/**
 * 日清bug统计规则设置模型
 */

declare (strict_types=1);

namespace app\project\model;

use basic\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use traits\CreateAndUpdateModelTrait;

/**
 * Class NissinBugStatisticsRulesModel
 * @package app\project\model
 * @property int $nissin_bug_statistics_rules_id id
 * @property int $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property int $is_delete 是否删除;1-是 0-否
 * @property int $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property int $project_id 项目id
 * @property string $time 定时任务执行时间，时分秒
 * @property int $statistics 统计日期：1当天、2昨天
 * @property int $enable 是否启用
 * @property int $use_custom_workdays 使用自定义工作日 0-否，1-是
 */
class NissinBugStatisticsRulesModel extends BaseModel
{
    use CreateAndUpdateModelTrait;
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'nissin_bug_statistics_rules_id';

    /**
     * 数据表名称
     * @var string
     */
    protected $table = 't_nissin_bug_statistics_rules';



    /**
     * 统计日期：当天
     */
    const STATISTICS_TODAY = 1;

    /**
     * 统计日期：昨天
     */
    const STATISTICS_YESTERDAY = 2;

    /**
     * 是否启用：是
     */
    const ENABLE_YES = 1;

    /**
     * 是否启用：否
     */
    const ENABLE_NO = 0;

    /**
     * 根据ID查找记录
     * @param int $id
     * @return NissinBugStatisticsRulesModel|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function findById(int $id)
    {
        return self::where('nissin_bug_statistics_rules_id', $id)
            ->where('is_delete', self::DELETE_NOT)
            ->find();
    }

    /**
     * 根据项目ID查找记录
     * @param int $projectId
     * @return NissinBugStatisticsRulesModel|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function findByProjectId(int $projectId)
    {
        return self::where('project_id', $projectId)
            ->where('is_delete', self::DELETE_NOT)
            ->find();
    }

    /**
     * 转换为详情数组
     * @return array
     */
    public function toDetail(): array
    {
        return [
            'nissin_bug_statistics_rules_id' => $this->nissin_bug_statistics_rules_id,
            'project_id' => $this->project_id,
            'time' => $this->time,
            'statistics' => $this->statistics,
            'enable' => $this->enable,
            'use_custom_workdays' => $this->use_custom_workdays,
            'create_at' => $this->create_at,
            'create_by' => $this->create_by,
            'create_by_name' => $this->create_by_name,
            'update_at' => $this->update_at,
            'update_by' => $this->update_by,
            'update_by_name' => $this->update_by_name
        ];
    }
} 