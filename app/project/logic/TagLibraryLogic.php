<?php
/**
 * Desc 标签管理 - 逻辑层
 * User Long
 * Date 2025/03/10
 */
declare (strict_types=1);

namespace app\project\logic;

use app\project\model\TagLibraryModel;
use app\project\validate\TagLibraryValidate;
use basic\BaseLogic;
use basic\BaseModel;
use exception\NotFoundException;
use exception\ParamsException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\exception\ValidateException;
use think\Paginator;
use utils\DBTransaction;

class TagLibraryLogic extends BaseLogic
{
    private int $groupType;

    public function __construct(int $groupType = 0)
    {
        $this->groupType = $groupType;
    }

    /**
     * 获取标签列表
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getSelector(): array
    {
        return TagLibraryModel::status()
            ->order('sort', 'ASC')
            ->where(['group_type' =>  $this->groupType])
            ->column(['tag_code' => 'value', 'tag_name' => 'label', 'sort', 'priority']);
    }

    /**
     * 创建标签
     * @param array $data
     * @return TagLibraryModel
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/3/11
     */
    public function create(array $data): TagLibraryModel
    {
        // 验证数据
        validate(TagLibraryValidate::class)->scene('create')->check($data);

        // 检查名称是否已存在
        $tagExist = TagLibraryModel::selectTagByGroupType($this->groupType);
        if (!$tagExist->where('tag_name', $data['tag_name'])->isEmpty()) {
            throw new ParamsException('名称已存在');
        }

        // 阶段分类参数，检查优先级是否存在重复
        if ($this->groupType == TagLibraryModel::GROUP_TYPE_PHASE) {
            if (!$tagExist->where('priority', $data['priority'])->isEmpty()) {
                throw new ParamsException('优先级已存在');
            }
        }

        $data['tag_code'] = uniqueCoding('tag');
        $data['group_type'] = $this->groupType;

        // 创建记录
        $model = new TagLibraryModel();
        $model->save($data);

        return $model;
    }

    /**
     * 更新标签
     * @param  string    $tagCode
     * @param  array  $data
     * @return bool
     * @throws NotFoundException
     * @throws ValidateException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(string $tagCode, array $data): bool
    {
        // 查找记录
        $model = TagLibraryModel::findByTagCode($this->groupType, $tagCode);
        if ( ! $model) {
            throw new NotFoundException('标签不存在');
        }

        // 检查是否可编辑
        if ($model->can_be_deleted && isset($data['tag_name']) && $model->tag_name != $data['tag_name']) {
            throw new ValidateException($model->tag_name.' 不可编辑');
        }

        // 验证数据
        validate(TagLibraryValidate::class)->scene('update')->check($data);

        // 检查名称是否已存在
        $tagExist = TagLibraryModel::selectTagByGroupType($this->groupType);
        $tagEditExist = $tagExist->where('tag_id', '!=', $model->tag_id);
        if (!$tagEditExist->where('tag_name', $data['tag_name'])->isEmpty()) {
            throw new ParamsException('名称已存在');
        }

        // 阶段分类参数，检查优先级是否存在重复
        if ($this->groupType == TagLibraryModel::GROUP_TYPE_PHASE) {
            if (!$tagEditExist->where('priority', $data['priority'])->isEmpty()) {
                throw new ParamsException('优先级已存在');
            }
        }

        // 更新记录
        return $model->save($data);
    }

    /**
     * 删除标签
     * @param array $tagCodes
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \Throwable
     * User Long
     * Date 2025/3/11
     */
    public function delete(array $tagCodes): bool
    {
        // 查找记录
        $models = TagLibraryModel::selectByTagCode($this->groupType, $tagCodes);

        if ($models->isEmpty()) {
            throw new NotFoundException('标签不存在');
        }

        try {
            DBTransaction::begin();

            foreach ($models as $model) {
                // 检查是否可删除
                if ($model->can_be_deleted) {
                    throw new ValidateException($model->tag_name.' 不可删除');
                }

                // 删除记录
                $model->save(['is_delete' => BaseModel::DELETE_YES]);
            }

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }

        return true;
    }

    /**
     * 分页查询标签列表
     * @param  array  $params
     * @return Paginator
     * @throws DbException
     */
    public function pageQuery(array $params): Paginator
    {
        $tagName = isset($params['tag_name']) ? trim($params['tag_name']) : '';

        $query = TagLibraryModel::status();

        // 按名称搜索
        if ($tagName) {
            $query->whereLike('tag_name', "%{$tagName}%");
        }

        // 查询列表
        return $query->field(TagLibraryModel::LIST_FIELDS)
            ->where(['group_type' =>  $this->groupType])
            ->order('sort', 'ASC')
            ->paginate(getPageSize());

    }

    /**
     * 获取标签详情
     * @param string $tagCode
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDetail(string $tagCode): array
    {
        $model = TagLibraryModel::findByTagCode($this->groupType, $tagCode);
        if ( ! $model) {
            throw new NotFoundException('标签不存在');
        }

        return $model->toDetail();
    }


    /**
     * 更新数据排序
     * @param array $sortData
     *    $sortData[] = ['tag_code', 'sort']
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/3/20
     */
    public function updateSort(array $sortData): bool
    {
        if ( ! $sortData) {
            return true;
        }

        foreach ($sortData as $param) {
            // 验证器校验
            validate(TagLibraryValidate::class)->scene('updateSort')->check($param);
        }

        $sortData = array_column($sortData, 'sort', 'tag_code');
        $tagCodes = array_keys($sortData);
        $models = TagLibraryModel::selectByTagCode($this->groupType, $tagCodes);

        try {
            DBTransaction::begin();

            foreach ($models as $model) {
                if (isset($sortData[$model->tag_code])) {
                    $model->save(['sort' => $sortData[$model->tag_code]]);
                }
            }

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }

        return true;
    }
}
