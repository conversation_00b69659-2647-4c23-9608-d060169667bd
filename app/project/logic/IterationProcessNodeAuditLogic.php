<?php
/**
 * Desc 迭代节点审批 - 逻辑处理
 * User Long
 * Date 2024/11/9
 */

namespace app\project\logic;

use app\project\model\IterationProcessNodeAuditModel;

class IterationProcessNodeAuditLogic
{
    /**
     * 根据迭代节点 id 查询审批记录
     * @param int $iterationProcessNodeId
     * @return array
     * User Long
     * Date 2024/11/16
     */
    public static function columnAuditListByNodeId(int $iterationProcessNodeId)
    {
        return IterationProcessNodeAuditModel::status()
            ->where(['iteration_process_node_id' => $iterationProcessNodeId])
            ->column('iteration_process_node_audit_id, create_by, create_by_name, create_at, is_audit, remark');
    }

    /**
     * 发起审批
     * @param int $iterationProcessNodeId
     * @param string $remark
     * @return bool
     * User Long
     * Date 2024/11/14
     */
    public static function initiate(int $iterationProcessNodeId, string $remark)
    {
        return self::createAudit($iterationProcessNodeId, IterationProcessNodeAuditModel::INITIATE, $remark);
    }

    /**
     * 通过
     * @param int $iterationProcessNodeId
     * @param string $remark
     * @return bool
     * User Long
     * Date 2024/11/14
     */
    public static function pass(int $iterationProcessNodeId, string $remark)
    {
        return self::createAudit($iterationProcessNodeId, IterationProcessNodeAuditModel::PASS, $remark);
    }

    /**
     * 拒绝
     * @param int $iterationProcessNodeId
     * @param string $remark
     * @return bool
     * User Long
     * Date 2024/11/14
     */
    public static function turnDown(int $iterationProcessNodeId, string $remark)
    {
        return self::createAudit($iterationProcessNodeId, IterationProcessNodeAuditModel::TURN_DOWN, $remark);
    }

    /**
     * 创建审批记录
     * @param int $iterationProcessNodeId
     * @param int $isAudit
     * @param string $remark
     * @return bool
     * User Long
     * Date 2024/11/14
     */
    private static function createAudit(int $iterationProcessNodeId, int $isAudit, string $remark)
    {
        $iterationModel = new IterationProcessNodeAuditModel();

        return $iterationModel->save([
            'iteration_process_node_id' => $iterationProcessNodeId,
            'is_audit' => $isAudit,
            'remark' => $remark,
        ]);
    }
}
