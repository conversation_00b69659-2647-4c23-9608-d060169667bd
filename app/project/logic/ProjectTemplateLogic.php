<?php
/**
 * Desc 项目模板 - 逻辑层
 * User Long
 * Date 2024/10/9
 */
declare (strict_types=1);

namespace app\project\logic;

use app\infrastructure\logic\FieldConfigLogic;
use app\infrastructure\logic\TemplateLogic;
use app\iterate\logic\FlowProcessLogic;
use app\iterate\logic\FlowProcessNodeRelationLogic;
use app\iterate\logic\FlowStatusEnumLogic;
use app\iterate\logic\FlowStatusLogic;
use app\iterate\logic\FlowStatusNodeRelationLogic;
use app\iterate\logic\FlowStatusTextLogic;
use app\iterate\logic\FlowStatusTransferLogic;
use app\project\model\ProjectModel;
use app\project\model\ProjectTemplateModel;
use app\project\validate\ProjectTemplateValidate;
use basic\BaseLogic;
use basic\BaseModel;
use exception\BusinessException;
use exception\NotFoundException;
use exception\ParamsException;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use Throwable;

class ProjectTemplateLogic extends BaseLogic
{
    private int $templateMaxNum = 20; // 模板最多可添加20个状态

    /**
     * 获取模板名称
     * @param $projectTemplateId
     * @return mixed
     * User Long
     * Date 2024/10/11
     */
    public static function getProductNameByProductId($projectTemplateId): mixed
    {
        $model = ProjectTemplateModel::where(['project_template_id' => $projectTemplateId]);

        return $model->value('project_template_name');
    }

    /**
     * 新增
     * @param  int    $oldProjectId
     * @param  array  $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/19
     */
    public function create(int $oldProjectId, array $params): void
    {
        // 验证器校验
        validate(ProjectTemplateValidate::class)->scene('create')->check($params);

        if (ProjectTemplateModel::countProjectTemplate() >= $this->templateMaxNum) {
            throw new ParamsException('仅限添加'.$this->templateMaxNum.'个状态');
        }

        // 检查名称是否已存在
        $templateNameExist = ProjectTemplateModel::findEnumAndName((string)$params['project_template_name']);
        if ($templateNameExist) {
            throw new ParamsException('名称不可相同');
        }

        try {
            Db::startTrans();
            // 创建模板项目
            $projectModel = ProjectInfoLogic::createTemplateProject(ProjectInfoLogic::getProjectName((int)$params['project_id']));

            // 保存模板
            $templateModel = new ProjectTemplateModel();
            $params['project_id'] = $projectModel->project_id;
            $templateModel->save($params);

            // 更新模板项目关联模板id
            ProjectInfoLogic::updateTemplateIdByProjectId($projectModel, (int)$templateModel->project_template_id);

            // 复制项目模板数据
            self::copyProjectTemplate($oldProjectId, (int)$projectModel->project_id);

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 复制项目模板数据
     * @param  int  $oldProjectId
     * @param  int  $newProjectId
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/15
     */
    public static function copyProjectTemplate(int $oldProjectId, int $newProjectId)
    {
        // 复制项目相关设置，需求、迭代、缺陷、任务、测试用例、测试计划
        // 复制字段管理
        FieldConfigLogic::copyProjectDataByProjectId($oldProjectId, $newProjectId);

        // 复制页面管理
        TemplateLogic::copyProjectDataByProjectId($oldProjectId, $newProjectId);
        $templateData = TemplateLogic::selectAttributionIdByProjectId($newProjectId);

        // 复制工作流程
        FlowProcessLogic::copyProjectDataByProjectId($oldProjectId, $newProjectId);
        $flowProcessIds = FlowProcessLogic::selectFlowProcessIdByProjectId($newProjectId);
        $flowProcessData = FlowProcessLogic::selectAttributionIdByProjectId($newProjectId);
        $flowProcessNodeData = FlowProcessLogic::selectAttributionIdByFlowProcessIds($flowProcessIds);

        // 复制状态库
        $statusEnumData = FlowStatusEnumLogic::selectAttributionIdByProjectId();

        // 复制工作流
        FlowStatusLogic::copyProjectDataByProjectId($oldProjectId, $newProjectId, $flowProcessData, $statusEnumData);
        $oldFlowStatusIds = FlowStatusLogic::selectFlowStatusIdByProjectId($oldProjectId);
        $flowStatusIds = FlowStatusLogic::selectFlowStatusIdByProjectId($newProjectId);
        $flowStatusData = FlowStatusLogic::selectAttributionIdByProjectId($newProjectId);
        $flowStatusTextData = FlowStatusTextLogic::selectAttributionIdByFlowStatusIds($flowStatusIds);

        // 复制状态与节点关系
        FlowStatusNodeRelationLogic::copyProjectDataByOldFlowStatusId($oldFlowStatusIds, $flowStatusData, $flowStatusTextData, $flowProcessNodeData);

        // 复制状态流转
        FlowStatusTransferLogic::copyProjectDataByProjectId($oldFlowStatusIds, $flowStatusData, $flowStatusTextData, $statusEnumData);

        // 复制类别
        ProjectCategorySettingsLogic::copyProjectDataByProjectId($oldProjectId, $newProjectId, $templateData, $flowProcessData, $flowStatusData);

        // 复制工作流程节点关联关系
        FlowProcessNodeRelationLogic::copyProjectDataByOldNodeId($flowProcessNodeData);
    }

    /**
     * 根据id集删除
     * @param  array  $projectTemplateIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/9
     */
    public function delete(array $projectTemplateIds): void
    {
        $models = ProjectTemplateModel::status()
            ->whereIn('project_template_id', $projectTemplateIds)
            ->with([
                'projectMany' => function ($sql) {
                    $sql->field('project_id, project_template_id')->where(['is_delete' => BaseModel::DELETE_NOT, 'is_template' => ProjectTemplateModel::IS_TEMPLATE_NO]);
                }
            ])
            ->select();

        try {
            Db::startTrans();

            foreach ($models as $model) {
                if ( ! $model->is_allow_delete) {
                    throw new BusinessException('当前模版不允许删除');
                }
                if ($model->projectMany) {
                    foreach ($model->projectMany as $project) {
                        // 复制模板数据 到项目中
                        self::copyProjectTemplate((int)$model->project_id, (int)$project->project_id);
                    }
                }
                $model->save(['is_delete' => BaseModel::DELETE_YES]);
            }

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 根据状态库id更新数据
     * @param         $projectTemplateId
     * @param  array  $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/9
     */
    public function update($projectTemplateId, array $params): void
    {
        // 验证器校验
        validate(ProjectTemplateValidate::class)->scene('update')->check($params);

        $model = ProjectTemplateModel::findById($projectTemplateId);
        if ( ! $model) {
            throw new NotFoundException();
        }
        if ($model->project_template_name != $params['project_template_name'] && ! $model->is_allow_delete) {
            throw new BusinessException('当前模版不允许编辑');
        }

        $templateNameExist = ProjectTemplateModel::findEnumAndName((string)$params['project_template_name']);
        if ($templateNameExist && $templateNameExist->project_template_id != $projectTemplateId) {
            throw new ParamsException('名称已存在');
        }

        try {
            Db::startTrans();

            $model->save($params);

            if ($params['sort_data']) {
                $this->updateSort($params['sort_data']);
            }
            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 更新数据排序
     * @param  array  $sortData
     *    $sortData[] = ['project_template_id', 'sort']
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     *    User Long
     *    Date 2024/10/9
     */
    public function updateSort(array $sortData): void
    {
        if ( ! $sortData) {
            return;
        }

        foreach ($sortData as $param) {
            // 验证器校验
            validate(ProjectTemplateValidate::class)->scene('updateSort')->check($param);
        }

        $sortData = array_column($sortData, 'sort', 'project_template_id');
        $projectTemplateIds = array_keys($sortData);
        $models = ProjectTemplateModel::selectByIds($projectTemplateIds);

        foreach ($models as $model) {
            if (isset($sortData[$model->project_template_id])) {
                $model->save(['sort' => $sortData[$model->project_template_id]]);
            }
        }
    }

    /**
     * 详情
     * @param  int  $projectTemplateId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/9
     */
    public function detail(int $projectTemplateId): array
    {
        $model = ProjectTemplateModel::findById($projectTemplateId);

        if ( ! $model) {
            throw new NotFoundException();
        }

        $model = $model->toDetail()->toArray();
        $model['project_name'] = ProjectInfoLogic::getProjectName($model['project_id']);

        return $model;
    }

    /**
     * 列表数据
     * @return ProjectTemplateModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/9
     */
    public function listQuery()
    {
        $templateList = ProjectTemplateModel::status()
            ->order('sort ASC, create_at DESC')
            ->hidden(ProjectTemplateModel::HIDDEN_FIELD)
            ->select();

        $projectList = ProjectModel::where('project_template_id', 'in', $templateList->column('project_template_id'))
            ->where('is_template', '=', ProjectModel::IS_TEMPLATE_NOT)
            ->field('project_id,project_name,project_template_id')
            ->select();

        foreach ($templateList as $template) {
            $template['project_list'] = array_values($projectList->where('project_template_id', $template->project_template_id)->toArray());
        }

        return $templateList;
    }

    /**
     * 获取模板下拉数据
     * @return array
     * User Long
     * Date 2024/10/10
     */
    public function getTemplateSelector($where = null): array
    {
        $model = ProjectTemplateModel::status();

        if ($where) {
            $model->where($where);
        }

        return $model->order('sort ASC, create_at DESC')->column('project_template_name as label, project_template_id as value');
    }

    /**
     * 根据模板id 获取 对应的项目id
     * @param  int  $projectTemplateId
     * @param       $scenario
     * @return mixed
     */
    public static function getProjectIdByProjectTemplateId(int $projectTemplateId, $scenario = false)
    {
        $model = ProjectTemplateModel::where(['project_template_id' => $projectTemplateId]);

        if ( ! $scenario) {
            $model->where(['is_delete' => BaseModel::DELETE_NOT]);
        }

        return $model->value('project_id');
    }

    /**
     * 根据模板id集 获取 对应的项目id集
     * @param  array  $projectTemplateIds
     * @param         $scenario
     * @return array
     */
    public static function getProjectIdsByProjectTemplateIds(array $projectTemplateIds, $scenario = false)
    {
        $model = ProjectTemplateModel::whereIn('project_template_id', $projectTemplateIds);

        if ( ! $scenario) {
            $model->where(['is_delete' => BaseModel::DELETE_NOT]);
        }

        return $model->column('project_id');
    }


    /**
     * 以项目id为key获取项目名
     * @param  array  $projectTemplateIds
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/15
     */
    public static function getTemplateNameByProjetIds(array $projectTemplateIds)
    {
        $model = ProjectTemplateModel::selectByIds($projectTemplateIds);
        return $model->column('project_template_name', 'project_id');
    }
}
