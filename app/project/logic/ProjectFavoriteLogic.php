<?php
/**
 * Desc 项目收藏 - 逻辑层
 * User Long
 * Date 2024/10/11
 */

namespace app\project\logic;

use app\project\model\ProjectFavoriteModel;
use basic\BaseLogic;
use basic\BaseModel;
use think\facade\Db;
use utils\Ctx;

class ProjectFavoriteLogic extends BaseLogic
{

    /**
     * 收藏
     * @param array $projectIds
     * @throws \Throwable
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public static function like(array $projectIds): void
    {
        try {
            Db::startTrans();

            foreach ($projectIds as $projectId) {
                $data = ProjectFavoriteModel::findById(Ctx::$userId, $projectId);

                if (!$data) {
                    $model = new ProjectFavoriteModel();
                    $model->save(['project_id' => $projectId]);
                }
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 取消收藏
     * @param array $projectIds
     * @throws \Throwable
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public static function dislike(array $projectIds): void
    {
        $models = ProjectFavoriteModel::selectByIds(Ctx::$userId, $projectIds);

        try {
            Db::startTrans();
            foreach ($models as $model) {
                $model->save(['is_delete' => BaseModel::DELETE_YES]);
            }
            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }
}
