<?php
/**
 * Desc 项目分类管理 - 逻辑层
 * User Long
 * Date 2024/08/27
 */
declare (strict_types=1);

namespace app\project\logic;

use app\project\model\ProjectCategoryModel;
use app\project\validate\ProjectCategoryValidate;
use app\work_items\logic\TestCaseLogic;
use app\work_items\logic\WorkItemsLogic;
use basic\BaseLogic;
use basic\BaseModel;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\MissingParameterException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use exception\BusinessException;
use exception\NotFoundException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use Throwable;

class ProjectClassifyLogic extends BaseLogic
{
    private int $projectCategoryType; // 分类所属 1=>迭代 2=>需求 3=>任务 4=>缺陷 5=>测试用例 6=>测试计划
    private string $projectCategoryTypeText; // 分类所属 描述

    public function __construct(int $projectCategoryType = BaseModel::SETTING_TYPE_DEMAND)
    {
        $this->projectCategoryType = $projectCategoryType;
        $this->projectCategoryTypeText = BaseModel::SETTING_TYPE_TEXT[$projectCategoryType] ?? '';
    }

    /**
     * 插入一条记录
     * @param  array  $params
     * User Long
     * Date 2024/8/27
     */
    public function create(array $params): ProjectCategoryModel
    {
        // 验证器校验
        validate(ProjectCategoryValidate::class)->scene('create')->check($params);

        $model = new ProjectCategoryModel();
        $params['project_category_type'] = $this->projectCategoryType; // 设置分类类型
        $model->save($params);
        return $model;
    }

    /**
     * 根据分类id集删除分类
     * @param  array  $categoryIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/27
     */
    public function delete(array $categoryIds): void
    {
        $models = ProjectCategoryModel::selectByCategoryIds($this->projectCategoryType, $categoryIds);

        $notDeleteData = $models->where('is_allow_delete', '=', ProjectCategoryModel::NOT_ALLOW_DELETE);
        if ( ! $notDeleteData->isEmpty()) {
            throw new BusinessException('“'.implode('、', $notDeleteData->column('category_name')).'” 默认有，不可进行删除、编辑');
        }

        foreach ($models as $model) {
            $model->save(['is_delete' => BaseModel::DELETE_YES]);
        }

    }

    /**
     * 根据分类id更新数据
     * @param         $categoryId
     * @param  array  $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/8/27
     */
    public function update($categoryId, array $params): void
    {
        // 验证器校验
        validate(ProjectCategoryValidate::class)->scene('update')->check($params);

        $model = ProjectCategoryModel::findByCategoryId($this->projectCategoryType, $categoryId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        try {
            Db::startTrans();

            $model->save($params);

            if ($params['sort_data']) {
                $this->updateSort($params['sort_data']);
            }
            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 迭代分类详情
     * @param  int  $categoryId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/27
     */
    public function detail(int $categoryId): array
    {
        $model = ProjectCategoryModel::findByCategoryId($this->projectCategoryType, $categoryId);

        if ( ! $model) {
            throw new NotFoundException();
        }

        $model->toDetail();
        return $model->toArray();
    }

    /**
     * 迭代分类分页
     * @param  int  $projectId
     * @return array
     * @throws ClientResponseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws ServerResponseException
     * User Long
     * Date 2024/8/27
     */
    public function listQuery(int $projectId): array
    {
        $esAggs = [];
        // 获取es聚合数据数据
        $esData = match ($this->projectCategoryType) {
            BaseModel::SETTING_TYPE_TEST_CASE => TestCaseLogic::getAggsCategoryByProjectId($projectId),
            default => WorkItemsLogic::getAggsCategoryByProjectId($projectId, $this->projectCategoryType),
        };

        if ($esData['aggs']) {
            $esAggs = array_column($esData['aggs'], 'doc_count', 'key');
        }

        $res = [
            'project_category_id' => 0,
            'is_allow_delete'     => 0,
            'project_id'          => $projectId,
            'category_name'       => '所有'.$this->projectCategoryTypeText,
            'total'               => $esData['total'] ?? 0,
            'pid'                 => 0,
            'sort'                => 0,
            'next'                => [
                [
                    'project_category_id' => '-1',
                    'is_allow_delete'     => 0,
                    'project_id'          => $projectId,
                    'category_name'       => '未分类',
                    'total'               => $esAggs['-1'] ?? 0,
                    'pid'                 => 0,
                    'sort'                => 0,
                    'next'                => []
                ]
            ]
        ];

        $models = ProjectCategoryModel::status()
            ->where(['project_id' => $projectId, 'project_category_type' => $this->projectCategoryType])
            ->hidden(ProjectCategoryModel::HIDDEN_FIELD)
            ->order('sort ASC, create_at ASC')
            ->select();

        if ($models->isEmpty()) {
            return $res;
        }

        // 获取分类排序数据
        foreach ($models as $model) {
            if ($model->pid == 0) {
                $res['next'][] = [
                    'project_category_id' => $model->project_category_id,
                    'is_allow_delete'     => $model->is_allow_delete,
                    'project_id'          => $model->project_id,
                    'category_name'       => $model->category_name,
                    'total'               => $esAggs[$model->project_category_id] ?? 0,
                    'pid'                 => $model->pid,
                    'sort'                => $model->sort,
                    'next'                => $this->getOrderingData($model->project_category_id, $models, $esAggs)
                ];
            }
        }

        return $res;
    }

    /**
     * 获取分类排序数据
     * @param $id
     * @param $models
     * @param $esAggs
     * @return array
     * User Long
     * Date 2024/8/27
     */
    private function getOrderingData($id, $models, $esAggs): array
    {
        $res = [];
        $nextModel = $models->where('pid', '=', $id);

        if ($nextModel->isEmpty()) {
            return $res;
        }

        foreach ($nextModel as $model) {
            $res[] = [
                'project_category_id' => $model->project_category_id,
                'is_allow_delete'     => $model->is_allow_delete,
                'project_id'          => $model->project_id,
                'category_name'       => $model->category_name,
                'total'               => $esAggs[$model->project_category_id] ?? 0,
                'pid'                 => $model->pid,
                'sort'                => $model->sort,
                'next'                => $this->getOrderingData($model->project_category_id, $models, $esAggs)
            ];
        }

        return $res;
    }

    /**
     * 更新数据排序
     * @param  array  $sortData
     *   $sortData[] = ['category_id', 'sort']
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     *   User Long
     *   Date 2024/8/27
     */
    public function updateSort(array $sortData): void
    {
        if ( ! $sortData) {
            return;
        }

        foreach ($sortData as $param) {
            // 验证器校验
            validate(ProjectCategoryValidate::class)->scene('updateSort')->check($param);
        }

        $sortData = array_column($sortData, 'sort', 'category_id');
        $categoryIds = array_keys($sortData);
        $models = ProjectCategoryModel::selectByCategoryIds($this->projectCategoryType, $categoryIds);

        foreach ($models as $model) {
            if (isset($sortData[$model->project_category_id])) {
                $model->save(['sort' => $sortData[$model->project_category_id]]);
            }
        }
    }

    /**
     * 获取相关分类id集合
     * @param  int  $categoryId  分类id
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     *                           User Long
     *                           Date 2024/10/8
     */
    public function getRelevanceDemand(int $categoryId): array
    {
        $res = [];

        switch ($this->projectCategoryType) {
        case BaseModel::SETTING_TYPE_DEMAND:
            $res = WorkItemsLogic::getItemByCategoryId($categoryId, $this->projectCategoryType);
            break;
        case BaseModel::SETTING_TYPE_TEST_CASE:
            $res = TestCaseLogic::getItemByCategoryId($categoryId);
            break;
        default:
            break;
        }

        return $res;
    }

    /**
     * 修改相关分类
     * @param  array  $workItemIds       工作项id合集
     * @param  int    $targetCategoryId  流转至的分类id
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws Throwable
     *                                   User Long
     *                                   Date 2024/8/30
     */
    public function updateRelevanceDemand(array $workItemIds, int $targetCategoryId): void
    {
        switch ($this->projectCategoryType) {
        case BaseModel::SETTING_TYPE_DEMAND:
            (new WorkItemsLogic)->setItemsCategoryId($workItemIds, $targetCategoryId);
            break;
        case BaseModel::SETTING_TYPE_TEST_CASE:
            TestCaseLogic::getInstance()->setItemsCategoryId($workItemIds, $targetCategoryId);
            break;
        default:
            break;
        }
    }

    /**
     * 分类下拉数据
     * @param  int|array  $projectId
     * @return ProjectCategoryModel[]|array|\think\Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/9/9 15:16
     */
    public function categorySelector(int|array $projectId)
    {
        $model = ProjectCategoryModel::status()
            ->where(['project_category_type' => $this->projectCategoryType]);

        // 兼容多项目查询
        if (is_array($projectId)) {
            $model = $model->whereIn('project_id', $projectId);
        } else {
            $model = $model->where('project_id', $projectId);
        }

        return $model->order('create_at DESC')
            ->field(['project_category_id value', 'category_name as label'])
            ->select()->unshift([
                'value' => '-1',
                'label' => '未分类'
            ]);
    }

    /**
     * 分类下拉数据
     * @param  int  $projectId
     * @param  int  $type
     * @return ProjectCategoryModel[]|array|\think\Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/9/9 15:16
     */
    public static function list(int $projectId, int $type)
    {
        return ProjectCategoryModel::status()
            ->where(['project_id' => $projectId, 'project_category_type' => $type])
            ->order('sort asc')
            ->field(['project_category_id', 'category_name', 'pid'])
            ->select();
    }


    /**
     * 获取指定类型，项目的所有分类
     * @param $projectId
     * @param $projectCategoryType
     * @return ProjectCategoryModel[]|array|\think\Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/10/11 15:03
     */
    public static function getListByType($projectId, $projectCategoryType)
    {
        return ProjectCategoryModel::where([
            ['project_category_type', '=', $projectCategoryType],
            ['project_id', '=', $projectId],
        ])->select();
    }


    /**
     * 获取工作项分类/目录，平铺树形结构，键值对数据，key是id，value是以'-'分割的包含路径的name
     * @param $projectCategoryType
     * @param $projectId
     * @param $rollback bool 是否反转键值对
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/12/12 下午3:55
     */
    public static function getCategoryListParentList($projectCategoryType, $projectId, bool $rollback = false)
    {
        $categoryTree = (new ProjectClassifyLogic($projectCategoryType))->listQuery((int)$projectId);

        $result = self::flattenTreeWithPath($categoryTree['next'], 'next', 'project_category_id', 'project_category_id', 'category_name', '-');
        if ($rollback) {
            $result = array_flip($result);
        }
        return $result;
    }


    /**
     * 接收getCategoryListParentList返回的数据格式创建层级分类
     * @param $projectCategoryType
     * @param $projectId
     * @param $data
     * @return void
     * <AUTHOR>
     * @date   2024/12/17 上午9:22
     */
    public static function createCategoryListParentList($projectCategoryType, $projectId, $data, $delimiter)
    {
        $tree = self::buildCategoryTree($data, $delimiter);

        self::createCategoryByTree($tree, 0, $projectCategoryType, $projectId, new ProjectClassifyLogic($projectCategoryType));
    }


    /**
     * 递归创建分类，通过分类名和层级标识数据，存则则不创建
     * @param  array  $tree
     * @param         $pid
     * @param         $projectCategoryType
     * @param         $projectId
     * @return void
     * <AUTHOR>
     * @date   2024/12/17 上午9:23
     */
    private static function createCategoryByTree(array $tree, $pid, $projectCategoryType, $projectId, ProjectClassifyLogic $project_classify_logic)
    {
        foreach ($tree as $name => $child) {
            $model = ProjectCategoryModel::findByCategoryNameAndPid((int)$projectCategoryType, (int)$projectId, $name, $pid);
            if ( ! $model) {
                $model = $project_classify_logic->create([
                    'project_id'    => $projectId,
                    'category_name' => $name,
                    'pid'           => $pid,
                    'sort'          => '1',
                ]);
            }

            // 如果有子节点，递归创建
            if ($child) {
                self::createCategoryByTree(
                    $child,
                    $model->project_category_id,
                    $projectCategoryType,
                    $projectId,
                    $project_classify_logic
                );
            }
        }
    }


    /**
     * 将按"-"分割的被平铺的树形结构数据重新解析为树
     * @param  array   $flatData
     * @param  string  $delimiter
     * @return array
     * <AUTHOR>
     * @date   2024/12/17 上午9:23
     */
    private static function buildCategoryTree(array $flatData, string $delimiter = '-'): array
    {
        $tree = [];
        $references = [];

        foreach ($flatData as $path) {
            // 分割路径
            $parts = explode($delimiter, $path);
            $currentLevel = &$tree; // 引用到当前层

            foreach ($parts as $part) {
                if ( ! isset($currentLevel[$part])) {
                    // 新节点
                    $currentLevel[$part] = [];
                }
                $currentLevel = &$currentLevel[$part]; // 引用进入下一层
            }
        }

        return $tree;
    }


    /**
     * 将树结构按指定字段平铺，并以路径作为结果的键
     *
     * @param  array   $tree           树形结构数据
     * @param  string  $childrenField  子节点字段名称
     * @param  string  $idField        节点的唯一标识字段
     * @param  string  $pathField      用于生成路径的字段名称
     * @param  string  $valueField     用于生成值路径的字段名称
     * @param  string  $separator      路径分隔符
     * @param  string  $currentPath    当前路径，递归时使用
     * @return array 平铺后的数组，键为路径
     */
    private static function flattenTreeWithPath(
        array $tree,
        string $childrenField = 'children',
        string $idField = 'id',
        string $pathField = 'name',
        string $valueField = 'label',
        string $separator = '/',
        string $currentPath = '',
        string $currentValuePath = '',
    ): array {
        $flattened = [];

        foreach ($tree as $node) {
            // 构造当前节点路径

            $nodePath = ltrim($currentPath.$separator.$node[$pathField], $separator);
            $valuePath = ltrim($currentValuePath.$separator.$node[$valueField], $separator);
//            var_dump($currentPath);
//            $value= explode($separator, $valuePath);
            // 提取当前节点信息，移除子节点
            $currentNode = $node;
            $currentNode['path'] = $nodePath; // 保存路径
            unset($currentNode[$childrenField]);

            // 使用路径作为键
            $flattened[$node[$pathField]] = $valuePath;

            // 如果有子节点，递归平铺
            if ( ! empty($node[$childrenField])) {
                $flattened += self::flattenTreeWithPath(
                    $node[$childrenField],
                    $childrenField,
                    $idField,
                    $pathField,
                    $valueField,
                    $separator,
                    $nodePath,
                    $valuePath,
                );
            }
        }

        return $flattened;
    }


}
