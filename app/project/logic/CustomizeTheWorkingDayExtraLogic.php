<?php
/**
 * Desc 自定义工作日额外调整 - 逻辑层
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);

namespace app\project\logic;

use app\project\model\CustomizeTheWorkingDayExtraModel;
use app\project\model\CustomizeTheWorkingDayModel;
use app\project\validate\CustomizeTheWorkingDayExtraValidate;
use basic\BaseLogic;
use basic\BaseModel;
use exception\NotFoundException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\exception\ValidateException;
use think\Paginator;
use utils\DBTransaction;
use app\project\model\ProjectModel;

class CustomizeTheWorkingDayExtraLogic extends BaseLogic
{
    /**
     * 获取自定义工作日额外调整详情
     * @param  int  $id
     * @return array
     * @throws NotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDetail(int $id): array
    {
        $model = CustomizeTheWorkingDayExtraModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('自定义工作日额外调整不存在');
        }

        return $model->toDetail();
    }

    /**
     * 根据项目ID和年份获取自定义工作日额外调整列表
     * @param  int  $projectId
     * @param  int  $year
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getListByProjectId(int $projectId, int $year): array
    {
        $query = CustomizeTheWorkingDayExtraModel::status()->where(['project_id' => $projectId]);

        // 添加年份筛选
        if ($year > 0) {
            $startDate = $year . '-01-01';
            $endDate = $year . '-12-31';
            $query->where('date', '>=', $startDate)
                ->where('date', '<=', $endDate);
        }

        $list = $query->select();
        if ($list->isEmpty()) {
            return [];
        }

        return $list->toArray();
    }

    /**
     * 分页查询自定义工作日额外调整列表
     * @param  array  $params
     * @return Paginator
     * @throws DbException
     */
    public function pageQuery(array $params): Paginator
    {
        $projectId = isset($params['project_id']) ? intval($params['project_id']) : 0;
        $startDate = isset($params['start_date']) ? trim($params['start_date']) : '';
        $endDate = isset($params['end_date']) ? trim($params['end_date']) : '';
        $type = isset($params['type']) ? intval($params['type']) : 0;

        $query = CustomizeTheWorkingDayExtraModel::status();

        // 按项目ID筛选
        if ($projectId > 0) {
            $query->where('project_id', $projectId);
        }

        // 按日期范围筛选
        if ($startDate) {
            $query->where('date', '>=', $startDate);
        }
        if ($endDate) {
            $query->where('date', '<=', $endDate);
        }

        // 按类型筛选
        if ($type > 0) {
            $query->where('type', $type);
        }

        // 查询列表
        return $query->field(CustomizeTheWorkingDayExtraModel::LIST_FIELDS)
            ->order('date', 'desc')
            ->paginate(getPageSize());

    }

    /**
     * 创建或更新自定义工作日额外调整
     * @param array $data
     * @return CustomizeTheWorkingDayExtraModel|bool
     * @throws ValidateException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function createOrUpdate(array $data)
    {
        // 验证数据
        $this->validateData($data);

        DBTransaction::begin();
        try {
            // 获取同步参数并移除
            $syncToAllProjects = $this->getAndRemoveSyncParam($data);

            // 保存记录
            $model = $this->saveRecord($data);

            // 同步到其他项目
            if ($syncToAllProjects === 1 && $data['project_id'] === -1) {
                $this->syncToOtherProjects($data);
            }

            DBTransaction::commit();
            return $model;
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            //dd((string)$e);
            throw  $e;
        }

    }

    /**
     * 验证数据
     * @param array $data
     * @throws ValidateException
     */
    private function validateData(array $data): void
    {
        validate(CustomizeTheWorkingDayExtraValidate::class)->check($data);
    }

    /**
     * 获取并移除同步参数
     * @param array $data
     * @return int
     */
    private function getAndRemoveSyncParam(array &$data): int
    {
        $syncToAllProjects = isset($data['sync_to_all_projects']) ? intval($data['sync_to_all_projects']) : 0;
        
        // 保存同步参数到is_sync_to_all_projects字段
        if ($data['project_id'] === -1) {
            $data['is_sync_to_all_projects'] = $syncToAllProjects;
        } else {
            $data['is_sync_to_all_projects'] = CustomizeTheWorkingDayExtraModel::SYNC_TO_ALL_PROJECTS_NO;
        }
        
        if (isset($data['sync_to_all_projects'])) {
            unset($data['sync_to_all_projects']);
        }
        return $syncToAllProjects;
    }

    /**
     * 保存记录
     * @param array $data
     * @return CustomizeTheWorkingDayExtraModel
     * @throws NotFoundException
     * @throws ValidateException
     */
    private function saveRecord(array $data): CustomizeTheWorkingDayExtraModel
    {
        $isUpdate = isset($data['customize_the_working_day_extra_id']);

        if ($isUpdate) {
            return $this->updateRecord($data);
        }

        return $this->createRecord($data);
    }

    /**
     * 更新记录
     * @param array $data
     * @return CustomizeTheWorkingDayExtraModel
     * @throws NotFoundException
     * @throws ValidateException
     */
    private function updateRecord(array $data): CustomizeTheWorkingDayExtraModel
    {
        $model = CustomizeTheWorkingDayExtraModel::findById($data['customize_the_working_day_extra_id']);
        if (!$model) {
            throw new NotFoundException('自定义工作日额外调整不存在');
        }

        // 检查日期冲突
        $this->checkDateConflict($data, $model);

        // 更新记录
        $model->save($data);
        return $model;
    }

    /**
     * 创建记录
     * @param array $data
     * @return CustomizeTheWorkingDayExtraModel
     * @throws ValidateException
     */
    private function createRecord(array $data): CustomizeTheWorkingDayExtraModel
    {
        // 检查日期冲突
        $exist = CustomizeTheWorkingDayExtraModel::findByProjectIdAndDate((int)$data['project_id'], $data['date']);
        if ($exist) {
            throw new ValidateException('该日期已存在调整记录，请勿重复添加');
        }

        // 创建记录
        $model = new CustomizeTheWorkingDayExtraModel();
        $model->save($data);
        return $model;
    }

    /**
     * 检查日期冲突
     * @param array $data
     * @param CustomizeTheWorkingDayExtraModel $model
     * @throws ValidateException
     */
    private function checkDateConflict(array $data, CustomizeTheWorkingDayExtraModel $model): void
    {
        if (isset($data['date']) && $data['date'] != $model['date']) {
            $exist = CustomizeTheWorkingDayExtraModel::findByProjectIdAndDate($data['project_id'], $data['date']);
            if ($exist && $exist['customize_the_working_day_extra_id'] != $data['customize_the_working_day_extra_id']) {
                throw new ValidateException('该日期已存在调整记录，请勿重复添加');
            }
        }
    }

    /**
     * 同步到其他项目
     * @param array $data
     * @throws \Exception
     */
    private function syncToOtherProjects(array $data): void
    {
        // 获取所有项目ID，而不仅仅是已有自定义工作日配置的项目
        $targetProjects = ProjectModel::getAllProjectIds();

        if (empty($targetProjects)) {
            return;
        }

        // 开启事务
        CustomizeTheWorkingDayExtraModel::startTrans();
        try {
            foreach ($targetProjects as $targetProjectId) {
                // 跳过原项目ID(如果存在)
                if ($targetProjectId == $data['project_id']) {
                    continue;
                }
                
                $this->syncToProject($targetProjectId, $data);
            }
            CustomizeTheWorkingDayExtraModel::commit();
        } catch (\Exception $e) {
            CustomizeTheWorkingDayExtraModel::rollback();
            throw $e;
        }
    }

    /**
     * 同步到指定项目
     * @param int $targetProjectId
     * @param array $data
     */
    private function syncToProject(int $targetProjectId, array $data): void
    {
        $targetExist = CustomizeTheWorkingDayExtraModel::findByProjectIdAndDate($targetProjectId, $data['date']);

        if ($targetExist) {
            $targetExist->save(['type' => $data['type']]);
        } else {
            $newAdjustment = new CustomizeTheWorkingDayExtraModel();
            $newAdjustment->save([
                'project_id' => $targetProjectId,
                'date' => $data['date'],
                'type' => $data['type'],
            ]);
        }
    }

    /**
     * 删除自定义工作日额外调整
     * @param  int  $id
     * @return bool
     * @throws NotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function delete(int $id): bool
    {
        // 查找记录
        $model = CustomizeTheWorkingDayExtraModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('自定义工作日额外调整不存在');
        }

        // 开启事务
        DBTransaction::begin();
        try {
            // 记录当前日期，用于同步删除
            $date = $model['date'];
            
            // 如果是全局项目(-1)且勾选了同步选项，则同步删除所有项目相同日期的数据
            if ($model['project_id'] === -1 && $model['is_sync_to_all_projects'] === CustomizeTheWorkingDayExtraModel::SYNC_TO_ALL_PROJECTS_YES) {
                // 获取所有项目ID
                $projectIds = ProjectModel::getAllProjectIds();
                
                // 同步删除其他项目相同日期的数据
                if (!empty($projectIds)) {
                    foreach ($projectIds as $projectId) {
                        // 跳过当前项目
                        if ($projectId == $model['project_id']) {
                            continue;
                        }
                        
                        // 查找并删除其他项目相同日期的数据
                        $targetModel = CustomizeTheWorkingDayExtraModel::findByProjectIdAndDate($projectId, $date);
                        if ($targetModel) {
                            $targetModel->save(['is_delete' => CustomizeTheWorkingDayExtraModel::DELETE_YES]);
                        }
                    }
                }
            }
            
            // 删除当前记录
            $result = $model->save(['is_delete' => CustomizeTheWorkingDayExtraModel::DELETE_YES]);
            
            DBTransaction::commit();
            return $result;
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 批量删除自定义工作日额外调整
     * @param  array  $ids
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function batchDelete(array $ids): bool
    {
        if (empty($ids)) {
            return false;
        }

        // 开启事务
        DBTransaction::begin();
        try {
            // 查找所有待删除的记录
            $models = CustomizeTheWorkingDayExtraModel::whereIn('customize_the_working_day_extra_id', $ids)->select();
            if ($models->isEmpty()) {
                return false;
            }
            
            // 获取所有项目ID，用于同步删除
            $allProjectIds = null;
            
            foreach ($models as $model) {
                // 如果是全局项目(-1)且勾选了同步选项，则同步删除所有项目相同日期的数据
                if ($model['project_id'] === -1 && $model['is_sync_to_all_projects'] === CustomizeTheWorkingDayExtraModel::SYNC_TO_ALL_PROJECTS_YES) {
                    // 延迟加载项目列表
                    if ($allProjectIds === null) {
                        $allProjectIds = ProjectModel::getAllProjectIds();
                    }
                    
                    // 同步删除其他项目相同日期的数据
                    if (!empty($allProjectIds)) {
                        foreach ($allProjectIds as $projectId) {
                            // 跳过当前项目
                            if ($projectId == $model['project_id']) {
                                continue;
                            }
                            
                            // 查找并删除其他项目相同日期的数据
                            $targetModel = CustomizeTheWorkingDayExtraModel::findByProjectIdAndDate($projectId, $model['date']);
                            if ($targetModel) {
                                $targetModel->save(['is_delete' => CustomizeTheWorkingDayExtraModel::DELETE_YES]);
                            }
                        }
                    }
                }
            }
            
            // 批量删除记录
            $result = (bool)CustomizeTheWorkingDayExtraModel::update(['is_delete' => CustomizeTheWorkingDayExtraModel::DELETE_YES], ['customize_the_working_day_extra_id' => $ids]);
            
            DBTransaction::commit();
            return $result;
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 同步自定义工作日额外调整到所有项目
     * @param int $sourceProjectId 源项目ID
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function syncExtraAdjustments(int $sourceProjectId): void
    {
        // 获取源项目的所有额外调整
        $sourceAdjustments = CustomizeTheWorkingDayExtraModel::findByProjectId($sourceProjectId);

        // 获取所有项目ID，而不仅仅是已有自定义工作日配置的项目
        $targetProjects = ProjectModel::getAllProjectIds();

        if (empty($targetProjects)) {
            return;
        }

        // 如果源项目没有额外调整，则清空所有目标项目的额外调整
        if ($sourceAdjustments->isEmpty()) {
            foreach ($targetProjects as $targetProjectId) {
                // 跳过源项目
                if ($targetProjectId == $sourceProjectId) {
                    continue;
                }
                
                // 获取目标项目的所有额外调整
                $targetAdjustments = CustomizeTheWorkingDayExtraModel::findByProjectId($targetProjectId);

                // 如果目标项目有额外调整，则清空它们（标记为删除）
                if (!$targetAdjustments->isEmpty()) {
                    foreach ($targetAdjustments as $adjustment) {
                        $adjustment->save(['is_delete' => 1]); // 1表示已删除
                    }
                }
            }
            return;
        }

        // 遍历所有目标项目
        foreach ($targetProjects as $targetProjectId) {
            // 跳过源项目
            if ($targetProjectId == $sourceProjectId) {
                continue;
            }
            
            // 获取目标项目的所有额外调整
            $targetAdjustments = CustomizeTheWorkingDayExtraModel::findByProjectId($targetProjectId);
            $targetDates = [];
            $sourceDates = [];

            // 构建目标项目日期映射
            foreach ($targetAdjustments as $adjustment) {
                $targetDates[$adjustment['date']] = $adjustment;
            }

            // 构建源项目日期映射
            foreach ($sourceAdjustments as $adjustment) {
                $sourceDates[$adjustment['date']] = $adjustment;
            }

            // 遍历源项目的所有额外调整
            foreach ($sourceAdjustments as $sourceAdjustment) {
                $date = $sourceAdjustment['date'];
                $type = $sourceAdjustment['type'];

                // 如果目标项目已有该日期的调整，则更新
                if (isset($targetDates[$date])) {
                    $targetAdjustment = $targetDates[$date];
                    $targetAdjustment->save(['type' => $type]);
                } else {
                    // 否则创建新的调整
                    $newAdjustment = new CustomizeTheWorkingDayExtraModel();
                    $newAdjustment->save([
                        'project_id' => $targetProjectId,
                        'date' => $date,
                        'type' => $type,
                    ]);
                }
            }

            // 删除目标项目中存在但源项目中不存在的额外调整
            foreach ($targetDates as $date => $targetAdjustment) {
                if (!isset($sourceDates[$date])) {
                    $targetAdjustment->save(['is_delete' => 1]); // 1表示已删除
                }
            }
        }
    }
} 