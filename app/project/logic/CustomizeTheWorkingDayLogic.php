<?php
/**
 * Desc 自定义工作日 - 逻辑层
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);

namespace app\project\logic;

use app\project\model\CustomizeTheWorkingDayExtraModel;
use app\project\model\CustomizeTheWorkingDayModel;
use app\project\model\ProjectModel;
use app\project\validate\CustomizeTheWorkingDayValidate;
use basic\BaseLogic;
use exception\NotFoundException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\exception\ValidateException;
use think\Paginator;
use utils\DBTransaction;
use utils\Holiday;

class CustomizeTheWorkingDayLogic extends BaseLogic
{
    /**
     * 获取自定义工作日详情
     * @param  int  $id
     * @return array
     * @throws NotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDetail(int $id): array
    {
        $model = CustomizeTheWorkingDayModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('自定义工作日不存在');
        }

        return $model->toDetail();
    }

    /**
     * 根据项目ID获取自定义工作日
     * @param  int  $projectId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getByProjectId(int $projectId): array
    {
        $model = CustomizeTheWorkingDayModel::findByProjectId($projectId);
        if ( ! $model) {
            // 返回默认配置
            return [
                'project_id'           => $projectId,
                'weekday'              => 31, // 默认周一到周五为工作日 (1+2+4+8+16=31)
                'work_hours'           => 7, // 默认7小时工作制
                'skip_public_holidays' => 1, // 默认跳过法定节假日
            ];
        }

        return $model->toDetail();
    }

    /**
     * 创建或更新自定义工作日
     * @param  array  $data
     * @return CustomizeTheWorkingDayModel
     * @throws ValidateException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function createOrUpdate(array $data): CustomizeTheWorkingDayModel
    {
        // 验证数据
        validate(CustomizeTheWorkingDayValidate::class)->check($data);

        // 获取是否同步到所有项目参数，默认为0
        $syncToAllProjects = isset($data['sync_to_all_projects']) ? intval($data['sync_to_all_projects']) : 0;

        // 移除同步参数，避免保存到数据库
        if (isset($data['sync_to_all_projects'])) {
            unset($data['sync_to_all_projects']);
        }

        // 查找是否已存在该项目的配置
        $model = CustomizeTheWorkingDayModel::findByProjectId((int)$data['project_id']);

        if (!$model) {
            // 创建新记录
            $model = new CustomizeTheWorkingDayModel();
            $model->save($data);
        } else {
            // 更新记录
            $model->save($data);
        }

        // 如果需要同步到所有项目
        if ($syncToAllProjects === 1) {
            // 开启事务
            CustomizeTheWorkingDayModel::startTrans();
            try {
                // 获取所有项目ID
                $allProjectIds = ProjectModel::getAllProjectIds();
                
                // 准备同步数据（移除项目ID）
                $syncData = $data;
                unset($syncData['project_id']);
                
                // 遍历所有项目ID
                foreach ($allProjectIds as $projectId) {
                    // 跳过当前项目
                    if ($projectId == $data['project_id']) {
                        continue;
                    }
                    
                    // 查询项目是否已有配置
                    $projectModel = CustomizeTheWorkingDayModel::findByProjectId($projectId);
                    
                    if ($projectModel) {
                        // 已有配置，更新
                        $projectModel->save($syncData);
                    } else {
                        // 无配置，创建新配置
                        $newModel = new CustomizeTheWorkingDayModel();
                        $newData = array_merge($syncData, ['project_id' => $projectId]);
                        $newModel->save($newData);
                    }
                }

                // 同步自定义工作日额外调整
                $extraLogic = new CustomizeTheWorkingDayExtraLogic();
                $extraLogic->syncExtraAdjustments($data['project_id']);

                // 提交事务
                CustomizeTheWorkingDayModel::commit();
            } catch (\Exception $e) {
                // 回滚事务
                CustomizeTheWorkingDayModel::rollback();
                throw $e;
            }
        }

        return $model;
    }

    /**
     * 删除自定义工作日
     * @param  int  $id
     * @return bool
     * @throws NotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function delete(int $id): bool
    {
        // 查找记录
        $model = CustomizeTheWorkingDayModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('自定义工作日不存在');
        }

        // 删除记录
        return $model->save(['is_delete' => CustomizeTheWorkingDayModel::DELETE_YES]);
    }


    /**
     * 判断指定日期是否为工作日
     * @param  int          $projectId  项目ID
     * @param  string|null  $date       日期，格式为Y-m-d，如果为null则使用当前日期
     * @return bool 是否为工作日
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function isWorkingDay(int $projectId, ?string $date = null): bool
    {
        // 如果未指定日期，则使用当前日期
        if ($date === null) {
            $date = date('Y-m-d');
        }

        // 先查找是否有额外调整的配置
        $extraModel = CustomizeTheWorkingDayExtraModel::findByProjectIdAndDate($projectId, $date);

        // 如果有额外调整,直接返回调整后的结果
        if ($extraModel) {
            return $extraModel['type'] == CustomizeTheWorkingDayExtraModel::TYPE_WORK_DAY;
        }

        // 获取项目的自定义工作日配置
        $config = $this->getByProjectId($projectId);

        // 判断是否需要跳过法定节假日
        if ($config['skip_public_holidays'] == 1) {
            // 使用Holiday工具类判断是否为工作日
            if ( ! Holiday::isWorkday($date)) {
                return false;
            }
        }

        // 获取当天是星期几（1-7，对应周一到周日）
        $weekDay = date('N', strtotime($date));

        // 将weekDay转换为位值（1,2,4,8,16,32,64）
        $weekDayBit = pow(2, $weekDay - 1);

        // 判断当天是否在配置的工作日中
        return ($config['weekday'] & $weekDayBit) > 0;
    }

} 