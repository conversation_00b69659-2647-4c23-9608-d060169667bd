<?php
/**
 * Desc 项目角色权限处理
 * User Long
 * Date 2024/12/5
 */

namespace app\project\logic;

use app\infrastructure\model\EnumModel;
use app\project\model\ProjectUserRoleModel;
use basic\BaseModel;

class ProjectUserRoleLogic extends BaseModel
{
    /**
     * 获取成员角色
     * @param array $userIds
     * @param array $roleCodes
     * @return ProjectUserRoleModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/12/31
     */
    public static function selectUserRole(array $userIds, array $roleCodes = [])
    {
        $model = new ProjectUserRoleModel();

        $model = $model->whereIn('user_id', $userIds);

        if ($roleCodes) {
            $model = $model->whereIn('project_role', $roleCodes);
        }

        return $model->select();
    }

    /**
     * 创建项目角色权限
     * @param int $userId
     * @param array $roleData
     * User Long
     * Date 2024/12/5
     */
    public static function createRoleData(int $userId, array $roleData)
    {
        $systemRole = valueKeyConvert(getSystemEnumLibrary(EnumModel::SYSTEM_ROLE));

        foreach ($roleData as $roleDatum) {
            $model = new ProjectUserRoleModel();
            $model->user_id = $userId;
            $model->project_role = $roleDatum;
            if ($roleDatum == 'Admin') {
                $model->project_name = '管理员';
            } else {
                $model->project_name = $systemRole[$roleDatum] ?? '';
            }
            $model->save();
        }
    }

    /**
     * 删除角色与项目人员关系（真删）
     * @param int $userId
     * User Long
     * Date 2024/12/5
     */
    public static function destroyRoleData(int $userId): void
    {
        $model = new ProjectUserRoleModel();
        $model->destroy(['user_id' => $userId], true);
    }
}
