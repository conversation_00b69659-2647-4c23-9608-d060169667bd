<?php
/**
 * 日清bug统计规则设置逻辑层
 */

declare (strict_types=1);

namespace app\project\logic;

use app\project\model\NissinBugStatisticsRulesModel;
use app\project\model\ProjectModel;
use app\project\validate\NissinBugStatisticsRulesValidate;
use basic\BaseLogic;
use exception\NotFoundException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\exception\ValidateException;
use think\Paginator;
use utils\DBTransaction;

class NissinBugStatisticsRulesLogic extends BaseLogic
{
    /**
     * 默认日清bug统计规则
     * @var array
     */
    protected const DEFAULT_RULE
        = [
            'time'                => '00:00:00',
            'statistics'          => NissinBugStatisticsRulesModel::STATISTICS_TODAY,
            'enable'              => NissinBugStatisticsRulesModel::ENABLE_NO,
            'use_custom_workdays' => 0
        ];

    /**
     * 获取默认规则（带项目ID）
     * @param  int  $projectId
     * @return array
     */
    protected function getDefaultRule(int $projectId): array
    {
        return array_merge(['project_id' => $projectId], self::DEFAULT_RULE);
    }

    /**
     * 获取日清bug统计规则详情
     * @param  int  $id
     * @return array
     * @throws NotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDetail(int $id): array
    {
        $model = NissinBugStatisticsRulesModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('日清bug统计规则不存在');
        }

        return $model->toDetail();
    }

    /**
     * 根据项目ID获取日清bug统计规则
     * @param  int  $projectId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getByProjectId(int $projectId): array
    {
        $model = NissinBugStatisticsRulesModel::findByProjectId($projectId);
        if ( ! $model) {
            return $this->getDefaultRule($projectId);
        }

        return $model->toDetail();
    }

    /**
     * 分页查询日清bug统计规则
     * @param  array  $params
     * @return Paginator
     * @throws DbException
     */
    public function pageQuery(array $params): Paginator
    {

        $query = NissinBugStatisticsRulesModel::where('is_delete', NissinBugStatisticsRulesModel::DELETE_NOT);

        // 项目ID筛选
        if (isset($params['project_id']) && $params['project_id'] > 0) {
            $query->where('project_id', intval($params['project_id']));
        }

        // 是否启用筛选
        if (isset($params['enable']) && in_array($params['enable'], [0, 1])) {
            $query->where('enable', intval($params['enable']));
        }

        // 统计日期筛选
        if (isset($params['statistics']) && in_array($params['statistics'], [1, 2])) {
            $query->where('statistics', intval($params['statistics']));
        }

        return $query->order('nissin_bug_statistics_rules_id', 'desc')
            ->paginate(getPageSize());
    }

    /**
     * 创建日清bug统计规则
     * @param  array  $data
     * @return NissinBugStatisticsRulesModel
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws ValidateException
     */
    public function create(array $data): NissinBugStatisticsRulesModel
    {
        // 验证数据
        validate(NissinBugStatisticsRulesValidate::class)
            ->scene('create')
            ->check($data);

        // 检查项目是否已存在规则
        $existRule = NissinBugStatisticsRulesModel::findByProjectId($data['project_id']);
        if ($existRule) {
            throw new ValidateException('该项目已存在日清bug统计规则');
        }


        // 创建规则
        $model = new NissinBugStatisticsRulesModel();
        $model->save([
            'project_id' => $data['project_id'],
            'time'       => $data['time'],
            'statistics' => $data['statistics'],
            'enable'     => $data['enable'],
            'is_delete'  => NissinBugStatisticsRulesModel::DELETE_NOT
        ]);

        return $model;
    }

    /**
     * 更新日清bug统计规则
     * @param  int    $id
     * @param  array  $data
     * @return NissinBugStatisticsRulesModel
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws NotFoundException
     * @throws ValidateException
     */
    public function update(int $id, array $data): NissinBugStatisticsRulesModel
    {
        // 验证数据
        validate(NissinBugStatisticsRulesValidate::class)
            ->scene('update')
            ->check(array_merge(['nissin_bug_statistics_rules_id' => $id], $data));

        // 查找记录
        $model = NissinBugStatisticsRulesModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('日清bug统计规则不存在');
        }

        // 更新记录
        $model->save([
            'time'                => $data['time'],
            'statistics'          => $data['statistics'],
            'enable'              => $data['enable'],
            'use_custom_workdays' => $data['use_custom_workdays'],
        ]);

        return $model;
    }

    /**
     * 删除日清bug统计规则
     * @param  int  $id
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws NotFoundException
     * @throws ValidateException
     */
    public function delete(int $id): bool
    {
        // 验证数据
        validate(NissinBugStatisticsRulesValidate::class)
            ->scene('delete')
            ->check(['nissin_bug_statistics_rules_id' => $id]);

        // 查找记录
        $model = NissinBugStatisticsRulesModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('日清bug统计规则不存在');
        }


        // 删除记录
        return $model->save([
            'is_delete' => NissinBugStatisticsRulesModel::DELETE_YES,
        ]);
    }

    /**
     * 设置启用/禁用状态
     * @param int $id 规则ID
     * @param int $isEnable 启用状态（1启用，0禁用）
     * @param int $syncToAllProjects 是否同步到所有项目（1是，0否）
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function setEnable(int $id, int $isEnable, int $syncToAllProjects = 0): bool
    {
        $model = NissinBugStatisticsRulesModel::findById($id);
        if (!$model) {
            throw new NotFoundException('日清bug统计规则不存在');
        }

        try {
            DBTransaction::begin();

            // 更新当前规则状态
            $model->save(['enable' => $isEnable]);
            
            // 如果需要同步到所有项目
            if ($syncToAllProjects == 1) {
                // 获取系统中所有项目ID，而不仅是有规则的项目
                $allProjectIds = ProjectModel::getAllProjectIds();
                
                // 准备同步数据（复制原规则的所有字段）
                $syncData = $model->toArray();
                unset($syncData['nissin_bug_statistics_rules_id']); // 移除ID
                $syncData['enable'] = $isEnable; // 更新启用状态
                
                foreach ($allProjectIds as $projectId) {
                    // 跳过原规则所属项目
                    if ($projectId == $model->project_id) {
                        continue;
                    }
                    
                    // 查找目标项目是否已有规则
                    $existModel = NissinBugStatisticsRulesModel::where('project_id', $projectId)
                        ->where('is_delete', NissinBugStatisticsRulesModel::DELETE_NOT)
                        ->find();
                    
                    if ($existModel) {
                        // 更新现有规则（同步所有字段）
                        $updateData = $syncData;
                        $updateData['project_id'] = $projectId; // 确保项目ID正确
                        $existModel->save($updateData);
                    } else {
                        // 创建新规则（复制原规则的所有字段）
                        $newData = $syncData;
                        $newData['project_id'] = $projectId;
                        
                        $newModel = new NissinBugStatisticsRulesModel();
                        $newModel->save($newData);
                    }
                }
                
//                // 如果规则使用自定义工作日，同步自定义工作日设置
//                if ($model->use_custom_workdays == 1) {
//                    // 同步自定义工作日基本设置
//                    $workdayLogic = new CustomizeTheWorkingDayLogic();
//                    $workdayData = $workdayLogic->getByProjectId($model->project_id);
//                    if (!empty($workdayData)) {
//                        // 添加同步标记
//                        $workdayLogic->createOrUpdate($workdayData);
//                    }
                    
                    // // 同步自定义工作日额外调整
                    // $extraWorkdayLogic = new CustomizeTheWorkingDayExtraLogic();
                    // $extraWorkdayLogic->syncExtraAdjustments($model->project_id);
//                }
            }
            
            DBTransaction::commit();
            return true;
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 根据项目ID获取日清bug统计规则和工作日数据
     * @param  int  $projectId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getByProjectIdDefault(int $projectId): array
    {
        // 获取项目的日清bug统计规则
        $model = NissinBugStatisticsRulesModel::findByProjectId($projectId);

        // 如果没有找到数据，则尝试获取projectId为0的默认规则
        if ( ! $model) {
//            $model = NissinBugStatisticsRulesModel::findByProjectId(0);
//
//            // 如果仍然没有找到，则返回默认值
//            if ( ! $model) {
            $ruleData = $this->getDefaultRule($projectId);
//            } else {
//                $ruleData = $model->toDetail();
//                $ruleData['project_id'] = $projectId; // 替换为请求的projectId
//            }
        } else {
            $ruleData = $model->toDetail();
        }

        return $ruleData;
    }

    /**
     * 根据项目ID和日期判断是否为工作日
     * @param  int          $projectId  项目ID
     * @param  string|null  $date       日期，格式为Y-m-d，如果为null则使用当前日期
     * @return bool 是否为工作日
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function isWorkingDayByProjectId(int $projectId, ?string $date = null): bool
    {
        // 获取项目的日清bug统计规则
        $ruleData = $this->getByProjectIdDefault($projectId);
        if ($ruleData['enable'] === NissinBugStatisticsRulesModel::ENABLE_NO) {
            return false;
        }

        // 判断是否使用自定义工作日
        $useCustomWorkdays = isset($ruleData['use_custom_workdays']) && $ruleData['use_custom_workdays'] == 1;

        // 根据是否使用自定义工作日决定使用哪个projectId
        $targetProjectId = $useCustomWorkdays ? $projectId : -1;

        // 调用CustomizeTheWorkingDayLogic的isWorkingDay方法
        $workingDayLogic = new CustomizeTheWorkingDayLogic();
        return $workingDayLogic->isWorkingDay($targetProjectId, $date);
    }
} 