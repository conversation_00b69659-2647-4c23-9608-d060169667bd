<?php
/**
 * Desc 迭代过程节点关系 - 逻辑
 * User Long
 * Date 2024/11/9
 */

namespace app\project\logic;

use app\project\model\IterationProcessNodeRelationModel;
use think\model\Collection;

class IterationProcessNodeRelationLogic
{
    /**
     * 复制 节点关系
     * @param $nodeRelationModel
     * User Long
     * Date 2024/11/9
     */
    public static function copyNodeData($nodeRelationModel, $iterationNodeData)
    {
        foreach ($nodeRelationModel as $oldDatum) {
            // 复制类别
            $param['process_node_id'] = $iterationNodeData[$oldDatum->process_node_id] ?? 0; // 当前节点 id
            $param['next_node_id'] = $iterationNodeData[$oldDatum->next_node_id] ?? 0; // 后置节点 id

            $iterationProcessNodeRelationModel = new IterationProcessNodeRelationModel();
            $iterationProcessNodeRelationModel->save($param);
        }
    }

    /**
     * 获取下一节点数据
     * @param array $nodeIds
     * @return IterationProcessNodeRelationModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/12
     */
    public static function getNextRelationDataByNodeId(array $nodeIds)
    {
        return IterationProcessNodeRelationModel::whereIn('process_node_id', $nodeIds)->field('next_node_id, process_node_id')->select();
    }

    /**
     * 获取上一节点数据
     * @param array $nodeIds
     * @return IterationProcessNodeRelationModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/12
     */
    public static function getPrevRelationDataByNodeId(array $nodeIds)
    {
        return IterationProcessNodeRelationModel::whereIn('next_node_id', $nodeIds)->field('process_node_id, next_node_id')->select();
    }
}
