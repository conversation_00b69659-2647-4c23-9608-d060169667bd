<?php
/**
 * Desc 迭代节点提醒 - 逻辑
 * User Long
 * Date 2024/11/9
 */

namespace app\project\logic;

use app\project\model\IterationProcessNodeTipModel;

class IterationProcessNodeTipLogic
{
    /**
     * 跳过节点记录
     * @param int $iterationProcessNodeId
     * @return bool
     * User Long
     * Date 2024/11/15
     */
    public static function skipNode(int $iterationProcessNodeId)
    {
        return self::createTip($iterationProcessNodeId, '节点设置', '跳过节点');
    }

    /**
     * 审批拒绝记录
     * @param int $iterationProcessNodeId
     * @return bool
     * User Long
     * Date 2024/11/15
     */
    public static function turnDown(int $iterationProcessNodeId)
    {
        return self::createTip($iterationProcessNodeId, '审批', '不同意');
    }

    /**
     * 创建提醒记录
     * @param int $iterationProcessNodeId
     * @param string $title
     * @param string $content
     * @return bool
     * User Long
     * Date 2024/11/15
     */
    private static function createTip(int $iterationProcessNodeId, string $title, string $content)
    {
        $iterationProcessNodeTipModel = new IterationProcessNodeTipModel();

        return $iterationProcessNodeTipModel->save([
            'iteration_process_node_id' => $iterationProcessNodeId,
            'title' => $title,
            'content' => $content,
        ]);
    }
}
