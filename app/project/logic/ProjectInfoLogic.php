<?php
/**
 * Desc 项目管理 - 逻辑层
 * User Long
 * Date 2024/08/12
 */
declare (strict_types=1);

namespace app\project\logic;

use app\infrastructure\model\EnumModel;
use app\product\logic\ProductLogic;
use app\project\model\ProjectModel;
use app\project\validate\ProjectInfoValidate;
use app\work_items\logic\WorkItemsLogic;
use app\work_items\model\WorkItemsModel;
use basic\BaseModel;
use exception\NotFoundException;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use Throwable;
use utils\Ctx;
use utils\DBTransaction;

class ProjectInfoLogic
{
    /**
     * 实例化 项目、收藏表
     * @return mixed
     * User Long
     * Date 2024/10/11
     */
    private function projectModel()
    {
        $model = ProjectModel::alias('p')
            ->field('IF(pf.id IS NOT NULL, 1, 0) as star')
            ->join('project_favorite pf', 'pf.project_id=p.project_id and pf.is_delete='.BaseModel::DELETE_NOT.' and pf.create_by='.Ctx::$userId, 'left')
            ->where(['p.is_delete' => BaseModel::DELETE_NOT, 'p.is_template' => ProjectModel::IS_TEMPLATE_NOT])
            ->order('project_status ASC, star DESC, create_at DESC');

        // 获取当前登录用户参与的项目 id
        $projectIds = ProjectUserLogic::getProjectIdsByUserId();
        if (is_array($projectIds)) {
            $model = $model->whereIn('p.project_id', $projectIds);
        }

        return $model;
    }

    /**
     * 项目icon
     * @return array
     * User Long
     * Date 2024/10/8
     */
    public function getIconPath(): array
    {
        return getSystemEnumLibrary(EnumModel::PROJECT_ICON);
    }

    /**
     * 项目状态下拉数据
     * @return array
     * User Long
     * Date 2024/10/11
     */
    public function getProjectStatus(): array
    {
        return getSystemEnumLibrary(EnumModel::PROJECT_STATUS_TYPE);
    }

    /**
     * 获取项目下拉数据
     * @return array
     * User Long
     * Date 2024/10/10
     */
    public function getProjectSelector($where = null) :array
    {
        $model = ProjectModel::status();

        if ($where) {
            $model->where($where);
        }

        return $model->column('project_name as label, project_id as value');
    }

    /**
     * 获取项目名称
     * @param $projectId
     * @return mixed
     * User Long
     * Date 2024/10/10
     */
    public static function getProjectName($projectId): mixed
    {
        return ProjectModel::where(['project_id' => $projectId])->value('project_name');
    }

    /**
     * 获取项目内容
     * @param $projectId
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/20
     */
    public static function getProjectData($projectId): mixed
    {
        return ProjectModel::where(['project_id' => $projectId])->find();
    }

    /**
     * 获取项目内容集
     * @param $projectIds
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/9
     */
    public static function getProjectDataSet($projectIds): mixed
    {
        return ProjectModel::whereIn('project_id', $projectIds)->select();
    }

    /**
     * 新增
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/11
     */
    public function create(array $params): void
    {
        // 验证器校验
        validate(ProjectInfoValidate::class)->scene('create')->check($params);

        // 查询模板关联的项目id  2025-4-15注释
//        $templateProjectId = ProjectTemplateLogic::getProjectIdByProjectTemplateId((int)$params['template_id']);

        $projectUserIds = array_values(array_unique(array_merge($params['project_manager_user_ids'], [Ctx::$userId])));

        try {
            DBTransaction::begin();

            $projectModel = new ProjectModel();
            $params['project_no'] = time();
            $params['project_template_id'] = $params['template_id'];

            // 获取默认人数
            $params['project_user_count'] = count($projectUserIds);
            $projectModel->save($params);

            // 增加项目成员
            (new ProjectUserLogic())->create((int)$projectModel->project_id, $projectUserIds);

            // 复制项目模板数据 2025-4-15注释
//            ProjectTemplateLogic::copyProjectTemplate((int)$templateProjectId, (int)$projectModel->project_id);

            DBTransaction::commit();
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }

    }

    /**
     * 创建模版项目
     * @param string $projectName
     * @return ProjectModel
     * User Long
     * Date 2024/10/14
     */
    public static function createTemplateProject(string $projectName)
    {
        $projectModel = new ProjectModel();
        $params['project_no'] = time();
        $params['project_name'] = $projectName;
        $params['is_template'] = ProjectModel::IS_TEMPLATE_YES;

        $projectModel->save($params);

        return $projectModel;
//        return $projectModel->project_id;
    }

    /**
     * 更新项目关联模板id
     * @param ProjectModel $model
     * @param int $projectTemplateId
     * User Long
     * Date 2024/10/19
     */
    public static function updateTemplateIdByProjectId(ProjectModel $model, int $projectTemplateId)
    {
        $model->project_template_id = $projectTemplateId;
        $model->save();
    }

    /**
     * 根据分类id集删除分类
     * @param array $projectIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function delete(array $projectIds): void
    {
        $models = ProjectModel::selectByIds($projectIds);

        foreach ($models as $model) {
            $model->save(['is_delete' => BaseModel::DELETE_YES]);
        }
    }

    /**
     * 编辑数据
     * @param $projectId
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/11
     */
    private function edit($projectId, array $params = []): void
    {
        $projectModel = ProjectModel::findById($projectId);
        if (!$projectModel) {
            throw new NotFoundException();
        }

        try {
            DBTransaction::begin();

            if (!empty($params['project_manager_user_ids'])) {
                // 删除项目成员
                ProjectUserLogic::deleteByProjectRole($projectId, EnumModel::PROJECT_MANAGER);

                // 增加项目成员
                (new ProjectUserLogic())->create($projectId, $params['project_manager_user_ids']);
            }

            // 获取人数
            $params['project_user_count'] = ProjectUserLogic::countByProjectId($projectId);
            $projectModel->save($params);

            DBTransaction::commit();
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 根据id更新数据
     * @param $projectId
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/11
     */
    public function update($projectId, array $params): void
    {
        // 验证器校验
        validate(ProjectInfoValidate::class)->scene('update')->check($params);

        $this->edit($projectId, $params);
    }

    /**
     * 根据id更新项目人数
     * @param $projectId
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/10/11
     */
    public function updateUserCount($projectId): void
    {
        $this->edit($projectId);
    }

    /**
     * 迭代分类详情
     * @param int $projectId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function detail(int $projectId): array
    {
        $model = ProjectModel::findById($projectId);
        if (!$model) {
            throw new NotFoundException();
        }

        $res = $model->toDetail()->toArray();
        $res['project_manager'] = [];

        // 查询项目经理
        $projectManager = ProjectUserLogic::selectByProjectId($projectId, EnumModel::PROJECT_MANAGER);
        foreach ($projectManager as $item) {
            $res['project_manager'][] = [
                'user_id' => $item->user_id,
                'user_name' => $item->user_name
            ];
        }

        // 查询产品名称
        $res['product_name'] = ProductLogic::getProductNameByProductId($res['product_id']);

        // 查询模板名称
        $res['project_template_name'] = ProjectTemplateLogic::getProductNameByProductId($res['project_template_id']);

        return $res;
    }

    /**
     * 分页
     * @param string $projectName
     * @param string $productName
     * @param int $projectStatus
     * @return ProjectModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/10
     */
    public function listQuery(string $projectName,string $productName,int $projectStatus)
    {
        $where = [];

        if ($projectName) {
            $where[] = ['p.project_name', 'like', "%$projectName%"];
        }
        if ($productName) {
            $productIds = ProductLogic::getProductIdColumnProductName($productName);
            $where[] = ['p.product_id', 'in', $productIds];
        }
        if ($projectStatus) {
            $where[] = ['p.project_status', '=', $projectStatus];
        }

        return $this->projectModel()
            ->field('p.*')
            ->with([
                'product' => function($sql) {
                    $sql->bind(['product_name']);
                },
                'project_manager' => function($sql) {
                    $sql->alias('pm')
                        ->join('project_user_role pr', 'pm.user_id=pr.user_id', 'left')
                        ->field('pm.project_id, pm.user_id, pm.user_name')
                        ->where([
                            'pm.is_delete' => BaseModel::DELETE_NOT,
                            'pr.project_role' => EnumModel::PROJECT_MANAGER
                        ])
                        ->hidden(['project_id']);
                }
            ])
            ->where($where)
            ->hidden(ProjectModel::HIDDEN_FIELD)
            ->append(['product_name'])
            ->select();
    }

    /**
     * 项目左侧列表
     * @return ProjectModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function abbreviate()
    {
        return $this->projectModel()
            ->field('p.project_id, p.project_name, p.project_icon, p.create_at')
            ->where([
                'p.project_status' => EnumModel::PROJECT_PROCESS_STATUS
            ])
            ->select();
    }

    /**
     * 启用
     * @param array $projectIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function enable(array $projectIds): void
    {

        $data = ProjectModel::selectByIds($projectIds);

        foreach ($data as $model) {
            $model->save(['project_status' => EnumModel::PROJECT_PROCESS_STATUS]);
        }

    }

    /**
     * 禁用
     * @param array $projectIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function disable(array $projectIds): void
    {

        $data = ProjectModel::selectByIds($projectIds);

        foreach ($data as $datum) {
            $datum->save(['project_status' =>  EnumModel::PROJECT_END_STATUS]);
        }

    }

    /**
     * 获取需要退出项目的用户信息
     * @param int $projectId
     * @return array
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/12/9
     */
    public function exitProjectInfo(int $projectId)
    {
        $res = [];

        $esData = WorkItemsLogic::findUserProjectData($projectId, Ctx::$userId);

        foreach ($esData as $datum) {
            switch ($datum['cnt_type']) {
                case WorkItemsModel::CNT_TYPE_DEMAND:
                    $res['demand_ids'][] = $datum['cnt_id'];
                    break;
                case WorkItemsModel::CNT_TYPE_TASK:
                    $res['task_ids'][] = $datum['cnt_id'];
                    break;
                case WorkItemsModel::CNT_TYPE_FLAW:
                    $res['defect_ids'][] = $datum['cnt_id'];
                    break;
                default:
                    break;
            }
        }

        return $res;
    }

    /**
     * 退出项目用户（无需交接）
     * @param int $projectId
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/12/9
     */
    public function directQuitProjectUser(int $projectId)
    {
        // 用户移出项目
        (new ProjectUserLogic())->removeUser($projectId, Ctx::$userId);
    }

    /**
     * 退出项目用户（需交接）
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/12/10
     */
    public function quitProjectUser(array $params)
    {
        // 验证器校验
        validate(ProjectInfoValidate::class)->scene('quitProjectUser')->check($params);

        // 不需要跟进人，清除跟进人数据
        if (!$params['is_follow_up']) {
            $params['demand_user_ids'] = $params['defect_user_ids'] = $params['task_user_ids'] = [];
        }

        try {
            DBTransaction::begin();

            // 需求
            WorkItemsLogic::replaceProjectUser($params['demand_ids'], Ctx::$userId, $params['demand_user_ids']);

            // 缺陷
            WorkItemsLogic::replaceProjectUser($params['defect_ids'], Ctx::$userId, $params['defect_user_ids']);

            // 任务
            WorkItemsLogic::replaceProjectUser($params['task_ids'], Ctx::$userId, $params['task_user_ids']);

            // 用户移出项目
            (new ProjectUserLogic())->removeUser((int)$params['project_id'], Ctx::$userId);

            DBTransaction::commit();
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 更改项目是否为模板（不对外使用）
     * @param int $projectId
     * @param int $isTemplate
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/1/3
     */
    public function upIsTemp(int $projectId, int $isTemplate)
    {
        $model = ProjectModel::status()->where(['project_id' => $projectId])->find();
        $model->is_template = $isTemplate;
        $model->save();
    }

    /**
     * 以项目id为key获取项目名
     * @param array $projectIds
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/15
     */
    public static function getProjetNameByProjetIds(array $projectIds)
    {
        $model = ProjectModel::selectByIds($projectIds);
        return $model->column('project_name', 'project_id');
    }

    /**
     * 根据模板 id获取项目 id 合集
     * @param int $projectTemplateId
     * @return ProjectModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getProjectIdsByTemplateId(int $projectTemplateId)
    {
        return ProjectModel::status()->where(['project_template_id' => $projectTemplateId])->select();
    }
}
