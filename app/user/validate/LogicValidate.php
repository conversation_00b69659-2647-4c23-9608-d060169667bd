<?php
/**
 * Desc 登录 - 验证器
 * User Long
 * Date 2023/07/1
 */

namespace app\user\validate;

use basic\BaseValidate;

class LogicValidate extends BaseValidate
{
    protected $rule = [
        'id|id' => 'require|integer|egt:0',
        'ids|id集' => 'require|isArray',
        'sort|排序' => 'require|integer|egt:0|elt:9999',
        'keyword|搜索词' => 'require|max:100',
        'page|页码' => 'require|integer|gt:0',
        'list_rows|查询条数' => 'require|integer|gt:0|elt:2000',
        'user_id|用户id' => 'require|integer|egt:0',
        'name|名称' => 'require|max:100',
        'url|链接' => 'require|max:255',
        'type|类型' => 'require|integer|egt:0',

        'account|帐号' => 'require',
        'password|密码' => 'require',
    ];

    // 直接返回语言包key，抛异常是会统一调用语言包处理
    protected $message = [
    ];

    /**
     * 登录
     * @return LogicValidate
     * User Long
     * Date 2023/7/6
     */
    public function sceneLogin(): LogicValidate
    {
        return $this->only(['account', 'password']);
    }
}
