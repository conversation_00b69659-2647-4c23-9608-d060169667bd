<?php
/**
 * Desc 登录 - 控制器
 * User Long
 * Date 2024/7/1
 */

namespace app\user\controller;


use app\user\logic\LoginLogic;
use GuzzleHttp\Exception\GuzzleException;

class Login
{
    /**
     * 帐号登录
     * @return mixed
     * @throws GuzzleException
     * User Long
     * Date 2024/7/1
     */
    public function login(): mixed
    {

        $account = app('request')->post('account/s', '');
        $password = app('request')->post('password/s', '');
        $res = LoginLogic::login($account, $password);

        return app('json')->success($res);
    }
}
