<?php

namespace app\user\logic;

use components\platform\user\dto\CompanyDto;
use components\platform\user\dto\EmpInfoDto;
use components\platform\user\dto\FsDto;
use components\platform\user\dto\OrgDto;
use components\platform\user\dto\PostionDto;
use components\platform\user\dto\UserDetailDto;
use components\platform\user\dto\UserShortInfoDto;
use components\platform\user\requests\Auth;
use components\platform\user\requests\User;
use Psr\SimpleCache\InvalidArgumentException;
use think\facade\Cache;
use utils\Ctx;

class UserLogic
{
    private const USER_SHORT_INFO_KEY = 'user_short_info:%s';
    private const TOKEN_USERID_KEY = "token:%s";
    private const USER_DETAIL_KEY = "user_detail:%s";

    public function getUserByToken($token)
    {
        $cacheKey = sprintf(self::TOKEN_USERID_KEY, md5($token));
        $redis = Cache::store('redis');
        $userId = $redis->get($cacheKey);
        $userShortInfo = null;
        if ($userId) {
            $userShortInfo = $this->getUserShortInfoById($userId);
        }

        if (!$userShortInfo) {
            $userShortInfo = (new Auth(Ctx::$token))->getUserByToken()->wait();
            $userId = $userShortInfo->userId;
            // 将用户ID缓存起来
            $redis->set($cacheKey, $userId, 3600 * 6);
            $redis->set(sprintf(self::USER_SHORT_INFO_KEY, $userId), json_encode($userShortInfo, 320), 3600 * 6);
        }


        return $userShortInfo;

    }

    /**
     * 根据用户id获取基本信息
     * @param $userId
     * @return UserShortInfoDto | null
     * @throws InvalidArgumentException
     * @see UserShortInfoDto
     */
    public function getUserShortInfoById($userId): ?UserShortInfoDto
    {
        $cacheKey = sprintf(self::USER_SHORT_INFO_KEY, $userId);
        $redis = Cache::store('redis');
        $user = $redis->get($cacheKey);
        if (!$user) {
            return null;
        }

        return (new UserShortInfoDto())->load(json_decode($user, true));
    }

    public function logout($userId): void
    {
        $redis = Cache::store('redis');
        $redis->delete(sprintf(self::USER_SHORT_INFO_KEY, $userId));
        $redis->delete(sprintf(self::USER_DETAIL_KEY, $userId));
    }

    /**
     * 根据用户id获取详情
     * @param mixed $userId 用户id
     * @return \UserDetailDto
     * @throws InvalidArgumentException
     * @see UserDetailDto
     */
    public function getUserDetailById($userId): \UserDetailDto
    {
        $cacheKey = sprintf(self::USER_DETAIL_KEY, $userId);
        $redis = Cache::store('redis');
        $cacheUserDetail = $redis->get($cacheKey);

        if ($cacheUserDetail) {
            $cacheUserDetail = json_decode($cacheUserDetail, true);
            $userDetail = new UserDetailDto();
            $userDetail->user = (new UserShortInfoDto())->load($cacheUserDetail['user']);
            $userDetail->fsInfo = (new FsDto())->load($cacheUserDetail['fsInfo']);
            $userDetail->empInfo = (new EmpInfoDto())->load($cacheUserDetail['empInfo']);

            $positions = [];
            foreach ($cacheUserDetail['positions'] as $v) {
                $companyDto = (new CompanyDto())->load($v['company']);
                $orgDto = (new OrgDto())->load($v['org']);
                $positionDto = (new PostionDto())->load($v);
                $positionDto->company = $companyDto;
                $positionDto->org = $orgDto;

                $positions[] = $positionDto;
            }

            $userDetail->positions = $positions;


            return $userDetail;
        }

        //通过接口获取并设置缓存
        /** @var UserDetailDto $userDetail */
        $userDetail = (new User(Ctx::$token))->getUserDetail($userId)->wait();
        $redis->set($cacheKey, json_encode($userDetail, 320), 6 * 3600);

        return $userDetail;
    }


}
