<?php
/**
 * Desc 登录 - 逻辑层
 * User Long
 * Date 2024/06/29
 */

declare (strict_types=1);

namespace app\user\logic;


use app\user\validate\LogicValidate;
use basic\BaseLogic;
use components\platform\user\requests\User;
use GuzzleHttp\Exception\GuzzleException;

class LoginLogic extends BaseLogic
{
    /**
     * 登录
     * @param string $account 账号
     * @param string $password 密码
     * @return array
     * @throws GuzzleException
     * User Long
     * Date 2024/7/3
     */
    public static function login(string $account, string $password): array
    {
        // 验证参数
        validate(LogicValidate::class)->scene('login')->check(['account' => $account, 'password' => $password]);

        // 账号大写
        $account = strtoupper(trim($account));

        // 请求中台登录
        $token_info = (new User(null))->getOauthLogin($account, $password);

        return [
            'token' => trim(ltrim($token_info['token'], 'Bearer')),
            'name' => $token_info['userInfo']['name'] ?? '',
            'user_id' => $token_info['userInfo']['id'] ?? 0
        ];
    }
}
