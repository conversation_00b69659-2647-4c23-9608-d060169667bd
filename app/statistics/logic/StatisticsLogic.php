<?php
/**
 * Desc 统计查询逻辑层
 * User AI Assistant
 * Date 2024/12/20
 */

declare(strict_types=1);

namespace app\statistics\logic;

use think\facade\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class StatisticsLogic
{
    // 链接格式常量
    private const FORMAT_URL_PROJECT = '/project/projectDetail/%s/%s?project_id=%s&activeTab=demand';
    private const FORMAT_URL_ITERATION = '/project/projectDetail/%s/%s?project_id=%s&activeTab=iterative&iteration_id=%s&iterativeActiveTab=overview';
    private const FORMAT_URL_ITEM = '/project/%sDetail/%s?id=%s&project_id=%s';

    // 条目类型常量
    private const ITEM_TYPE_DEMAND = 'demand';
    private const ITEM_TYPE_TASK = 'task';
    private const ITEM_TYPE_DEFECT = 'defect';

    // 节点阶段常量
    private const NODE_STAGE_DEVELOPMENT = 'tag_49809162008138929929038';
    private const NODE_STAGE_TESTING = 'tag_49809162267640520716923';

    /**
     * 场景1: 获取需求变更记录
     * @param array $params
     * @return array
     * @throws DbException
     */
    public function getDemandChangeRecords(array $params): array
    {
        $sql = "SELECT p.project_name AS '所属项目',
                       p.project_id AS _link_id_project,
                       wicr.cnt_id AS '需求ID',
                       wicr.cnt_title AS '需求标题',
                       wicr.handler_uid AS '需求负责人id',
                       wicr.handler_name AS '需求负责人',
                       i.iteration_name AS '迭代',
                       i.iteration_id AS _link_id_iteration,
                       wicr.node_name AS '迭代节点',
                       ttl_stage.tag_name  AS '迭代节点阶段',
                       wicr.change_reason AS '变更原因',
                       wicr.submitter_name AS '提交人',
                       wicr.submit_at AS '提交时间'
                FROM t_work_items_change_records wicr
                LEFT JOIN t_project p ON wicr.project_id = p.project_id
                LEFT JOIN t_iteration i ON wicr.iteration_id = i.iteration_id
                LEFT JOIN t_tag_library ttl_stage ON wicr.node_stage = ttl_stage.tag_code
                WHERE 1=1";

        $bindings = [];
        if (!empty($params['project_id'])) {
            $sql .= " AND wicr.project_id = ?";
            $bindings[] = $params['project_id'];
        }
        if (!empty($params['iteration_id'])) {
            $sql .= " AND wicr.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }
        if (!empty($params['change_reason'])) {
            $sql .= " AND wicr.change_reason LIKE ?";
            $bindings[] = "%{$params['change_reason']}%";
        }
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $sql .= " AND wicr.submit_at BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        $sql .= " ORDER BY wicr.submit_at DESC";

        $rawResult = Db::query($sql, $bindings);
        $displayHeaders = ['所属项目', '需求ID', '需求标题', '需求负责人id', '需求负责人', '迭代', '迭代节点', '迭代节点阶段', '变更原因', '提交人', '提交时间'];

        return $this->processRecordsWithLinks($rawResult, $displayHeaders);
    }

    /**
     * 场景2: 获取新增需求记录
     * @param array $params
     * @return array
     * @throws DbException
     */
    public function getNewDemandRecords(array $params): array
    {
        $sql = "SELECT p.project_name AS '所属项目',
                       idrr.project_id AS _link_id_project,
                       idrr.cnt_id AS '需求ID',
                       idrr.cnt_title AS '需求标题',
                       idrr.operator_name AS '操作人',
                       idrr.iteration_name AS '迭代',
                       idrr.iteration_id AS _link_id_iteration,
                       idrr.node_name AS '迭代节点',
                       ttl_stage.tag_name AS '迭代节点阶段',
                       idrr.operation_time AS '操作时间'
                FROM t_iteration_demand_relation_records idrr
                LEFT JOIN t_project p ON idrr.project_id = p.project_id
                LEFT JOIN t_tag_library ttl_stage ON idrr.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1
                WHERE idrr.operation_type = 1";

        $bindings = [];
        if (!empty($params['project_id'])) {
            $sql .= " AND idrr.project_id = ?";
            $bindings[] = $params['project_id'];
        }
        if (!empty($params['iteration_id'])) {
            $sql .= " AND idrr.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $sql .= " AND idrr.operation_time BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        $sql .= " ORDER BY idrr.operation_time DESC";

        $rawResult = Db::query($sql, $bindings);
        $displayHeaders = ['所属项目', '需求ID', '需求标题', '操作人', '迭代', '迭代节点', '迭代节点阶段', '操作时间'];

        return $this->processRecordsWithLinks($rawResult, $displayHeaders);
    }

    /**
     * 场景3/7/10: 获取需求评审记录
     * @param array $params
     * @return array
     * @throws DbException
     */
    public function getDemandReviewRecords(array $params): array
    {
        $sql = "SELECT mcc.change_id,
                       p.project_name AS '所属项目',
                       mcc.project_id AS _link_id_project,
                       mcc.task_id AS '任务ID',
                       mcc.task_title AS '任务标题',
                       GROUP_CONCAT(DISTINCT nr_user.user_name SEPARATOR ', ') AS '节点负责人',
                       GROUP_CONCAT(DISTINCT spk_user.user_name SEPARATOR ', ') AS '主讲人',
                       ttl.tag_name AS '会议类型名称',
                       ttl.tag_code AS '会议类型代码',
                       mcc.create_at AS '创建时间'
                FROM t_meeting_collection_change mcc
                JOIN t_meeting_collection_change_type mcct ON mcc.change_id = mcct.change_id
                JOIN t_tag_library ttl ON mcct.type_code = ttl.tag_code AND ttl.group_type = 2
                LEFT JOIN t_project p ON mcc.project_id = p.project_id
                LEFT JOIN t_meeting_collection_change_user mccu_nr ON mcc.change_id = mccu_nr.change_id AND mccu_nr.user_type = 1
                LEFT JOIN t_project_user nr_user ON mccu_nr.user_id = nr_user.user_id
                LEFT JOIN t_meeting_collection_change_user mccu_spk ON mcc.change_id = mccu_spk.change_id AND mccu_spk.user_type = 2
                LEFT JOIN t_project_user spk_user ON mccu_spk.user_id = spk_user.user_id
                WHERE mcc.is_delete = 0";

        $bindings = [];
        if (!empty($params['project_id'])) {
            $sql .= " AND mcc.project_id = ?";
            $bindings[] = $params['project_id'];
        }
        if (!empty($params['type_code'])) {
            $sql .= " AND mcct.type_code = ?";
            $bindings[] = $params['type_code'];
        }
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $sql .= " AND DATE(mcc.create_at) BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        $sql .= " GROUP BY mcc.change_id, p.project_name, mcc.task_id, mcc.task_title, ttl.tag_name, ttl.tag_code, mcc.create_at";
        $sql .= " ORDER BY mcc.create_at DESC";

        $rawResult = Db::query($sql, $bindings);
        $displayHeaders = ['change_id', '所属项目', '任务ID', '任务标题', '节点负责人', '主讲人', '会议类型名称', '会议类型代码', '创建时间'];

        return $this->processRecordsWithLinks($rawResult, $displayHeaders);
    }

    /**
     * 场景4: 获取开发延期记录
     * @param array $params
     * @return array
     * @throws DbException
     */
    public function getDevelopmentDelayRecords(array $params): array
    {
        $sql = "WITH UserNamesCTE AS (
                    SELECT inner_ipn.iteration_process_node_id,
                           GROUP_CONCAT(pu.user_name SEPARATOR ', ') AS ResponsibleUserNames
                    FROM t_iteration_process_node inner_ipn
                    CROSS JOIN JSON_TABLE(inner_ipn.node_data, '$.node_manger.users[*]' COLUMNS (json_user_id INT PATH '$')) AS jt
                    JOIN t_project_user pu ON jt.json_user_id = pu.user_id AND pu.project_id = ?
                    GROUP BY inner_ipn.iteration_process_node_id
                )
                SELECT p.project_name AS '所属项目',
                       p.project_id AS _link_id_project,
                       i.iteration_name AS '迭代',
                       i.iteration_id AS _link_id_iteration,
                       ipn.node_name AS '迭代节点',
                       ttl_stage.tag_name AS '节点阶段',
                       r.estimate_start_time AS '预估开始时间',
                       r.estimate_end_time AS '预估结束时间',
                       r.actual_start_time AS '实际开始时间',
                       r.actual_end_time AS '实际结束时间',
                       unc.ResponsibleUserNames AS '节点负责人',
                       CASE WHEN r.is_delayed = 1 THEN '是' ELSE '否' END AS '是否延期'
                FROM t_iteration_process_node_end_record r
                JOIN t_iteration_process_node ipn ON r.iteration_process_node_id = ipn.iteration_process_node_id
                LEFT JOIN UserNamesCTE unc ON r.iteration_process_node_id = unc.iteration_process_node_id
                LEFT JOIN t_tag_library ttl_stage ON r.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1
                LEFT JOIN t_project p ON r.project_id = p.project_id
                LEFT JOIN t_iteration i ON r.iteration_id = i.iteration_id
                WHERE r.project_id = ? AND r.is_delayed = 1";

        $bindings = [$params['project_id'], $params['project_id']];
        if (!empty($params['iteration_id'])) {
            $sql .= " AND r.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }
        if (!empty($params['node_stage'])) {
            $sql .= " AND ttl_stage.tag_code = ?";
            $bindings[] = $params['node_stage'];
        } else {
            $sql .= " AND ttl_stage.tag_code = ?";
            $bindings[] = self::NODE_STAGE_DEVELOPMENT;
        }
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $sql .= " AND r.actual_end_time BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        $sql .= " ORDER BY r.actual_end_time DESC";

        $rawResult = Db::query($sql, $bindings);
        $displayHeaders = ['所属项目', '迭代', '迭代节点', '节点阶段', '预估开始时间', '预估结束时间', '实际开始时间', '实际结束时间', '节点负责人', '是否延期'];

        return $this->processRecordsWithLinks($rawResult, $displayHeaders);
    }

    /**
     * 场景5: 获取Bug统计记录
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getBugStatisticsRecords(array $params): array
    {
        [$sql, $bindings] = $this->_buildBugStatisticsQuery($params);
        $rawResult = Db::query($sql, $bindings);

        $baseDisplayHeaders = ['id', '统计日期', '统计时间点', '处理人姓名', '处理人ID', 'count', '所属项目', '统计方式', '统计范围（日期）', '统计截止时间', '缺陷未清数量'];

        if (empty($rawResult)) {
            return [$baseDisplayHeaders];
        }

        $groupedBugDetails = $this->_getGroupedBugDetailsForStats($rawResult);

        $maxBugDetailsCount = 0;
        if (!empty($groupedBugDetails)) {
            $maxBugDetailsCount = max(array_map('count', $groupedBugDetails)) ?: 0;
        }

        $finalDisplayHeaders = $baseDisplayHeaders;
        for ($i = 1; $i <= $maxBugDetailsCount; $i++) {
            $finalDisplayHeaders[] = "缺陷标题_{$i}";
        }

        $processedRows = $this->_buildBugStatisticsFinalRows($rawResult, $groupedBugDetails, $maxBugDetailsCount, $baseDisplayHeaders);

        return array_merge([$finalDisplayHeaders], $processedRows);
    }

    /**
     * 场景6: 获取工时评估打回记录
     * @param array $params
     * @return array
     * @throws DbException
     */
    public function getWorkHoursRejectionRecords(array $params): array
    {
        $sql = "SELECT whrr.cnt_id AS '任务ID',
                       whrr.cnt_title AS '任务标题',
                       whrr.handler_name AS '处理人',
                       whrr.rejection_reason AS '打回原因',
                       whrr.submitter_name AS '提交人',
                       whrr.submit_at AS '提交时间',
                       JSON_UNQUOTE(wi.extends->'$.project_id') AS _link_id_project,
                       p.project_name AS '所属项目'
                FROM t_work_hours_rejection_records whrr
                LEFT JOIN t_work_items wi ON whrr.cnt_id = wi.cnt_id
                LEFT JOIN t_project p ON JSON_UNQUOTE(wi.extends->'$.project_id') = p.project_id";

        $bindings = [];
        $whereConditions = [];
        if (!empty($params['submitter_uid'])) {
            $whereConditions[] = "whrr.submitter_uid = ?";
            $bindings[] = $params['submitter_uid'];
        }
        if (!empty($params['project_id'])) {
            $whereConditions[] = "JSON_UNQUOTE(wi.extends->'$.project_id') = ?";
            $bindings[] = $params['project_id'];
        }
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $whereConditions[] = "whrr.create_at BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(' AND ', $whereConditions);
        }
        $sql .= " ORDER BY whrr.submit_at DESC";

        $rawResult = Db::query($sql, $bindings);
        $displayHeaders = ['任务ID', '任务标题', '处理人', '打回原因', '提交人', '提交时间', '所属项目'];

        return $this->processRecordsWithLinks($rawResult, $displayHeaders);
    }

    /**
     * 场景8-字段1: 获取迭代级别统计
     * @param array $params
     * @return array
     * @throws DbException
     */
    public function getScene8Field1Statistics(array $params): array
    {
        $timeCondition1 = '';
        $timeCondition2 = '';
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $timeCondition1 = " AND DATE(create_at) BETWEEN ? AND ?";
            $timeCondition2 = " AND DATE(create_at) BETWEEN ? AND ?";
        }

        $sql = "WITH IterationChanges AS (
                    SELECT iteration_id, project_id, count(*) AS demand_change_count
                    FROM t_work_items_change_records
                    WHERE project_id = ? {$timeCondition1}
                    GROUP BY iteration_id, project_id
                ), IterationNewDemandsFromRelation AS (
                    SELECT iteration_id, project_id, COUNT(DISTINCT cnt_id) as new_demand_relation_count
                    FROM t_iteration_demand_relation_records
                    WHERE operation_type = 1 AND project_id = ? {$timeCondition2}
                    GROUP BY iteration_id, project_id
                ), IterationOverallDelayStatus AS (
                    SELECT iteration_id, project_id, MAX(is_delayed) AS is_overall_delayed
                    FROM t_iteration_process_node_end_record
                    WHERE project_id = ?
                    GROUP BY iteration_id, project_id
                )
                SELECT p.project_name AS '所属项目',
                       p.project_id AS _link_id_project,
                       i.iteration_name AS '迭代',
                       i.iteration_id AS _link_id_iteration,
                       COALESCE(ic.demand_change_count, 0) AS '需求变更次数',
                       COALESCE(indr.new_demand_relation_count, 0) AS '需求新增次数',
                       CONCAT(i.estimate_start_time, ' 至 ', i.estimate_end_time) AS '迭代预估周期',
                       CONCAT(i.start_time, ' 至 ', i.end_time) AS '迭代实际周期',
                       CASE
                           WHEN iods.is_overall_delayed = 1 THEN '是'
                           WHEN iods.is_overall_delayed = 0 THEN '否'
                           ELSE (CASE WHEN i.end_time > i.estimate_end_time THEN '是' ELSE '否' END)
                       END AS '是否延期'
                FROM t_iteration i
                LEFT JOIN t_project p ON i.project_id = p.project_id
                LEFT JOIN IterationChanges ic ON i.iteration_id = ic.iteration_id AND i.project_id = ic.project_id
                LEFT JOIN IterationNewDemandsFromRelation indr ON i.iteration_id = indr.iteration_id AND i.project_id = indr.project_id
                LEFT JOIN IterationOverallDelayStatus iods ON i.iteration_id = iods.iteration_id AND i.project_id = iods.project_id
                WHERE ((ic.iteration_id IS NOT NULL OR indr.iteration_id IS NOT NULL) and iods.is_overall_delayed = 1) AND i.project_id = ?";

        $bindings = [$params['project_id']];
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        $bindings[] = $params['project_id'];
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        $bindings[] = $params['project_id'];
        $bindings[] = $params['project_id'];

        if (!empty($params['iteration_id'])) {
            $sql .= " AND i.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }
        $sql .= " ORDER BY i.iteration_name";

        $rawResult = Db::query($sql, $bindings);
        $displayHeaders = ['所属项目', '迭代', '需求变更次数', '需求新增次数', '迭代预估周期', '迭代实际周期', '是否延期'];

        return $this->processRecordsWithLinks($rawResult, $displayHeaders);
    }

    /**
     * 场景8-字段2: 获取迭代节点级别统计
     * @param array $params
     * @return array
     * @throws DbException
     */
    public function getScene8Field2Statistics(array $params): array
    {
        $timeCondition1 = '';
        $timeCondition2 = '';
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $timeCondition1 = " AND DATE(create_at) BETWEEN ? AND ?";
            $timeCondition2 = " AND DATE(create_at) BETWEEN ? AND ?";
        }

        $sql = "WITH NodeDemandChanges AS (
                    SELECT node_id, project_id, iteration_id, COUNT(change_id) AS node_demand_change_count
                    FROM t_work_items_change_records
                    WHERE 1=1 {$timeCondition1}
                    GROUP BY node_id, project_id, iteration_id
                ), NodeNewDemands AS (
                    SELECT node_id, project_id, iteration_id, COUNT(record_id) AS node_new_demand_count
                    FROM t_iteration_demand_relation_records
                    WHERE operation_type = 1 {$timeCondition2}
                    GROUP BY node_id, project_id, iteration_id
                )
                SELECT ipn.process_node_id AS '迭代节点id',
                       ipn.node_name AS '迭代节点',
                       ttl_stage.tag_name AS '阶段',
                       COALESCE(ndc.node_demand_change_count, 0) AS '需求变更次数',
                       COALESCE(nnd.node_new_demand_count, 0) AS '需求新增次数',
                       i.iteration_name AS '所属迭代',
                       i.iteration_id AS _link_id_iteration,
                       p.project_name AS '所属项目',
                       p.project_id AS _link_id_project
                FROM t_iteration_process_node ipn
                LEFT JOIN t_iteration i ON ipn.iteration_id = i.iteration_id
                LEFT JOIN t_project p ON i.project_id = p.project_id
                LEFT JOIN t_tag_library ttl_stage ON JSON_UNQUOTE(ipn.node_data->'$.node_setting.node_stage') = ttl_stage.tag_code AND ttl_stage.group_type = 1
                LEFT JOIN NodeDemandChanges ndc ON ipn.iteration_process_node_id = ndc.node_id AND ipn.iteration_id = ndc.iteration_id AND i.project_id = ndc.project_id
                LEFT JOIN NodeNewDemands nnd ON ipn.iteration_process_node_id = nnd.node_id AND ipn.iteration_id = nnd.iteration_id AND i.project_id = nnd.project_id
                WHERE i.project_id = ? AND ipn.is_delete = 0 AND (ndc.iteration_id IS NOT NULL OR nnd.iteration_id IS NOT NULL)";

        $bindings = [];
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        $bindings[] = $params['project_id'];

        if (!empty($params['iteration_id'])) {
            $sql .= " AND i.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }
        $sql .= " ORDER BY i.iteration_name, ipn.create_at, ipn.iteration_process_node_id";

        $rawResult = Db::query($sql, $bindings);
        $displayHeaders = ['迭代节点id', '迭代节点', '阶段', '需求变更次数', '需求新增次数', '所属迭代', '所属项目'];

        return $this->processRecordsWithLinks($rawResult, $displayHeaders);
    }

    /**
     * 场景9: 获取测试延期记录
     * @param array $params
     * @return array
     * @throws DbException
     */
    public function getTestDelayRecords(array $params): array
    {
        $sql = "WITH UserNamesCTE AS (
                    SELECT inner_ipn.iteration_process_node_id,
                           GROUP_CONCAT(pu.user_name SEPARATOR ', ') AS ResponsibleUserNames
                    FROM t_iteration_process_node inner_ipn
                    CROSS JOIN JSON_TABLE(inner_ipn.node_data, '$.node_manger.users[*]' COLUMNS (json_user_id INT PATH '$')) AS jt
                    JOIN t_project_user pu ON jt.json_user_id = pu.user_id AND pu.project_id = ?
                    GROUP BY inner_ipn.iteration_process_node_id
                )
                SELECT p.project_name AS '所属项目',
                       p.project_id AS _link_id_project,
                       i.iteration_name AS '迭代',
                       i.iteration_id AS _link_id_iteration,
                       ipn.node_name AS '迭代节点',
                       ttl_stage.tag_name AS '节点阶段',
                       r.estimate_start_time AS '预估开始时间',
                       r.estimate_end_time AS '预估结束时间',
                       r.actual_start_time AS '实际开始时间',
                       r.actual_end_time AS '实际结束时间',
                       unc.ResponsibleUserNames AS '节点负责人',
                       r.group_leader_name AS '组长',
                       CASE WHEN r.is_delayed = 1 THEN '是' ELSE '否' END AS '是否延期'
                FROM t_iteration_process_node_end_record r
                JOIN t_iteration_process_node ipn ON r.iteration_process_node_id = ipn.iteration_process_node_id
                LEFT JOIN UserNamesCTE unc ON r.iteration_process_node_id = unc.iteration_process_node_id
                LEFT JOIN t_tag_library ttl_stage ON r.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1
                LEFT JOIN t_project p ON r.project_id = p.project_id
                LEFT JOIN t_iteration i ON r.iteration_id = i.iteration_id
                WHERE r.project_id = ? AND r.is_delete = 0 AND r.is_delayed = 1";

        $bindings = [$params['project_id'], $params['project_id']];
        if (!empty($params['iteration_id'])) {
            $sql .= " AND r.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }
        if (!empty($params['node_stage'])) {
            $sql .= " AND ttl_stage.tag_code = ?";
            $bindings[] = $params['node_stage'];
        } else {
            $sql .= " AND ttl_stage.tag_code = ?";
            $bindings[] = self::NODE_STAGE_TESTING;
        }
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $sql .= " AND r.actual_end_time BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        $sql .= " ORDER BY r.actual_end_time DESC";

        $rawResult = Db::query($sql, $bindings);
        $displayHeaders = ['所属项目', '迭代', '迭代节点', '节点阶段', '预估开始时间', '预估结束时间', '实际开始时间', '实际结束时间', '节点负责人', '组长', '是否延期'];

        return $this->processRecordsWithLinks($rawResult, $displayHeaders);
    }

/**
     * 场景11: 获取工时填报记录
     * @param array $params
     * @return array
     * @throws DbException
     */
    public function getWorkHoursRecords(array $params): array
    {
        $sql = "SELECT
                    wh.create_by_name AS '花费人',
                    wh.working_hours AS '工作量',
                    '人时' AS '工作量单位',
                    wh.work_date AS '花费日期',
                    wh.create_at AS '填写日期',
                    i.iteration_name AS '迭代',
                    p.project_name AS '所属项目',
                    CASE JSON_UNQUOTE(wi.extends->'$.cnt_type')
                        WHEN '1' THEN '需求'
                        WHEN '2' THEN '任务'
                        WHEN '3' THEN '缺陷'
                        ELSE '未知'
                    END AS '对象类型',
                    wh.cnt_id AS 'ID',
                    JSON_UNQUOTE(wi.extends->'$.title') AS '标题',
                    wh.remark AS '备注',
                    p.project_id AS _link_id_project,
                    i.iteration_id AS _link_id_iteration
                FROM
                    t_work_hours wh
                LEFT JOIN t_work_items wi ON wh.cnt_id = wi.cnt_id
                LEFT JOIN t_project p ON JSON_UNQUOTE(wi.extends->'$.project_id') = p.project_id
                LEFT JOIN t_iteration i ON JSON_UNQUOTE(wi.extends->'$.iteration_id') = i.iteration_id
                WHERE wh.is_delete = 0 AND wh.type = 2"; // type=2 for actual work hours

        $bindings = [];
        $projectId = $params['project_id'] ?? null;
        if ($projectId) {
            $sql .= " AND JSON_UNQUOTE(wi.extends->'$.project_id') = ?";
            $bindings[] = $projectId;
        }
        if (!empty($params['iteration_id'])) {
            $sql .= " AND JSON_UNQUOTE(wi.extends->'$.iteration_id') = ?";
            $bindings[] = $params['iteration_id'];
        }
        if (!empty($params['user_name'])) {
            $sql .= " AND wh.create_by_name LIKE ?";
            $bindings[] = "%{$params['user_name']}%";
        }
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            // Assuming work_date is stored as an integer like 20250301
            $sql .= " AND wh.work_date BETWEEN ? AND ?";
            $bindings[] = str_replace('-', '', $params['start_date']);
            $bindings[] = str_replace('-', '', $params['end_date']);
        }
        $sql .= " ORDER BY wh.work_date ASC, wh.create_at ASC";

        $rawResult = Db::query($sql, $bindings);
        $displayHeaders = ['花费人', '工作量', '工作量单位', '花费日期', '填写日期', '迭代', '所属项目', '对象类型', 'ID', '标题', '备注'];

        $processedRows = [];
        if (!empty($rawResult)) {
            foreach ($rawResult as $dbRow) {
                $outputRow = [];
                // Format work_date from YYYYMMDD to YYYY-MM-DD
                if (isset($dbRow['花费日期'])) {
                    $dateStr = (string)$dbRow['花费日期'];
                    if (strlen($dateStr) === 8) {
                        $dbRow['花费日期'] = substr($dateStr, 0, 4) . '-' . substr($dateStr, 4, 2) . '-' . substr($dateStr, 6, 2);
                    }
                }

                foreach ($displayHeaders as $header) {
                    $cellValue = $dbRow[$header] ?? null;
                    if ($header === '所属项目' && !empty($dbRow['_link_id_project']) && !empty($dbRow['所属项目'])) {
                        $outputRow[] = ['text' => $cellValue, 'url' => sprintf(self::FORMAT_URL_PROJECT, $dbRow['_link_id_project'], rawurlencode($dbRow['所属项目']), $dbRow['_link_id_project'])];
                    } elseif ($header === '迭代' && !empty($dbRow['_link_id_iteration']) && !empty($dbRow['_link_id_project']) && !empty($dbRow['所属项目'])) {
                        $outputRow[] = ['text' => $cellValue, 'url' => sprintf(self::FORMAT_URL_ITERATION, $dbRow['_link_id_project'], rawurlencode($dbRow['所属项目']), $dbRow['_link_id_project'], $dbRow['_link_id_iteration'])];
                    } elseif ($header === '标题' && !empty($dbRow['ID']) && !empty($dbRow['_link_id_project']) && !empty($dbRow['对象类型'])) {
                        $itemType = $this->mapObjectTypeToConstant($dbRow['对象类型']);
                        $outputRow[] = ['text' => $cellValue, 'url' => sprintf(self::FORMAT_URL_ITEM, $itemType, $dbRow['_link_id_project'], $dbRow['ID'], $dbRow['_link_id_project'])];
                    } else {
                        $outputRow[] = $cellValue;
                    }
                }
                $processedRows[] = $outputRow;
            }
        }

        return array_merge([$displayHeaders], $processedRows);
    }

    /**
     * Maps object type string to a defined constant.
     * @param string|null $objectType
     * @return string
     */
    private function mapObjectTypeToConstant(?string $objectType): string
    {
        switch (strtolower($objectType ?? '')) {
            case 'task':
            case '任务':
                return self::ITEM_TYPE_TASK;
            case 'defect':
            case '缺陷':
                return self::ITEM_TYPE_DEFECT;
            case 'demand':
            case '需求':
            default:
                return self::ITEM_TYPE_DEMAND;
        }
    }
    // --- Private Helper Methods ---

    /**
     * 构建场景5 (Bug统计) 的主查询语句和绑定参数
     * @param array $params
     * @return array
     */
    private function _buildBugStatisticsQuery(array $params): array
    {
        $sql = "SELECT bs.id,
                       bs.project_id AS _link_id_project,
                       bs.iteration_id AS _link_id_iteration,
                       bs.date AS '统计日期',
                       bs.time AS '统计时间点',
                       u_handler.user_name AS '处理人姓名',
                       bs.user_id AS '处理人ID',
                       bs.count,
                       p.project_name AS '所属项目',
                       CASE bs.statistics
                           WHEN 1 THEN '今日'
                           WHEN 2 THEN '昨日'
                           ELSE CONCAT('未知统计方式 (', bs.statistics, ')')
                       END AS '统计方式',
                       bs.date AS '统计范围（日期）',
                       bs.time AS '统计截止时间',
                       bs.count AS '缺陷未清数量'
                FROM t_bug_statistics bs
                LEFT JOIN t_project p ON bs.project_id = p.project_id
                LEFT JOIN t_project_user u_handler ON bs.user_id = u_handler.user_id AND u_handler.project_id = bs.project_id
                WHERE bs.is_delete = 0 AND bs.count > 0";

        $bindings = [];
        if (!empty($params['project_id'])) {
            $sql .= " AND bs.project_id = ?";
            $bindings[] = $params['project_id'];
        }
        if (!empty($params['date'])) {
            $sql .= " AND bs.date = ?";
            $bindings[] = $params['date'];
        }
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $sql .= " AND bs.date BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }
        if (!empty($params['user_id'])) {
            $sql .= " AND bs.user_id = ?";
            $bindings[] = $params['user_id'];
        }
        if (!empty($params['statistics'])) {
            $sql .= " AND bs.statistics = ?";
            $bindings[] = $params['statistics'];
        }
        $sql .= " ORDER BY bs.date DESC, bs.time DESC, u_handler.user_name";

        return [$sql, $bindings];
    }

    /**
     * 为Bug统计结果批量获取关联的缺陷详情 (ID和标题)
     * @param array $rawResult
     * @return array
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    private function _getGroupedBugDetailsForStats(array $rawResult): array
    {
        $statRowConditions = [];
        foreach ($rawResult as $dbRow) {
            $statisticsValue = 0;
            if ($dbRow['统计方式'] === '今日') $statisticsValue = 1;
            elseif ($dbRow['统计方式'] === '昨日') $statisticsValue = 2;

            $statRowConditions[] = [
                ['bsd.project_id', '=', $dbRow['_link_id_project']],
                ['bsd.iteration_id', '=', $dbRow['_link_id_iteration']],
                ['bsd.user_id', '=', $dbRow['处理人ID']],
                ['bsd.date', '=', $dbRow['统计日期']],
                ['bsd.time', '=', $dbRow['统计时间点']],
                ['bsd.statistics', '=', $statisticsValue],
                ['bsd.is_delete', '=', 0]
            ];
        }

        if (empty($statRowConditions)) {
            return [];
        }

        $query = Db::table('t_bug_statistics_detail')->alias('bsd');
        $query->where(function ($q) use ($statRowConditions) {
            foreach ($statRowConditions as $conditionGroup) {
                $q->whereOr(fn($sq) => $sq->where($conditionGroup));
            }
        });
        $allBugDetailRecords = $query->field(['project_id', 'iteration_id', 'user_id', 'date', 'time', 'statistics', 'bug_id'])->select()->toArray();

        $uniqueBugIds = array_unique(array_column($allBugDetailRecords, 'bug_id'));
        if (empty($uniqueBugIds)) {
            return [];
        }

        $workItems = Db::table('t_work_items')
            ->whereIn('cnt_id', $uniqueBugIds)
            ->where('is_delete', 0)
            ->field(['cnt_id', "JSON_UNQUOTE(JSON_EXTRACT(extends, '$.title')) AS bug_title"])
            ->select()->toArray();
        $bugTitlesMap = array_column($workItems, 'bug_title', 'cnt_id');

        $groupedBugDetails = [];
        foreach ($allBugDetailRecords as $detail) {
            $mapKey = "{$detail['project_id']}-{$detail['iteration_id']}-{$detail['user_id']}-{$detail['date']}-{$detail['time']}-{$detail['statistics']}";
            if (!isset($groupedBugDetails[$mapKey])) {
                $groupedBugDetails[$mapKey] = [];
            }
            $groupedBugDetails[$mapKey][] = [
                'id' => $detail['bug_id'],
                'title' => $bugTitlesMap[$detail['bug_id']] ?? 'N/A'
            ];
        }
        return $groupedBugDetails;
    }

    /**
     * 构建场景5 (Bug统计) 的最终输出行
     * @param array $rawResult
     * @param array $groupedBugDetails
     * @param int $maxBugDetailsCount
     * @param array $baseDisplayHeaders
     * @return array
     */
    private function _buildBugStatisticsFinalRows(array $rawResult, array $groupedBugDetails, int $maxBugDetailsCount, array $baseDisplayHeaders): array
    {
        $processedRows = [];
        foreach ($rawResult as $dbRow) {
            $outputRow = [];
            foreach ($baseDisplayHeaders as $header) {
                $cellValue = $dbRow[$header] ?? null;
                if ($header === '所属项目' && isset($dbRow['_link_id_project'], $dbRow['所属项目'])) {
                    $outputRow[] = ['text' => $cellValue, 'url' => sprintf(self::FORMAT_URL_PROJECT, $dbRow['_link_id_project'], rawurlencode($dbRow['所属项目']), $dbRow['_link_id_project'])];
                } else {
                    $outputRow[] = $cellValue;
                }
            }

            $statisticsValue = 0;
            if ($dbRow['统计方式'] === '今日') $statisticsValue = 1;
            elseif ($dbRow['统计方式'] === '昨日') $statisticsValue = 2;
            $compositeKey = "{$dbRow['_link_id_project']}-{$dbRow['_link_id_iteration']}-{$dbRow['处理人ID']}-{$dbRow['统计日期']}-{$dbRow['统计时间点']}-{$statisticsValue}";

            $bugDetailsForThisRow = $groupedBugDetails[$compositeKey] ?? [];
            for ($i = 0; $i < $maxBugDetailsCount; $i++) {
                if (isset($bugDetailsForThisRow[$i])) {
                    $bug = $bugDetailsForThisRow[$i];
                    $outputRow[] = ['text' => $bug['title'], 'url' => sprintf(self::FORMAT_URL_ITEM, self::ITEM_TYPE_DEFECT, $dbRow['_link_id_project'], $bug['id'], $dbRow['_link_id_project'])];
                } else {
                    $outputRow[] = '';
                }
            }
            $processedRows[] = $outputRow;
        }
        return $processedRows;
    }

    /**
     * 通用记录处理器，用于将数据库行转换为带链接的输出格式。
     * @param array $rawResult 原始数据库查询结果
     * @param array $displayHeaders 要显示的列标题
     * @return array 包含表头和处理后数据行的数组
     */
    private function processRecordsWithLinks(array $rawResult, array $displayHeaders): array
    {
        if (empty($rawResult)) {
            return [$displayHeaders];
        }

        $processedRows = [];
        foreach ($rawResult as $dbRow) {
            $outputRow = [];
            foreach ($displayHeaders as $header) {
                $cellValue = $dbRow[$header] ?? null;

                if ($header === '所属项目' && isset($dbRow['_link_id_project'], $dbRow['所属项目'])) {
                    $outputRow[] = ['text' => $cellValue, 'url' => sprintf(self::FORMAT_URL_PROJECT, $dbRow['_link_id_project'], rawurlencode($dbRow['所属项目']), $dbRow['_link_id_project'])];
                } elseif (($header === '迭代' || $header === '所属迭代') && isset($dbRow['_link_id_iteration'], $dbRow['_link_id_project'], $dbRow['所属项目'])) {
                    $outputRow[] = ['text' => $cellValue, 'url' => sprintf(self::FORMAT_URL_ITERATION, $dbRow['_link_id_project'], rawurlencode($dbRow['所属项目']), $dbRow['_link_id_project'], $dbRow['_link_id_iteration'])];
                } elseif ($header === '需求标题' && isset($dbRow['需求ID'], $dbRow['_link_id_project'])) {
                    $outputRow[] = ['text' => $cellValue, 'url' => sprintf(self::FORMAT_URL_ITEM, self::ITEM_TYPE_DEMAND, $dbRow['_link_id_project'], $dbRow['需求ID'], $dbRow['_link_id_project'])];
                } elseif ($header === '任务标题' && isset($dbRow['任务ID'], $dbRow['_link_id_project'])) {
                    $outputRow[] = ['text' => $cellValue, 'url' => sprintf(self::FORMAT_URL_ITEM, self::ITEM_TYPE_TASK, $dbRow['_link_id_project'], $dbRow['任务ID'], $dbRow['_link_id_project'])];
                } else {
                    $outputRow[] = $cellValue;
                }
            }
            $processedRows[] = $outputRow;
        }
        return array_merge([$displayHeaders], $processedRows);
    }
}
