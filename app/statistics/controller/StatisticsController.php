<?php
/**
 * Desc 统计查询控制器
 * User AI Assistant
 * Date 2024/12/20
 */

namespace app\statistics\controller;

use app\statistics\logic\StatisticsLogic;
use excel_utils\Hanlder;
use resp\Result;
use think\Request;
use think\response\Json;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class StatisticsController
{
    private StatisticsLogic $logic;

    public function __construct()
    {
        $this->logic = new StatisticsLogic();
    }

    /**
     * 场景1: 查询需求变更记录
     * @param Request $request
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDemandChangeRecords(Request $request)
    {
        $params = $request->get([
            'project_id',
            'iteration_id',
            'start_date',
            'end_date',
            'change_reason'
        ]);

        $result = $this->logic->getDemandChangeRecords($params);

        $fileName = '场景1-需求变更记录_' . date('Y-m-d_H-i-s');
        return Hanlder::outputFile($result, $fileName);
    }

    /**
     * 场景2: 查询新增需求记录
     * @param Request $request
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getNewDemandRecords(Request $request)
    {
        $params = $request->get([
            'project_id',
            'iteration_id',
            'start_date',
            'end_date'
        ]);

        $result = $this->logic->getNewDemandRecords($params);

        $fileName = '场景2-新增需求记录_' . date('Y-m-d_H-i-s');
        return Hanlder::outputFile($result, $fileName);
    }

    /**
     * 场景3/7/10: 查询需求评审记录（场景3、7、10使用相同SQL）
     * @param Request $request
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDemandReviewRecords(Request $request)
    {
        $params = $request->get([
            'project_id',
            'type_code',
            'start_date',
            'end_date'
        ]);

        $result = $this->logic->getDemandReviewRecords($params);

        $fileName = '场景3-7-10-需求评审记录_' . date('Y-m-d_H-i-s');
        return Hanlder::outputFile($result, $fileName);
    }

    /**
     * 场景4: 查询开发延期记录
     * @param Request $request
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDevelopmentDelayRecords(Request $request)
    {
        $params = $request->get([
            'project_id',
            'iteration_id',
            'node_stage',
            'start_date',
            'end_date'
        ]);

        $result = $this->logic->getDevelopmentDelayRecords($params);

        $fileName = '场景4-开发延期记录_' . date('Y-m-d_H-i-s');
        return Hanlder::outputFile($result, $fileName);
    }

    /**
     * 场景5-字段1: 查询Bug统计记录（统计时间、处理人）
     * @param Request $request
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getBugStatisticsRecords(Request $request)
    {
        $params = $request->get([
            'project_id',
            'date',
            'start_date',
            'end_date',
            'user_id',
            'statistics'
        ]);

        $result = $this->logic->getBugStatisticsRecords($params);

        $fileName = '场景5-字段1-Bug统计记录_' . date('Y-m-d_H-i-s');
        return Hanlder::outputFile($result, $fileName);
    }

    /**
     * 场景5-字段2: 查询工时填报记录
     * @param Request $request
     * @return mixed
     * @throws DbException
     */
    public function getBugStatisticsRecordsField2(Request $request)
    {
        $params = $request->get([
            'project_id',
            'iteration_id',
            'user_name',
            'start_date',
            'end_date'
        ]);

        $result = $this->logic->getWorkHoursRecords($params);

        $fileName = '场景5-字段2-工时填报记录_' . date('Y-m-d_H-i-s');
        return Hanlder::outputFile($result, $fileName);
    }

    /**
     * 场景6: 查询工时评估打回记录
     * @param Request $request
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getWorkHoursRejectionRecords(Request $request)
    {
        $params = $request->get([
            'submitter_uid',
            'project_id',
            'start_date',
            'end_date'
        ]);

        $result = $this->logic->getWorkHoursRejectionRecords($params);

        $fileName = '场景6-工时评估打回记录_' . date('Y-m-d_H-i-s');
        return Hanlder::outputFile($result, $fileName);
    }

    /**
     * 场景8-字段1: 迭代级别统计
     * @param Request $request
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getScene8Field1Statistics(Request $request)
    {
        $params = $request->get([
            'project_id',
            'iteration_id',
            'start_date',
            'end_date'
        ]);

        $result = $this->logic->getScene8Field1Statistics($params);

        $fileName = '场景8-字段1-迭代级别统计_' . date('Y-m-d_H-i-s');
        return Hanlder::outputFile($result, $fileName);
    }

    /**
     * 场景8-字段2: 迭代节点级别统计
     * @param Request $request
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getScene8Field2Statistics(Request $request)
    {
        $params = $request->get([
            'project_id',
            'iteration_id',
            'start_date',
            'end_date'
        ]);

        $result = $this->logic->getScene8Field2Statistics($params);

        $fileName = '场景8-字段2-迭代节点级别统计_' . date('Y-m-d_H-i-s');
        return Hanlder::outputFile($result, $fileName);
    }

    /**
     * 场景9: 测试延期记录查询（带用户名解析）
     * @param Request $request
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getTestDelayRecords(Request $request)
    {
        $params = $request->get([
            'project_id',
            'iteration_id',
            'node_stage',
            'start_date',
            'end_date'
        ]);

        $result = $this->logic->getTestDelayRecords($params);

        $fileName = '场景9-测试延期记录_' . date('Y-m-d_H-i-s');
        return Hanlder::outputFile($result, $fileName);
    }


}
