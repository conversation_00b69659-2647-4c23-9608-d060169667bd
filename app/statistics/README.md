# 统计查询模块 API 文档

## 概述
本模块按照SQL注释中的场景编号组织，提供多个统计查询场景的API接口。某些场景有多个版本或字段，直接使用SQL查询，支持参数化查询以防止SQL注入。

**重要说明：所有接口都返回Excel文件下载，不再返回JSON数据。**

## API接口列表（按场景编号组织）

### 场景1: 需求变更记录查询
**接口地址：** `GET /statistics/scene1/demandChangeRecords`

**参数说明：**
- `project_id` (必填): 项目ID
- `iteration_id` (可选): 迭代ID
- `change_reason` (可选): 变更原因（模糊查询）
- `start_date` (可选): 开始时间
- `end_date` (可选): 结束时间

**示例请求：**
```
GET /statistics/scene1/demandChangeRecords?project_id=2744&iteration_id=123&change_reason=需求变更&start_date=2024-01-01&end_date=2024-12-31
```

**返回：** Excel文件下载（文件名：场景1-需求变更记录_2024-12-20_14-30-25.xlsx）

### 场景2: 新增需求记录查询
**接口地址：** `GET /statistics/scene2/newDemandRecords`

**参数说明：**
- `project_id` (必填): 项目ID
- `iteration_id` (可选): 迭代ID
- `start_date` (可选): 开始时间
- `end_date` (可选): 结束时间

---

### 场景3/7/10: 需求评审记录查询（相同SQL）
**接口地址：** `GET /statistics/scene3-7-10/demandReviewRecords`

**参数说明：**
- `project_id` (必填): 项目ID
- `type_code` (可选): 会议类型代码
  - `tag_49809163548077666888096`: 需求评审
  - `tag_49809164358291370933180`: 反讲
  - `tag_49809164810772888159260`: 用例
- `start_date` (可选): 开始日期
- `end_date` (可选): 结束日期

**说明：** 场景3、7、10使用相同的SQL查询，支持按创建时间进行日期区间筛选

---

### 场景4: 开发延期记录查询（带用户名解析）
**接口地址：** `GET /statistics/scene4/developmentDelayRecords`

**参数说明：**
- `project_id` (必填): 项目ID
- `iteration_id` (可选): 迭代ID
- `node_stage` (可选): 节点阶段代码（默认：tag_49809162008138929929038）
- `start_date` (可选): 开始时间
- `end_date` (可选): 结束时间

**特点：** 使用CTE和JSON_TABLE从JSON字段解析节点负责人用户名

---

### 场景5-字段1: Bug统计记录查询（统计时间、处理人）
**接口地址：** `GET /statistics/scene5/bugStatisticsField1`

**参数说明：**
- `project_id` (必填): 项目ID
- `date` (可选): 统计日期（单个日期筛选）
- `start_date` (可选): 开始日期（日期区间筛选）
- `end_date` (可选): 结束日期（日期区间筛选）
- `user_id` (可选): 用户ID
- `statistics` (可选): 统计方式（1=今日，2=昨日）

### 场景5-字段2: Bug统计记录查询（所属项目、统计方式、统计范围等）
**接口地址：** `GET /statistics/scene5/bugStatisticsField2`

**参数说明：** 同字段1

**说明：** 场景5有两个字段版本，SQL相同但业务含义不同。支持单个日期筛选和日期区间筛选两种方式

---

### 场景6: 工时评估打回记录查询
**接口地址：** `GET /statistics/scene6/workHoursRejectionRecords`

**参数说明：**
- `submitter_uid` (必填): 提交人用户ID（业务架构师）
- `project_id` (可选): 项目ID
- `start_date` (可选): 开始时间
- `end_date` (可选): 结束时间

---

### 场景8-字段1: 迭代级别统计查询
**接口地址：** `GET /statistics/scene8/field1/iterationLevelStatistics`

**参数说明：**
- `project_id` (必填): 项目ID
- `iteration_id` (可选): 迭代ID

**返回字段：**
- 所属项目、迭代、需求变更次数、需求新增次数、迭代预估周期、迭代实际周期、是否延期

### 场景8-字段2: 迭代节点级别统计查询
**接口地址：** `GET /statistics/scene8/field2/iterationNodeLevelStatistics`

**参数说明：**
- `project_id` (必填): 项目ID
- `iteration_id` (可选): 迭代ID

**返回字段：**
- 迭代id、迭代节点、阶段、需求变更次数、需求新增次数、所属迭代、所属项目

**特点：** 阶段信息从 `t_iteration_process_node.node_data` 的JSON字段获取

---

### 场景9: 测试延期记录查询（带用户名解析）
**接口地址：** `GET /statistics/scene9/testDelayRecords`

**参数说明：**
- `project_id` (必填): 项目ID
- `iteration_id` (可选): 迭代ID
- `node_stage` (可选): 节点阶段代码（默认：tag_49809162267640520716923）
- `start_date` (可选): 开始时间
- `end_date` (可选): 结束时间

**特点：** 使用CTE和JSON_TABLE从JSON字段解析节点负责人用户名

## 技术特点

1. **Excel文件导出**：使用 `extend/excel_utils/Hanlder.php` 工具导出Excel文件
2. **正确的数据格式**：第一个元素是列名数组，后面是数据行，符合Excel工具要求
3. **直接SQL查询**：按要求使用原生SQL，未转换为ORM操作
4. **参数化查询**：使用占位符防止SQL注入
5. **中文字段别名**：便于前端直接展示
6. **复杂查询支持**：支持CTE、JSON解析、用户名关联等高级功能
7. **灵活参数传递**：支持必填和可选参数组合，无格式化限制
8. **空数据处理**：当查询无结果时，返回包含列名和"暂无数据"的Excel文件

## 返回格式

所有接口都返回Excel文件下载，响应头包含：
- `Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- `Content-Disposition: attachment; filename="场景X-XXX_YYYY-MM-DD_HH-mm-ss.xlsx"`

**文件名格式说明：**
- 基础名称：场景编号-功能描述
- 时间后缀：年-月-日_时-分-秒
- 示例：`场景1-需求变更记录_2024-12-20_14-30-25.xlsx`

## Apifox导入

项目根目录下的 `app/statistics/apifox-openapi.json` 文件可以直接导入到Apifox中，包含所有接口的完整定义。

**导入步骤：**
1. 打开Apifox
2. 选择"导入" -> "导入数据"
3. 选择"OpenAPI/Swagger"格式
4. 上传 `apifox-openapi.json` 文件
5. 完成导入

## 注意事项

- 所有接口都需要认证
- 日期格式：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss
- 查询结果按时间倒序排列
- 场景4和场景9使用了复杂的CTE和JSON解析功能
- 如果查询无数据，会返回包含"暂无数据"的Excel文件
