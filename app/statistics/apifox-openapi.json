{"openapi": "3.0.0", "info": {"title": "统计查询模块API", "description": "按场景编号组织的统计查询接口，支持Excel文件导出", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "开发环境"}], "paths": {"/statistics/scene1/demandChangeRecords": {"get": {"tags": ["场景1"], "summary": "需求变更记录查询", "parameters": [{"name": "project_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 2744}}, {"name": "iteration_id", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "change_reason", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Excel文件下载", "content": {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/statistics/scene2/newDemandRecords": {"get": {"tags": ["场景2"], "summary": "新增需求记录查询", "parameters": [{"name": "project_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 23}}, {"name": "iteration_id", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Excel文件下载", "content": {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/statistics/scene3-7-10/demandReviewRecords": {"get": {"tags": ["场景3/7/10"], "summary": "需求评审记录查询", "parameters": [{"name": "project_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 23}}, {"name": "type_code", "in": "query", "required": false, "schema": {"type": "string", "enum": ["tag_49809163548077666888096", "tag_49809164358291370933180", "tag_49809164810772888159260"]}}, {"name": "start_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Excel文件下载", "content": {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/statistics/scene4/developmentDelayRecords": {"get": {"tags": ["场景4"], "summary": "开发延期记录查询", "parameters": [{"name": "project_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 23}}, {"name": "iteration_id", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "node_stage", "in": "query", "required": false, "schema": {"type": "string", "example": "tag_49809162008138929929038"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Excel文件下载", "content": {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/statistics/scene5/bugStatisticsField1": {"get": {"tags": ["场景5"], "summary": "Bug统计记录查询-字段1", "parameters": [{"name": "project_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 23}}, {"name": "date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "statistics", "in": "query", "required": false, "schema": {"type": "integer", "enum": [1, 2]}}], "responses": {"200": {"description": "Excel文件下载", "content": {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/statistics/scene5/bugStatisticsField2": {"get": {"tags": ["场景5"], "summary": "Bug统计记录查询-字段2", "parameters": [{"name": "project_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 23}}, {"name": "date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "statistics", "in": "query", "required": false, "schema": {"type": "integer", "enum": [1, 2]}}], "responses": {"200": {"description": "Excel文件下载", "content": {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/statistics/scene6/workHoursRejectionRecords": {"get": {"tags": ["场景6"], "summary": "工时评估打回记录查询", "parameters": [{"name": "submitter_uid", "in": "query", "required": true, "schema": {"type": "integer", "example": 6387}}, {"name": "project_id", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Excel文件下载", "content": {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/statistics/scene8/field1/iterationLevelStatistics": {"get": {"tags": ["场景8"], "summary": "迭代级别统计查询", "parameters": [{"name": "project_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 23}}, {"name": "iteration_id", "in": "query", "required": false, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Excel文件下载", "content": {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/statistics/scene8/field2/iterationNodeLevelStatistics": {"get": {"tags": ["场景8"], "summary": "迭代节点级别统计查询", "parameters": [{"name": "project_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 2964}}, {"name": "iteration_id", "in": "query", "required": false, "schema": {"type": "integer", "example": 747}}], "responses": {"200": {"description": "Excel文件下载", "content": {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/statistics/scene9/testDelayRecords": {"get": {"tags": ["场景9"], "summary": "测试延期记录查询", "parameters": [{"name": "project_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 23}}, {"name": "iteration_id", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "node_stage", "in": "query", "required": false, "schema": {"type": "string", "example": "tag_49809162267640520716923"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Excel文件下载", "content": {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {"schema": {"type": "string", "format": "binary"}}}}}}}}}