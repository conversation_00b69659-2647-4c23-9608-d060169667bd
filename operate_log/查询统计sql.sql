-- =================================================================================
-- SQL查询详细数据集合
-- 注意: 请仔细检查并替换所有占位符、JSON路径、表名、字段名和条件值。
-- =================================================================================

-- 场景1: 开发测试过程中需求变更的次数
-- 查询需求变更记录的详细信息
SELECT p.project_name      AS "所属项目",
       wicr.cnt_id         AS "需求ID",
       wicr.cnt_title      AS "需求标题",
       wicr.handler_uid    AS "需求负责人id", -- 或从 t_work_items.extends 获取
       wicr.handler_name   AS "需求负责人",   -- 或从 t_work_items.extends 获取
       i.iteration_name    AS "迭代",
       wicr.node_name      AS "迭代节点",
       wicr.node_stage     AS "迭代节点阶段",
       wicr.change_reason  AS "变更原因",
       wicr.submitter_name AS "提交人",
       wicr.submit_at      AS "提交时间"      -- 或 wicr.create_at
FROM
    #     t_iteration_demand_relation_records
    t_work_items_change_records wicr
    LEFT JOIN t_project p ON wicr.project_id = p.project_id
    LEFT JOIN t_iteration i ON wicr.iteration_id = i.iteration_id
-- LEFT JOIN t_user需求负责人表 u_handler ON wicr.handler_uid = u_handler.user_id
-- LEFT JOIN t_user提交人表 u_submitter ON wicr.submitter_uid = u_submitter.user_id
WHERE wicr.project_id = 2744
    #     AND wicr.iteration_id = your_iteration_id -- 可选
    #     AND (wicr.change_reason LIKE '%需求变更%' OR wicr.change_reason LIKE '%some_other_demand_change_indicator%') -- 调整此条件
-- AND wicr.submit_at BETWEEN 'start_date' AND 'end_date'
ORDER BY wicr.submit_at DESC;

-- =================================================================================

-- 场景2: 开发测试过程中增加新需求的次数 (已更新为使用 t_iteration_demand_relation_records)
-- 查询通过 t_iteration_demand_relation_records 表增加的新需求记录的详细信息
SELECT idrr.project_id     AS "所属项目",
       idrr.cnt_id         AS "需求ID",
       idrr.cnt_title      AS "需求标题",
       idrr.operator_name  AS "操作人",
       idrr.iteration_name AS "迭代",
       idrr.node_name      AS "迭代节点",     -- 注意：此表直接记录了关联时的节点信息
       ttl_stage.tag_name  AS "迭代节点阶段", -- 注意：此表直接记录了关联时的节点阶段信息
       idrr.operation_time AS "操作时间"
FROM t_iteration_demand_relation_records idrr
         left join t_tag_library ttl_stage
                   ON idrr.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1 -- 替换 your_node_stage_tag_group_type_id
-- LEFT JOIN t_project p ON idrr.project_id = p.project_id -- project_name 已在 idrr 中
-- LEFT JOIN t_iteration i ON idrr.iteration_id = i.iteration_id -- iteration_name 已在 idrr 中
-- LEFT JOIN t_work_items wi ON idrr.cnt_id = wi.cnt_id -- cnt_title 已在 idrr 中
-- LEFT JOIN t_user u_op ON idrr.operator_uid = u_op.user_id -- operator_name 已在 idrr 中
WHERE idrr.project_id = 23    -- 替换为实际项目ID
    #     AND idrr.iteration_id = your_iteration_id -- 替换为实际迭代ID (可选)
  AND idrr.operation_type = 1 -- 假设 operation_type = 1 代表 "新增需求到迭代"
-- AND idrr.operation_time BETWEEN 'start_date' AND 'end_date'; -- 可选时间范围
ORDER BY idrr.operation_time DESC;

-- =================================================================================


-- 场景3: 需求评审次数 (查询单次评审的详细信息)     场景 7 、10

SELECT mcc.change_id,
       p.project_name                                           AS "所属项目",
       mcc.task_id                                              AS "任务ID",
       mcc.task_title                                           AS "任务标题",
       GROUP_CONCAT(DISTINCT nr_user.user_name SEPARATOR ', ')  AS "节点负责人",
       GROUP_CONCAT(DISTINCT spk_user.user_name SEPARATOR ', ') AS "主讲人",
       ttl.tag_name                                             AS "会议类型名称", -- 从 t_tag_library 获取
       ttl.tag_code                                             AS "会议类型代码"  -- 从 t_tag_library 获取
FROM t_meeting_collection_change mcc
         JOIN
     t_meeting_collection_change_type mcct ON mcc.change_id = mcct.change_id
         JOIN
     t_tag_library ttl ON mcct.type_code = ttl.tag_code AND ttl.group_type = 2 -- 替换 your_meeting_tag_group_type_id
         LEFT JOIN
     t_project p ON mcc.project_id = p.project_id
         LEFT JOIN
     t_meeting_collection_change_user mccu_nr ON mcc.change_id = mccu_nr.change_id AND mccu_nr.user_type = 1
         LEFT JOIN
     t_project_user nr_user ON mccu_nr.user_id = nr_user.user_id
         LEFT JOIN
     t_meeting_collection_change_user mccu_spk ON mcc.change_id = mccu_spk.change_id AND mccu_spk.user_type = 2
         LEFT JOIN
     t_project_user spk_user ON mccu_spk.user_id = spk_user.user_id
WHERE mcc.project_id = 23                            -- 您的示例 project_id
  and mcc.is_delete = 0
  AND mcct.type_code = 'tag_49809164810772888159260' -- 您的示例 type_code
GROUP BY mcc.change_id, p.project_name, mcc.task_id, mcc.task_title, ttl.tag_name, ttl.tag_code, mcc.create_at
ORDER BY mcc.create_at DESC;


# tag_49809163548077666888096  需求评审
# tag_49809164358291370933180  反讲
# tag_49809164810772888159260  用例


-- =================================================================================

-- 场景4: 迭代中，开发延期次数 (查询延期的开发节点的详细信息)

SELECT p.project_name                                     AS "所属项目",
       i.iteration_name                                   AS "迭代",
       ipn.node_name                                      AS "迭代节点",
       ttl_stage.tag_name                                 AS "节点阶段", -- 从 t_tag_library 获取
       r.estimate_start_time                              AS "预估开始时间",
       r.estimate_end_time                                AS "预估结束时间",
       r.actual_start_time                                AS "实际开始时间",
       r.actual_end_time                                  AS "实际结束时间",
       r.node_owner_name                                  AS "节点负责人",
       r.group_leader_id                                  AS "组长1",
       r.group_leader_name                                AS "组长",
       CASE WHEN r.is_delayed = 1 THEN '是' ELSE '否' END AS "是否延期"
FROM t_iteration_process_node_end_record r
         JOIN
     t_iteration_process_node ipn ON r.iteration_process_node_id = ipn.iteration_process_node_id
         LEFT JOIN
     t_tag_library ttl_stage
     ON r.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1 -- 替换 your_node_stage_tag_group_type_id
         LEFT JOIN
     t_project p ON r.project_id = p.project_id
         LEFT JOIN
     t_iteration i ON r.iteration_id = i.iteration_id
WHERE r.project_id = 23                                    -- 替换
    #     AND r.iteration_id = your_iteration_id -- 替换 (可选)
  AND r.is_delayed = 1
  AND (ttl_stage.tag_code = 'tag_49809162008138929929038') -- 筛选开发相关的节点 (现在可以用 ttl_stage.tag_name)
ORDER BY r.actual_end_time DESC;



-- =================================================================================
-- =================================================================================
-- 场景5 - 字段1: 统计时间、处理人（仅需开发人员）
-- =================================================================================
SELECT bs.id,
       bs.project_id,
       bs.iteration_id,
       bs.date             AS "统计日期",
       bs.time             AS "统计时间点",
       u_handler.user_name AS "处理人姓名",
       bs.user_id          AS "处理人ID",
       bs.count
-- bs.statistics AS "日清状态代码", -- 可选，如果需要显示原始状态码
-- CASE bs.statistics
--     WHEN 1 THEN '未达标'
--     WHEN 0 THEN '达标'
--     ELSE '未知'
-- END AS "日清状态描述" -- 可选，具体值需确认
FROM t_bug_statistics bs
         LEFT JOIN
     t_project_user u_handler ON bs.user_id = u_handler.user_id and u_handler.project_id = 23 -- 假设用户表为 t_project_user
WHERE bs.project_id = 23 -- 替换 your_project_id
    #   and date = '2025-05-22'
  and bs.is_delete = 0
#     AND u_handler.user_role = 'developer' -- 假设 t_project_user 有 user_role 字段，替换 'developer'
#     AND bs.date = 'specific_date' -- 替换为特定统计日期
  -- AND bs.time = 'specific_time' -- 可选，如果需要特定时间点
#     AND (bs.statistics = 1 OR bs.count > 0) -- 可选，如果只看未达标的记录
  AND bs.count > 0      -- 可选，如果只看未达标的记录
ORDER BY bs.date, bs.time, u_handler.user_name;
# 63  23
-- =================================================================================
-- 场景5 - 字段2: 所属项目、统计方式、统计范围（日期）、统计截止时间、缺陷未清数量、处理人
-- (已更新 "统计方式" 字段，并调整了对 bs.statistics 的理解)
-- =================================================================================
SELECT bs.id,
       bs.iteration_id,
       p.project_name      AS "所属项目",
       CASE bs.statistics
           WHEN 1 THEN '今日'
           WHEN 2 THEN '昨日'
           ELSE CONCAT('未知统计方式 (', bs.statistics, ')')
           END             AS "统计方式",
       bs.date             AS "统计范围（日期）", -- 该日期结合“统计方式”来理解
       bs.time             AS "统计截止时间",
       bs.count            AS "缺陷未清数量",
       u_handler.user_name AS "处理人姓名"
-- bs.user_id AS "处理人ID" -- 可选
FROM t_bug_statistics bs
         LEFT JOIN
     t_project p ON bs.project_id = p.project_id
         LEFT JOIN
     t_project_user u_handler ON bs.user_id = u_handler.user_id and u_handler.project_id = 23 -- 假设用户表为 t_project_user
WHERE bs.project_id = 23      -- 替换 your_project_id
    #   AND bs.date = '2025-05-15' -- 可选: 如果只想看特定一天的统计(例如，查看 '2023-10-27' 这天的“今日统计”或“昨日统计”)
  AND bs.is_delete = 0       -- 可选: 如果只想看特定一天的统计(例如，查看 '2023-10-27' 这天的“今日统计”或“昨日统计”)
#   AND bs.user_id = 0       -- 可选: 如果只想看特定一天的统计(例如，查看 '2023-10-27' 这天的“今日统计”或“昨日统计”)
#   AND bs.statistics IN (1, 2) -- 筛选有效的统计方式
-- AND u_handler.user_role = 'developer' -- 可选，如果只看开发人员
ORDER BY p.project_name, bs.date DESC, bs.statistics, bs.time DESC, u_handler.user_name;



-- =================================================================================
-- SQL查询详细数据集合 (场景 6 已更新为使用 t_work_hours_rejection_records)
-- 注意: 请仔细检查并替换所有占位符、表名、字段名和条件值。
-- =================================================================================

-- 场景6: 评估工时被业务架构师打回次数 (查询被打回的工时评估记录详情)
SELECT
    -- p.project_name AS "所属项目", -- 若需项目名,需通过 cnt_id JOIN t_work_items 再 JOIN t_project
    whrr.cnt_id           AS "任务ID",
    whrr.cnt_title        AS "任务标题",
    whrr.handler_name     AS "处理人",
    whrr.rejection_reason AS "打回原因",
    whrr.submitter_name   AS "提交人",
    whrr.submit_at        AS "提交时间"
FROM t_work_hours_rejection_records whrr
-- EXAMPLE JOIN for project name:
-- LEFT JOIN t_work_items wi ON whrr.cnt_id = wi.cnt_id
-- LEFT JOIN t_project p ON JSON_UNQUOTE(wi.extends -> '$.project_id') = p.project_id -- 假设 project_id 在 work_items.extends 中
WHERE whrr.submitter_uid = 6387 -- 替换为实际的业务架构师用户ID
-- AND p.project_id = your_project_id -- 如果通过JOIN筛选项目
-- AND whrr.create_at BETWEEN 'start_date' AND 'end_date' -- 可选时间范围筛选
ORDER BY whrr.submit_at DESC;


-- =================================================================================


# t_iteration_demand_relation_records
# t_work_items_change_records
#     t_iteration_process_node_end_record
-- =================================================================================
-- SQL查询详细数据集合 (场景 8 已根据最新反馈更新)
-- 注意: 请仔细检查并替换所有占位符、JSON路径、表名、字段名和条件值。
-- 场景 8 - 字段 2 中的 "阶段" 现在从 t_iteration_process_node.node_data 获取。
-- 其他场景 (3, 4, 7, 9, 10) 仍按之前约定使用 t_tag_library。
-- =================================================================================

-- 场景8 - 字段1: 迭代级别统计 (此部分逻辑不变，保持之前版本)
-- 所属项目、迭代、需求变更次数、需求新增次数、迭代预估周期、迭代实际周期、是否延期（是、否）
WITH IterationChanges AS (SELECT iteration_id,
                                 project_id,
                                 SUM(CASE WHEN change_reason LIKE '%需求变更%' THEN 1 ELSE 0 END) AS demand_change_count
                          FROM t_work_items_change_records
                          WHERE project_id = 2744 -- 替换 your_project_id
                          GROUP BY iteration_id, project_id),
     IterationNewDemandsFromRelation AS (SELECT iteration_id,
                                                project_id,
                                                COUNT(DISTINCT cnt_id) as new_demand_relation_count
                                         FROM t_iteration_demand_relation_records
                                         WHERE operation_type = 1
                                           AND project_id = 2744 -- 假设1是新增, 替换 your_project_id
                                         GROUP BY iteration_id, project_id),
     IterationOverallDelayStatus AS (SELECT iteration_id,
                                            project_id,
                                            MAX(is_delayed) AS is_overall_delayed
                                     FROM t_iteration_process_node_end_record
                                     WHERE project_id = 2744 -- 替换 your_project_id
                                     GROUP BY iteration_id, project_id)
SELECT p.project_name                                             AS "所属项目",
       i.iteration_name                                           AS "迭代",
       COALESCE(ic.demand_change_count, 0)                        AS "需求变更次数",
       COALESCE(indr.new_demand_relation_count, 0)                AS "需求新增次数",
       CONCAT(i.estimate_start_time, ' 至 ', i.estimate_end_time) AS "迭代预估周期",
       CONCAT(i.start_time, ' 至 ', i.end_time)                   AS "迭代实际周期",
       CASE
           WHEN iods.is_overall_delayed = 1 THEN '是'
           WHEN iods.is_overall_delayed = 0 THEN '否'
           ELSE
               (CASE
                    WHEN i.end_time IS NOT NULL AND i.estimate_end_time IS NOT NULL AND i.end_time > i.estimate_end_time
                        THEN '是'
                    ELSE '否' END)
           END                                                    AS "是否延期"
FROM t_iteration i
         LEFT JOIN
     t_project p ON i.project_id = p.project_id
         LEFT JOIN
     IterationChanges ic ON i.iteration_id = ic.iteration_id AND i.project_id = ic.project_id
         LEFT JOIN
     IterationNewDemandsFromRelation indr ON i.iteration_id = indr.iteration_id AND i.project_id = indr.project_id
         LEFT JOIN
     IterationOverallDelayStatus iods ON i.iteration_id = iods.iteration_id AND i.project_id = iods.project_id
WHERE i.project_id = 2744 -- 替换 your_project_id
-- AND i.iteration_id = your_specific_iteration_id -- 可选，筛选特定迭代
ORDER BY i.iteration_name;

-- =================================================================================
-- 场景8 - 字段2: 迭代节点级别统计 (阶段信息从 t_iteration_process_node.node_data 获取)
-- 迭代节点、阶段、需求变更次数、需求新增次数
-- =================================================================================
WITH NodeDemandChanges AS (SELECT node_id,
                                  project_id,
                                  iteration_id,
                                  COUNT(change_id) AS node_demand_change_count
                           FROM t_work_items_change_records
                           WHERE (change_reason LIKE '%需求变更%' OR
                                  change_reason LIKE '%some_other_demand_change_indicator%')
                           GROUP BY node_id, project_id, iteration_id),
     NodeNewDemands AS (SELECT node_id,
                               project_id,
                               iteration_id,
                               COUNT(record_id) AS node_new_demand_count
                        FROM t_iteration_demand_relation_records
                        WHERE operation_type = 1
                        GROUP BY node_id, project_id, iteration_id)
SELECT ipn.process_node_id                       AS "迭代id",
       ipn.node_name                             AS "迭代节点",
       ttl_stage.tag_name                        AS "阶段", -- 从 t_tag_library 获取阶段名称
       COALESCE(ndc.node_demand_change_count, 0) AS "需求变更次数",
       COALESCE(nnd.node_new_demand_count, 0)    AS "需求新增次数",
       i.iteration_name                          AS "所属迭代",
       p.project_name                            AS "所属项目"
FROM t_iteration_process_node ipn
         LEFT JOIN
     t_iteration i ON ipn.iteration_id = i.iteration_id
         LEFT JOIN
     t_project p ON i.project_id = p.project_id
         LEFT JOIN
     t_tag_library ttl_stage ON ipn.node_data -> '$.node_setting.node_stage' =
                                ttl_stage.tag_code -- 假设 ipn.node_stage 存储的是 tag_code, 如果在JSON中则用 JSON_UNQUOTE(ipn.node_data -> '$.path.to.stage_code')
         AND ttl_stage.group_type = 1 -- 替换 your_node_stage_tag_group_type_id
         LEFT JOIN
     NodeDemandChanges ndc ON ipn.iteration_process_node_id = ndc.node_id AND ipn.iteration_id = ndc.iteration_id AND
                              i.project_id = ndc.project_id
         LEFT JOIN
     NodeNewDemands nnd ON ipn.iteration_process_node_id = nnd.node_id AND ipn.iteration_id = nnd.iteration_id AND
                           i.project_id = nnd.project_id
WHERE i.project_id = 2964  -- 替换 your_project_id
  AND i.iteration_id = 747 -- 建议筛选特定迭代
  AND ipn.is_delete = 0
ORDER BY i.iteration_name, ipn.create_at, ipn.iteration_process_node_id;

-- =================================================================================

-- 场景9: 测试延期次数 (查询延期的测试节点的详细信息)
SELECT p.project_name                                     AS "所属项目",
       i.iteration_name                                   AS "迭代",
       ipn.node_name                                      AS "迭代节点",
       ttl_stage.tag_name                                 AS "节点阶段", -- 从 t_tag_library 获取
       r.estimate_start_time                              AS "预估开始时间",
       r.estimate_end_time                                AS "预估结束时间",
       r.actual_start_time                                AS "实际开始时间",
       r.actual_end_time                                  AS "实际结束时间",
       ipn.node_data -> '$.node_manger.users'             AS "节点负责人",
       r.group_leader_name                                AS "组长",
       CASE WHEN r.is_delayed = 1 THEN '是' ELSE '否' END AS "是否延期"
FROM t_iteration_process_node_end_record r
         JOIN
     t_iteration_process_node ipn ON r.iteration_process_node_id = ipn.iteration_process_node_id
         LEFT JOIN
     t_tag_library ttl_stage
     ON r.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1 -- 替换 your_node_stage_tag_group_type_id
         LEFT JOIN
     t_project p ON r.project_id = p.project_id
         LEFT JOIN
     t_iteration i ON r.iteration_id = i.iteration_id
WHERE r.project_id = 23                                    -- 替换
  and r.is_delete = 0
    #     AND r.iteration_id = your_iteration_id -- 替换 (可选)
  AND r.is_delayed = 1
  AND (ttl_stage.tag_code = 'tag_49809162267640520716923') -- 筛选开发相关的节点 (现在可以用 ttl_stage.tag_name)
ORDER BY r.actual_end_time DESC;
-- =================================================================================

-- 场景9: 测试延期次数 (查询延期的测试节点的详细信息)
WITH UserNamesCTE AS (SELECT inner_ipn.iteration_process_node_id,
                             GROUP_CONCAT(pu.user_name SEPARATOR ', ') AS ResponsibleUserNames
                      FROM t_iteration_process_node inner_ipn
                               CROSS JOIN JSON_TABLE(
                              inner_ipn.node_data,
                              '$.node_manger.users[*]' COLUMNS (json_user_id INT PATH '$')
                                          ) AS jt
                               JOIN t_project_user pu ON jt.json_user_id = pu.user_id and pu.project_id = 23-- 使用 project_user 表和假设的列名
                      GROUP BY inner_ipn.iteration_process_node_id)
SELECT p.project_name                                     AS "所属项目",
       i.iteration_name                                   AS "迭代",
       ipn.node_name                                      AS "迭代节点",
       ttl_stage.tag_name                                 AS "节点阶段",
       r.estimate_start_time                              AS "预估开始时间",
       r.estimate_end_time                                AS "预估结束时间",
       r.actual_start_time                                AS "实际开始时间",
       r.actual_end_time                                  AS "实际结束时间",
       unc.ResponsibleUserNames                           AS "节点负责人",
       r.group_leader_name                                AS "组长",
       CASE WHEN r.is_delayed = 1 THEN '是' ELSE '否' END AS "是否延期"
FROM t_iteration_process_node_end_record r
         JOIN t_iteration_process_node ipn ON r.iteration_process_node_id = ipn.iteration_process_node_id
         LEFT JOIN UserNamesCTE unc ON r.iteration_process_node_id = unc.iteration_process_node_id
         LEFT JOIN t_tag_library ttl_stage ON r.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1
         LEFT JOIN t_project p ON r.project_id = p.project_id
         LEFT JOIN t_iteration i ON r.iteration_id = i.iteration_id
WHERE r.project_id = 23
  AND r.is_delete = 0
  AND r.is_delayed = 1
  AND (ttl_stage.tag_code = 'tag_49809162267640520716923')
ORDER BY r.actual_end_time DESC;



-- =================================================================================

-- 场景4: 迭代中，开发延期次数 (查询延期的开发节点的详细信息)
WITH UserNamesCTE AS (SELECT inner_ipn.iteration_process_node_id,
                             GROUP_CONCAT(pu.user_name SEPARATOR ', ') AS ResponsibleUserNames
                      FROM t_iteration_process_node inner_ipn
                               CROSS JOIN JSON_TABLE(
                              inner_ipn.node_data,
                              '$.node_manger.users[*]' COLUMNS (json_user_id INT PATH '$')
                                          ) AS jt
                               JOIN t_project_user pu ON jt.json_user_id = pu.user_id and pu.project_id = 23-- 使用 project_user 表和假设的列名
                      GROUP BY inner_ipn.iteration_process_node_id)
SELECT p.project_name                                     AS "所属项目",
       i.iteration_name                                   AS "迭代",
       ipn.node_name                                      AS "迭代节点",
       ttl_stage.tag_name                                 AS "节点阶段", -- 从 t_tag_library 获取
       r.estimate_start_time                              AS "预估开始时间",
       r.estimate_end_time                                AS "预估结束时间",
       r.actual_start_time                                AS "实际开始时间",
       r.actual_end_time                                  AS "实际结束时间",
       unc.ResponsibleUserNames                           AS "节点负责人",
       CASE WHEN r.is_delayed = 1 THEN '是' ELSE '否' END AS "是否延期"
FROM t_iteration_process_node_end_record r
         JOIN t_iteration_process_node ipn ON r.iteration_process_node_id = ipn.iteration_process_node_id
         LEFT JOIN UserNamesCTE unc ON r.iteration_process_node_id = unc.iteration_process_node_id
         LEFT JOIN t_tag_library ttl_stage
                   ON r.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1 -- 替换 your_node_stage_tag_group_type_id
         LEFT JOIN t_project p ON r.project_id = p.project_id
         LEFT JOIN t_iteration i ON r.iteration_id = i.iteration_id
WHERE r.project_id = 23                                    -- 替换
    #     AND r.iteration_id = your_iteration_id -- 替换 (可选)
  AND r.is_delayed = 1
  AND (ttl_stage.tag_code = 'tag_49809162008138929929038') -- 筛选开发相关的节点 (现在可以用 ttl_stage.tag_name)
ORDER BY r.actual_end_time
    DESC;

