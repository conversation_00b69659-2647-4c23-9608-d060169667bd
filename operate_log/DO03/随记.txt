# 子系统添加角色（角色内人员不可重复，需要产品测试维护好）
## 流程：登录中台-》系统管理-》子系统列表-》效能平台-》角色-》新增角色
## code不可变更，数据：
{
    {
            角色名称：软件测试,
            code: softwareTest,
            角色类型：个人,
            状态: 启动
    },
    {
            角色名称：后端开发,
            code: backendDevelopment,
            角色类型：个人,
            状态: 启动
    },
    {
            角色名称：前端开发,
            code: webDevelop,
            角色类型：个人,
            状态: 启动
    },
    {
            角色名称：UI设计师,
            code: uiDesigner,
            角色类型：个人,
            状态: 启动
    },
    {
            角色名称：产品经理,
            code: productManager,
            角色类型：个人,
            状态: 启动
    },
    {
            角色名称：项目经理,
            code: projectManager,
            角色类型：个人,
            状态: 启动
    }
}
