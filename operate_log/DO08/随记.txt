修改 需求、 缺陷 处理人字段入参
项目设置 -> 应用设置 -> 需求、 缺陷 -> 字段管理 -> 处理人字段 -> 编辑

接口入参(这里的参数都会优先取地址栏，取不到就默认)
{"project_id":null,"map":null}


增加枚举数据
枚举code                 枚举名称         枚举值                父级
demandHandler	        需求工作流-处理人
demandCreateUser	    创建人	        create_by           需求工作流-处理人
demandHandlerUser	    处理人	        handler_uid         需求工作流-处理人
demandDeveloperUser	    开发人员	        developer_uid       需求工作流-处理人
demandTesterUser	    测试人员	        tester_uid          需求工作流-处理人
defectHandler	        缺陷工作流-处理人
defectCreateUser	    创建人	        create_by           缺陷工作流-处理人
defectHandlerUser	    处理人	        handler_uid         缺陷工作流-处理人
defectDeveloperUser	    开发人员	        developer_uid       缺陷工作流-处理人
defectTesterUser	    测试人员	        tester_uid          缺陷工作流-处理人
defectVerifyUser	    验证人	        verify_uid          缺陷工作流-处理人
defectClosureUser	    关闭人	        closure_uid         缺陷工作流-处理人
defectSuspendUser	    挂起人	        suspend_uid         缺陷工作流-处理人
defectSolveUser	        解决人	        solve_uid           缺陷工作流-处理人


ES 执行
//设置分页数量最大值
PUT /devops_content_index/_settings
{
  "index" : {
    "max_result_window" : 1000000
  }
}

