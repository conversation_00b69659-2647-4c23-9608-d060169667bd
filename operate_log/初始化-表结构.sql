/*
Navicat Premium Data Transfer

Source Server         : 堡垒机mysql
Source Server Type    : MySQL
Source Server Version : 80040 (8.0.40)
Source Host           : 127.0.0.1:3306
Source Schema         : devops_uat

Target Server Type    : MySQL
Target Server Version : 80040 (8.0.40)
File Encoding         : 65001

Date: 03/01/2025 18:00:08
*/

-- 表结构更新
CREATE TABLE `t_client`
(
    `client_id`      int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`      int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`      tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`      int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `client_name`    varchar(50) NOT NULL DEFAULT '' COMMENT '终端名称',
    `extends`        json                 DEFAULT NULL COMMENT '自定义字段',
    `is_enable`      tinyint     NOT NULL DEFAULT '1' COMMENT '是否开启;1-是 0-否',
    `version`        int         NOT NULL DEFAULT '1' COMMENT '版本号',
    PRIMARY KEY (`client_id`) USING BTREE
) ENGINE=InnoDB COMMENT='终端';

CREATE TABLE `t_customer_table_config`
(
    `table_config_id` int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`       int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`  varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `table_unique`    varchar(50) NOT NULL DEFAULT '' COMMENT '表格唯一标识',
    `user_fields`     json        NOT NULL COMMENT '用户自定义显示字段',
    PRIMARY KEY (`table_config_id`) USING BTREE,
    KEY               `idx_create_by_table` (`create_by`,`table_unique`) USING BTREE
) ENGINE=InnoDB COMMENT='用户表头设置';

CREATE TABLE `t_enum`
(
    `enum_id`    int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `is_delete`  tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `enum_code`  varchar(20)  NOT NULL DEFAULT '' COMMENT '枚举code',
    `enum_name`  varchar(20)  NOT NULL DEFAULT '' COMMENT '枚举名称',
    `enum_value` varchar(200) NOT NULL DEFAULT '' COMMENT '枚举值',
    `parent_id`  int          NOT NULL DEFAULT '0' COMMENT '上级id 0表示顶级',
    `enum_type`  tinyint      NOT NULL DEFAULT '1' COMMENT '枚举类型;1-系统 2-自定义',
    PRIMARY KEY (`enum_id`) USING BTREE
) ENGINE=InnoDB COMMENT='枚举';

CREATE TABLE `t_execution_record`
(
    `execution_record_id` int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`           int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`      varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`           timestamp    NOT NULL COMMENT '创建时间',
    `is_delete`           tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `test_plan_id`        int          NOT NULL DEFAULT '0' COMMENT '计划Id',
    `plan_use_case_id`    int          NOT NULL DEFAULT '0' COMMENT '计划用例关联Id',
    `result`              tinyint      NOT NULL DEFAULT '1' COMMENT '1-通过;2-阻塞;4-不通过',
    `remark`              varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`execution_record_id`) USING BTREE
) ENGINE=InnoDB COMMENT='执行记录';

CREATE TABLE `t_field_config`
(
    `field_id`         int           NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`        int           NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`   varchar(50)   NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`        timestamp     NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`        tinyint       NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`        int           NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`   varchar(50)   NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`        timestamp     NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `field_type`       tinyint       NOT NULL DEFAULT '1' COMMENT '字段类型;1-系统 2-自定义',
    `remark`           varchar(1000) NOT NULL DEFAULT '' COMMENT '备注',
    `field_label`      varchar(50)   NOT NULL DEFAULT '' COMMENT '字段显示名称',
    `field_name`       varchar(50)   NOT NULL DEFAULT '' COMMENT '字段名',
    `field_component`  json          NOT NULL COMMENT '组件内容',
    `field_sort`       int           NOT NULL DEFAULT '1' COMMENT '排序值 降序排序',
    `module_id`        tinyint       NOT NULL DEFAULT '0' COMMENT '归属模块;1-终端，2-微服务，3-产品，4-迭代，5-需求，6-任务，7-缺陷，8-测试用例，9-测试计划，10-测试计划规划与执行',
    `project_id`       int           NOT NULL DEFAULT '0' COMMENT '所属项目id，属于项目的模块：4、5、6、7、8、9，只有自定义字段有所属项目这个概念，系统字段为通用，所有项目都有',
    `is_edit`          tinyint       NOT NULL DEFAULT '1' COMMENT '是否可编辑;1-允许,0-不允许',
    `template_default` tinyint       NOT NULL DEFAULT '0' COMMENT '是否是模版固定字段,1:是，0:否',
    `category_id`      tinyint       NOT NULL DEFAULT '0' COMMENT '分类id，1-基础字段、2-人员与时间字段、3-工时字段，只有系统字段有分类',
    `allow_setting`    tinyint       NOT NULL DEFAULT '1' COMMENT '是否可添加到模版中;1-允许 0-不允许',
    `component_type`   char(30)      NOT NULL DEFAULT '' COMMENT '组件类型，取值为field_component中的componentType属性',
    PRIMARY KEY (`field_id`) USING BTREE,
    KEY                `idx_field_name` (`field_name`) USING BTREE,
    KEY                `idx_module_id_is_delete` (`module_id`,`is_delete`) USING BTREE
) ENGINE=InnoDB COMMENT='字段管理';

CREATE TABLE `t_field_subset`
(
    `sub_id`         int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `sub_key`        varchar(255) NOT NULL DEFAULT '' COMMENT '标识',
    `field_list`     json                  DEFAULT NULL COMMENT '字段集合;json格式',
    `include_custom` tinyint      NOT NULL DEFAULT '0' COMMENT '是否包含自定义字段;0：不是，1：是',
    `remark`         varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `module_id`      json                  DEFAULT NULL COMMENT '归属模块',
    PRIMARY KEY (`sub_id`) USING BTREE
) ENGINE=InnoDB COMMENT='以标识区分各个模块字段集合的子集';

CREATE TABLE `t_flow_process`
(
    `flow_process_id`   int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `attribution_id`    int unsigned NOT NULL DEFAULT '0' COMMENT '归属来源id,默认0',
    `create_by`         int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`    varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`         timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`         tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`         int          NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`    varchar(50)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`         timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_id`        int          NOT NULL DEFAULT '0' COMMENT '项目Id',
    `flow_process_name` varchar(50)  NOT NULL DEFAULT '' COMMENT '流程名称',
    `flow_process_desc` varchar(500) NOT NULL DEFAULT '' COMMENT '流程说明',
    `version`           int          NOT NULL DEFAULT '0' COMMENT '版本',
    PRIMARY KEY (`flow_process_id`) USING BTREE
) ENGINE=InnoDB COMMENT='迭代流程模板';

CREATE TABLE `t_flow_process_node`
(
    `process_node_id` int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `attribution_id`  int unsigned NOT NULL DEFAULT '0' COMMENT '归属来源id,默认0',
    `create_at`       timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`       tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`       int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`  varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`       timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `flow_process_id` int         NOT NULL DEFAULT '0' COMMENT '流程Id',
    `node_name`       varchar(50) NOT NULL DEFAULT '' COMMENT '节点名称',
    `node_data`       json                 DEFAULT NULL COMMENT '节点设置',
    `row_id`          int         NOT NULL DEFAULT '0' COMMENT '节点标识',
    PRIMARY KEY (`process_node_id`) USING BTREE
) ENGINE=InnoDB COMMENT='流程节点模板';

CREATE TABLE `t_flow_status`
(
    `flow_status_id`   int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `attribution_id`   int unsigned NOT NULL DEFAULT '0' COMMENT '归属来源id,默认0',
    `create_by`        int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`   varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`        timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`        tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`        int          NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`   varchar(50)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`        timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `flow_process_id`  int          NOT NULL DEFAULT '0' COMMENT '迭代流程id',
    `status_flow_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '工作流名称',
    `status_flow_desc` varchar(200) NOT NULL DEFAULT '' COMMENT '描述',
    `project_id`       int          NOT NULL DEFAULT '0' COMMENT '项目id',
    `flow_status_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '类型1迭代2需求3任务4缺陷5测试用例6测试计划',
    PRIMARY KEY (`flow_status_id`) USING BTREE
) ENGINE=InnoDB COMMENT='状态流';

CREATE TABLE `t_flow_status_enum`
(
    `status_enum_id`     int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `attribution_id`     int unsigned NOT NULL DEFAULT '0' COMMENT '归属来源id,默认0',
    `create_by`          int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`     varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`          timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`          tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`          int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`     varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`          timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `name`               varchar(20) NOT NULL DEFAULT '' COMMENT '名称',
    `project_id`         int         NOT NULL DEFAULT '0' COMMENT '项目id',
    `colour`             varchar(15) NOT NULL DEFAULT '' COMMENT '颜色',
    `is_permanent`       tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否常驻0否1是',
    `status_enum_status` tinyint     NOT NULL DEFAULT '0' COMMENT '状态库枚举0默认1新2接受/处理3已解决4已验证5重新打开6已拒绝7挂起8已关闭',
    `status_enum_type`   tinyint unsigned NOT NULL DEFAULT '1' COMMENT '类型1迭代2需求3任务4缺陷5测试用例6测试计划',
    PRIMARY KEY (`status_enum_id`) USING BTREE
) ENGINE=InnoDB COMMENT='状态库';

CREATE TABLE `t_flow_status_node_relation`
(
    `id`              int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `flow_status_id`  int NOT NULL DEFAULT '0' COMMENT '状态流程id',
    `status_text_id`  int NOT NULL DEFAULT '0' COMMENT '状态描述id',
    `process_node_id` int NOT NULL DEFAULT '0' COMMENT '流程节点id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='状态与节点关系';

CREATE TABLE `t_flow_status_text`
(
    `status_text_id` int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `attribution_id` int unsigned NOT NULL DEFAULT '0' COMMENT '归属来源id,默认0',
    `create_by`      int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`      tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`      int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `flow_status_id` int         NOT NULL DEFAULT '0' COMMENT '流程状态id',
    `status_enum_id` int         NOT NULL DEFAULT '0' COMMENT '状态库Id',
    `status_type`    tinyint     NOT NULL DEFAULT '1' COMMENT '状态类型;1-开始,2-过程,3-结束',
    `sort`           tinyint unsigned NOT NULL DEFAULT '1' COMMENT '排序，默认正序',
    PRIMARY KEY (`status_text_id`) USING BTREE
) ENGINE=InnoDB COMMENT='状态描述';

CREATE TABLE `t_flow_status_transfer`
(
    `id`                    int  NOT NULL AUTO_INCREMENT COMMENT 'id',
    `flow_status_id`        int  NOT NULL DEFAULT '0' COMMENT '状态流程id',
    `status_text_id`        int  NOT NULL DEFAULT '0' COMMENT '状态描述id',
    `status_enum_id`        int  NOT NULL DEFAULT '0' COMMENT '状态id',
    `target_status_enum_id` int  NOT NULL DEFAULT '0' COMMENT '流转到的状态id',
    `extends`               json NOT NULL COMMENT '自定义字段',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='状态流转';

CREATE TABLE `t_iteration`
(
    `iteration_id`                 int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`                    int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`               varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`                    timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`                    tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`                    int          NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`               varchar(10)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`                    timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_id`                   int          NOT NULL DEFAULT '0' COMMENT '项目Id',
    `iteration_name`               varchar(200) NOT NULL DEFAULT '' COMMENT '迭代名称',
    `iteration_icon`               varchar(255) NOT NULL DEFAULT '' COMMENT '迭代icon',
    `extends`                      text COMMENT '自定义字段',
    `estimate_start_time`          timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '预估开始时间',
    `estimate_end_time`            timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '预估结束时间',
    `start_time`                   timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '开始时间',
    `end_time`                     timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '结束时间',
    `flow_process_id`              int          NOT NULL DEFAULT '0' COMMENT '迭代流程id',
    `project_category_settings_id` int          NOT NULL DEFAULT '0' COMMENT '类别id',
    `flow_status_id`               int          NOT NULL DEFAULT '0' COMMENT '状态流程id',
    `status_text_id`               int          NOT NULL DEFAULT '0' COMMENT '当前状态',
    `status_enum_id`               int          NOT NULL DEFAULT '0' COMMENT '状态库Id',
    `sort`                         tinyint unsigned NOT NULL DEFAULT '0' COMMENT '排序，默认正序',
    PRIMARY KEY (`iteration_id`) USING BTREE
) ENGINE=InnoDB COMMENT='迭代';

CREATE TABLE `t_iteration_process_node`
(
    `iteration_process_node_id` int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`                 int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`            varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`                 timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`                 tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`                 int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`            varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`                 timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `iteration_id`              int         NOT NULL DEFAULT '0' COMMENT '迭代id',
    `node_name`                 varchar(20) NOT NULL DEFAULT '' COMMENT '节点名称',
    `node_data`                 json                 DEFAULT NULL COMMENT '节点数据',
    `row_id`                    int         NOT NULL DEFAULT '0' COMMENT '节点标识',
    `process_node_id`           int         NOT NULL DEFAULT '0' COMMENT '原节点Id',
    `status`                    tinyint     NOT NULL DEFAULT '1' COMMENT '节点状态;1-未开始,2-进行中,3-已完成',
    `status_text_id`            int         NOT NULL DEFAULT '0' COMMENT '流转状态描述id',
    `is_audit_status`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '审批是否通过0默认1通过2不通过3审批中',
    `is_auto_status`            tinyint(1) NOT NULL DEFAULT '0' COMMENT '自动化流程是否通过0默认1通过2不通过（获取详情时校验更新）',
    `estimate_start_time`       timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '预估开始时间',
    `estimate_end_time`         timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '预估结束时间',
    `start_time`                timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '实际开始时间',
    `end_time`                  timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '实际结束时间',
    PRIMARY KEY (`iteration_process_node_id`) USING BTREE
) ENGINE=InnoDB COMMENT='迭代流程节点';

CREATE TABLE `t_iteration_process_node_audit`
(
    `iteration_process_node_audit_id` int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`                       int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`                  varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`                       timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`                       tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`                       int          NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`                  varchar(50)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`                       timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `is_audit`                        tinyint(1) NOT NULL DEFAULT '0' COMMENT '审批类型0默认1发起2通过3拒绝',
    `iteration_process_node_id`       int          NOT NULL COMMENT '关联节点id',
    `remark`                          varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`iteration_process_node_audit_id`) USING BTREE
) ENGINE=InnoDB COMMENT='迭代节点审批表';

CREATE TABLE `t_iteration_process_node_relation`
(
    `id`              int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `process_node_id` int NOT NULL DEFAULT '0' COMMENT '当前节点id;指iteration_process_node_id',
    `next_node_id`    int NOT NULL DEFAULT '0' COMMENT '后置;指iteration_process_node_id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='迭代过程节点关系';

CREATE TABLE `t_iteration_process_node_tip`
(
    `iteration_process_node_tip_id` int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`                     int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`                varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`                     timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `iteration_process_node_id`     int          NOT NULL COMMENT '关联节点id',
    `title`                         varchar(50)  NOT NULL DEFAULT '' COMMENT '标题',
    `content`                       varchar(200) NOT NULL DEFAULT '' COMMENT '内容',
    PRIMARY KEY (`iteration_process_node_tip_id`) USING BTREE
) ENGINE=InnoDB COMMENT='迭代节点提醒表';

CREATE TABLE `t_microservice`
(
    `microservice_id`   int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`         int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`    varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`         timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`         tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`         int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`    varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`         timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `microservice_name` varchar(50) NOT NULL COMMENT '微服务名称',
    `extends`           json                 DEFAULT NULL COMMENT '自定义字段',
    `is_enable`         tinyint     NOT NULL DEFAULT '1' COMMENT '是否开启;1-是 0-否',
    `version`           int         NOT NULL DEFAULT '1' COMMENT '版本号',
    PRIMARY KEY (`microservice_id`) USING BTREE
) ENGINE=InnoDB COMMENT='微服务';

CREATE TABLE `t_operation_log`
(
    `log_id`          int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`       int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`  varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`       timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `operation_table` varchar(50) NOT NULL DEFAULT '' COMMENT '操作类型',
    `table_id`        int unsigned NOT NULL DEFAULT '0' COMMENT '关联Id',
    `log_details`     text        NOT NULL COMMENT '详情',
    PRIMARY KEY (`log_id`) USING BTREE,
    KEY               `idx_operation_table_id` (`operation_table`,`table_id`) USING BTREE
) ENGINE=InnoDB COMMENT='操作日志';

CREATE TABLE `t_plan_use_case`
(
    `plan_use_case_id` int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`        int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`   varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`        timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`        tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`        int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`   varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`        timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `test_plan_id`     int         NOT NULL DEFAULT '0' COMMENT '计划Id',
    `execution_times`  int         NOT NULL DEFAULT '0' COMMENT '执行次数',
    `bug_count`        int         NOT NULL DEFAULT '0' COMMENT '关联Bug数',
    `test_case_id`     int         NOT NULL DEFAULT '0' COMMENT '用例id',
    PRIMARY KEY (`plan_use_case_id`) USING BTREE,
    UNIQUE KEY `t_plan_use_case_test_plan_id_test_case_id_uindex` (`test_plan_id`,`test_case_id`) USING BTREE
) ENGINE=InnoDB COMMENT='测试计划用例关联';

CREATE TABLE `t_plan_use_case_bug`
(
    `id`                  int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`           int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`      varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`           timestamp   NOT NULL COMMENT '创建时间',
    `is_delete`           tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `plan_use_case_id`    int         NOT NULL DEFAULT '0' COMMENT '计划用例关联id',
    `cnt_id`              int         NOT NULL DEFAULT '0' COMMENT '缺陷Id',
    `test_plan_id`        int         NOT NULL DEFAULT '0' COMMENT '计划id',
    `execution_record_id` int         NOT NULL DEFAULT '0' COMMENT '执行记录Id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='测试计划用例bug关联表';

CREATE TABLE `t_process_node_relation`
(
    `id`              int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `process_node_id` int NOT NULL DEFAULT '0' COMMENT '当前Id;指process_node_id',
    `next_node_id`    int NOT NULL DEFAULT '0' COMMENT '后置;指process_node_id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='迭代流程节点关系';

CREATE TABLE `t_product`
(
    `product_id`     int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`      int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`      tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`      int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `product_name`   varchar(50) NOT NULL DEFAULT '' COMMENT '产品名称',
    `product_no`     int         NOT NULL DEFAULT '0' COMMENT '产品编号',
    `extends`        json                 DEFAULT NULL COMMENT '自定义字段',
    `version`        int         NOT NULL DEFAULT '0' COMMENT '版本',
    `is_enable`      tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否开启;1-是 0-否',
    PRIMARY KEY (`product_id`) USING BTREE
) ENGINE=InnoDB COMMENT='产品';

CREATE TABLE `t_product_client`
(
    `id`             int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`      int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`      tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`      int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `product_id`     int         NOT NULL DEFAULT '0' COMMENT '产品id',
    `client_id`      int         NOT NULL DEFAULT '0' COMMENT '终端id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='产品终端关系';

CREATE TABLE `t_product_microservice`
(
    `id`              int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`       int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`  varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`       timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`       tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`       int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`  varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`       timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `product_id`      int         NOT NULL DEFAULT '0' COMMENT '产品id',
    `microservice_id` int         NOT NULL DEFAULT '0' COMMENT '终端id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='产品微服务关系';

CREATE TABLE `t_project`
(
    `project_id`          int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`           int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`      varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`           timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`           tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`           int          NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`      varchar(50)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`           timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_no`          int          NOT NULL DEFAULT '0' COMMENT '项目编号',
    `project_name`        varchar(50)  NOT NULL DEFAULT '' COMMENT '项目名称',
    `product_id`          int unsigned NOT NULL DEFAULT '0' COMMENT '产品Id',
    `project_user_count`  int          NOT NULL DEFAULT '0' COMMENT '项目成员数量',
    `project_status`      tinyint(1) NOT NULL DEFAULT '1' COMMENT '项目状态1进行中2关闭',
    `project_remark`      varchar(200) NOT NULL DEFAULT '' COMMENT '项目描述',
    `project_template_id` int          NOT NULL DEFAULT '0' COMMENT '项目模板id',
    `project_icon`        varchar(255) NOT NULL DEFAULT '' COMMENT '项目icon',
    `is_template`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否是模板;1-是 0-否',
    PRIMARY KEY (`project_id`) USING BTREE
) ENGINE=InnoDB COMMENT='项目';

CREATE TABLE `t_project_category`
(
    `project_category_id`   int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`             int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`        varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`             timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`             tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`             int          NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`        varchar(50)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`             timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `is_allow_delete`       tinyint      NOT NULL DEFAULT '1' COMMENT '是否允许删除1允许0禁止',
    `project_id`            int          NOT NULL DEFAULT '0' COMMENT '项目id',
    `category_name`         varchar(20)  NOT NULL DEFAULT '' COMMENT '名称',
    `pid`                   int          NOT NULL DEFAULT '0' COMMENT '父级id',
    `remark`                varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
    `project_category_type` tinyint unsigned NOT NULL DEFAULT '2' COMMENT '分类所属1迭代2需求3任务4缺陷5测试用例6测试计划',
    `sort`                  tinyint unsigned NOT NULL DEFAULT '1' COMMENT '排序，默认正序',
    PRIMARY KEY (`project_category_id`) USING BTREE,
    KEY                     `idx_project_cate` (`project_id`,`project_category_type`) USING BTREE
) ENGINE=InnoDB COMMENT='项目分类管理';

CREATE TABLE `t_project_category_settings`
(
    `project_category_settings_id`   int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `attribution_id`                 int unsigned NOT NULL DEFAULT '0' COMMENT '归属来源id,默认0',
    `create_by`                      int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`                 varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`                      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`                      tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`                      int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`                 varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`                      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `category_name`                  varchar(20) NOT NULL DEFAULT '' COMMENT '类别名称',
    `category_en_name`               varchar(20) NOT NULL DEFAULT '' COMMENT '类别英文名称',
    `icon`                           varchar(15) NOT NULL DEFAULT '' COMMENT '图标',
    `template_id`                    int         NOT NULL DEFAULT '0' COMMENT '创建页id(模版id)',
    `flow_status_id`                 int         NOT NULL DEFAULT '0' COMMENT '工作流id',
    `flow_process_id`                int         NOT NULL DEFAULT '0' COMMENT '工作流程id',
    `project_id`                     int         NOT NULL DEFAULT '0' COMMENT '项目Id',
    `is_enable`                      tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否开启;1-是 0-否',
    `sort`                           tinyint unsigned NOT NULL DEFAULT '0' COMMENT '排序，默认正序',
    `project_category_settings_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '类别所属1迭代2需求3任务4缺陷5测试用例6测试计划',
    PRIMARY KEY (`project_category_settings_id`) USING BTREE,
    KEY                              `idx_project_sort` (`project_id`,`sort`) USING BTREE
) ENGINE=InnoDB COMMENT='项目类别管理';

CREATE TABLE `t_project_favorite`
(
    `id`             int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`      int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`      tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`      int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`      timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_id`     int         NOT NULL DEFAULT '0' COMMENT '项目id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_project_cate` (`project_id`) USING BTREE
) ENGINE=InnoDB COMMENT='项目收藏表';

CREATE TABLE `t_project_template`
(
    `project_template_id`   int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`             int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`        varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`             timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`             tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`             int          NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`        varchar(50)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`             timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_template_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '模板名称',
    `project_id`            int          NOT NULL DEFAULT '0' COMMENT '项目id',
    `is_allow_delete`       tinyint      NOT NULL DEFAULT '1' COMMENT '是否允许删除1允许0禁止',
    `remark`                varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
    `sort`                  tinyint unsigned NOT NULL DEFAULT '1' COMMENT '排序，默认正序',
    PRIMARY KEY (`project_template_id`) USING BTREE,
    KEY                     `idx_project_cate` (`project_template_name`) USING BTREE
) ENGINE=InnoDB COMMENT='项目模板表';

CREATE TABLE `t_project_user`
(
    `id`             int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`      int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name` varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`      timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`      tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是,0-否',
    `update_by`      int          NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name` varchar(50)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`      timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_id`     int          NOT NULL DEFAULT '0' COMMENT '项目Id',
    `user_id`        int          NOT NULL DEFAULT '0' COMMENT '用户Id',
    `user_name`      varchar(50)  NOT NULL DEFAULT '' COMMENT '名称',
    `en_user_name`   varchar(50)  NOT NULL DEFAULT '' COMMENT '英文名称',
    `avatar`         varchar(255) NOT NULL DEFAULT '' COMMENT '用户头像',
    `project_role`   varchar(20)  NOT NULL DEFAULT '' COMMENT '项目角色code(数据来源t_enum->enum_value) 字段废弃',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB COMMENT='项目成员';

CREATE TABLE `t_project_user_role`
(
    `id`           bigint      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id`      int         NOT NULL DEFAULT '0' COMMENT '用户Id',
    `project_role` varchar(20) NOT NULL DEFAULT '' COMMENT '项目角色code(数据来源t_enum->enum_value) ',
    `project_name` varchar(20) NOT NULL DEFAULT '' COMMENT '角色名',
    PRIMARY KEY (`id`) USING BTREE,
    KEY            `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB COMMENT='成员角色';

CREATE TABLE `t_script_version`
(
    `Id`         bigint NOT NULL AUTO_INCREMENT COMMENT '自增Id',
    `NAME`       varchar(100) DEFAULT NULL COMMENT '脚本文件名称',
    `Info`       varchar(100) DEFAULT NULL COMMENT '迭代名称',
    `createTime` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updateTime` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB COMMENT='数据库脚本控制';

CREATE TABLE `t_template`
(
    `id`               int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `attribution_id`   int unsigned NOT NULL DEFAULT '0' COMMENT '归属来源id,默认0',
    `create_by`        int         NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`   varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`        timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`        tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`        int         NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`   varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`        timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `is_default`       tinyint     NOT NULL DEFAULT '0' COMMENT '是否默认模板;1-是 0-否',
    `template_name`    varchar(50) NOT NULL DEFAULT '' COMMENT '模板名称',
    `template_content` json        NOT NULL COMMENT '模板内容',
    `module_id`        tinyint     NOT NULL DEFAULT '0' COMMENT '归属模块;1-终端，2-微服务，3-产品，4-迭代，5-需求，6-任务，7-缺陷，8-测试用例，9-测试计划',
    `project_id`       int         NOT NULL DEFAULT '0' COMMENT '所属项目id，属于项目的模块：4、5、6、7',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='终端模板';

CREATE TABLE `t_test_case`
(
    `test_case_id`     int     NOT NULL AUTO_INCREMENT COMMENT 'id',
    `extends`          text COMMENT '自定义字段',
    `case_tep`         text    NOT NULL COMMENT '用例步骤',
    `preconditions`    text    NOT NULL COMMENT '前置条件',
    `expected_results` text    NOT NULL COMMENT '预期结果',
    `is_delete`        tinyint NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `version`          int     NOT NULL DEFAULT '1' COMMENT '版本号',
    PRIMARY KEY (`test_case_id`) USING BTREE
) ENGINE=InnoDB COMMENT='测试用例';

CREATE TABLE `t_test_case_work`
(
    `test_case_work_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `test_case_id`      int NOT NULL DEFAULT '0' COMMENT '测试用例id',
    `cnt_id`            int NOT NULL DEFAULT '0' COMMENT '工作项id',
    PRIMARY KEY (`test_case_work_id`) USING BTREE
) ENGINE=InnoDB COMMENT='测试用例与工作项(需求)的关联表';

CREATE TABLE `t_test_plan`
(
    `test_plan_id` int     NOT NULL AUTO_INCREMENT COMMENT 'id',
    `extends`      text COMMENT '自定义字段',
    `contents`     text    NOT NULL COMMENT '描述',
    `is_delete`    tinyint NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `version`      int     NOT NULL DEFAULT '1' COMMENT '版本号',
    PRIMARY KEY (`test_plan_id`) USING BTREE
) ENGINE=InnoDB COMMENT='测试计划';

CREATE TABLE `t_test_plan_work_case`
(
    `test_plan_work_case_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `test_plan_id`           int NOT NULL DEFAULT '0' COMMENT '计划id',
    `cnt_id`                 int NOT NULL DEFAULT '0' COMMENT '需求id',
    `test_case_id`           int NOT NULL DEFAULT '0' COMMENT '用例id',
    PRIMARY KEY (`test_plan_work_case_id`) USING BTREE
) ENGINE=InnoDB COMMENT='工作项下的需求与用例的关联表';

CREATE TABLE `t_work_comment`
(
    `comment_id`       int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`        int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name`   varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`        timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`        tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`        int          NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name`   varchar(50)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`        timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `work_items_id`    int          NOT NULL DEFAULT '0' COMMENT '所属内容id',
    `content`          text         NOT NULL COMMENT '内容',
    `remark`           varchar(100) NOT NULL DEFAULT '' COMMENT '其他说明',
    `reply_id`         int          NOT NULL DEFAULT '0' COMMENT '回复的内容id(可以理解为记录pid，评论记录0，回复记录对应comment_id)',
    `reply_comment_id` int          NOT NULL DEFAULT '0' COMMENT '回复的评论id(当前所属评论id)',
    `comment_type`     tinyint unsigned NOT NULL DEFAULT '1' COMMENT '类型1迭代2需求3任务4缺陷5测试用例6测试计划',
    `is_top`           tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否置顶1-是 0-否',
    `is_fake_delete`   tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否假删1-是 0-否',
    PRIMARY KEY (`comment_id`) USING BTREE
) ENGINE=InnoDB COMMENT='工作项-评论表';

CREATE TABLE `t_work_hours`
(
    `work_hours_id`  int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`      int          NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_by_name` varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`      timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`      tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `update_by`      int          NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_by_name` varchar(50)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`      timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `cnt_id`         int          NOT NULL DEFAULT '0' COMMENT '工作项id',
    `type`           tinyint      NOT NULL DEFAULT '1' COMMENT '类型;1-预估,2-实际,3-剩余',
    `remark`         varchar(200) NOT NULL DEFAULT '' COMMENT '描述',
    `working_hours`  float        NOT NULL DEFAULT '0' COMMENT '工时',
    `work_date`      int          NOT NULL DEFAULT '0' COMMENT '工作日期',
    PRIMARY KEY (`work_hours_id`) USING BTREE
) ENGINE=InnoDB COMMENT='工时';

CREATE TABLE `t_work_items`
(
    `cnt_id`    int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `extends`   text COMMENT '自定义字段',
    `contents`  text COMMENT '内容',
    `cnt_type`  varchar(255) NOT NULL DEFAULT '0' COMMENT '类型;1-需求,2-任务,3-缺陷',
    `is_delete` tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除;1-是 0-否',
    `version`   int          NOT NULL DEFAULT '1' COMMENT '版本号',
    PRIMARY KEY (`cnt_id`) USING BTREE
) ENGINE=InnoDB COMMENT='工作项';

