-- 表数据更新
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (0, 'env', '环境集合', '', 0, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (0, 'DEV', '开发环境', '1', 1, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (0, 'TEST', '测试环境', '2', 1, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (0, 'UAT', 'UAT环境', '3', 1, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (0, 'PRO', '生产环境', '4', 1, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (0, 'systemRole', '角色集合', '', 0, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (0, 'softwareTest', '软件测试', 'softwareTest', 6, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (0, 'backendDevelopment', '后端开发', 'backendDevelopment', 6, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (0, 'webDevelop', '前端开发', 'webDevelop', 6, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'uiDesigner', 'UI设计师', 'uiDesigner', 6, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'productManager', '产品经理', 'productManager', 6, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectManager', '项目经理', 'projectManager', 6, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'flowStatusType', '工作流-状态类型', '', 0, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'flowStartStatus', '开始状态', '1', 13, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'flowProcessStatus', '过程状态', '2', 13, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'flowEndStatus', '结束状态', '3', 13, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon', '项目图标', '', 0, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon1', '项目图标1', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/14%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon2', '项目图标2', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/2%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon3', '项目图标3', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/3%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon4', '项目图标4', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/4%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon5', '项目图标5', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/5%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon6', '项目图标6', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/6%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon7', '项目图标7', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/7%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon8', '项目图标8', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/8%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon9', '项目图标9', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/9%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon10', '项目图标10', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/10%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon11', '项目图标11', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/11%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon12', '项目图标12', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/12%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon13', '项目图标13', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/13%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon14', '项目图标14', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/1%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectIcon15', '项目图标15', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/project_icon/15%401x.png', 17, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'task_status_type', '任务状态', '', 0, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'notStarted', '未开始', '1', 33, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'inProgress', '进行中', '2', 33, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'completed', '已完成', '3', 33, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectStatusType', '项目状态', '', 0, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 1, 'projectStartStatus', '待启动', '0', 37, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectProcessStatus', '进行中', '1', 37, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'projectEndStatus', '关闭', '2', 37, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'plan_status_type', '计划状态', '', 0, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'plan_status_open', '开启', '1', 41, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'plan_status_close', '关闭', '2', 41, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'iterationLeader', '迭代leader', 'iterationLeader', 6, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'iterationIcon', '迭代图标', '', 0, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'iterationIcon1', '迭代图标1', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/iteration_icon/1%401x.png', 45, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'iterationIcon2', '迭代图标2', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/iteration_icon/2%401x.png', 45, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'iterationIcon3', '迭代图标3', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/iteration_icon/3%401x.png', 45, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'iterationIcon4', '迭代图标4', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/iteration_icon/4%401x.png', 45, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'iterationIcon5', '迭代图标5', 'https://ylw-devops-uat.oss-cn-shenzhen.aliyuncs.com/iteration_icon/5%401x.png', 45, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'test_plan_type', '测试计划类型', '', 0, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'test_plan', '测试计划', '1', 51, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'dev_self_test_plan', '开发自测计划', '2', 51, 1);
INSERT INTO `t_enum` (`is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES ( 0, 'automate', '自动化', '3', 51, 1);


INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (1, 'demand_flow', '[{\"field_name\": \"priority\"}, {\"field_name\": \"category_id\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"developer_uid\"}, {\"field_name\": \"tester_uid\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"estimate_end_time\"}]', 1, '', '[5]');
INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (2, 'flaw_flow', '[{\"field_name\": \"severity_level\"}, {\"field_name\": \"priority\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"bug_type\"}, {\"field_name\": \"bug_source\"}, {\"field_name\": \"environment\"}, {\"field_name\": \"suspend_reason\"}, {\"field_name\": \"bug_reason\"}, {\"field_name\": \"automated_testing\"}, {\"field_name\": \"responsible_department\"}, {\"field_name\": \"reason_desc\"}, {\"field_name\": \"related_functions\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"developer_uid\"}, {\"field_name\": \"tester_uid\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"estimated_work_hours\"}, {\"field_name\": \"actual_work_hours\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"estimate_end_time\"}, {\"field_name\": \"remaining_work\"}]', 1, '', '[7]');
INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (3, 'test_case_execution', '[{\"field_name\": \"test_case_id\"}, {\"field_name\": \"title\"}, {\"field_name\": \"case_tep\"}, {\"field_name\": \"preconditions\"}, {\"field_name\": \"expected_results\"}, {\"field_name\": \"status\"}, {\"field_name\": \"use_case_type\"}, {\"field_name\": \"category_id\"}, {\"field_name\": \"is_main_process\"}, {\"field_name\": \"update_by\"}, {\"field_name\": \"update_at\"}, {\"field_name\": \"create_by\"}, {\"field_name\": \"create_at\"}, {\"field_name\": \"level\"}, {\"field_name\": \"plan_result\"}, {\"field_name\": \"plan_execution_times\"}, {\"field_name\": \"plan_bug_count\"}, {\"field_name\": \"plan_create_by\"}, {\"field_name\": \"plan_create_at\"}]', 1, '', '[8, 10]');
INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (4, 'demand_output_add', '[{\"field_name\": \"estimate_end_time\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"tester_uid\"}, {\"field_name\": \"developer_uid\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"type_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"category_id\"}, {\"field_name\": \"priority\"}, {\"field_name\": \"contents\"}, {\"field_name\": \"title\"}]', 1, '', '[5]');
INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (5, 'demand_output_update', '[{\"field_name\": \"estimate_end_time\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"tester_uid\"}, {\"field_name\": \"developer_uid\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"type_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"category_id\"}, {\"field_name\": \"priority\"}, {\"field_name\": \"contents\"}, {\"field_name\": \"title\"}, {\"field_name\": \"cnt_id\"}]', 1, '', '[5]');
INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (6, 'task_output_add', '[{\"field_name\": \"title\"}, {\"field_name\": \"contents\"}, {\"field_name\": \"priority\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"type_id\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"estimate_end_time\"}, {\"field_name\": \"estimated_work_hours\"}]', 1, '', '[6]');
INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (7, 'task_output_update', '[{\"field_name\": \"cnt_id\"}, {\"field_name\": \"title\"}, {\"field_name\": \"contents\"}, {\"field_name\": \"priority\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"type_id\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"estimate_end_time\"}, {\"field_name\": \"estimated_work_hours\"}]', 1, '', '[6]');
INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (8, 'flaw_output_add', '[{\"field_name\": \"title\"}, {\"field_name\": \"contents\"}, {\"field_name\": \"severity_level\"}, {\"field_name\": \"priority\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"type_id\"}, {\"field_name\": \"bug_type\"}, {\"field_name\": \"bug_source\"}, {\"field_name\": \"environment\"}, {\"field_name\": \"suspend_reason\"}, {\"field_name\": \"bug_reason\"}, {\"field_name\": \"automated_testing\"}, {\"field_name\": \"responsible_department\"}, {\"field_name\": \"reason_desc\"}, {\"field_name\": \"related_functions\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"developer_uid\"}, {\"field_name\": \"tester_uid\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"estimate_end_time\"}, {\"field_name\": \"estimated_work_hours\"}]', 1, '', '[7]');
INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (9, 'flaw_output_update', '[{\"field_name\": \"cnt_id\"}, {\"field_name\": \"title\"}, {\"field_name\": \"contents\"}, {\"field_name\": \"severity_level\"}, {\"field_name\": \"priority\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"type_id\"}, {\"field_name\": \"bug_type\"}, {\"field_name\": \"bug_source\"}, {\"field_name\": \"environment\"}, {\"field_name\": \"suspend_reason\"}, {\"field_name\": \"bug_reason\"}, {\"field_name\": \"automated_testing\"}, {\"field_name\": \"responsible_department\"}, {\"field_name\": \"reason_desc\"}, {\"field_name\": \"related_functions\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"developer_uid\"}, {\"field_name\": \"tester_uid\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"estimate_end_time\"}, {\"field_name\": \"estimated_work_hours\"}]', 1, '', '[7]');
INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (10, 'case_output_add', '[{\"field_name\": \"title\"}, {\"field_name\": \"case_tep\"}, {\"field_name\": \"preconditions\"}, {\"field_name\": \"expected_results\"}, {\"field_name\": \"status\"}, {\"field_name\": \"use_case_type\"}, {\"field_name\": \"category_id\"}, {\"field_name\": \"is_main_process\"}, {\"field_name\": \"level\"}, {\"field_name\": \"cnt_id_list\"}]', 1, '', '[8]');
INSERT INTO `t_field_subset` (`sub_id`, `sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES (11, 'case_output_update', '[{\"field_name\": \"test_case_id\"}, {\"field_name\": \"title\"}, {\"field_name\": \"case_tep\"}, {\"field_name\": \"preconditions\"}, {\"field_name\": \"expected_results\"}, {\"field_name\": \"status\"}, {\"field_name\": \"use_case_type\"}, {\"field_name\": \"category_id\"}, {\"field_name\": \"is_main_process\"}, {\"field_name\": \"level\"}, {\"field_name\": \"cnt_id_list\"}]', 1, '', '[8]');


INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '终端名称', '终端名称', 'client_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 50, \"componentType\": \"Input\"}', 3, 1, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '创建人', 'create_by_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 1, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '创建时间', 'create_at', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 1, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '终端环境配置', '终端环境配置', 'client_env', 'null', 2, 1, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '微服务名称', '微服务名称', 'microservice_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 50, \"componentType\": \"Input\"}', 3, 2, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '创建人', 'create_by_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 2, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '创建时间', 'create_at', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 2, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '微服务环境配置', '微服务环境配置', 'microservice_env', 'null', 2, 2, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '产品名称', '产品名称', 'product_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 50, \"componentType\": \"Input\"}', 4, 3, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '创建人', 'create_by_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 3, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '创建时间', 'create_at', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 3, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '迭代名称', '迭代名称', 'iteration_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 50, \"componentType\": \"Input\"}', 4, 4, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '创建人', 'create_by_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 4, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '创建时间', 'create_at', '{\"type\": \"datetime\", \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 4, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-07-25 17:21:52', 0, 818, '韦顺隆', '2024-07-25 17:22:56', 1, '终端', '终端', 'product_client_list', '{\"url\": \"/devops/client/selector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_139\", \"key\": \"label\", \"title\": \"终端名称\", \"width\": \"100\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 3, 3, 0, 1, 1, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-07-25 17:23:47', 0, 818, '韦顺隆', '2024-07-25 17:24:39', 1, '微服务', '微服务', 'product_microservice_list', '{\"url\": \"/devops/microservice/selector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_492\", \"key\": \"label\", \"title\": \"微服务名称\", \"width\": \"100\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 2, 3, 0, 1, 1, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-07-25 17:21:52', 0, 818, '韦顺隆', '2024-07-25 17:22:56', 1, '终端', '终端', 'iteration_client_list', '{\"url\": \"/devops/client/selectorByProduct\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_139\", \"key\": \"label\", \"title\": \"终端名称\", \"width\": \"100\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 3, 4, 0, 1, 1, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-07-25 17:23:47', 0, 818, '韦顺隆', '2024-07-25 17:24:39', 1, '微服务', '微服务', 'iteration_microservice_list', '{\"url\": \"/devops/microservice/selectorByProduct\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_492\", \"key\": \"label\", \"title\": \"微服务名称\", \"width\": \"100\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 2, 4, 0, 1, 1, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', 'ID', 'cnt_id', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 1, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-24 10:42:52', 1, '', '标题', 'title', '{\"type\": \"text\", \"options\": [], \"maxlength\": 200, \"componentType\": \"Input\"}', 1, 5, 0, 1, 1, 1, 1, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '描述', 'contents', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 5, 0, 1, 1, 1, 1, 'Editor');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-10 17:19:30', 1, '', '状态', 'status_enum_id', '{\"url\": \"/devops/project/flowStatusEnum/selectorEnumStatus\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":2}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 1, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-02 11:29:45', 1, '', '优先级', 'priority', '{\"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_429\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_430\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 5, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-18 15:00:54', 1, '', '分类', 'category_id', '{\"url\": \"/devops/workItems/classify/categorySelector\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":2}\", \"props\": [], \"fields\": [{\"id\": \"row_467\", \"key\": \"label\", \"title\": \"分类名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 1, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-18 11:53:44', 1, '', '父需求', 'parent_id', '{\"url\": \"/devops/workItems/demandSelector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_234\", \"key\": \"label\", \"title\": \"需求名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 1, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-11-15 09:42:33', 1, '', '迭代', 'iteration_id', '{\"url\": \"/devops/project/iterationCatalog/selectorIteration\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"迭代名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"key\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-18 15:05:18', 1, '', '需求类别', 'type_id', '{\"url\": \"/devops/project/category/selector\", \"extra\": \"{\\\"_type\\\":2,\\\"project_id\\\":null}\", \"props\": [], \"fields\": [{\"id\": \"row_2311\", \"key\": \"category_name\", \"title\": \"类别名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"project_category_settings_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 1, 1, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-18 14:57:24', 1, '', '处理人', 'handler_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"props\": [], \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:14:43', 1, '', '开发人员', 'developer_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:14:52', 1, '', '测试人员', 'tester_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:15:02', 1, '', '抄送人', 'cc_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 10:59:56', 1, '', '预估开始时间', 'estimate_start_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 5, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:00:08', 1, '', '预估完成时间', 'estimate_end_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 5, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-12-23 15:56:51', 1, '', '完成时间', 'finish_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 5, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:14:12', 1, '', '修改人', 'update_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:17:11', 1, '', '最后修改时间', 'update_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 5, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:50', 1, '', '创建人', 'create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:17:34', 1, '', '创建时间', 'create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 5, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '预估工时', 'estimated_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '实际工时', 'actual_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '剩余工时', 'remaining_work', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '超出工时', 'exceeding_working_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '进度', 'speed_of_progress', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', 'ID', 'cnt_id', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 6, 0, 0, 0, 1, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '标题', 'title', '{\"type\": \"text\", \"options\": [], \"maxlength\": 200, \"componentType\": \"Input\"}', 1, 6, 0, 1, 1, 1, 1, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '描述', 'contents', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 6, 0, 1, 1, 1, 1, 'Editor');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-28 15:53:36', 1, '', '状态', 'status_enum_id', '{\"url\": \"/devops/workItems/getTaskStatusList\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":3}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 0, 1, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '优先级', 'priority', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 6, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 16:28:06', 1, '', '需求', 'parent_id', '{\"url\": \"/devops/workItems/demandSelector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_234\", \"key\": \"label\", \"title\": \"需求名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 1, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 16:28:20', 1, '', '迭代', 'iteration_id', '{\"url\": \"/devops/project/iterationCatalog/selectorIteration\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"迭代名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"key\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 0, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 809, '田嫚', '2024-10-11 11:18:33', 1, '', '任务类别', 'type_id', '{\"url\": \"/devops/project/category/selector\", \"extra\": \"{\\\"_type\\\":3,\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"category_name\", \"title\": \"类别名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"project_category_settings_id\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 1, 1, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '处理人', 'handler_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:05', 1, '', '抄送人', 'cc_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:02:27', 1, '', '预估开始时间', 'estimate_start_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 6, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:55', 1, '', '预估完成时间', 'estimate_end_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 6, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:02:39', 1, '', '完成时间', 'finish_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 6, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:22', 1, '', '修改人', 'update_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:30', 1, '', '最后修改时间', 'update_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 6, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:34', 1, '', '创建人', 'create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:07', 1, '', '创建时间', 'create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 6, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-11-28 11:36:22', 1, '', '预估工时', 'estimated_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 6, 0, 1, 0, 3, 1, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '实际工时', 'actual_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 6, 0, 1, 0, 3, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '剩余工时', 'remaining_work', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 6, 0, 1, 0, 3, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '超出工时', 'exceeding_working_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 6, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '进度', 'speed_of_progress', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 6, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 1, 6387, '范铁丁', '2024-09-04 10:47:50', 1, '', '迭代节点', 'iteration_process_node_id', '{\"type\": \"text\", \"options\": [], \"maxlength\": 25, \"componentType\": \"Input\"}', 1, 6, 0, 0, 0, 1, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', 'ID', 'cnt_id', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 7, 0, 0, 0, 1, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 6387, '范铁丁', '2024-10-19 16:18:22', 1, '', '标题', 'title', '{\"type\": \"text\", \"props\": [], \"fields\": [], \"options\": [], \"maxlength\": 200, \"componentType\": \"Input\"}', 1, 7, 0, 1, 1, 1, 1, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '描述', 'contents', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 7, 0, 1, 1, 1, 1, 'Editor');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 19:04:53', 1, '', '状态', 'status_enum_id', '{\"url\": \"/devops/project/flowStatusEnum/selectorEnumStatus\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":4}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 1, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '严重程度', 'severity_level', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '优先级', 'priority', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 16:28:06', 1, '', '关联需求', 'parent_id', '{\"url\": \"/devops/workItems/demandSelector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_234\", \"key\": \"label\", \"title\": \"需求名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 1, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 16:28:20', 1, '', '迭代', 'iteration_id', '{\"url\": \"/devops/project/iterationCatalog/selectorIteration\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"迭代名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"key\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-18 10:57:30', 1, '', '缺陷类别', 'type_id', '{\"url\": \"/devops/project/category/selector\", \"extra\": \"{\\\"_type\\\":4,\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"category_name\", \"title\": \"类别名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"project_category_settings_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 1, 1, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '缺陷类型', 'bug_type', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '创建缺陷来源', 'bug_source', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '软件平台', 'environment', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '挂起原因', 'suspend_reason', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '缺陷根源', 'bug_reason', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '自动化发现', 'automated_testing', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '负责部门', 'responsible_department', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '原因描述', 'reason_desc', '{\"type\": \"textarea\", \"options\": [], \"maxlength\": 1000, \"componentType\": \"Input\"}', 1, 7, 0, 1, 0, 1, 1, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '涉及功能点', 'related_functions', '{\"type\": \"textarea\", \"options\": [], \"maxlength\": 1000, \"componentType\": \"Input\"}', 1, 7, 0, 1, 0, 1, 1, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '处理人', 'handler_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '开发人员', 'developer_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '测试人员', 'tester_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:05', 1, '', '抄送人', 'cc_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '验证人', 'verify_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '关闭人', 'closure_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '挂起人', 'suspend_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '解决人', 'solve_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:02:27', 1, '', '预估开始时间', 'estimate_start_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:55', 1, '', '预估完成时间', 'estimate_end_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:02:39', 1, '', '完成时间', 'finish_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:22', 1, '', '修改人', 'update_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:30', 1, '', '最后修改时间', 'update_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:34', 1, '', '创建人', 'create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:07', 1, '', '创建时间', 'create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '接受处理时间', 'acceptance_processing_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '解决时间', 'resolution_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '拒绝时间', 'rejection_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '验证时间', 'verification_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '分配时间', 'assignment_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '关闭时间', 'close_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '重新打开时间', 'reopen_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '挂起时间', 'pending_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-11 14:48:38', 1, '', '预估工时', 'estimated_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 7, 0, 1, 0, 3, 1, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '实际工时', 'actual_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 7, 0, 1, 0, 3, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '剩余工时', 'remaining_work', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 7, 0, 1, 0, 3, 0, '');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '超出工时', 'exceeding_working_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 7, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '进度', 'speed_of_progress', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 7, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', 'ID', 'test_case_id', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 8, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '标题', 'title', '{\"type\": \"text\", \"options\": [], \"maxlength\": 200, \"componentType\": \"Input\"}', 1, 8, 0, 1, 1, 0, 1, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例步骤', 'case_tep', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 8, 0, 1, 1, 0, 1, 'Editor');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '前置条件', 'preconditions', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 8, 0, 1, 1, 0, 1, 'Editor');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '预期结果', 'expected_results', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 8, 0, 1, 1, 0, 1, 'Editor');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例状态', 'status', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 8, 0, 1, 0, 0, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例类型', 'use_case_type', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 8, 0, 1, 0, 0, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例目录', 'category_id', '{\"url\": \"/devops/workItems/classify/categorySelector\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":5}\", \"props\": [], \"fields\": [{\"id\": \"row_467\", \"key\": \"label\", \"title\": \"分类名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 8, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '是否主流程冒烟用例', 'is_main_process', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 8, 0, 1, 0, 0, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:22', 1, '', '修改人', 'update_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 8, 0, 0, 0, 0, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:30', 1, '', '最后修改时间', 'update_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 8, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:34', 1, '', '创建人', 'create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 8, 0, 0, 0, 0, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:07', 1, '', '创建时间', 'create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 8, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '最终结果', 'plan_result', '{\"url\": \"/devops/planUseCase/recordResultAddNotExecutedSelector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_77\", \"key\": \"label\", \"title\": \"标题\", \"width\": \"100\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 10, 0, 0, 0, 0, 0, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '执行次数', 'plan_execution_times', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 10, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '关联缺陷数', 'plan_bug_count', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 10, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '最后执行人', 'plan_create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 10, 0, 0, 0, 0, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '最后执行时间', 'plan_create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 10, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', 'ID', 'test_plan_id', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '标题', 'title', '{\"type\": \"text\", \"options\": [], \"maxlength\": 200, \"componentType\": \"Input\"}', 1, 9, 0, 1, 1, 0, 1, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:22', 1, '', '描述', 'contents', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 9, 0, 1, 1, 0, 1, 'Editor');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 6387, '范铁丁', '2024-10-29 11:57:02', 1, '', '状态', 'status', '{\"url\": \"/devops/testPlan/getStatusList\", \"extra\": \"{\\\"project_id\\\":null}\", \"props\": [], \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"状态名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:34', 1, '', '测试类型', 'user_case_type', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 9, 0, 1, 1, 0, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:07', 1, '', '计划类型', 'plan_type', '{\"url\": \"/devops/testPlan/getPlanTypeList\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '迭代', 'iteration_id', '{\"url\": \"/devops/project/iterationCatalog/selectorIteration\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"迭代名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"key\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '需求数', 'story_count', '{\"step\": 1, \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例数', 'use_case_count', '{\"step\": 1, \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '测试通过率', 'test_pass_rate', '{\"step\": 1, \"options\": [], \"controls\": false, \"precision\": 2, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '测试执行进度', 'execution_progress', '{\"step\": 1, \"options\": [], \"controls\": false, \"precision\": 2, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例覆盖率', 'use_case_coverage', '{\"step\": 1, \"options\": [], \"controls\": false, \"precision\": 2, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '测试负责人', 'test_manager', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '开始时间', 'estimate_start_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 9, 0, 1, 0, 0, 1, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '结束时间', 'estimate_end_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 9, 0, 1, 0, 0, 1, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '修改人', 'update_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 0, 0, 0, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '最后修改时间', 'update_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 9, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '创建人', 'create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 0, 0, 0, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '创建时间', 'create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 9, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES ( 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例等级', 'level', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 8, 0, 1, 0, 0, 1, 'Select');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:22:39', 0, 0, '', '1971-01-01 00:00:00', 1, '预估迭代周期', '预估迭代周期', 'estimate_iteration_cycle', '{\"type\": \"daterange\", \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 4, 0, 1, 0, 0, 1, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2024-11-14 15:35:01', 1, '迭代leader', '迭代leader', 'iteration_leader', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"iterationLeader\\\"}\", \"fields\": [{\"id\": \"row_219\", \"key\": \"user_name\", \"title\": \"用户名\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 1, 1, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:30:47', 0, 818, '韦顺隆', '2024-11-27 10:48:12', 1, '状态', '状态', 'status_enum_id', '{\"url\": \"/devops/project/flowStatusEnum/selectorEnumStatus\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":1}\", \"fields\": [{\"id\": \"row_593\", \"key\": \"label\", \"title\": \"状态\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:31:03', 0, 818, '韦顺隆', '2024-11-27 14:19:30', 1, '当前节点', '当前节点', 'present_node', '{\"url\": \"/devops/iterate/workflowDiagram/nodeSelector\", \"extra\": \"{\\\"flow_process_id\\\":null}\", \"fields\": [{\"id\": \"row_108\", \"key\": \"label\", \"title\": \"节点\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:33:11', 0, 0, '', '1971-01-01 00:00:00', 1, '迭代进度', '迭代进度', 'iteration_progress', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:34:27', 0, 0, '', '1971-01-01 00:00:00', 1, '测试计划完成进度', '测试计划完成进度', 'test_plan_completion_progress', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:34:40', 0, 0, '', '1971-01-01 00:00:00', 1, '开发自测计划完成进度', '开发自测计划完成进度', 'self_test_plan_completion_progress', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:34:56', 0, 0, '', '1971-01-01 00:00:00', 1, '自动化完成进度', '自动化完成进度', 'auto_completion_progress', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:35:06', 0, 0, '', '1971-01-01 00:00:00', 1, '需求数', '需求数', 'demand_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:35:26', 0, 0, '', '1971-01-01 00:00:00', 1, '需求未完成数量', '需求未完成数量', 'demand_incomplete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:35:42', 0, 818, '韦顺隆', '2024-11-07 16:42:36', 1, '需求已完成数量', '需求已完成数量', 'demand_complete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:35:54', 0, 818, '韦顺隆', '2024-11-07 16:42:31', 1, '缺陷数', '缺陷数', 'defect_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:48:53', 0, 818, '韦顺隆', '2024-11-07 16:42:21', 1, '缺陷未完成数量', '缺陷未完成数量', 'defect_incomplete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:49:05', 0, 818, '韦顺隆', '2024-11-07 16:42:15', 1, '缺陷已完成数量', '缺陷已完成数量', 'defect_complete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:49:18', 0, 818, '韦顺隆', '2024-11-07 16:44:35', 1, '任务数', '任务数', 'task_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:49:27', 0, 0, '', '1971-01-01 00:00:00', 1, '任务未完成数量', '任务未完成数量', 'task_incomplete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:49:38', 0, 0, '', '1971-01-01 00:00:00', 1, '任务已完成数量', '任务已完成数量', 'task_complete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-09 14:32:56', 0, 818, '韦顺隆', '2024-11-09 14:37:01', 1, 'ID', 'ID', 'iteration_id', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 4, 0, 0, 0, 0, 1, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-09 14:33:18', 0, 0, '', '1971-01-01 00:00:00', 1, '迭代图标', '迭代图标', 'iteration_icon', '{\"type\": \"text\", \"options\": [], \"maxlength\": 255, \"componentType\": \"Input\"}', 1, 4, 0, 0, 1, 0, 1, 'Input');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-12 15:26:18', 0, 0, '', '1971-01-01 00:00:00', 1, '实际迭代周期', '实际迭代周期', 'actual_iteration_cycle', '{\"type\": \"daterange\", \"format\": \"YYYY-MM-DD hh:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD hh:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 4, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-08-26 15:01:45', 0, 6387, '范铁丁', '2024-09-04 10:47:50', 1, '', '迭代节点名称', 'iteration_process_node_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 25, \"componentType\": \"Input\"}', 1, 6, 0, 0, 0, 1, 0, 'Editor');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2025-01-02 01:33:51', 1, '', '关联需求', 'cnt_id_list', '{\"url\": \"/devops/workItems/demandSelector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_234\", \"key\": \"label\", \"title\": \"需求名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": true, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 8, 0, 0, 0, 0, 1, 'ApiSelect');



INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '新', 0, 'dark-gray', 0, 0, 1);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '接受/处理', 0, 'dark-gray', 0, 0, 1);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已解决', 0, 'dark-gray', 0, 0, 1);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已验证', 0, 'dark-gray', 0, 0, 1);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '重新打开', 0, 'dark-gray', 0, 0, 1);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已拒绝', 0, 'dark-gray', 0, 0, 1);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '挂起', 0, 'dark-gray', 0, 0, 1);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已关闭', 0, 'dark-gray', 0, 0, 1);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '新', 0, 'dark-gray', 0, 0, 2);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '接受/处理', 0, 'dark-gray', 0, 0, 2);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已解决', 0, 'dark-gray', 0, 0, 2);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已验证', 0, 'dark-gray', 0, 0, 2);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '重新打开', 0, 'dark-gray', 0, 0, 2);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已拒绝', 0, 'dark-gray', 0, 0, 2);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '挂起', 0, 'dark-gray', 0, 0, 2);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已关闭', 0, 'dark-gray', 0, 0, 2);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '新', 0, 'dark-gray', 0, 0, 3);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '接受/处理', 0, 'dark-gray', 0, 0, 3);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已解决', 0, 'dark-gray', 0, 0, 3);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已验证', 0, 'dark-gray', 0, 0, 3);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '重新打开', 0, 'dark-gray', 0, 0, 3);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已拒绝', 0, 'dark-gray', 0, 0, 3);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '挂起', 0, 'dark-gray', 0, 0, 3);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已关闭', 0, 'dark-gray', 0, 0, 3);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '新', 0, 'dark-gray', 1, 1, 4);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '接受/处理', 0, 'dark-gray', 1, 2, 4);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已解决', 0, 'dark-gray', 1, 3, 4);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已验证', 0, 'dark-gray', 1, 4, 4);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '重新打开', 0, 'dark-gray', 1, 5, 4);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已拒绝', 0, 'dark-gray', 1, 6, 4);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '挂起', 0, 'dark-gray', 1, 7, 4);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已关闭', 0, 'dark-gray', 1, 8, 4);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '新', 0, 'dark-gray', 0, 0, 5);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '接受/处理', 0, 'dark-gray', 0, 0, 5);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已解决', 0, 'dark-gray', 0, 0, 5);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已验证', 0, 'dark-gray', 0, 0, 5);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '重新打开', 0, 'dark-gray', 0, 0, 5);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已拒绝', 0, 'dark-gray', 0, 0, 5);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '挂起', 0, 'dark-gray', 0, 0, 5);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已关闭', 0, 'dark-gray', 0, 0, 5);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '新', 0, 'dark-gray', 0, 0, 6);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '接受/处理', 0, 'dark-gray', 0, 0, 6);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已解决', 0, 'dark-gray', 0, 0, 6);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已验证', 0, 'dark-gray', 0, 0, 6);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '重新打开', 0, 'dark-gray', 0, 0, 6);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已拒绝', 0, 'dark-gray', 0, 0, 6);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '挂起', 0, 'dark-gray', 0, 0, 6);
INSERT INTO `t_flow_status_enum` (`attribution_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `name`, `project_id`, `colour`, `is_permanent`, `status_enum_status`, `status_enum_type`) VALUES (0, 818, '韦顺隆', '2021-10-19 18:26:02', 0, 0, '', '1971-01-01 00:00:00', '已关闭', 0, 'dark-gray', 0, 0, 6);


INSERT INTO `t_project` (`project_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `project_no`, `project_name`, `product_id`, `project_user_count`, `project_status`, `project_remark`, `project_template_id`, `project_icon`, `is_template`) VALUES (1, 818, '韦顺隆', '2024-10-10 15:18:07', 0, 818, '韦顺隆', '2024-10-21 18:49:32', 0, '手动创建项目', 0, 1, 1, '手动创建项目', 1, 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/1%401x.png', 1);
INSERT INTO `t_project_template` (`project_template_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `project_template_name`, `project_id`, `is_allow_delete`, `remark`, `sort`) VALUES (1, 818, '韦顺隆', '2024-10-09 15:48:40', 0, 896, '王赞深', '2024-12-18 15:33:24', '项目通用模板', 1, 0, '项目通用模板', 5);
