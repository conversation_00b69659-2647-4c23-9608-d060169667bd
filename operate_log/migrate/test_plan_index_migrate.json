
//创建
PUT /test_plan_index_new1
{
  "mappings": {
    "properties": {
      "test_plan_id": {
        "type": "integer"
      },
      "project_id": {
        "type": "integer"
      },
      "create_by": {
        "type": "integer"
      },
      "create_by_name": {
        "type": "keyword"
      },
      "create_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "is_delete": {
        "type": "integer"
      },
      "update_by": {
        "type": "integer"
      },
      "update_by_name": {
        "type": "keyword"
      },
      "update_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "title": {
        "type": "keyword"
      },
      "status": {
        "type": "integer"
      },
      "user_case_type": {
        "type": "keyword"
      },
      "plan_type": {
        "type": "keyword"
      },
      "iteration_id": {
        "type": "integer"
      },
      "story_count": {
        "type": "integer"
      },
      "use_case_count": {
        "type": "integer"
      },
      "test_pass_rate": {
        "type": "float"
      },
      "execution_progress": {
        "type": "float"
      },
      "use_case_coverage": {
        "type": "float"
      },
      "test_manager": {
        "type": "integer"
      },
      "estimate_start_time": {
        "type": "date",
        "format": "yyyy-MM-dd||epoch_millis"
      },
      "estimate_end_time": {
        "type": "date",
        "format": "yyyy-MM-dd||epoch_millis"
      }
    },
    "dynamic_templates": [
      {
        "strings_as_keywords": {
          "match_mapping_type": "string",
          "mapping": {
            "type": "keyword"
          }
        }
      }
    ]
  },
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1,
    "refresh_interval": "500ms"
  }
}

//设置源只读
PUT /test_plan_index/_settings
{
  "settings": {
    "index.blocks.write": true
  }
}

//迁移
POST /_reindex
{
  "source": {
    "index": "test_plan_index"
  },
  "dest": {
    "index": "test_plan_index_new1"
  }
}

//删除源
DELETE /test_plan_index

//设置别名
POST /_aliases
{
  "actions": [
    { "add": { "index": "test_plan_index_new1", "alias": "test_plan_index" } }
  ]
}
