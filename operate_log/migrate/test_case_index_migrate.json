
//创建
PUT /test_case_index_new1
{
  "mappings": {
    "properties": {
      "test_case_id": {
        "type": "integer"
      },
      "create_by": {
        "type": "integer"
      },
      "create_by_name": {
        "type": "keyword"
      },
      "create_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "is_delete": {
        "type": "integer"
      },
      "update_by": {
        "type": "integer"
      },
      "update_by_name": {
        "type": "keyword"
      },
      "update_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "project_id": {
        "type": "integer"
      },
      "title": {
        "type": "keyword"
      },
      "case_tep": {
        "type": "keyword"
      },
      "preconditions": {
        "type": "keyword"
      },
      "expected_results": {
        "type": "keyword"
      },
      "status": {
        "type": "keyword"
      },
      "use_case_type": {
        "type": "keyword"
      },
      "category_id": {
        "type": "integer"
      },
      "is_main_process": {
        "type": "keyword"
      },
      "level": {
        "type": "keyword"
      },
      "plan_list": {
        "type": "nested"
      }
    },
    "dynamic_templates": [
      {
        "strings_as_keywords": {
          "match_mapping_type": "string",
          "mapping": {
            "type": "keyword"
          }
        }
      }
    ]
  },
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1,
    "refresh_interval": "500ms"
  }
}

//设置源只读
PUT /test_case_index/_settings
{
  "settings": {
    "index.blocks.write": true
  }
}

//迁移
POST /_reindex
{
  "source": {
    "index": "test_case_index"
  },
  "dest": {
    "index": "test_case_index_new1"
  }
}

//删除源
DELETE /test_case_index

//设置别名
POST /_aliases
{
  "actions": [
    { "add": { "index": "test_case_index_new1", "alias": "test_case_index" } }
  ]
}
