POST devops_content_index/_update_by_query
{
  "script": {
    "source": """
      // 初始化meeting_collection数组
      ctx._source.meeting_collection = [];
      
      // 如果原字段存在，创建新对象并添加到数组中
      if (ctx._source.meeting_recording != null || ctx._source.meeting_link != null) {
        Map meetingObj = new HashMap();
        
        if (ctx._source.meeting_recording != null) {
          meetingObj.put("meeting_recording", ctx._source.meeting_recording);
        }
        
        if (ctx._source.meeting_link != null) {
          meetingObj.put("meeting_link", ctx._source.meeting_link);
        }
        
        ctx._source.meeting_collection.add(meetingObj);
      }
      
      // 删除原字段
      ctx._source.remove("meeting_recording");
      ctx._source.remove("meeting_link");
    """,
    "lang": "painless"
  },
  "query": {
    "bool": {
      "should": [
        {
          "exists": {
            "field": "meeting_recording"
          }
        },
        {
          "exists": {
            "field": "meeting_link"
          }
        }
      ]
    }
  }
}

// 验证迁移结果的查询
//GET devops_content_index/_search
//{
//  "query": {
//    "exists": {
//      "field": "meeting_collection"
//    }
//  }
//}