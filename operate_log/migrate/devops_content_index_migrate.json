
//源 devops_content_index
//目标 devops_content_index_new1
//别名 devops_content_index





//创建
PUT /devops_content_index_new1
{
  "mappings": {
    "properties": {
      "cnt_id": {
        "type": "integer"
      },
      "create_by": {
        "type": "integer"
      },
      "create_by_name": {
        "type": "keyword"
      },
      "create_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "is_delete": {
        "type": "byte"
      },
      "update_by": {
        "type": "integer"
      },
      "update_by_name": {
        "type": "keyword"
      },
      "update_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "cnt_type": {
        "type": "keyword"
      },
      "title": {
        "type": "keyword"
      },
      "category_id": {
        "type": "integer"
      },
      "project_id": {
        "type": "integer"
      },
      "iteration_id": {
        "type": "integer"
      },
      "parent_id": {
        "type": "integer"
      },
      "priority": {
        "type": "keyword"
      },
      "type_id": {
        "type": "integer"
      },
      "handler_uid": {
        "type": "keyword"
      },
      "developer_uid": {
        "type": "keyword"
      },
      "tester_uid": {
        "type": "keyword"
      },
      "cc_uid": {
        "type": "keyword"
      },
      "estimate_start_time": {
        "type": "date",
        "format": "yyyy-MM-dd||epoch_millis"
      },
      "estimate_end_time": {
        "type": "date",
        "format": "yyyy-MM-dd||epoch_millis"
      },
      "finish_time": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "estimated_work_hours": {
        "type": "float"
      },
      "actual_work_hours": {
        "type": "float"
      },
      "remaining_work": {
        "type": "float"
      },
      "exceeding_working_hours": {
        "type": "float"
      },
      "speed_of_progress": {
        "type": "integer"
      },
      "iteration_process_node_id": {
        "type": "integer"
      },
      "task_type": {
        "type": "integer"
      },
      "isEnd": {
        "type": "boolean"
      },
      "subset": {
        "type": "keyword"
      },
      "subset_cnt_type": {
        "type": "keyword"
      },
      "is_set_iteration": {
        "type": "boolean"
      },
      "deliverables_to_be_submitted": {
        "type": "boolean"
      },
      "upload_link": {
        "type": "keyword"
      },
      "upload_attachments": {
        "type": "object"
      },
      "meeting_required": {
        "type": "boolean"
      },
      "meeting_recording": {
        "type": "keyword"
      },
      "meeting_link": {
        "type": "keyword"
      },
      "only_transferable_after_completion": {
        "type": "boolean"
      }
    },
    "dynamic_templates": [
      {
        "strings_as_keywords": {
          "match_mapping_type": "string",
          "mapping": {
            "type": "keyword"
          }
        }
      }
    ]
  },
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1,
    "refresh_interval": "500ms"
  }
}

//设置源只读
PUT /devops_content_index/_settings
{
  "settings": {
    "index.blocks.write": true
  }
}

//迁移
POST /_reindex
{
  "source": {
    "index": "devops_content_index"
  },
  "dest": {
    "index": "devops_content_index_new1"
  }
}

//删除源
DELETE /devops_content_index

//设置别名
POST /_aliases
{
  "actions": [
    { "add": { "index": "devops_content_index_new1", "alias": "devops_content_index" } }
  ]
}
