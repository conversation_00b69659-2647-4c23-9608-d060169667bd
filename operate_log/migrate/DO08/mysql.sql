UPDATE t_field_config
SET field_component = JSON_REPLACE(field_component, '$.method', 'POST')
WHERE field_component ->> '$.method' = 'GET'
  and module_id in (4,5, 6, 7, 8, 9,10);


alter table t_project_category
    modify category_name varchar(100) default '' not null comment '名称';



-- 更新所有选人组件
UPDATE t_field_config
SET field_component = JSON_SET(
        field_component,
        '$.fields', JSON_ARRAY(
                JSON_OBJECT('id', 'row_446', 'key', 'en_user_name', 'title', '英文名', 'width', '200'),
                JSON_OBJECT('id', 'row_453', 'key', 'user_name', 'title', '中文名', 'width', '200'),
                JSON_OBJECT('id', 'row_461', 'key', 'position_name', 'title', '岗位', 'width', '200')
                    )
                      )
WHERE field_component ->> '$.url' = '/devops/project/projectUser/selectorListQuery';

UPDATE t_field_config
SET field_component = JSON_SET(
        field_component,
        '$.inputKey', JSON_ARRAY('en_user_name', 'user_name', 'position_name')
                      )
WHERE field_component ->> '$.url' = '/devops/project/projectUser/selectorListQuery';

