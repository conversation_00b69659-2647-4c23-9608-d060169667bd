PUT /devops_content_index/_mapping
{
  "properties": {
    "sort_order": {
      "type": "double"
    }
  }
}

POST /devops_content_index/_update_by_query
{
  "script": {
    "source": """
    if (ctx._source.cnt_id != null) {
  try {
  ctx._source.sort_order = Double.parseDouble(ctx._source.cnt_id.toString());
} catch (Exception e) {
  ctx._source.sort_order = 0.0;
}
}
""",
"lang": "painless"
},
"query": {
"match_all": {}
}
}



PUT /devops_content_index/_settings
{
"index.max_inner_result_window": 1000000
}