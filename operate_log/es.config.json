PUT /devops_content_index
{
  "mappings": {
    "properties": {
      "cnt_id": {
        "type": "integer"
      },
      "create_by": {
        "type": "integer"
      },
      "create_by_name": {
        "type": "keyword"
      },
      "create_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "is_delete": {
        "type": "byte"
      },
      "update_by": {
        "type": "integer"
      },
      "update_by_name": {
        "type": "keyword"
      },
      "update_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "cnt_type": {
        "type": "keyword"
      },
      "title": {
        "type": "keyword"
      },
      "category_id": {
        "type": "integer"
      },
      "project_id": {
        "type": "integer"
      },
      "iteration_id": {
        "type": "integer"
      },
      "parent_id": {
        "type": "integer"
      },
      "priority": {
        "type": "keyword"
      },
      "type_id": {
        "type": "integer"
      },
      "handler_uid": {
        "type": "keyword"
      },
      "developer_uid": {
        "type": "keyword"
      },
      "tester_uid": {
        "type": "keyword"
      },
      "cc_uid": {
        "type": "keyword"
      },
      "estimate_start_time": {
        "type": "date",
        "format": "yyyy-MM-dd||epoch_millis"
      },
      "estimate_end_time": {
        "type": "date",
        "format": "yyyy-MM-dd||epoch_millis"
      },
      "finish_time": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "estimated_work_hours": {
        "type": "float"
      },
      "actual_work_hours": {
        "type": "float"
      },
      "remaining_work": {
        "type": "float"
      },
      "exceeding_working_hours": {
        "type": "float"
      },
      "speed_of_progress": {
        "type": "integer"
      },
      "iteration_process_node_id": {
        "type": "integer"
      },
      "task_type": {
        "type": "integer"
      },
      "isEnd": {
        "type": "boolean"
      },
      "subset": {
        "type": "keyword"
      },
      "subset_cnt_type": {
        "type": "keyword"
      },
      "is_set_iteration": {
        "type": "boolean"
      },
      "deliverables_to_be_submitted": {
        "type": "boolean"
      },
      "upload_link": {
        "type": "keyword"
      },
      "upload_attachments": {
        "type": "object"
      },
      "meeting_required": {
        "type": "boolean"
      },
      "meeting_recording": {
        "type": "keyword"
      },
      "meeting_link": {
        "type": "keyword"
      },
      "only_transferable_after_completion": {
        "type": "boolean"
      }
    }
  },
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1
  }
}


PUT /test_case_index
{
  "mappings": {
    "properties": {
      "test_case_id": {
        "type": "integer"
      },
      "create_by": {
        "type": "integer"
      },
      "create_by_name": {
        "type": "keyword"
      },
      "create_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "is_delete": {
        "type": "integer"
      },
      "update_by": {
        "type": "integer"
      },
      "update_by_name": {
        "type": "keyword"
      },
      "update_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "project_id": {
        "type": "integer"
      },
      "title": {
        "type": "keyword"
      },
       "case_tep": {
        "type": "keyword"
      },
       "preconditions": {
        "type": "keyword"
      },
       "expected_results": {
        "type": "keyword"
      },
      "status": {
        "type": "keyword"
      },
      "use_case_type": {
        "type": "keyword"
      },
      "category_id": {
        "type": "integer"
      },
      "is_main_process": {
        "type": "keyword"
      },
          "level": {
        "type": "keyword"
      },
      "plan_list": {
        "type": "nested"
      }
    }
  },
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1
  }
}

PUT /test_plan_index
{
  "mappings": {
    "properties": {
      "test_plan_id": {
        "type": "integer"
      },
      "project_id": {
        "type": "integer"
      },
      "create_by": {
        "type": "integer"
      },
      "create_by_name": {
        "type": "keyword"
      },
      "create_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "is_delete": {
        "type": "integer"
      },
      "update_by": {
        "type": "integer"
      },
      "update_by_name": {
        "type": "keyword"
      },
      "update_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
      },
      "title": {
        "type": "keyword"
      },
      "status": {
        "type": "integer"
      },
      "user_case_type": {
        "type": "keyword"
      },
      "plan_type": {
        "type": "keyword"
      },
      "iteration_id": {
        "type": "integer"
      },
      "story_count": {
        "type": "integer"
      },
      "use_case_count": {
        "type": "integer"
      },
      "test_pass_rate": {
        "type": "float"
      },
      "execution_progress": {
        "type": "float"
      },
      "use_case_coverage": {
        "type": "float"
      },
      "test_manager": {
        "type": "integer"
      },
      "estimate_start_time": {
        "type": "date",
        "format": "yyyy-MM-dd||epoch_millis"
      },
      "estimate_end_time": {
        "type": "date",
        "format": "yyyy-MM-dd||epoch_millis"
      }
    }
  },
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1
  }
}