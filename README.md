ThinkPHP 6.0
===============

> 运行环境要求PHP7.2+，兼容PHP8.1
>
设置提交模板
> git config commit.template commit_template.txt

环境信息
~~~
# dev
域名 前端 devops.dev.shijizhongyun.com  接口 devopsapi.dev.shijizhongyun.com

kibana:
http://kibana-dev.default.svc.cluster.local:5601/

#test
域名 前端 devops.test.shijizhongyun.com  接口 devopsapi.test.shijizhongyun.com
~~~

>快捷指令集合
~~~
// 命令行方法，增加应用模块
php think app user(user = 需要增加的应用名)
php think model 表名 模型名 生成的目录(app/test/model/system)
php think logic 模型名称 生成的目录(app/test/logic)
~~~

>路由器权限配置
~~~
示例 1：路由需要权限校验
Route::get('permissions', '\app\infrastructure\controller\Permissions@permissions')
         ->middleware(PermissionMiddleware::class); // 获取按钮权限

示例 2：需要存在指定角色才能访问
Route::get('permissions', '\app\infrastructure\controller\Permissions@permissions')
        ->middleware(AppointPermissionRoleMiddleware::class, 'iterationLeader'); // 获取按钮权限
【Ps:前置条件，需要加载 PermissionMiddleware 中间件】
~~~
