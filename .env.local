APP_DEBUG = true
ENVIRONMENT = local

[APP]
DEFAULT_TIMEZONE = Asia/Shanghai

[DATABASE]
TYPE = mysql
HOSTNAME = localhost
DATABASE = devops
USERNAME = devops
PASSWORD = J2deNWWzJ6TAHtA5
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = true
PREFIX = t_

[REDIS]
password = gaVEHR3V7qiY
host = devops-redis.dev.sjzy.local
port = 6379
select = 0

[ES]
host = http://localhost:9200
user =
pass =
content_index = devops_content_index
test_case_index = test_case_index
test_plan_index = test_plan_index

[LANG]
default_lang = zh-cn

[OPENTELEMETRY]
HOST = http://daemonset-collector.opentelemetry-operator-system.svc.cluster.local:4318
SERVICE_NAME = devops.dev
OPEN = TRUE

[PLATFORM]
HOST = https://centerapi.dev.shijizhongyun.com
TIMEOUT = 50
# 当前系统id（中台）
CENTER_SUB_SYSTEM_ID = 9534

[OSS]
endpoint = https://oss-cn-shenzhen.aliyuncs.com
access_key_id = LTAI5t8xkpnoT1SnPXTsszKH
access_key_secret = ******************************
bucket = ylw-devops-test



[FRONT_END_ADDRESS]
url=https://devops.gotofreight.com