{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.1", "topthink/framework": "^6.1.0", "topthink/think-orm": "^2.0", "topthink/think-filesystem": "^1.0", "open-telemetry/sdk": "^1.0", "open-telemetry/exporter-otlp": "^1.0", "open-telemetry/opentelemetry": "^1.0", "guzzlehttp/promises": "^2.0", "php-http/message-factory": "^1.1", "php-http/httplug": "^2.4", "guzzlehttp/guzzle": "^7.8", "elasticsearch/elasticsearch": "^8.13", "aliyuncs/oss-sdk-php": "^2.7", "predis/predis": "^2.2", "phpoffice/phpspreadsheet": "^3.6", "react/event-loop": "^1.5", "overtrue/pinyin": "^5.0"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "allow-plugins": {"php-http/discovery": true, "tbachert/spi": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}