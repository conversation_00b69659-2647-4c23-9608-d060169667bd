# Go 重构项目存储架构设计文档

## 1. 背景与目标

本项目旨在对现有的 PHP 项目进行 Go 语言重构。在重构过程中，我们希望解决当前存储架构的核心痛点，并设计一套能够满足未来业务发展需求的、现代化、高性能、易于维护的存储方案。

### 1.1. 核心需求与挑战

经过深入分析，我们确定了新架构必须满足的几个核心需求：

1.  **实时一致性**: 用户对数据的任何修改，都必须能立刻在查询结果中体现，不能有延迟。
2.  **动态字段支持**: 系统必须支持用户自定义字段，这些字段的名字（key）和值（value）都是完全动态的。
3.  **动态字段类型可知**: 在创建自定义字段时，其数据类型（如文本、日期、数字等）是已知的。
4.  **强大的查询能力**: 必须支持对这些完全动态的字段进行快速的**等值查询**和**范围查询**。

这些需求的组合，特别是“实时一致性”和“对完全动态 key 的范围查询”，对架构设计提出了极高的挑战。

## 2. 最终架构方案：元数据驱动的 PostgreSQL

为了在单一数据库内完美解决上述所有矛盾，我们决定采用一种创新的、基于 PostgreSQL 的“元数据驱动”架构。该方案的核心思想是，通过 `JSONB` 的灵活性承载数据的动态性，并通过一个元数据表和后台任务来驱动查询性能的动态优化。

### 2.1. 架构图

```mermaid
graph TD
    subgraph "应用层 (Go)"
        A[API Service] --> B{业务逻辑}
        B -- "读/写 work_items" --> C[PostgreSQL]
        B -- "读/写 dynamic_field_definitions" --> C
        D[后台索引管理服务] -- "监控元数据表变更" --> C
        D -- "执行 CREATE/DROP INDEX" --> C
    end

    subgraph "数据库层"
        C
    end

    style C fill:#6fe,stroke:#333,stroke-width:2px
```

### 2.2. 数据库表结构设计

#### 2.2.1. `work_items` 表

存储核心业务数据。所有动态字段被归类到 `dynamic_fields` 这一个 `JSONB` 列中，并按类型分区。

```sql
CREATE TABLE work_items (
    id SERIAL PRIMARY KEY,
    project_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    -- ... 其他固定字段

    -- 所有动态字段都存放在这里，并按类型分组
    dynamic_fields JSONB
);

-- 示例 `dynamic_fields` 内容:
-- {
--   "texts": {
--     "assignee": "张三",
--     "status_text": "进行中"
--   },
--   "dates": {
--     "deadline": "2025-12-31",
--     "start_date": "2025-01-01"
--   },
--   "numerics": {
--     "story_points": 8,
--     "priority_score": 95.5
--   }
-- }
```

#### 2.2.2. `dynamic_field_definitions` 表

存储动态字段的元数据定义，是驱动整个方案的核心。

```sql
CREATE TABLE dynamic_field_definitions (
    field_key VARCHAR(100) PRIMARY KEY, -- 动态字段的key，如 "deadline"
    field_name VARCHAR(255) NOT NULL,   -- 字段的显示名称，如 "截止日期"
    field_type VARCHAR(20) NOT NULL,    -- 字段类型: 'text', 'date', 'numeric', 'boolean'
    is_indexed_for_range BOOLEAN DEFAULT FALSE -- 核心标记：此字段是否需要高性能范围查询？
);
```

### 2.3. 索引策略

1.  **通用 GIN 索引 (为等值查询)**
    为 `dynamic_fields` 列创建一个 GIN 索引，以支持对**任何**动态字段进行快速的等值/包含查询。
    ```sql
    CREATE INDEX idx_gin_dynamic_fields ON work_items USING GIN (dynamic_fields);
    ```
    **查询示例**: `WHERE dynamic_fields @> '{"texts": {"assignee": "张三"}}';`

2.  **部分表达式索引 (为范围查询)**
    这是本方案的精髓。我们**只为**在 `dynamic_field_definitions` 表中被标记为 `is_indexed_for_range = TRUE` 的字段创建 B-Tree 索引，以支持高性能的范围查询。这些索引由后台服务动态、异步、安全地创建和删除。

    **示例**: 当 `field_key = 'deadline'` 的记录被创建且 `is_indexed_for_range` 为 `TRUE` 时，后台服务将执行：
    ```sql
    CREATE INDEX CONCURRENTLY idx_range_deadline 
    ON work_items (((dynamic_fields -> 'dates' ->> 'deadline')::date))
    WHERE (dynamic_fields -> 'dates' ->> 'deadline') IS NOT NULL;
    ```

### 2.4. 核心工作流程

#### 2.4.1. 数据操作流程 (高频)
Go 应用在处理日常的数据增、删、改时，只需操作 `work_items` 表即可。数据库会自动维护已存在的索引，应用层代码无需关心索引的存在。

#### 2.4.2. 字段定义流程 (低频)
当管理员通过界面定义一个新的自定义字段时：
1.  在 `dynamic_field_definitions` 表中 `INSERT` 或 `UPDATE` 一条记录。
2.  Go 应用的后台索引管理服务监听到这张表的变更（通过轮询或数据库触发器）。
3.  如果一个字段的 `is_indexed_for_range` 状态发生改变，后台服务会：
    *   **变为 `TRUE`**: 生成并执行 `CREATE INDEX CONCURRENTLY` 命令。
    *   **变为 `FALSE`**: 生成并执行 `DROP INDEX CONCURRENTLY` 命令。
4.  所有索引操作都在后台安全执行，不影响主应用。

## 3. 方案优势总结

*   **强一致性**: 完美满足实时可见的需求，无数据延迟。
*   **极高灵活性**: 支持完全动态的字段名和值。
*   **高性能**: 通过按需创建的表达式索引，保证了范围查询的性能。
*   **架构简洁**: 仅需维护 PostgreSQL 一个数据存储系统，降低了运维成本和系统复杂性。

## 4. 风险与应对

*   **实现复杂度**: 方案的核心挑战在于实现“后台索引管理服务”。
    *   **应对**: 该服务逻辑清晰，与核心业务解耦，可以独立开发和测试。需注意 SQL 注入风险，所有动态生成的 SQL 片段必须严格校验。
*   **索引管理**: 大量可范围查询的字段可能导致索引过多。
    *   **应对**: 在产品层面可以对可创建的、需要范围索引的字段数量进行限制。同时，后台服务应提供完善的索引监控和管理功能。

## 5. 下一步行动

建议成立一个技术小组，基于本设计文档，进行为期约一周的技术验证（PoC），以确认方案在真实场景下的性能和开发体验，为全面的重构工作奠定坚实的基础。