{"openapi": "3.0.0", "info": {"title": "日清bug统计规则设置接口文档", "description": "日清bug统计规则设置(NissinBugStatisticsRules)相关接口的详细说明", "version": "1.0.0", "contact": {"name": "开发团队"}}, "servers": [{"url": "/", "description": "API服务器"}], "tags": [{"name": "日清bug统计规则设置", "description": "日清bug统计规则设置相关接口"}], "paths": {"/project/nissinBugStatisticsRules/detail/{id}": {"get": {"tags": ["日清bug统计规则设置"], "summary": "获取日清bug统计规则详情", "description": "根据ID获取日清bug统计规则的详细信息", "operationId": "getDetail", "parameters": [{"name": "id", "in": "path", "description": "日清bug统计规则ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesDetailResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/nissinBugStatisticsRules/byProject/{projectId}": {"get": {"tags": ["日清bug统计规则设置"], "summary": "根据项目ID获取日清bug统计规则", "description": "根据项目ID获取日清bug统计规则，如果不存在则返回默认配置", "operationId": "getByProjectId", "parameters": [{"name": "projectId", "in": "path", "description": "项目ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesDetailResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/nissinBugStatisticsRules/page": {"get": {"tags": ["日清bug统计规则设置"], "summary": "分页查询日清bug统计规则", "description": "分页查询日清bug统计规则列表", "operationId": "pageQuery", "parameters": [{"name": "project_id", "in": "query", "description": "项目ID", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "page", "in": "query", "description": "页码，默认1", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "description": "每页条数，默认20", "required": false, "schema": {"type": "integer", "default": 20}}, {"name": "enable", "in": "query", "description": "是否启用：1-是，0-否", "required": false, "schema": {"type": "integer", "enum": [0, 1]}}, {"name": "statistics", "in": "query", "description": "统计日期：1-当天，2-昨天", "required": false, "schema": {"type": "integer", "enum": [1, 2]}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesPageResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/nissinBugStatisticsRules/create": {"post": {"tags": ["日清bug统计规则设置"], "summary": "创建日清bug统计规则", "description": "创建日清bug统计规则", "operationId": "create", "requestBody": {"description": "日清bug统计规则信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesCreateResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/nissinBugStatisticsRules/update": {"post": {"tags": ["日清bug统计规则设置"], "summary": "更新日清bug统计规则", "description": "更新日清bug统计规则", "operationId": "update", "requestBody": {"description": "日清bug统计规则信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesUpdateResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/nissinBugStatisticsRules/delete": {"post": {"tags": ["日清bug统计规则设置"], "summary": "删除日清bug统计规则", "description": "删除日清bug统计规则", "operationId": "delete", "requestBody": {"description": "日清bug统计规则ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesDeleteRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesDeleteResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/nissinBugStatisticsRules/enable": {"post": {"tags": ["日清bug统计规则设置"], "summary": "启用日清bug统计规则", "description": "启用日清bug统计规则", "operationId": "enable", "requestBody": {"description": "日清bug统计规则ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesEnableRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesEnableResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/nissinBugStatisticsRules/disable": {"post": {"tags": ["日清bug统计规则设置"], "summary": "禁用日清bug统计规则", "description": "禁用日清bug统计规则", "operationId": "disable", "requestBody": {"description": "日清bug统计规则ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesDisableRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NissinBugStatisticsRulesDisableResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"NissinBugStatisticsRulesDetail": {"type": "object", "properties": {"nissin_bug_statistics_rules_id": {"type": "integer", "format": "int64", "description": "日清bug统计规则ID"}, "project_id": {"type": "integer", "format": "int64", "description": "项目ID"}, "time": {"type": "string", "description": "定时任务执行时间，格式：HH:MM:SS，例如：08:30:00"}, "statistics": {"type": "integer", "description": "统计日期：1-当天，2-昨天"}, "enable": {"type": "integer", "description": "是否启用：1-是，0-否"}, "create_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "create_by": {"type": "integer", "format": "int64", "description": "创建人ID"}, "create_by_name": {"type": "string", "description": "创建人名称"}, "update_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "update_by": {"type": "integer", "format": "int64", "description": "更新人ID"}, "update_by_name": {"type": "string", "description": "更新人名称"}}}, "NissinBugStatisticsRulesCreateRequest": {"type": "object", "required": ["project_id", "time", "statistics", "enable"], "properties": {"project_id": {"type": "integer", "format": "int64", "description": "项目ID"}, "time": {"type": "string", "description": "定时任务执行时间，格式：HH:MM:SS，例如：08:30:00"}, "statistics": {"type": "integer", "description": "统计日期：1-当天，2-昨天", "enum": [1, 2]}, "enable": {"type": "integer", "description": "是否启用：1-是，0-否", "enum": [0, 1]}}}, "NissinBugStatisticsRulesUpdateRequest": {"type": "object", "required": ["nissin_bug_statistics_rules_id", "time", "statistics", "enable"], "properties": {"nissin_bug_statistics_rules_id": {"type": "integer", "format": "int64", "description": "日清bug统计规则ID"}, "time": {"type": "string", "description": "定时任务执行时间，格式：HH:MM:SS，例如：08:30:00"}, "statistics": {"type": "integer", "description": "统计日期：1-当天，2-昨天", "enum": [1, 2]}, "enable": {"type": "integer", "description": "是否启用：1-是，0-否", "enum": [0, 1]}}}, "NissinBugStatisticsRulesDeleteRequest": {"type": "object", "required": ["nissin_bug_statistics_rules_id"], "properties": {"nissin_bug_statistics_rules_id": {"type": "integer", "format": "int64", "description": "日清bug统计规则ID"}}}, "NissinBugStatisticsRulesEnableRequest": {"type": "object", "required": ["nissin_bug_statistics_rules_id"], "properties": {"nissin_bug_statistics_rules_id": {"type": "integer", "format": "int64", "description": "日清bug统计规则ID"}}}, "NissinBugStatisticsRulesDisableRequest": {"type": "object", "required": ["nissin_bug_statistics_rules_id"], "properties": {"nissin_bug_statistics_rules_id": {"type": "integer", "format": "int64", "description": "日清bug统计规则ID"}}}, "NissinBugStatisticsRulesDetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"$ref": "#/components/schemas/NissinBugStatisticsRulesDetail"}}}, "NissinBugStatisticsRulesPageResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总记录数"}, "per_page": {"type": "integer", "description": "每页记录数"}, "current_page": {"type": "integer", "description": "当前页码"}, "last_page": {"type": "integer", "description": "最后一页页码"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/NissinBugStatisticsRulesDetail"}}}}}}, "NissinBugStatisticsRulesCreateResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"$ref": "#/components/schemas/NissinBugStatisticsRulesDetail"}}}, "NissinBugStatisticsRulesUpdateResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"$ref": "#/components/schemas/NissinBugStatisticsRulesDetail"}}}, "NissinBugStatisticsRulesDeleteResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "boolean", "description": "是否成功"}}}, "NissinBugStatisticsRulesEnableResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "boolean", "description": "是否成功"}}}, "NissinBugStatisticsRulesDisableResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "boolean", "description": "是否成功"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "msg": {"type": "string", "description": "错误信息"}, "data": {"type": "object", "description": "错误数据"}}}}}}