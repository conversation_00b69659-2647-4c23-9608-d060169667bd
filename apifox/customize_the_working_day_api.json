{"openapi": "3.0.0", "info": {"title": "自定义工作日接口文档", "description": "自定义工作日(CustomizeTheWorkingDay)相关接口的详细说明", "version": "1.0.0", "contact": {"name": "开发团队"}}, "servers": [{"url": "/", "description": "API服务器"}], "tags": [{"name": "自定义工作日", "description": "自定义工作日相关接口"}], "paths": {"/project/customizeTheWorkingDay/detail/{id}": {"get": {"tags": ["自定义工作日"], "summary": "获取自定义工作日详情", "description": "根据ID获取自定义工作日的详细信息", "operationId": "getDetail", "parameters": [{"name": "id", "in": "path", "description": "自定义工作日ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayDetailResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/customizeTheWorkingDay/byProject/{projectId}": {"get": {"tags": ["自定义工作日"], "summary": "根据项目ID获取自定义工作日", "description": "根据项目ID获取自定义工作日配置，如果不存在则返回默认配置", "operationId": "getByProjectId", "parameters": [{"name": "projectId", "in": "path", "description": "项目ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayProjectResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/customizeTheWorkingDay/createOrUpdate": {"post": {"tags": ["自定义工作日"], "summary": "创建或更新自定义工作日", "description": "创建或更新自定义工作日配置", "operationId": "createOrUpdate", "requestBody": {"description": "自定义工作日信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayCreateOrUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayCreateOrUpdateResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/customizeTheWorkingDay/delete": {"post": {"tags": ["自定义工作日"], "summary": "删除自定义工作日", "description": "删除自定义工作日配置", "operationId": "delete", "requestBody": {"description": "自定义工作日ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayDeleteRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayDeleteResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"CustomizeTheWorkingDayDetail": {"type": "object", "properties": {"customize_the_working_day_id": {"type": "integer", "format": "int64", "description": "自定义工作日ID"}, "project_id": {"type": "integer", "format": "int64", "description": "项目ID"}, "weekday": {"type": "integer", "description": "工作日设置，以右7位二进制位表示一周中的7天"}, "work_hours": {"type": "number", "format": "float", "description": "一天工时"}, "skip_public_holidays": {"type": "integer", "description": "是否跳过法定节假日(1是、0否)"}, "create_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "create_by": {"type": "integer", "format": "int64", "description": "创建人ID"}, "create_by_name": {"type": "string", "description": "创建人名称"}, "update_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "update_by": {"type": "integer", "format": "int64", "description": "更新人ID"}, "update_by_name": {"type": "string", "description": "更新人名称"}}}, "CustomizeTheWorkingDayProject": {"type": "object", "properties": {"project_id": {"type": "integer", "format": "int64", "description": "项目ID"}, "weekday": {"type": "integer", "description": "工作日设置，以右7位二进制位表示一周中的7天"}, "work_hours": {"type": "number", "format": "float", "description": "一天工时"}, "skip_public_holidays": {"type": "integer", "description": "是否跳过法定节假日(1是、0否)"}}}, "CustomizeTheWorkingDayCreateOrUpdateRequest": {"type": "object", "required": ["project_id", "weekday", "work_hours", "skip_public_holidays"], "properties": {"project_id": {"type": "integer", "format": "int64", "description": "项目ID"}, "weekday": {"type": "integer", "description": "工作日设置，以右7位二进制位表示一周中的7天"}, "work_hours": {"type": "number", "format": "float", "description": "一天工时(1-24)"}, "skip_public_holidays": {"type": "integer", "description": "是否跳过法定节假日(1是、0否)"}}}, "CustomizeTheWorkingDayDeleteRequest": {"type": "object", "required": ["customize_the_working_day_id"], "properties": {"customize_the_working_day_id": {"type": "integer", "format": "int64", "description": "自定义工作日ID"}}}, "CustomizeTheWorkingDayDetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"$ref": "#/components/schemas/CustomizeTheWorkingDayDetail"}}}, "CustomizeTheWorkingDayProjectResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"$ref": "#/components/schemas/CustomizeTheWorkingDayProject"}}}, "CustomizeTheWorkingDayCreateOrUpdateResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "object", "properties": {"customize_the_working_day_id": {"type": "integer", "format": "int64", "description": "自定义工作日ID"}}}}}, "CustomizeTheWorkingDayDeleteResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "boolean", "description": "是否成功"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "msg": {"type": "string", "description": "错误信息"}, "data": {"type": "object", "description": "错误数据"}}}}}}