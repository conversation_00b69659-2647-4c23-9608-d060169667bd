{"openapi": "3.0.0", "info": {"title": "会议类型接口文档", "description": "会议类型(MeetingType)相关接口的详细说明", "version": "1.0.0", "contact": {"name": "开发团队"}}, "servers": [{"url": "/", "description": "API服务器"}], "tags": [{"name": "会议类型", "description": "会议类型相关接口"}], "paths": {"/meetingType/list": {"get": {"tags": ["会议类型"], "summary": "获取会议类型列表", "description": "获取所有会议类型的列表数据", "operationId": "getList", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeetingTypeListResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/meetingType/detail/{id}": {"get": {"tags": ["会议类型"], "summary": "获取会议类型详情", "description": "根据ID获取会议类型的详细信息", "operationId": "getDetail", "parameters": [{"name": "id", "in": "path", "description": "会议类型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeetingTypeDetailResponse"}}}}, "404": {"description": "会议类型不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/meetingType/page": {"get": {"tags": ["会议类型"], "summary": "分页查询会议类型列表", "description": "根据条件分页查询会议类型列表", "operationId": "pageQuery", "parameters": [{"name": "page", "in": "query", "description": "页码，默认为1", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量，默认为10", "required": false, "schema": {"type": "integer", "default": 10}}, {"name": "type_name", "in": "query", "description": "会议类型名称（模糊查询）", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeetingTypePageResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/meetingType/create": {"post": {"tags": ["会议类型"], "summary": "创建会议类型", "description": "创建新的会议类型", "operationId": "create", "requestBody": {"description": "会议类型信息", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeetingTypeCreateRequest"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeetingTypeCreateResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/meetingType/update": {"post": {"tags": ["会议类型"], "summary": "更新会议类型", "description": "更新已有的会议类型信息", "operationId": "update", "requestBody": {"description": "会议类型更新信息", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeetingTypeUpdateRequest"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "会议类型不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/meetingType/delete": {"post": {"tags": ["会议类型"], "summary": "删除会议类型", "description": "删除指定的会议类型", "operationId": "delete", "requestBody": {"description": "会议类型ID", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeetingTypeDeleteRequest"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "参数错误或会议类型不可删除", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "会议类型不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"MeetingType": {"type": "object", "properties": {"meeting_type_id": {"type": "integer", "description": "会议类型ID"}, "type_name": {"type": "string", "description": "会议类型名称"}, "sort": {"type": "integer", "description": "排序"}, "can_be_deleted": {"type": "integer", "description": "是否可删除，1-可删除，0-不可删除"}, "create_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "create_by": {"type": "integer", "description": "创建人ID"}, "create_by_name": {"type": "string", "description": "创建人名称"}, "update_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "update_by": {"type": "integer", "description": "更新人ID"}, "update_by_name": {"type": "string", "description": "更新人名称"}}}, "MeetingTypeListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "msg": {"type": "string", "description": "消息", "example": "success"}, "data": {"type": "array", "description": "会议类型列表", "items": {"$ref": "#/components/schemas/MeetingType"}}}}, "MeetingTypeDetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "msg": {"type": "string", "description": "消息", "example": "success"}, "data": {"$ref": "#/components/schemas/MeetingType"}}}, "MeetingTypePageResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "msg": {"type": "string", "description": "消息", "example": "success"}, "data": {"type": "object", "properties": {"list": {"type": "array", "description": "会议类型列表", "items": {"$ref": "#/components/schemas/MeetingType"}}, "total": {"type": "integer", "description": "总记录数"}, "page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页记录数"}}}}}, "MeetingTypeCreateRequest": {"type": "object", "required": ["type_name", "can_be_deleted"], "properties": {"type_name": {"type": "string", "description": "会议类型名称", "maxLength": 20}, "sort": {"type": "integer", "description": "排序", "default": 0}, "can_be_deleted": {"type": "integer", "description": "是否可删除，1-可删除，0-不可删除", "enum": [0, 1]}}}, "MeetingTypeCreateResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "msg": {"type": "string", "description": "消息", "example": "success"}, "data": {"$ref": "#/components/schemas/MeetingType"}}}, "MeetingTypeUpdateRequest": {"type": "object", "required": ["meeting_type_id"], "properties": {"meeting_type_id": {"type": "integer", "description": "会议类型ID"}, "type_name": {"type": "string", "description": "会议类型名称", "maxLength": 20}, "sort": {"type": "integer", "description": "排序"}, "can_be_deleted": {"type": "integer", "description": "是否可删除，1-可删除，0-不可删除", "enum": [0, 1]}}}, "MeetingTypeDeleteRequest": {"type": "object", "required": ["meeting_type_id"], "properties": {"meeting_type_id": {"type": "integer", "description": "会议类型ID"}}}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "msg": {"type": "string", "description": "消息", "example": "success"}, "data": {"type": "boolean", "description": "操作结果", "example": true}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 400}, "msg": {"type": "string", "description": "错误消息"}}}}}}