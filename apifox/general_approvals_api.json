{"openapi": "3.0.0", "info": {"title": "通用审批接口文档", "description": "通用审批(GeneralApprovals)相关接口的详细说明", "version": "1.0.0", "contact": {"name": "开发团队"}}, "servers": [{"url": "/", "description": "API服务器"}], "tags": [{"name": "通用审批", "description": "通用审批相关接口"}], "paths": {"/infrastructure/generalApprovals/detail/{id}": {"get": {"tags": ["通用审批"], "summary": "获取通用审批详情", "description": "根据ID获取通用审批的详细信息，包含审批人信息（approvers字段）", "operationId": "getDetail", "parameters": [{"name": "id", "in": "path", "description": "通用审批ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneralApprovalsDetailResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/infrastructure/generalApprovals/byBusinessModel": {"get": {"tags": ["通用审批"], "summary": "根据业务模型ID和业务类型获取通用审批", "description": "根据业务模型ID和业务类型获取通用审批，包含审批人信息（approvers字段）", "operationId": "getByBusinessModelIdAndType", "parameters": [{"name": "business_model_id", "in": "query", "description": "业务模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "type", "in": "query", "description": "业务类型：1-迭代节点修改实际结束时间、2-需求变更、3-工时打回", "required": true, "schema": {"type": "integer", "enum": [1, 2, 3]}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneralApprovalsDetailResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/infrastructure/generalApprovals/page": {"get": {"tags": ["通用审批"], "summary": "分页查询通用审批", "description": "分页查询通用审批列表，返回结果中每条记录都包含审批人信息（approvers字段）", "operationId": "pageQuery", "parameters": [{"name": "page", "in": "query", "description": "页码，默认1", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "description": "每页条数，默认20", "required": false, "schema": {"type": "integer", "default": 20}}, {"name": "type", "in": "query", "description": "业务类型：1-迭代节点修改实际结束时间、2-需求变更、3-工时打回", "required": false, "schema": {"type": "integer", "enum": [1, 2, 3]}}, {"name": "is_audit", "in": "query", "description": "审批类型：0-默认、1-发起、2-同意、3-拒绝", "required": false, "schema": {"type": "integer", "enum": [0, 1, 2, 3]}}, {"name": "business_model_id", "in": "query", "description": "业务模型ID", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneralApprovalsPageResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/infrastructure/generalApprovals/create": {"post": {"tags": ["通用审批"], "summary": "创建通用审批", "description": "创建通用审批", "operationId": "create", "requestBody": {"description": "通用审批信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneralApprovalsCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneralApprovalsCreateResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/infrastructure/generalApprovals/approve": {"post": {"tags": ["通用审批"], "summary": "审批", "description": "审批通用审批", "operationId": "approve", "requestBody": {"description": "审批信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneralApprovalsApproveRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneralApprovalsApproveResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/infrastructure/generalApprovals/delete": {"post": {"tags": ["通用审批"], "summary": "删除通用审批", "description": "删除通用审批", "operationId": "delete", "requestBody": {"description": "通用审批ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneralApprovalsDeleteRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneralApprovalsDeleteResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"GeneralApprovalsDetail": {"type": "object", "properties": {"general_approvals_id": {"type": "integer", "format": "int64", "description": "通用审批ID"}, "is_audit": {"type": "integer", "description": "审批类型：0-默认、1-发起、2-同意、3-拒绝"}, "business_model_id": {"type": "integer", "format": "int64", "description": "业务模型ID"}, "type": {"type": "integer", "description": "业务类型：1-迭代节点修改实际结束时间、2-需求变更、3-工时打回"}, "content": {"type": "object", "description": "内容，根据业务类型不同而不同"}, "approvers": {"type": "array", "description": "审批人信息", "items": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int64", "description": "用户ID"}, "user_name": {"type": "string", "description": "用户名称"}, "role": {"type": "string", "description": "角色"}}}}, "create_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "create_by": {"type": "integer", "format": "int64", "description": "创建人ID"}, "create_by_name": {"type": "string", "description": "创建人名称"}, "update_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "update_by": {"type": "integer", "format": "int64", "description": "更新人ID"}, "update_by_name": {"type": "string", "description": "更新人名称"}}}, "GeneralApprovalsCreateRequest": {"type": "object", "required": ["business_model_id", "type", "content"], "properties": {"business_model_id": {"type": "integer", "format": "int64", "description": "业务模型ID"}, "type": {"type": "integer", "description": "业务类型：1-迭代节点修改实际结束时间、2-需求变更、3-工时打回", "enum": [1, 2, 3]}, "content": {"type": "object", "description": "内容，根据业务类型不同而不同"}}}, "GeneralApprovalsApproveRequest": {"type": "object", "required": ["general_approvals_id", "is_audit"], "properties": {"general_approvals_id": {"type": "integer", "format": "int64", "description": "通用审批ID"}, "is_audit": {"type": "integer", "description": "审批类型：2-同意、3-拒绝", "enum": [2, 3]}, "content": {"type": "object", "description": "内容，可选"}}}, "GeneralApprovalsDeleteRequest": {"type": "object", "required": ["general_approvals_id"], "properties": {"general_approvals_id": {"type": "integer", "format": "int64", "description": "通用审批ID"}}}, "GeneralApprovalsDetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"$ref": "#/components/schemas/GeneralApprovalsDetail", "description": "通用审批详情，包含审批人信息（approvers字段）"}}}, "GeneralApprovalsPageResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总记录数"}, "per_page": {"type": "integer", "description": "每页记录数"}, "current_page": {"type": "integer", "description": "当前页码"}, "last_page": {"type": "integer", "description": "最后一页页码"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GeneralApprovalsDetail"}, "description": "通用审批列表，每条记录都包含审批人信息（approvers字段）"}}}}}, "GeneralApprovalsCreateResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "object", "properties": {"general_approvals_id": {"type": "integer", "format": "int64", "description": "通用审批ID"}}}}}, "GeneralApprovalsApproveResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "boolean", "description": "是否成功"}}}, "GeneralApprovalsDeleteResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "boolean", "description": "是否成功"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "msg": {"type": "string", "description": "错误信息"}, "data": {"type": "object", "description": "错误数据"}}}}}}