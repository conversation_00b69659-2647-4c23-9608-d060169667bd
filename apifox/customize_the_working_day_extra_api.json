{"openapi": "3.0.0", "info": {"title": "自定义工作日额外调整接口文档", "description": "自定义工作日额外调整(CustomizeTheWorkingDayExtra)相关接口的详细说明", "version": "1.0.0", "contact": {"name": "开发团队"}}, "servers": [{"url": "/", "description": "API服务器"}], "tags": [{"name": "自定义工作日额外调整", "description": "自定义工作日额外调整相关接口"}], "paths": {"/project/customizeTheWorkingDayExtra/detail/{id}": {"get": {"tags": ["自定义工作日额外调整"], "summary": "获取自定义工作日额外调整详情", "description": "根据ID获取自定义工作日额外调整的详细信息", "operationId": "getDetail", "parameters": [{"name": "id", "in": "path", "description": "自定义工作日额外调整ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraDetailResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/customizeTheWorkingDayExtra/byProject/{projectId}": {"get": {"tags": ["自定义工作日额外调整"], "summary": "根据项目ID获取自定义工作日额外调整列表", "description": "根据项目ID获取自定义工作日额外调整列表", "operationId": "getListByProjectId", "parameters": [{"name": "projectId", "in": "path", "description": "项目ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraListResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/customizeTheWorkingDayExtra/page": {"get": {"tags": ["自定义工作日额外调整"], "summary": "分页查询自定义工作日额外调整", "description": "分页查询自定义工作日额外调整列表", "operationId": "pageQuery", "parameters": [{"name": "project_id", "in": "query", "description": "项目ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "page", "in": "query", "description": "页码，默认1", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "description": "每页条数，默认20", "required": false, "schema": {"type": "integer", "default": 20}}, {"name": "date", "in": "query", "description": "日期，格式：YYYY-MM-DD，用于精确查询", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "type", "in": "query", "description": "类型：1-休息日，2-工作日", "required": false, "schema": {"type": "integer", "enum": [1, 2]}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraPageResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/customizeTheWorkingDayExtra/create": {"post": {"tags": ["自定义工作日额外调整"], "summary": "创建自定义工作日额外调整", "description": "创建自定义工作日额外调整", "operationId": "create", "requestBody": {"description": "自定义工作日额外调整信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraCreateResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/customizeTheWorkingDayExtra/update": {"post": {"tags": ["自定义工作日额外调整"], "summary": "更新自定义工作日额外调整", "description": "更新自定义工作日额外调整", "operationId": "update", "requestBody": {"description": "自定义工作日额外调整信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraUpdateResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/customizeTheWorkingDayExtra/delete": {"post": {"tags": ["自定义工作日额外调整"], "summary": "删除自定义工作日额外调整", "description": "删除自定义工作日额外调整", "operationId": "delete", "requestBody": {"description": "自定义工作日额外调整ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraDeleteRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraDeleteResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/project/customizeTheWorkingDayExtra/batchDelete": {"post": {"tags": ["自定义工作日额外调整"], "summary": "批量删除自定义工作日额外调整", "description": "批量删除自定义工作日额外调整", "operationId": "batchDelete", "requestBody": {"description": "自定义工作日额外调整ID数组", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraBatchDeleteRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraBatchDeleteResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"CustomizeTheWorkingDayExtraDetail": {"type": "object", "properties": {"customize_the_working_day_extra_id": {"type": "integer", "format": "int64", "description": "自定义工作日额外调整ID"}, "project_id": {"type": "integer", "format": "int64", "description": "项目ID"}, "date": {"type": "string", "format": "date", "description": "日期，格式：YYYY-MM-DD"}, "type": {"type": "integer", "description": "类型：1-休息日，2-工作日"}, "create_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "create_by": {"type": "integer", "format": "int64", "description": "创建人ID"}, "create_by_name": {"type": "string", "description": "创建人名称"}, "update_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "update_by": {"type": "integer", "format": "int64", "description": "更新人ID"}, "update_by_name": {"type": "string", "description": "更新人名称"}}}, "CustomizeTheWorkingDayExtraItem": {"type": "object", "properties": {"customize_the_working_day_extra_id": {"type": "integer", "format": "int64", "description": "自定义工作日额外调整ID"}, "project_id": {"type": "integer", "format": "int64", "description": "项目ID"}, "date": {"type": "string", "format": "date", "description": "日期，格式：YYYY-MM-DD"}, "type": {"type": "integer", "description": "类型：1-休息日，2-工作日"}}}, "CustomizeTheWorkingDayExtraCreateRequest": {"type": "object", "required": ["project_id", "date", "type"], "properties": {"project_id": {"type": "integer", "format": "int64", "description": "项目ID"}, "date": {"type": "string", "format": "date", "description": "日期，格式：YYYY-MM-DD"}, "type": {"type": "integer", "description": "类型：1-休息日，2-工作日", "enum": [1, 2]}}}, "CustomizeTheWorkingDayExtraUpdateRequest": {"type": "object", "required": ["customize_the_working_day_extra_id", "date", "type"], "properties": {"customize_the_working_day_extra_id": {"type": "integer", "format": "int64", "description": "自定义工作日额外调整ID"}, "date": {"type": "string", "format": "date", "description": "日期，格式：YYYY-MM-DD"}, "type": {"type": "integer", "description": "类型：1-休息日，2-工作日", "enum": [1, 2]}}}, "CustomizeTheWorkingDayExtraDeleteRequest": {"type": "object", "required": ["customize_the_working_day_extra_id"], "properties": {"customize_the_working_day_extra_id": {"type": "integer", "format": "int64", "description": "自定义工作日额外调整ID"}}}, "CustomizeTheWorkingDayExtraBatchDeleteRequest": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "description": "自定义工作日额外调整ID数组", "items": {"type": "integer", "format": "int64"}}}}, "CustomizeTheWorkingDayExtraDetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraDetail"}}}, "CustomizeTheWorkingDayExtraListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraItem"}}}}, "CustomizeTheWorkingDayExtraPageResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总记录数"}, "per_page": {"type": "integer", "description": "每页记录数"}, "current_page": {"type": "integer", "description": "当前页码"}, "last_page": {"type": "integer", "description": "最后一页页码"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CustomizeTheWorkingDayExtraDetail"}}}}}}, "CustomizeTheWorkingDayExtraCreateResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "object", "properties": {"customize_the_working_day_extra_id": {"type": "integer", "format": "int64", "description": "自定义工作日额外调整ID"}}}}}, "CustomizeTheWorkingDayExtraUpdateResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "boolean", "description": "是否成功"}}}, "CustomizeTheWorkingDayExtraDeleteResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "boolean", "description": "是否成功"}}}, "CustomizeTheWorkingDayExtraBatchDeleteResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "boolean", "description": "是否成功"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "msg": {"type": "string", "description": "错误信息"}, "data": {"type": "object", "description": "错误数据"}}}}}}