{"openapi": "3.0.0", "info": {"title": "节点阶段分类接口文档", "description": "节点阶段分类(NodeCategory)相关接口的详细说明", "version": "1.0.0", "contact": {"name": "开发团队"}}, "servers": [{"url": "/", "description": "API服务器"}], "tags": [{"name": "节点阶段分类", "description": "节点阶段分类相关接口"}], "paths": {"/nodeCategory/list": {"get": {"tags": ["节点阶段分类"], "summary": "获取节点阶段分类列表", "description": "获取所有节点阶段分类的列表数据", "operationId": "getList", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NodeCategoryListResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/nodeCategory/detail/{id}": {"get": {"tags": ["节点阶段分类"], "summary": "获取节点阶段分类详情", "description": "根据ID获取节点阶段分类的详细信息", "operationId": "getDetail", "parameters": [{"name": "id", "in": "path", "description": "节点阶段分类ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NodeCategoryDetailResponse"}}}}, "404": {"description": "节点阶段分类不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/nodeCategory/page": {"get": {"tags": ["节点阶段分类"], "summary": "分页查询节点阶段分类列表", "description": "根据条件分页查询节点阶段分类列表", "operationId": "pageQuery", "parameters": [{"name": "page", "in": "query", "description": "页码，默认为1", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量，默认为10", "required": false, "schema": {"type": "integer", "default": 10}}, {"name": "category_name", "in": "query", "description": "分类名称（模糊查询）", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NodeCategoryPageResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/nodeCategory/create": {"post": {"tags": ["节点阶段分类"], "summary": "创建节点阶段分类", "description": "创建新的节点阶段分类", "operationId": "create", "requestBody": {"description": "节点阶段分类信息", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NodeCategoryCreateRequest"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NodeCategoryCreateResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/nodeCategory/update": {"post": {"tags": ["节点阶段分类"], "summary": "更新节点阶段分类", "description": "更新已有的节点阶段分类信息", "operationId": "update", "requestBody": {"description": "节点阶段分类更新信息", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NodeCategoryUpdateRequest"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "节点阶段分类不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/nodeCategory/delete": {"post": {"tags": ["节点阶段分类"], "summary": "删除节点阶段分类", "description": "删除指定的节点阶段分类", "operationId": "delete", "requestBody": {"description": "节点阶段分类ID", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NodeCategoryDeleteRequest"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "参数错误或节点阶段分类不可删除", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "节点阶段分类不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"NodeCategory": {"type": "object", "properties": {"node_category_id": {"type": "integer", "description": "节点阶段分类ID"}, "category_name": {"type": "string", "description": "分类名称"}, "priority": {"type": "integer", "description": "优先级"}, "sort": {"type": "integer", "description": "排序"}, "can_be_deleted": {"type": "integer", "description": "是否可删除，1-可删除，0-不可删除"}, "create_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "create_by": {"type": "integer", "description": "创建人ID"}, "create_by_name": {"type": "string", "description": "创建人名称"}, "update_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "update_by": {"type": "integer", "description": "更新人ID"}, "update_by_name": {"type": "string", "description": "更新人名称"}}}, "NodeCategoryListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "msg": {"type": "string", "description": "消息", "example": "success"}, "data": {"type": "array", "description": "节点阶段分类列表", "items": {"$ref": "#/components/schemas/NodeCategory"}}}}, "NodeCategoryDetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "msg": {"type": "string", "description": "消息", "example": "success"}, "data": {"$ref": "#/components/schemas/NodeCategory"}}}, "NodeCategoryPageResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "msg": {"type": "string", "description": "消息", "example": "success"}, "data": {"type": "object", "properties": {"list": {"type": "array", "description": "节点阶段分类列表", "items": {"$ref": "#/components/schemas/NodeCategory"}}, "total": {"type": "integer", "description": "总记录数"}, "page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页记录数"}}}}}, "NodeCategoryCreateRequest": {"type": "object", "required": ["category_name", "can_be_deleted"], "properties": {"category_name": {"type": "string", "description": "分类名称", "maxLength": 20}, "priority": {"type": "integer", "description": "优先级", "default": 0}, "sort": {"type": "integer", "description": "排序", "default": 0}, "can_be_deleted": {"type": "integer", "description": "是否可删除，1-可删除，0-不可删除", "enum": [0, 1]}}}, "NodeCategoryCreateResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "msg": {"type": "string", "description": "消息", "example": "success"}, "data": {"$ref": "#/components/schemas/NodeCategory"}}}, "NodeCategoryUpdateRequest": {"type": "object", "required": ["node_category_id"], "properties": {"node_category_id": {"type": "integer", "description": "节点阶段分类ID"}, "category_name": {"type": "string", "description": "分类名称", "maxLength": 20}, "priority": {"type": "integer", "description": "优先级"}, "sort": {"type": "integer", "description": "排序"}, "can_be_deleted": {"type": "integer", "description": "是否可删除，1-可删除，0-不可删除", "enum": [0, 1]}}}, "NodeCategoryDeleteRequest": {"type": "object", "required": ["node_category_id"], "properties": {"node_category_id": {"type": "integer", "description": "节点阶段分类ID"}}}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "msg": {"type": "string", "description": "消息", "example": "success"}, "data": {"type": "boolean", "description": "操作结果", "example": true}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 400}, "msg": {"type": "string", "description": "错误消息"}}}}}}