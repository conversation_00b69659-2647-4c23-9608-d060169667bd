APP_DEBUG = true
ENVIRONMENT = DEVDEPLOYMENT

[APP]
DEFAULT_TIMEZONE = Asia/Shanghai

[DATABASE]
TYPE = mysql
HOSTNAME = middle-db.dev.sjzy.local
DATABASE = devops
USERNAME = devops_rw
PASSWORD = kkafa_7344fafaMMAS
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = true
PREFIX = t_

[REDIS]
password = gaVEHR3V7qiY
host = devops-redis.dev.sjzy.local
port = 6379
select = 0

[ES]
host = http://common-es.dev.sjzy.local:9200
user = elastic
pass = afyTgmVb8jq6
content_index = devops_content_index
test_case_index = test_case_index
test_plan_index = test_plan_index

[LANG]
default_lang = zh-cn

[OPENTELEMETRY]
HOST = http://daemonset-collector.opentelemetry-operator-system.svc.cluster.local:4318
SERVICE_NAME = devops.dev
OPEN = TRUE

[PLATFORM]
HOST = https://centerapi.dev.shijizhongyun.com
TIMEOUT = 50
# 当前系统id（中台）
CENTER_SUB_SYSTEM_ID = 9534

[OSS]
endpoint = https://oss-cn-shenzhen.aliyuncs.com
access_key_id = LTAI5t8xkpnoT1SnPXTsszKH
access_key_secret = ******************************
bucket = ylw-devops-test


[FRONT_END_ADDRESS]
url=https://devops.dev.shijizhongyun.com

[FS_CONFIG]
# 飞书审批配置
bot_user = 674d64ag,92gfc91g,c64g2822,c8ac7c96,4678536f
domain = http://feishu-uat.shijizhongyun.com
pc_url = https://devops.test.shijizhongyun.com/tlogin?general_approvals_id
mobile_url = https://crmh5.test.shijizhongyun.com/tlogin?general_approvals_id

