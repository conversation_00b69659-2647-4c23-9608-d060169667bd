server {
    listen 80;
    server_name localhost;
    types_hash_max_size 4096;

    #if ($scheme = http) {
    #   return 301 http://$host$request_uri;
    #}
    root /usr/share/nginx/html/public;
    index index.php index.html;

    client_max_body_size 128m;
    client_body_timeout 1200s;
    send_timeout 1200s;
    keepalive_timeout 1200s;

    gzip on;
    gzip_min_length 1k;
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Headers 'content-type,Authorization';
    add_header Access-Control-Allow-Methods 'get,post';

    location ~ ^/ossresoure/(.*)$ {
        rewrite ^/ossresoure/(.*)$ /oss/sign?path=$1 last;

    }

    location /devops {
       rewrite ^/devops(.*)$ $1 last;
    }

    location / {
    	if ($request_method = 'OPTIONS') {
    		return 204;
    	}

    	if (!-e $request_filename) {
    	   rewrite  ^(.*)$  /index.php?s=/$1  last;
    	}
    }


    location ~ \.php$ {
        root /usr/share/nginx/html/public;
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        #fastcgi_split_path_info  ^((?U).+\.php)(/?.+)$;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_connect_timeout 12000;
        fastcgi_send_timeout 12000;
        fastcgi_read_timeout 12000;
    }
}




server{
        listen 80;
        server_name 127.0.0.1;
        location = /fpmstatus {
           allow 127.0.0.1;
           deny all;
           include fastcgi_params;
           fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
           fastcgi_pass 127.0.0.1:9000;
        }

}
