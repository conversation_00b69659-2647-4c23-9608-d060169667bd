-- 表结构更新
ALTER TABLE `t_project_user` ADD COLUMN `position_name` varchar(100) NOT NULL DEFAULT '' COMMENT '主岗位名';
ALTER TABLE `t_project_user` ADD COLUMN `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号码';
UPDATE t_field_config SET field_component = JSON_REPLACE(field_component, '$.method', 'POST')WHERE field_component ->> '$.method' = 'GET' and module_id in (4,5, 6, 7, 8, 9,10);
alter table t_project_category modify category_name varchar(100) default '' not null comment '名称';
UPDATE t_field_config SET field_component = JSON_SET(field_component, '$.fields', JSON_ARRAY(
        JSON_OBJECT('id', 'row_446', 'key', 'en_user_name', 'title', '英文名', 'width', '200'),
        JSON_OBJECT('id', 'row_453', 'key', 'user_name', 'title', '中文名', 'width', '200'),
        JSON_OBJECT('id', 'row_461', 'key', 'position_name', 'title', '岗位', 'width', '200')
        )) WHERE field_component ->> '$.url' = '/devops/project/projectUser/selectorListQuery';

UPDATE t_field_config SET field_component = JSON_SET(field_component,'$.inputKey', JSON_ARRAY('en_user_name', 'user_name', 'position_name')) WHERE field_component ->> '$.url' = '/devops/project/projectUser/selectorListQuery';

-- 表数据更新
-- 迭代添加人员字段：产品经理、软件测试、前端开发、后端开发、UI设计师、项目经理、业务架构师
INSERT INTO `t_field_config` (
    `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`,`update_by_name`,
    `update_at`, `field_type`, `remark`, `field_label`,`field_name`,
    `field_component`,
    `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`
) VALUES (
          818, '韦顺隆', '2024-11-07 10:26:41', 0, 818,'韦顺隆',
          '2025-02-13 17:02:47', 1, '产品经理', '产品经理','product_manager',
          '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"productManager\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}',
          1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (
    `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`,`update_by_name`,
    `update_at`, `field_type`, `remark`, `field_label`,`field_name`,
    `field_component`,
    `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`
) VALUES (
             818, '韦顺隆', '2024-11-07 10:26:41', 0, 818,'韦顺隆',
             '2025-02-13 17:02:47', 1, '软件测试', '软件测试','software_test',
             '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"softwareTest\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}',
             1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (
    `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`,`update_by_name`,
    `update_at`, `field_type`, `remark`, `field_label`,`field_name`,
    `field_component`,
    `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`
) VALUES (
             818, '韦顺隆', '2024-11-07 10:26:41', 0, 818,'韦顺隆',
             '2025-02-13 17:02:47', 1, '前端开发', '前端开发','web_develop',
             '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"webDevelop\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}',
             1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (
    `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`,`update_by_name`,
    `update_at`, `field_type`, `remark`, `field_label`,`field_name`,
    `field_component`,
    `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`
) VALUES (
             818, '韦顺隆', '2024-11-07 10:26:41', 0, 818,'韦顺隆',
             '2025-02-13 17:02:47', 1, '后端开发', '后端开发','backend_development',
             '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"backendDevelopment\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}',
             1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (
    `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`,`update_by_name`,
    `update_at`, `field_type`, `remark`, `field_label`,`field_name`,
    `field_component`,
    `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`
) VALUES (
             818, '韦顺隆', '2024-11-07 10:26:41', 0, 818,'韦顺隆',
             '2025-02-13 17:02:47', 1, 'UI设计师', 'UI设计师','ui_designer',
             '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"uiDesigner\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}',
             1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (
    `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`,`update_by_name`,
    `update_at`, `field_type`, `remark`, `field_label`,`field_name`,
    `field_component`,
    `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`
) VALUES (
             818, '韦顺隆', '2024-11-07 10:26:41', 0, 818,'韦顺隆',
             '2025-02-13 17:02:47', 1, '项目经理', '项目经理','project_manager',
             '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"projectManager\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}',
             1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (
    `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`,`update_by_name`,
    `update_at`, `field_type`, `remark`, `field_label`,`field_name`,
    `field_component`,
    `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`
) VALUES (
             818, '韦顺隆', '2024-11-07 10:26:41', 0, 818,'韦顺隆',
             '2025-02-13 17:02:47', 1, '业务架构师', '业务架构师','business_architect',
             '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"businessArchitect\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}',
             1, 4, 0, 1, 0, 0, 1, 'ApiSelect');




