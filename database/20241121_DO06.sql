-- 表结构更新
CREATE TABLE `t_iteration`
(
    `iteration_id`                 int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`                    int          NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name`               varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`                    timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`                    tinyint      NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by`                    int          NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name`               varchar(10)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`                    timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_id`                   int          NOT NULL DEFAULT 0 COMMENT '项目Id',
    `iteration_name`               varchar(200) NOT NULL DEFAULT '' COMMENT '迭代名称',
    `iteration_icon`               varchar(255) NOT NULL DEFAULT '',
    `extends`                      text NULL COMMENT '自定义字段',
    `estimate_start_time`          timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '预估开始时间',
    `estimate_end_time`            timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '预估结束时间',
    `start_time`                   timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '开始时间',
    `end_time`                     timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '结束时间',
    `flow_process_id`              int          NOT NULL DEFAULT 0 COMMENT '迭代流程id',
    `project_category_settings_id` int          NOT NULL DEFAULT 0 COMMENT '类别id',
    `flow_status_id`               int          NOT NULL DEFAULT 0 COMMENT '状态流程id',
    `status_text_id`               int          NOT NULL DEFAULT 0 COMMENT '当前状态',
    `status_enum_id`               int          NOT NULL DEFAULT 0 COMMENT '状态库Id',
    `sort`                         tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序，默认正序',
    PRIMARY KEY (`iteration_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '迭代' ROW_FORMAT = Dynamic;

CREATE TABLE `t_iteration_process_node`
(
    `iteration_process_node_id` int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`                 int         NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name`            varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`                 timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`                 tinyint     NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by`                 int         NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name`            varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`                 timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `iteration_id`              int         NOT NULL DEFAULT 0 COMMENT '迭代id',
    `node_name`                 varchar(20) NOT NULL DEFAULT '' COMMENT '节点名称',
    `node_data`                 json NULL COMMENT '节点数据',
    `row_id`                    int         NOT NULL DEFAULT 0 COMMENT '节点标识',
    `process_node_id`           int         NOT NULL DEFAULT 0 COMMENT '原节点Id',
    `status`                    tinyint     NOT NULL DEFAULT 1 COMMENT '节点状态;1-未开始,2-进行中,3-已完成',
    `status_text_id`            int         NOT NULL DEFAULT 0 COMMENT '流转状态描述id',
    `is_audit_status`           tinyint(1) NOT NULL DEFAULT 0 COMMENT '审批是否通过0默认1通过2不通过3审批中',
    `is_auto_status`            tinyint(1) NOT NULL DEFAULT 0 COMMENT '自动化流程是否通过0默认1通过2不通过（获取详情时校验更新）',
    `estimate_start_time`       timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '预估开始时间',
    `estimate_end_time`         timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '预估结束时间',
    `start_time`                timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '实际开始时间',
    `end_time`                  timestamp   NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '实际结束时间',
    PRIMARY KEY (`iteration_process_node_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '迭代流程节点' ROW_FORMAT = Dynamic;

CREATE TABLE `t_iteration_process_node_audit`
(
    `iteration_process_node_audit_id` int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`                       int          NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name`                  varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`                       timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete`                       tinyint      NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by`                       int          NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name`                  varchar(50)  NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at`                       timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `is_audit`                        tinyint(1) NOT NULL DEFAULT 0 COMMENT '审批类型0默认1发起2通过3拒绝',
    `iteration_process_node_id`       int          NOT NULL COMMENT '关联节点id',
    `remark`                          varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`iteration_process_node_audit_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '迭代节点审批表' ROW_FORMAT = DYNAMIC;

CREATE TABLE `t_iteration_process_node_relation`
(
    `id`              int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `process_node_id` int NOT NULL DEFAULT 0 COMMENT '当前节点id;指iteration_process_node_id',
    `next_node_id`    int NOT NULL DEFAULT 0 COMMENT '后置;指iteration_process_node_id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '迭代过程节点关系' ROW_FORMAT = Dynamic;

CREATE TABLE `t_iteration_process_node_tip`
(
    `iteration_process_node_tip_id` int          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by`                     int          NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name`                varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at`                     timestamp    NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `iteration_process_node_id`     int          NOT NULL COMMENT '关联节点id',
    `title`                         varchar(50)  NOT NULL DEFAULT '' COMMENT '标题',
    `content`                       varchar(200) NOT NULL DEFAULT '' COMMENT '内容',
    PRIMARY KEY (`iteration_process_node_tip_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '迭代节点提醒表' ROW_FORMAT = DYNAMIC;

ALTER TABLE `t_work_hours` MODIFY COLUMN `create_by_name` varchar (50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_work_hours` MODIFY COLUMN `update_by_name` varchar (50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;


-- 表数据更新
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (67, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '终端名称', '终端名称', 'client_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 50, \"componentType\": \"Input\"}', 3, 1, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (68, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '创建人', 'create_by_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 1, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (69, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '创建时间', 'create_at', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 1, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (70, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '终端环境配置', '终端环境配置', 'client_env', 'null', 2, 1, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (71, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '微服务名称', '微服务名称', 'microservice_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 50, \"componentType\": \"Input\"}', 3, 2, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (72, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '创建人', 'create_by_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 2, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (73, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '创建时间', 'create_at', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 2, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (74, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '微服务环境配置', '微服务环境配置', 'microservice_env', 'null', 2, 2, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (75, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '产品名称', '产品名称', 'product_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 50, \"componentType\": \"Input\"}', 4, 3, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (76, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '创建人', 'create_by_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 3, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (77, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '创建时间', 'create_at', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 3, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (78, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '迭代名称', '迭代名称', 'iteration_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 50, \"componentType\": \"Input\"}', 4, 4, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (79, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '创建人', 'create_by_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 4, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (80, 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '创建时间', 'create_at', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 4, 0, 0, 0, 0, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (81, 818, '韦顺隆', '2024-07-25 17:21:52', 0, 818, '韦顺隆', '2024-07-25 17:22:56', 1, '终端', '终端', 'product_client_list', '{\"url\": \"/devops/client/selector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_139\", \"key\": \"label\", \"title\": \"终端名称\", \"width\": \"100\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 3, 3, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (82, 818, '韦顺隆', '2024-07-25 17:23:47', 0, 818, '韦顺隆', '2024-07-25 17:24:39', 1, '微服务', '微服务', 'product_microservice_list', '{\"url\": \"/devops/microservice/selector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_492\", \"key\": \"label\", \"title\": \"微服务名称\", \"width\": \"100\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 2, 3, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (83, 818, '韦顺隆', '2024-07-25 17:21:52', 0, 818, '韦顺隆', '2024-07-25 17:22:56', 1, '终端', '终端', 'iteration_client_list', '{\"url\": \"/devops/client/selectorByProduct\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_139\", \"key\": \"label\", \"title\": \"终端名称\", \"width\": \"100\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 3, 4, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (84, 818, '韦顺隆', '2024-07-25 17:23:47', 0, 818, '韦顺隆', '2024-07-25 17:24:39', 1, '微服务', '微服务', 'iteration_microservice_list', '{\"url\": \"/devops/microservice/selectorByProduct\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_492\", \"key\": \"label\", \"title\": \"微服务名称\", \"width\": \"100\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 2, 4, 0, 1, 1, 0, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (243, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', 'ID', 'cnt_id', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 1, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (244, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-24 10:42:52', 1, '', '标题', 'title', '{\"type\": \"text\", \"options\": [], \"maxlength\": 200, \"componentType\": \"Input\"}', 1, 5, 0, 1, 1, 1, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (245, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '描述', 'contents', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 5, 0, 1, 1, 1, 1, 'Editor');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (246, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-10 17:19:30', 1, '', '状态', 'status_enum_id', '{\"url\": \"/devops/project/flowStatusEnum/selectorEnumStatus\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":2}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 1, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (247, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-02 11:29:45', 1, '', '优先级', 'priority', '{\"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_429\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_430\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 5, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (248, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-18 15:00:54', 1, '', '分类', 'category_id', '{\"url\": \"/devops/workItems/classify/categorySelector\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":2}\", \"props\": [], \"fields\": [{\"id\": \"row_467\", \"key\": \"label\", \"title\": \"分类名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 1, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (249, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-18 11:53:44', 1, '', '父需求', 'parent_id', '{\"url\": \"/devops/workItems/demandSelector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_234\", \"key\": \"label\", \"title\": \"需求名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 1, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (250, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-11-15 09:42:33', 1, '', '迭代', 'iteration_id', '{\"url\": \"/devops/project/iterationCatalog/selectorIteration\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"迭代名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"key\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (251, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-18 15:05:18', 1, '', '需求类别', 'type_id', '{\"url\": \"/devops/project/category/selector\", \"extra\": \"{\\\"_type\\\":2,\\\"project_id\\\":null}\", \"props\": [], \"fields\": [{\"id\": \"row_2311\", \"key\": \"category_name\", \"title\": \"类别名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"project_category_settings_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 1, 1, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (252, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-18 14:57:24', 1, '', '处理人', 'handler_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"props\": [], \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (253, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:14:43', 1, '', '开发人员', 'developer_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (254, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:14:52', 1, '', '测试人员', 'tester_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (255, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:15:02', 1, '', '抄送人', 'cc_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (256, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 10:59:56', 1, '', '预估开始时间', 'estimate_start_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 5, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (257, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:00:08', 1, '', '预估完成时间', 'estimate_end_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 5, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (258, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:00:15', 1, '', '完成时间', 'finish_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 5, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (259, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:14:12', 1, '', '修改人', 'update_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (260, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:17:11', 1, '', '最后修改时间', 'update_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 5, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (261, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:50', 1, '', '创建人', 'create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 5, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (262, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:17:34', 1, '', '创建时间', 'create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 5, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (263, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '预估工时', 'estimated_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (264, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '实际工时', 'actual_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (265, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '剩余工时', 'remaining_work', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (266, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '超出工时', 'exceeding_working_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (267, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '进度', 'speed_of_progress', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 5, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (268, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', 'ID', 'cnt_id', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 6, 0, 0, 0, 1, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (269, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '标题', 'title', '{\"type\": \"text\", \"options\": [], \"maxlength\": 200, \"componentType\": \"Input\"}', 1, 6, 0, 1, 1, 1, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (270, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '描述', 'contents', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 6, 0, 1, 1, 1, 1, 'Editor');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (271, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-28 15:53:36', 1, '', '状态', 'status_enum_id', '{\"url\": \"/devops/workItems/getTaskStatusList\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":3}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 0, 1, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (272, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '优先级', 'priority', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 6, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (273, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 16:28:06', 1, '', '需求', 'parent_id', '{\"url\": \"/devops/workItems/taskDemandSelector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_234\", \"key\": \"label\", \"title\": \"需求名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 1, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (274, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 16:28:20', 1, '', '迭代', 'iteration_id', '{\"url\": \"/devops/project/iterationCatalog/selectorIteration\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"迭代名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"key\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 0, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (275, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 809, '田嫚', '2024-10-11 11:18:33', 1, '', '任务类别', 'type_id', '{\"url\": \"/devops/project/category/selector\", \"extra\": \"{\\\"_type\\\":3,\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"category_name\", \"title\": \"类别名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"project_category_settings_id\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 1, 1, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (276, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '处理人', 'handler_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (277, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:05', 1, '', '抄送人', 'cc_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (278, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:02:27', 1, '', '预估开始时间', 'estimate_start_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 6, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (279, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:55', 1, '', '预估完成时间', 'estimate_end_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 6, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (280, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:02:39', 1, '', '完成时间', 'finish_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 6, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (281, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:22', 1, '', '修改人', 'update_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (282, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:30', 1, '', '最后修改时间', 'update_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 6, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (283, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:34', 1, '', '创建人', 'create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 6, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (284, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:07', 1, '', '创建时间', 'create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 6, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (285, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-11 14:48:38', 1, '', '预估工时', 'estimated_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 6, 0, 1, 0, 3, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (286, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '实际工时', 'actual_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 6, 0, 1, 0, 3, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (287, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '剩余工时', 'remaining_work', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 6, 0, 1, 0, 3, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (288, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '超出工时', 'exceeding_working_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 6, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (289, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '进度', 'speed_of_progress', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 6, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (290, 818, '韦顺隆', '2024-08-26 15:01:45', 1, 6387, '范铁丁', '2024-09-04 10:47:50', 1, '', '迭代节点', 'iteration_process_node_id', '{\"type\": \"text\", \"options\": [], \"maxlength\": 25, \"componentType\": \"Input\"}', 1, 6, 0, 0, 0, 1, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (304, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', 'ID', 'cnt_id', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 7, 0, 0, 0, 1, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (305, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 6387, '范铁丁', '2024-10-19 16:18:22', 1, '', '标题', 'title', '{\"type\": \"text\", \"props\": [], \"fields\": [], \"options\": [], \"maxlength\": 200, \"componentType\": \"Input\"}', 1, 7, 0, 1, 1, 1, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (306, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '描述', 'contents', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 7, 0, 1, 1, 1, 1, 'Editor');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (307, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 19:04:53', 1, '', '状态', 'status_enum_id', '{\"url\": \"/devops/project/flowStatusEnum/selectorEnumStatus\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":4}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 1, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (308, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '严重程度', 'severity_level', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (309, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '优先级', 'priority', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (310, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 16:28:06', 1, '', '关联需求', 'parent_id', '{\"url\": \"/devops/workItems/taskDemandSelector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_234\", \"key\": \"label\", \"title\": \"需求名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 1, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (311, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 16:28:20', 1, '', '迭代', 'iteration_id', '{\"url\": \"/devops/project/iterationCatalog/selectorIteration\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"迭代名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"key\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 1, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (312, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-10-18 10:57:30', 1, '', '缺陷类别', 'type_id', '{\"url\": \"/devops/project/category/selector\", \"extra\": \"{\\\"_type\\\":4,\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"category_name\", \"title\": \"类别名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"project_category_settings_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 1, 1, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (313, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '缺陷类型', 'bug_type', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (314, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '创建缺陷来源', 'bug_source', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (315, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '软件平台', 'environment', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (316, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '挂起原因', 'suspend_reason', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (317, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '缺陷根源', 'bug_reason', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (318, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '自动化发现', 'automated_testing', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (319, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 14:21:59', 1, '', '负责部门', 'responsible_department', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 7, 0, 1, 0, 1, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (320, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '原因描述', 'reason_desc', '{\"type\": \"textarea\", \"options\": [], \"maxlength\": 1000, \"componentType\": \"Input\"}', 1, 7, 0, 1, 0, 1, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (321, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '涉及功能点', 'related_functions', '{\"type\": \"textarea\", \"options\": [], \"maxlength\": 1000, \"componentType\": \"Input\"}', 1, 7, 0, 1, 0, 1, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (322, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '处理人', 'handler_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (323, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '开发人员', 'developer_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (324, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '测试人员', 'tester_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (325, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:05', 1, '', '抄送人', 'cc_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 1, 0, 2, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (326, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '验证人', 'verify_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (327, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '关闭人', 'closure_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (328, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '挂起人', 'suspend_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (329, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:12:36', 1, '', '解决人', 'solve_uid', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (330, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:02:27', 1, '', '预估开始时间', 'estimate_start_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (331, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:55', 1, '', '预估完成时间', 'estimate_end_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 1, 0, 2, 1, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (332, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 11:02:39', 1, '', '完成时间', 'finish_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (333, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:22', 1, '', '修改人', 'update_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (334, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:30', 1, '', '最后修改时间', 'update_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (335, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:34', 1, '', '创建人', 'create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 7, 0, 0, 0, 2, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (336, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:07', 1, '', '创建时间', 'create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (337, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '接受处理时间', 'acceptance_processing_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (338, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '解决时间', 'resolution_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (339, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '拒绝时间', 'rejection_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (340, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '验证时间', 'verification_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (341, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '分配时间', 'assignment_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (342, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '关闭时间', 'close_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (343, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '重新打开时间', 'reopen_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (344, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '挂起时间', 'pending_time', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 7, 0, 0, 0, 2, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (345, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-11 14:48:38', 1, '', '预估工时', 'estimated_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 7, 0, 1, 0, 3, 1, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (346, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '实际工时', 'actual_work_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 7, 0, 1, 0, 3, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (347, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '剩余工时', 'remaining_work', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 7, 0, 1, 0, 3, 0, '');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (348, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '超出工时', 'exceeding_working_hours', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 7, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (349, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '进度', 'speed_of_progress', '{\"step\": 1, \"options\": [], \"controls\": true, \"precision\": 2, \"componentType\": \"Number\"}', 1, 7, 0, 0, 0, 3, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (368, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', 'ID', 'test_case_id', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 8, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (369, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '标题', 'title', '{\"type\": \"text\", \"options\": [], \"maxlength\": 200, \"componentType\": \"Input\"}', 1, 8, 0, 1, 1, 0, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (370, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例步骤', 'case_tep', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 8, 0, 1, 1, 0, 1, 'Editor');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (371, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '前置条件', 'preconditions', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 8, 0, 1, 1, 0, 1, 'Editor');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (372, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '预期结果', 'expected_results', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 8, 0, 1, 1, 0, 1, 'Editor');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (373, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例状态', 'status', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 8, 0, 1, 0, 0, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (374, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例类型', 'use_case_type', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 8, 0, 1, 0, 0, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (375, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例目录', 'category_id', '{\"url\": \"/devops/workItems/classify/categorySelector\", \"extra\": \"{\\\"project_id\\\":null,\\\"_type\\\":5}\", \"props\": [], \"fields\": [{\"id\": \"row_467\", \"key\": \"label\", \"title\": \"分类名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [{\"label\": \"1\", \"rowId\": \"row_679\", \"value\": \"1\"}], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 8, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (376, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '是否主流程冒烟用例', 'is_main_process', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 8, 0, 1, 0, 0, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (377, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:22', 1, '', '修改人', 'update_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 8, 0, 0, 0, 0, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (378, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:30', 1, '', '最后修改时间', 'update_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 8, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (379, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:34', 1, '', '创建人', 'create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 8, 0, 0, 0, 0, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (380, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:07', 1, '', '创建时间', 'create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 8, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (381, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '最终结果', 'plan_result', '{\"url\": \"/devops/planUseCase/recordResultAddNotExecutedSelector\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_77\", \"key\": \"label\", \"title\": \"标题\", \"width\": \"100\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 10, 0, 0, 0, 0, 0, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (382, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '执行次数', 'plan_execution_times', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 10, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (383, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '关联缺陷数', 'plan_bug_count', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 10, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (384, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '最后执行人', 'plan_create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 10, 0, 0, 0, 0, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (385, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '最后执行时间', 'plan_create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 10, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (386, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', 'ID', 'test_plan_id', '{\"step\": 1, \"props\": [], \"fields\": [], \"options\": [], \"controls\": true, \"precision\": 0, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (387, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 0, '', '1971-01-01 00:00:00', 1, '', '标题', 'title', '{\"type\": \"text\", \"options\": [], \"maxlength\": 200, \"componentType\": \"Input\"}', 1, 9, 0, 1, 1, 0, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (388, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:22', 1, '', '描述', 'contents', '{\"options\": [], \"componentType\": \"Editor\"}', 1, 9, 0, 1, 1, 0, 1, 'Editor');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (389, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 6387, '范铁丁', '2024-10-29 11:57:02', 1, '', '状态', 'status', '{\"url\": \"/devops/testPlan/getStatusList\", \"extra\": \"{\\\"project_id\\\":null}\", \"props\": [], \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"状态名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"value\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (390, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 818, '韦顺隆', '2024-09-03 17:13:34', 1, '', '用例类型', 'user_case_type', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 9, 0, 1, 1, 0, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (391, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 1046, '汪聪', '2024-09-04 11:25:07', 1, '', '计划类型', 'plan_type', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 9, 0, 1, 0, 0, 1, 'Select');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (392, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '迭代', 'iteration_id', '{\"url\": \"/devops/project/iterationCatalog/selectorIteration\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"label\", \"title\": \"迭代名称\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"key\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (393, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '需求数', 'story_count', '{\"step\": 1, \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (394, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例数', 'use_case_count', '{\"step\": 1, \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (395, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '测试通过率', 'test_pass_rate', '{\"step\": 1, \"options\": [], \"controls\": false, \"precision\": 2, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (396, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '测试执行进度', 'execution_progress', '{\"step\": 1, \"options\": [], \"controls\": false, \"precision\": 2, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (397, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例覆盖率', 'use_case_coverage', '{\"step\": 1, \"options\": [], \"controls\": false, \"precision\": 2, \"componentType\": \"Number\"}', 1, 9, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (398, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '测试负责人', 'test_manager', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (399, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '开始时间', 'estimate_start_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 9, 0, 1, 0, 0, 1, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (400, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '结束时间', 'estimate_end_time', '{\"type\": \"date\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 9, 0, 1, 0, 0, 1, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (401, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '修改人', 'update_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 0, 0, 0, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (402, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '最后修改时间', 'update_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 9, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (403, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '创建人', 'create_by', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null}\", \"fields\": [{\"id\": \"row_2311\", \"key\": \"user_name\", \"title\": \"title\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 9, 0, 0, 0, 0, 0, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (404, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '创建时间', 'create_at', '{\"type\": \"datetime\", \"props\": [], \"fields\": [], \"format\": \"YYYY-MM-DD HH:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD HH:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 9, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (411, 0, '', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 1, '', '用例等级', 'level', '{\"props\": [], \"fields\": [], \"options\": [{\"label\": \"高\", \"rowId\": \"row_679\", \"value\": \"高\"}, {\"label\": \"中\", \"rowId\": \"row_182\", \"value\": \"中\"}, {\"label\": \"低\", \"rowId\": \"row_183\", \"value\": \"低\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 8, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1317, 818, '韦顺隆', '2024-11-07 10:22:39', 0, 0, '', '1971-01-01 00:00:00', 1, '预估迭代周期', '预估迭代周期', 'estimate_iteration_cycle', '{\"type\": \"daterange\", \"format\": \"YYYY-MM-DD\", \"options\": [], \"value-format\": \"YYYY-MM-DD\", \"componentType\": \"DatePicker\"}', 1, 4, 0, 1, 0, 0, 1, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1318, 818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2024-11-14 15:35:01', 1, '迭代leader', '迭代leader', 'iteration_leader', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"iterationLeader\\\"}\", \"fields\": [{\"id\": \"row_219\", \"key\": \"user_name\", \"title\": \"用户名\", \"width\": \"\"}], \"method\": \"GET\", \"options\": [], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 1, 1, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1319, 818, '韦顺隆', '2024-11-07 10:30:47', 0, 0, '', '1971-01-01 00:00:00', 1, '状态', '状态', 'status_enum', '{\"options\": [{\"label\": \"a\", \"rowId\": \"row_53\", \"value\": \"row_53\"}, {\"label\": \"b\", \"rowId\": \"row_54\", \"value\": \"row_54\"}, {\"label\": \"c\", \"rowId\": \"row_55\", \"value\": \"row_55\"}, {\"label\": \"d\", \"rowId\": \"row_82\", \"value\": \"row_82\"}, {\"label\": \"e\", \"rowId\": \"row_159\", \"value\": \"row_159\"}], \"multiple\": false, \"componentType\": \"Select\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1320, 818, '韦顺隆', '2024-11-07 10:31:03', 0, 818, '韦顺隆', '2024-11-08 10:59:12', 1, '当前节点', '当前节点', 'present_node', '{\"url\": \"/iterate/workflowDiagram/nodeSelector\", \"extra\": \"{\\\"flow_process_id\\\":null}\", \"fields\": [], \"method\": \"GET\", \"options\": [], \"multiple\": false, \"valueKey\": \"node_id\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1321, 818, '韦顺隆', '2024-11-07 10:33:11', 0, 0, '', '1971-01-01 00:00:00', 1, '迭代进度', '迭代进度', 'iteration_progress', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1322, 818, '韦顺隆', '2024-11-07 10:34:27', 0, 0, '', '1971-01-01 00:00:00', 1, '测试计划完成进度', '测试计划完成进度', 'test_plan_completion_progress', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1323, 818, '韦顺隆', '2024-11-07 10:34:40', 0, 0, '', '1971-01-01 00:00:00', 1, '开发自测计划完成进度', '开发自测计划完成进度', 'self_test_plan_completion_progress', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1324, 818, '韦顺隆', '2024-11-07 10:34:56', 0, 0, '', '1971-01-01 00:00:00', 1, '自动化完成进度', '自动化完成进度', 'auto_completion_progress', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Number');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1325, 818, '韦顺隆', '2024-11-07 10:35:06', 0, 0, '', '1971-01-01 00:00:00', 1, '需求数', '需求数', 'demand_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1326, 818, '韦顺隆', '2024-11-07 10:35:26', 0, 0, '', '1971-01-01 00:00:00', 1, '需求未完成数量', '需求未完成数量', 'demand_incomplete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1327, 818, '韦顺隆', '2024-11-07 10:35:42', 0, 818, '韦顺隆', '2024-11-07 16:42:36', 1, '需求已完成数量', '需求已完成数量', 'demand_complete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1328, 818, '韦顺隆', '2024-11-07 10:35:54', 0, 818, '韦顺隆', '2024-11-07 16:42:31', 1, '缺陷数', '缺陷数', 'defect_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1329, 818, '韦顺隆', '2024-11-07 10:48:53', 0, 818, '韦顺隆', '2024-11-07 16:42:21', 1, '缺陷未完成数量', '缺陷未完成数量', 'defect_incomplete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1330, 818, '韦顺隆', '2024-11-07 10:49:05', 0, 818, '韦顺隆', '2024-11-07 16:42:15', 1, '缺陷已完成数量', '缺陷已完成数量', 'defect_complete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1331, 818, '韦顺隆', '2024-11-07 10:49:18', 0, 818, '韦顺隆', '2024-11-07 16:44:35', 1, '任务数', '任务数', 'task_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1332, 818, '韦顺隆', '2024-11-07 10:49:27', 0, 0, '', '1971-01-01 00:00:00', 1, '任务未完成数量', '任务未完成数量', 'task_incomplete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1333, 818, '韦顺隆', '2024-11-07 10:49:38', 0, 0, '', '1971-01-01 00:00:00', 1, '任务已完成数量', '任务已完成数量', 'task_complete_number', '{\"step\": 1, \"fields\": [], \"options\": [], \"controls\": false, \"precision\": 0, \"componentType\": \"Number\"}', 1, 4, 0, 0, 0, 0, 0, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1336, 818, '韦顺隆', '2024-11-09 14:32:56', 1, 818, '韦顺隆', '2024-11-09 14:37:01', 1, 'ID', 'ID', 'iteration_id', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 4, 0, 0, 0, 0, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1337, 818, '韦顺隆', '2024-11-09 14:33:18', 0, 0, '', '1971-01-01 00:00:00', 1, '迭代图标', '迭代图标', 'iteration_icon', '{\"type\": \"text\", \"options\": [], \"maxlength\": 255, \"componentType\": \"Input\"}', 1, 4, 0, 0, 1, 0, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1340, 818, '韦顺隆', '2024-11-12 15:26:18', 0, 0, '', '1971-01-01 00:00:00', 1, '实际迭代周期', '实际迭代周期', 'actual_iteration_cycle', '{\"type\": \"daterange\", \"format\": \"YYYY-MM-DD hh:mm:ss\", \"options\": [], \"value-format\": \"YYYY-MM-DD hh:mm:ss\", \"componentType\": \"DatePicker\"}', 1, 4, 0, 0, 0, 0, 0, 'DatePicker');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1450, 818, '韦顺隆', '2024-08-26 15:01:45', 0, 6387, '范铁丁', '2024-09-04 10:47:50', 1, '', '迭代节点名称', 'iteration_process_node_name', '{\"type\": \"text\", \"options\": [], \"maxlength\": 25, \"componentType\": \"Input\"}', 1, 6, 0, 0, 0, 1, 0, 'Editor');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1609, 818, '韦顺隆', '2024-11-20 14:30:34', 0, 0, '', '1971-01-01 00:00:00', 2, '', 'yzftest', 'f1', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 5, 1, 1, 0, 0, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1610, 818, '韦顺隆', '2024-11-20 14:30:38', 0, 0, '', '1971-01-01 00:00:00', 2, '', 'yzftest', 'f1', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 5, 48, 1, 0, 0, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1611, 818, '韦顺隆', '2024-11-20 14:38:03', 1, 818, '韦顺隆', '2024-11-20 14:39:04', 2, '', 'lx1', 'f1', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 5, 31, 1, 0, 0, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1612, 818, '韦顺隆', '2024-11-20 14:38:12', 0, 818, '韦顺隆', '2024-11-20 15:07:47', 2, '', 'lx2-1', 'f2', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 5, 31, 1, 0, 0, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1613, 777, '郑茂恭', '2024-11-20 17:17:02', 0, 0, '', '1971-01-01 00:00:00', 2, '', '111', 'f3', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 4, 31, 1, 0, 0, 1, 'Input');
INSERT INTO `t_field_config` (`field_id`, `create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (1614, 777, '郑茂恭', '2024-11-21 09:48:49', 0, 0, '', '1971-01-01 00:00:00', 2, '', 'yzftest', 'f1', '{\"type\": \"text\", \"options\": [], \"maxlength\": 100, \"componentType\": \"Input\"}', 1, 5, 49, 1, 0, 0, 1, 'Input');


INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (1, 0, 'env', '环境集合', '', 0, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (2, 0, 'DEV', '开发环境', '1', 1, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (3, 0, 'TEST', '测试环境', '2', 1, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (4, 0, 'UAT', 'UAT环境', '3', 1, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (5, 0, 'PRO', '生产环境', '4', 1, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (6, 0, 'systemRole', '角色集合', '', 0, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (7, 0, 'softwareTest', '软件测试', 'softwareTest', 6, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (8, 0, 'backendDevelopment', '后端开发', 'backendDevelopment', 6, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (9, 0, 'webDevelop', '前端开发', 'webDevelop', 6, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (10, 0, 'uiDesigner', 'UI设计师', 'uiDesigner', 6, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (11, 0, 'productManager', '产品经理', 'productManager', 6, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (12, 0, 'projectManager', '项目经理', 'projectManager', 6, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (13, 0, 'flowStatusType', '工作流-状态类型', '', 0, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (14, 0, 'flowStartStatus', '开始状态', '1', 13, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (15, 0, 'flowProcessStatus', '过程状态', '2', 13, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (16, 0, 'flowEndStatus', '结束状态', '3', 13, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (17, 0, 'projectIcon', '项目图标', '', 0, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (18, 0, 'projectIcon1', '项目图标1', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/14%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (19, 0, 'projectIcon2', '项目图标2', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/2%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (20, 0, 'projectIcon3', '项目图标3', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/3%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (21, 0, 'projectIcon4', '项目图标4', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/4%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (22, 0, 'projectIcon5', '项目图标5', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/5%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (23, 0, 'projectIcon6', '项目图标6', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/6%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (24, 0, 'projectIcon7', '项目图标7', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/7%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (25, 0, 'projectIcon8', '项目图标8', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/8%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (26, 0, 'projectIcon9', '项目图标9', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/9%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (27, 0, 'projectIcon10', '项目图标10', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/10%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (28, 0, 'projectIcon11', '项目图标11', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/11%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (29, 0, 'projectIcon12', '项目图标12', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/12%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (30, 0, 'projectIcon13', '项目图标13', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/13%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (31, 0, 'projectIcon14', '项目图标14', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/1%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (32, 0, 'projectIcon15', '项目图标15', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/project_icon/15%401x.png', 17, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (33, 0, 'task_status_type', '任务状态', '', 0, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (34, 0, 'notStarted', '未开始', '1', 33, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (35, 0, 'inProgress', '进行中', '2', 33, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (36, 0, 'completed', '已完成', '3', 33, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (37, 0, 'projectStatusType', '项目状态', '', 0, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (38, 1, 'projectStartStatus', '待启动', '0', 37, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (39, 0, 'projectProcessStatus', '进行中', '1', 37, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (40, 0, 'projectEndStatus', '关闭', '2', 37, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (42, 0, 'plan_status_type', '计划状态', '', 0, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (43, 0, 'plan_status_open', '开启', '1', 42, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (44, 0, 'plan_status_close', '关闭', '2', 42, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (45, 0, 'iterationLeader', '迭代leader', 'iterationLeader', 6, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (46, 0, 'iterationIcon', '迭代图标', '', 0, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (47, 0, 'iterationIcon1', '迭代图标1', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/iteration_icon/1%401x.png', 46, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (48, 0, 'iterationIcon2', '迭代图标2', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/iteration_icon/2%401x.png', 46, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (49, 0, 'iterationIcon3', '迭代图标3', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/iteration_icon/3%401x.png', 46, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (50, 0, 'iterationIcon4', '迭代图标4', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/iteration_icon/4%401x.png', 46, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (51, 0, 'iterationIcon5', '迭代图标5', 'https://ylw-devops-test.oss-cn-shenzhen.aliyuncs.com/iteration_icon/5%401x.png', 46, 1);


