DELIMITER $$
-- 删除存储过程
DROP PROCEDURE IF EXISTS execScript$$

-- 创建存储过程
CREATE PROCEDURE execScript (
    IN myfileName VARCHAR ( 100 ),-- 当前sql脚本文件名
    IN myinfo VARCHAR ( 200 ),-- 迭代名称
    OUT result VARCHAR ( 2000 )) -- 返回结果字符串
BEGIN

-- 定义sql异常捕获，回滚事务，输出异常
	DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN

-- 当发生 SQL 异常时，输出错误信息
-- 注意：SHOW ERRORS 是一个客户端命令，不能在存储过程中直接使用
-- 但我们可以使用 GET DIAGNOSTICS 来获取错误信息
    GET DIAGNOSTICS CONDITION 1
    @p1 = RETURNED_SQLSTATE,
    @p2 = MESSAGE_TEXT;
-- 可以选择回滚事务（如果存储过程中有事务的话）
ROLLBACK;
-- 输出错误信息（仅供测试，通常不会直接在存储过程中这样做）
SELECT CONCAT( 'Error: ', @p2 );
END;

-- 如果不存在t_script_version表，则创建
CREATE TABLE IF NOT EXISTS t_script_version (
    Id BIGINT auto_increment PRIMARY KEY COMMENT '自增Id',
    NAME VARCHAR ( 100 ) NULL COMMENT '脚本文件名称',
    Info VARCHAR ( 100 ) NULL COMMENT '迭代名称',
    createTime DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updateTime DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间'
    ) COMMENT = '数据库脚本控制';

-- 通过sql文件名 和 迭代名 判断该脚本文件是否已经执行过
SELECT COUNT( 1 ) INTO @exists_flag FROM t_script_version WHERE NAME = myfileName;

-- sql未执行过
IF @exists_flag < 1 THEN

-- 开启事务
START TRANSACTION;
-- 以上的内容不要修改
-- >>>>>>>>>>>>>>>>>>>>>> 执行业务逻辑 开始 <<<<<<<<<<<<<<<<<<<<<<

-- 表结构更新


-- 表数据更新


-- >>>>>>>>>>>>>>>>>>>>>> 执行业务逻辑 结束 <<<<<<<<<<<<<<<<<<<<<<
-- 以下的内容不要修改
    -- 插入 t_script_version 表
INSERT INTO t_script_version ( NAME, info ) VALUES ( myfileName, myinfo );
-- 提交事务
COMMIT;
-- 返回成功信息
SET @success = CONCAT( '成功 >> ', 'sql脚本:', myfileName, '；所属迭代:', myinfo );
    SET result = @success;
ELSE
    -- 返回sql脚本之前已被执行过，本次忽略
    SET @ignore_info = CONCAT( '忽略 >> ', 'sql脚本:', myfileName, '；所属迭代:', myinfo );
    SET result = @ignore_info;
END IF;

END $$

DELIMITER;

-- 执行存储过程
CALL execScript ( '20240606_D01.sql', 'D01', @output );

--  输出结果
SELECT @output;

-- 删除存储过程
DROP PROCEDURE IF EXISTS execScript;
