-- 表结构更新
ALTER TABLE `t_general_approvals` ADD COLUMN `sync_example_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '飞书审批实例ID' AFTER `content`;

CREATE TABLE `t_iteration_log`  (
    `log_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `iteration_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联迭代Id',
    `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详情',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    PRIMARY KEY (`log_id`) USING BTREE,
    INDEX `idx_iteration_id`(`iteration_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '迭代操作日志' ROW_FORMAT = Dynamic;

-- 表数据更新
INSERT INTO t_field_config (create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_label, field_name, field_component, field_sort, module_id, project_id, is_edit, template_default, category_id, allow_setting, component_type) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-26 14:15:57', 1, '所属项目', '所属项目', 'project_id', '{"url": "/devops/project/projectInfo/getProjectSelector", "extra": "{}", "fields": [{"id": "value", "key": "label", "title": "项目名称", "width": ""}], "method": "GET", "options": [], "multiple": false, "valueKey": "value", "componentType": "ApiSelect"}', 1, 5, 0, 0, 0, 1, 0, 'ApiSelect');
INSERT INTO t_field_config (create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_label, field_name, field_component, field_sort, module_id, project_id, is_edit, template_default, category_id, allow_setting, component_type) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-26 14:15:57', 1, '所属项目', '所属项目', 'project_id', '{"url": "/devops/project/projectInfo/getProjectSelector", "extra": "{}", "fields": [{"id": "value", "key": "label", "title": "项目名称", "width": ""}], "method": "GET", "options": [], "multiple": false, "valueKey": "value", "componentType": "ApiSelect"}', 1, 6, 0, 0, 0, 1, 0, 'ApiSelect');
INSERT INTO t_field_config (create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_label, field_name, field_component, field_sort, module_id, project_id, is_edit, template_default, category_id, allow_setting, component_type) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-26 14:15:57', 1, '所属项目', '所属项目', 'project_id', '{"url": "/devops/project/projectInfo/getProjectSelector", "extra": "{}", "fields": [{"id": "value", "key": "label", "title": "项目名称", "width": ""}], "method": "GET", "options": [], "multiple": false, "valueKey": "value", "componentType": "ApiSelect"}', 1, 7, 0, 0, 0, 1, 0, 'ApiSelect');

INSERT INTO t_field_subset (sub_key, field_list, include_custom, remark, module_id) VALUES ('iteration_output_add', '[{"field_name": "iteration_name"}, {"field_name": "iteration_client_list"}, {"field_name": "iteration_microservice_list"}, {"field_name": "estimate_iteration_cycle"}, {"field_name": "iteration_leader"}, {"field_name": "product_manager"}, {"field_name": "software_test"}, {"field_name": "web_develop"}, {"field_name": "backend_development"}, {"field_name": "ui_designer"}, {"field_name": "project_manager"}, {"field_name": "business_architect"}, {"field_name": "project_category_settings_id"}]', 1, '迭代导入新增字段', '[4]');
INSERT INTO t_field_subset (sub_key, field_list, include_custom, remark, module_id) VALUES ('iteration_output_update', '[{"field_name": "iteration_id"}, {"field_name": "iteration_name"}, {"field_name": "iteration_client_list"}, {"field_name": "iteration_microservice_list"}, {"field_name": "estimate_iteration_cycle"}, {"field_name": "iteration_leader"}, {"field_name": "product_manager"}, {"field_name": "software_test"}, {"field_name": "web_develop"}, {"field_name": "backend_development"}, {"field_name": "ui_designer"}, {"field_name": "project_manager"}, {"field_name": "business_architect"}, {"field_name": "project_category_settings_id"}]', 1, '迭代导入更新字段', '[4]');
INSERT INTO t_field_subset (sub_key, field_list, include_custom, remark, module_id) VALUES ('iteration_output', '[{"field_name": "iteration_id"}, {"field_name": "iteration_name"}, {"field_name": "create_by_name"}, {"field_name": "create_at"}, {"field_name": "iteration_client_list"}, {"field_name": "iteration_microservice_list"}, {"field_name": "estimate_iteration_cycle"}, {"field_name": "iteration_leader"}, {"field_name": "status_enum_id"}, {"field_name": "actual_iteration_cycle"}, {"field_name": "product_manager"}, {"field_name": "software_test"}, {"field_name": "web_develop"}, {"field_name": "backend_development"}, {"field_name": "ui_designer"}, {"field_name": "project_manager"}, {"field_name": "business_architect"}, {"field_name": "project_category_settings_id"}]', 1, '迭代可导出字段', '[4]');
INSERT INTO t_field_subset (sub_key, field_list, include_custom, remark, module_id) VALUES ('demand_task_flaw_group', '[{"field_name": "iteration_id"}, {"field_name": "handler_uid"}, {"field_name": "priority"}, {"field_name": "developer_uid"}, {"field_name": "tester_uid"}, {"field_name": "project_id"}, {"field_name": "severity_level"}, {"field_name": "bug_type"}, {"field_name": "bug_source"}, {"field_name": "environment"}, {"field_name": "suspend_reason"}, {"field_name": "bug_reason"}, {"field_name": "responsible_department"}, {"field_name": "verify_uid"}, {"field_name": "closure_uid"}, {"field_name": "solve_uid"}]', 0, '工作项分组字段', '[5, 6, 7]');

INSERT INTO t_field_subset (sub_key, field_list, include_custom, remark, module_id) VALUES ('workbench_demand_edit', '[{"field_name": "title"}, {"field_name": "contents"}, {"field_name": "priority"}, {"field_name": "parent_id"}, {"field_name": "iteration_id"}, {"field_name": "handler_uid"}, {"field_name": "developer_uid"}, {"field_name": "tester_uid"}, {"field_name": "cc_uid"}, {"field_name": "estimate_start_time"}, {"field_name": "estimate_end_time"}]', 0, '需求批量编辑可用字段', '[5]');
INSERT INTO t_field_subset (sub_key, field_list, include_custom, remark, module_id) VALUES ('workbench_task_edit', '[{"field_name": "title"}, {"field_name": "contents"}, {"field_name": "priority"}, {"field_name": "parent_id"}, {"field_name": "iteration_id"}, {"field_name": "handler_uid"}, {"field_name": "cc_uid"}, {"field_name": "estimate_start_time"}, {"field_name": "estimate_end_time"}, {"field_name": "estimated_work_hours"}, {"field_name": "actual_work_hours"}, {"field_name": "remaining_work"}]', 0, '任务批量编辑可用字段', '[6]');
INSERT INTO t_field_subset (sub_key, field_list, include_custom, remark, module_id) VALUES ('workbench_flaw_edit', '[{"field_name": "title"}, {"field_name": "contents"}, {"field_name": "severity_level"}, {"field_name": "priority"}, {"field_name": "parent_id"}, {"field_name": "iteration_id"}, {"field_name": "bug_type"}, {"field_name": "bug_source"}, {"field_name": "environment"}, {"field_name": "suspend_reason"}, {"field_name": "bug_reason"}, {"field_name": "automated_testing"}, {"field_name": "responsible_department"}, {"field_name": "reason_desc"}, {"field_name": "related_functions"}, {"field_name": "handler_uid"}, {"field_name": "developer_uid"}, {"field_name": "tester_uid"}, {"field_name": "cc_uid"}, {"field_name": "estimate_start_time"}, {"field_name": "estimate_end_time"}, {"field_name": "estimated_work_hours"}, {"field_name": "actual_work_hours"}, {"field_name": "remaining_work"}]', 0, '缺陷批量编辑可用字段', '[7]');


INSERT INTO t_field_config (create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_label, field_name, field_component, field_sort, module_id, project_id, is_edit, template_default, category_id, allow_setting, component_type) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-26 14:15:57', 1, '类别', '迭代类别', 'project_category_settings_id', '{"url": "/devops/project/category/selector", "extra": "{\\"_type\\":1,\\"project_id\\":null}", "fields": [{"id": "row_2311", "key": "category_name", "title": "类别名称", "width": ""}], "method": "POST", "options": [], "multiple": false, "valueKey": "project_category_settings_id", "componentType": "ApiSelect"}', 1, 4, 0, 0, 0, 0, 0, 'ApiSelect');

UPDATE t_field_config t SET t.component_type = 'ApiSelect' WHERE t.field_name='iteration_microservice_list' or  t.field_name='iteration_client_list'
