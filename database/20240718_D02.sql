DELIMITER $$
-- 删除存储过程
DROP PROCEDURE IF EXISTS execScript$$

create table `t_field_config`
(
    `allow_setting` int not null
);
-- 创建存储过程
CREATE PROCEDURE execScript (
    IN myfileName VARCHAR ( 100 ),-- 当前sql脚本文件名
    IN myinfo VARCHAR ( 200 ),-- 迭代名称
    OUT result VARCHAR ( 2000 )) -- 返回结果字符串
BEGIN

-- 定义sql异常捕获，回滚事务，输出异常
	DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN

-- 当发生 SQL 异常时，输出错误信息
-- 注意：SHOW ERRORS 是一个客户端命令，不能在存储过程中直接使用
-- 但我们可以使用 GET DIAGNOSTICS 来获取错误信息
    GET DIAGNOSTICS CONDITION 1
    @p1 = RETURNED_SQLSTATE,
    @p2 = MESSAGE_TEXT;
-- 可以选择回滚事务（如果存储过程中有事务的话）
ROLLBACK;
-- 输出错误信息（仅供测试，通常不会直接在存储过程中这样做）
SELECT CONCAT( 'Error: ', @p2 );
END;

-- 如果不存在t_script_version表，则创建
CREATE TABLE IF NOT EXISTS t_script_version (
    Id BIGINT auto_increment PRIMARY KEY COMMENT '自增Id',
    NAME VARCHAR ( 100 ) NULL COMMENT '脚本文件名称',
    Info VARCHAR ( 100 ) NULL COMMENT '迭代名称',
    createTime DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updateTime DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间'
    ) COMMENT = '数据库脚本控制';

-- 通过sql文件名 和 迭代名 判断该脚本文件是否已经执行过
SELECT COUNT( 1 ) INTO @exists_flag FROM t_script_version WHERE NAME = myfileName;

-- sql未执行过
IF @exists_flag < 1 THEN

-- 开启事务
START TRANSACTION;
-- 以上的内容不要修改
-- >>>>>>>>>>>>>>>>>>>>>> 执行业务逻辑 开始 <<<<<<<<<<<<<<<<<<<<<<

-- 表结构更新
DROP TABLE t_client_template;
create table t_microservice
(
    microservice_id   int auto_increment comment 'id'
        primary key,
    create_by         int          default 0                     not null comment '创建人',
    create_by_name    varchar(100) default ''                    not null comment '创建人名称',
    create_at         timestamp    default '1971-01-01 00:00:00' not null comment '创建时间',
    is_delete         tinyint      default 0                     not null comment '是否删除;1-是 0-否',
    update_by         int          default 0                     not null comment '更新人',
    update_by_name    varchar(10)  default ''                    not null comment '更新人名称',
    update_at         timestamp    default '1971-01-01 00:00:00' not null comment '更新时间',
    microservice_name varchar(50)                                not null comment '微服务名称',
    extends           json                                       null comment '自定义字段',
    is_enable         tinyint      default 1                     not null comment '是否开启;1-是 0-否',
    version           int          default 1                     not null comment '版本号'
)
    comment '微服务';

ALTER TABLE `t_field_config` ADD COLUMN `template_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否是模版固定字段,1:是，0:否' AFTER `allow_setting`;
ALTER TABLE `t_field_config` MODIFY COLUMN `field_label` varchar(20) NOT NULL DEFAULT '' COMMENT '字段显示名称' AFTER `remark`;
ALTER TABLE `t_field_config` MODIFY COLUMN `field_name` varchar(50) NOT NULL DEFAULT '' COMMENT '字段名' AFTER `field_label`;
ALTER TABLE `t_field_config` MODIFY COLUMN `module_id` tinyint NOT NULL DEFAULT 0 COMMENT '归属模块;1-终端 2微服务 3-产品 4-迭代' AFTER `field_sort`;
create index idx_field_name on t_field_config (field_name);
create index idx_module_id_is_delete on t_field_config (module_id, is_delete);

CREATE TABLE `t_iteration_category`  (
    `iteration_category_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `category_name` varchar(20) NOT NULL DEFAULT '' COMMENT '类别名称',
    `category_en_name` varchar(20) NOT NULL DEFAULT '' COMMENT '类别英文名称',
    `icon` varchar(15) NOT NULL DEFAULT '' COMMENT '图标',
    `template_id` int NOT NULL DEFAULT 0 COMMENT '创建页id(模版id)',
    `workflow_id` int NOT NULL DEFAULT 0 COMMENT '工作流id',
    `workflow_path_id` int NOT NULL DEFAULT 0 COMMENT '工作流程id',
    `project_id` int NOT NULL DEFAULT 0 COMMENT '项目Id',
    `is_enable` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否开启;1-是 0-否',
PRIMARY KEY (`iteration_category_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '迭代分类' ROW_FORMAT = Dynamic;

CREATE TABLE `t_product`  (
    `product_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `product_name` varchar(50) NOT NULL DEFAULT '' COMMENT '产品名称',
    `product_no` int NOT NULL DEFAULT 0 COMMENT '产品编号',
    `extends` json NULL COMMENT '自定义字段',
    `version` int NOT NULL DEFAULT 0,
    `is_enable` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否开启;1-是 0-否',
PRIMARY KEY (`product_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品' ROW_FORMAT = Dynamic;

CREATE TABLE `t_product_client`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `product_id` int NOT NULL DEFAULT 0 COMMENT '产品id',
    `client_id` int NOT NULL DEFAULT 0 COMMENT '终端id',
PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品终端关系' ROW_FORMAT = Dynamic;

CREATE TABLE `t_product_microservice`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `product_id` int NOT NULL DEFAULT 0 COMMENT '产品id',
    `microservice_id` int NOT NULL DEFAULT 0 COMMENT '终端id',
PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品微服务关系' ROW_FORMAT = Dynamic;

CREATE TABLE `t_project`  (
    `project_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_no` int NOT NULL DEFAULT 0 COMMENT '项目编号',
    `project_name` varchar(50) NOT NULL DEFAULT '' COMMENT '项目名称',
    `product_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品Id',
PRIMARY KEY (`project_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '项目' ROW_FORMAT = Dynamic;

CREATE TABLE `t_template`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `is_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否默认模板;1-是 0-否',
    `template_name` varchar(50) NOT NULL DEFAULT '' COMMENT '模板名称',
    `template_content` json NOT NULL COMMENT '模板内容',
    `module_id` tinyint NOT NULL DEFAULT 0 COMMENT '1-终端，2-微服务，3-产品，4-迭代',
PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '终端模板' ROW_FORMAT = DYNAMIC;


alter table t_field_config modify field_label varchar(50) default '' not null comment '字段显示名称';


-- 表数据更新
# 删除所有系统字段后再添加
delete  from t_field_config where field_type=1;
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('终端名称', 'client_name', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '终端名称', '{"type": "text", "options": [], "maxlength": 50, "componentType": "Input"}', 3, 1, 1, 1);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('创建人', 'create_by_name', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 1, 1, 0, 0);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('创建时间', 'create_at', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 1, 1, 0, 0);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('终端环境配置', 'client_env', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '终端环境配置', 'null', 2, 1, 1, 1);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('微服务名称', 'microservice_name', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '微服务名称', '{"type": "text", "options": [], "maxlength": 50, "componentType": "Input"}', 3, 2, 1, 1);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('创建人', 'create_by_name', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 1, 2, 0, 0);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('创建时间', 'create_at', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 1, 2, 0, 0);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('微服务环境配置', 'microservice_env', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '微服务环境配置', 'null', 2, 2, 1, 1);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('产品名称', 'product_name', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '产品名称', '{"type": "text", "options": [], "maxlength": 50, "componentType": "Input"}', 4, 3, 1, 1);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('创建人', 'create_by_name', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 1, 3, 0, 0);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('创建时间', 'create_at', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 1, 3, 0, 0);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('迭代名称', 'iteration_name', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '迭代名称', '{"type": "text", "options": [], "maxlength": 50, "componentType": "Input"}', 4, 4, 1, 1);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('创建人', 'create_by_name', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 1, 4, 0, 0);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('创建时间', 'create_at', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 1, 4, 0, 0);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('终端', 'product_client_list', 818, '韦顺隆', '2024-07-25 17:21:52', 0, 818, '韦顺隆', '2024-07-25 17:22:56', 1, '终端', '{"url": "/devops/client/selector", "extra": "{}", "fields": [{"id": "row_139", "key": "label", "title": "终端名称", "width": "100"}], "method": "GET", "options": [], "multiple": true, "valueKey": "value", "componentType": "ApiSelect"}', 3, 3, 1, 1);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('微服务', 'product_microservice_list', 818, '韦顺隆', '2024-07-25 17:23:47', 0, 818, '韦顺隆', '2024-07-25 17:24:39', 1, '微服务', '{"url": "/devops/microservice/selector", "extra": "{}", "fields": [{"id": "row_492", "key": "label", "title": "微服务名称", "width": "100"}], "method": "GET", "options": [], "multiple": true, "valueKey": "value", "componentType": "ApiSelect"}', 2, 3, 1, 1);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('终端', 'iteration_client_list', 818, '韦顺隆', '2024-07-25 17:21:52', 0, 818, '韦顺隆', '2024-07-25 17:22:56', 1, '终端', '{"url": "/devops/client/selector", "extra": "{}", "fields": [{"id": "row_139", "key": "label", "title": "终端名称", "width": "100"}], "method": "GET", "options": [], "multiple": true, "valueKey": "value", "componentType": "ApiSelect"}', 3, 4, 1, 1);
INSERT INTO t_field_config (field_label, field_name, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_component, field_sort, module_id, allow_setting, template_default) VALUES ('微服务', 'iteration_microservice_list', 818, '韦顺隆', '2024-07-25 17:23:47', 0, 818, '韦顺隆', '2024-07-25 17:24:39', 1, '微服务', '{"url": "/devops/microservice/selector", "extra": "{}", "fields": [{"id": "row_492", "key": "label", "title": "微服务名称", "width": "100"}], "method": "GET", "options": [], "multiple": true, "valueKey": "value", "componentType": "ApiSelect"}', 2, 4, 1, 1);

# 增加默认模板
INSERT INTO `t_template` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `is_default`, `template_name`, `template_content`, `module_id`) VALUES (818, '韦顺隆', '2024-07-23 09:51:04', 0, 818, '韦顺隆', '2024-07-25 14:33:22', 0, '默认模板', '[{\"field_name\": \"client_name\", \"is_required\": true}, {\"envData\": [{\"label\": \"开发环境\", \"value\": \"1\", \"field_id\": \"row_31\", \"enum_code\": \"DEV\", \"enum_name\": \"开发环境\", \"enum_type\": 1, \"enum_value\": \"1\", \"field_name\": \"envs\", \"field_label\": \"开发环境\", \"is_required\": true}, {\"label\": \"测试环境\", \"value\": \"2\", \"field_id\": \"row_32\", \"enum_code\": \"TEST\", \"enum_name\": \"测试环境\", \"enum_type\": 1, \"enum_value\": \"2\", \"field_name\": \"envs\", \"field_label\": \"测试环境\", \"is_required\": true}, {\"label\": \"UAT环境\", \"value\": \"3\", \"field_id\": \"row_33\", \"enum_code\": \"UAT\", \"enum_name\": \"UAT环境\", \"enum_type\": 1, \"enum_value\": \"3\", \"field_name\": \"envs\", \"field_label\": \"UAT环境\", \"is_required\": true}, {\"label\": \"生产环境\", \"value\": \"4\", \"field_id\": \"row_34\", \"enum_code\": \"PRO\", \"enum_name\": \"生产环境\", \"enum_type\": 1, \"enum_value\": \"4\", \"field_name\": \"envs\", \"field_label\": \"生产环境\", \"is_required\": true}], \"field_name\": \"client_env\", \"is_required\": true, \"default_value\": [\"1\", \"2\", \"3\", \"4\"]}]', 1);
INSERT INTO `t_template` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `is_default`, `template_name`, `template_content`, `module_id`) VALUES (818, '韦顺隆', '2024-07-24 11:47:42', 0, 818, '韦顺隆', '2024-07-26 14:22:09', 0, '默认模板', '[{\"field_name\": \"microservice_name\", \"is_required\": true}, {\"envData\": [{\"label\": \"开发环境\", \"value\": \"1\", \"field_id\": \"row_31\", \"enum_code\": \"DEV\", \"enum_name\": \"开发环境\", \"enum_type\": 1, \"enum_value\": \"1\", \"field_name\": \"envs\", \"field_label\": \"开发环境\", \"is_required\": true}, {\"label\": \"测试环境\", \"value\": \"2\", \"field_id\": \"row_32\", \"enum_code\": \"TEST\", \"enum_name\": \"测试环境\", \"enum_type\": 1, \"enum_value\": \"2\", \"field_name\": \"envs\", \"field_label\": \"测试环境\", \"is_required\": true}, {\"label\": \"UAT环境\", \"value\": \"3\", \"field_id\": \"row_33\", \"enum_code\": \"UAT\", \"enum_name\": \"UAT环境\", \"enum_type\": 1, \"enum_value\": \"3\", \"field_name\": \"envs\", \"field_label\": \"UAT环境\", \"is_required\": true}, {\"label\": \"生产环境\", \"value\": \"4\", \"field_id\": \"row_34\", \"enum_code\": \"PRO\", \"enum_name\": \"生产环境\", \"enum_type\": 1, \"enum_value\": \"4\", \"field_name\": \"envs\", \"field_label\": \"生产环境\", \"is_required\": true}], \"field_name\": \"microservice_env\", \"is_required\": true, \"default_value\": [\"1\", \"2\", \"3\", \"4\"]}]', 2);
INSERT INTO `t_template` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `is_default`, `template_name`, `template_content`, `module_id`) VALUES (818, '韦顺隆', '2024-07-25 17:27:06', 0, 818, '韦顺隆', '2024-07-26 14:51:12', 0, '默认模板', '[{\"field_name\": \"product_name\", \"is_required\": true}, {\"field_name\": \"product_client_list\", \"is_required\": true}, {\"field_name\": \"product_microservice_list\", \"is_required\": true}, {\"field_name\": \"f18\", \"is_required\": true}, {\"field_name\": \"f39\", \"is_required\": true}]', 3);
INSERT INTO `t_template` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `is_default`, `template_name`, `template_content`, `module_id`) VALUES (818, '韦顺隆', '2024-07-25 17:27:06', 0, 818, '韦顺隆', '2024-07-29 10:02:41', 0, '默认模板', '[{\"field_name\": \"product_name\", \"is_required\": true}, {\"field_name\": \"product_client_list\", \"is_required\": true}, {\"field_name\": \"product_microservice_list\", \"is_required\": true}]', 4);


-- >>>>>>>>>>>>>>>>>>>>>> 执行业务逻辑 结束 <<<<<<<<<<<<<<<<<<<<<<
-- 以下的内容不要修改
    -- 插入 t_script_version 表
INSERT INTO t_script_version ( NAME, info ) VALUES ( myfileName, myinfo );
-- 提交事务
COMMIT;
-- 返回成功信息
SET @success = CONCAT( '成功 >> ', 'sql脚本:', myfileName, '；所属迭代:', myinfo );
    SET result = @success;
ELSE
    -- 返回sql脚本之前已被执行过，本次忽略
    SET @ignore_info = CONCAT( '忽略 >> ', 'sql脚本:', myfileName, '；所属迭代:', myinfo );
    SET result = @ignore_info;
END IF;

END $$

DELIMITER;

-- 执行存储过程
CALL execScript ( '20240718_D02.sql', 'D02', @output );

--  输出结果
SELECT @output;

-- 删除存储过程
DROP PROCEDURE IF EXISTS execScript;
