DELIMITER $$
-- 删除存储过程
DROP PROCEDURE IF EXISTS execScript$$

-- 创建存储过程
CREATE PROCEDURE execScript (
    IN myfileName VARCHAR ( 100 ),-- 当前sql脚本文件名
    IN myinfo VARCHAR ( 200 ),-- 迭代名称
    OUT result VARCHAR ( 2000 )) -- 返回结果字符串
BEGIN

-- 定义sql异常捕获，回滚事务，输出异常
	DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN

-- 当发生 SQL 异常时，输出错误信息
-- 注意：SHOW ERRORS 是一个客户端命令，不能在存储过程中直接使用
-- 但我们可以使用 GET DIAGNOSTICS 来获取错误信息
    GET DIAGNOSTICS CONDITION 1
    @p1 = RETURNED_SQLSTATE,
    @p2 = MESSAGE_TEXT;
-- 可以选择回滚事务（如果存储过程中有事务的话）
ROLLBACK;
-- 输出错误信息（仅供测试，通常不会直接在存储过程中这样做）
SELECT CONCAT( 'Error: ', @p2 );
END;

-- 如果不存在t_script_version表，则创建
CREATE TABLE IF NOT EXISTS t_script_version (
    Id BIGINT auto_increment PRIMARY KEY COMMENT '自增Id',
    NAME VARCHAR ( 100 ) NULL COMMENT '脚本文件名称',
    Info VARCHAR ( 100 ) NULL COMMENT '迭代名称',
    createTime DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updateTime DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间'
    ) COMMENT = '数据库脚本控制';

-- 通过sql文件名 和 迭代名 判断该脚本文件是否已经执行过
SELECT COUNT( 1 ) INTO @exists_flag FROM t_script_version WHERE NAME = myfileName;

-- sql未执行过
IF @exists_flag < 1 THEN

-- 开启事务
START TRANSACTION;
-- 以上的内容不要修改
-- >>>>>>>>>>>>>>>>>>>>>> 执行业务逻辑 开始 <<<<<<<<<<<<<<<<<<<<<<

-- 表结构更新
CREATE TABLE `t_client`  (
    `client_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `client_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '终端名称',
    `extends` json NULL COMMENT '自定义字段',
    `is_enable` tinyint NOT NULL DEFAULT 1 COMMENT '是否开启;1-是 0-否',
    `version` int NOT NULL DEFAULT 1 COMMENT '版本号',
PRIMARY KEY (`client_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '终端' ROW_FORMAT = Dynamic;

CREATE TABLE `t_client_template`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `is_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否默认模板;1-是 0-否',
    `template_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模板名称',
    `template_content` json NOT NULL COMMENT '模板内容',
PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '终端模板' ROW_FORMAT = Dynamic;

CREATE TABLE `t_customer_table_config`  (
    `table_config_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `table_unique` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '表格唯一标识',
    `user_fields` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户自定义显示字段',
PRIMARY KEY (`table_config_id`) USING BTREE,
INDEX `idx_create_by_table`(`create_by` ASC, `table_unique` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '用户表头设置' ROW_FORMAT = Dynamic;

CREATE TABLE `t_enum`  (
    `enum_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `enum_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '枚举code',
    `enum_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '枚举名称',
    `enum_value` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '枚举值',
    `parent_id` int NOT NULL DEFAULT 0 COMMENT '上级id 0表示顶级',
    `enum_type` tinyint NOT NULL DEFAULT 1 COMMENT '枚举类型;1-系统 2-自定义',
PRIMARY KEY (`enum_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '枚举' ROW_FORMAT = Dynamic;

CREATE TABLE `t_field_config`  (
    `field_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `field_type` tinyint NOT NULL DEFAULT 1 COMMENT '字段类型;1-系统 2-自定义',
    `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
    `field_label` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字段显示名称',
    `field_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字段名',
    `field_component` json NOT NULL COMMENT '组件内容',
    `field_sort` int NOT NULL DEFAULT 1 COMMENT '排序值 降序排序',
    `module_id` tinyint NOT NULL DEFAULT 0 COMMENT '归属模块;1-终端 2微服务 3-迭代',
    `allow_setting` tinyint NOT NULL DEFAULT 1 COMMENT '是否允许配置;1-允许 0-不允许',
PRIMARY KEY (`field_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '字段管理' ROW_FORMAT = Dynamic;

CREATE TABLE `t_operation_log`  (
    `log_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `operation_table` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '操作类型',
    `table_id` int NOT NULL COMMENT '关联Id',
    `log_details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详情',
PRIMARY KEY (`log_id`) USING BTREE,
INDEX `idx_operation_table_id`(`operation_table` ASC, `table_id` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '操作日志' ROW_FORMAT = Dynamic;


-- 表数据更新
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (1, 0, 'env', '环境集合', '', 0, 1);
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (2, 0, 'DEV', '开发环境', '1', 1, 1);
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (3, 0, 'TEST', '测试环境', '2', 1, 1);
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (4, 0, 'UAT', 'UAT环境', '3', 1, 1);
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (5, 0, 'PRO', '生产环境', '4', 1, 1);
INSERT INTO t_field_config (field_component, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_label, field_name, field_sort, module_id, allow_setting) VALUES ('{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '终端名称', '终端名称', 'client_name', 1, 1, 1);
INSERT INTO t_field_config (field_component, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_label, field_name, field_sort, module_id, allow_setting) VALUES ('{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建人', '创建人', 'create_by_name', 1, 1, 0);
INSERT INTO t_field_config (field_component, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, field_type, remark, field_label, field_name, field_sort, module_id, allow_setting) VALUES ('{"type": "text", "options": [], "maxlength": 100, "componentType": "Input"}', 0, '', '2024-07-09 15:54:39', 0, 0, '', '1971-01-01 00:00:00', 1, '创建时间', '创建时间', 'create_at', 1, 1, 0);
INSERT INTO t_client_template (template_name, template_content, create_by, create_by_name, create_at, is_delete, update_by, update_by_name, update_at, is_default) VALUES ('默认模板', '{"fixedTableData": [{"field_id": "row_162", "field_name": "终端名称", "is_required": true, "default_value": ""}, {"field_id": "row_163", "field_name": "终端环境配置", "is_required": true, "cliet_env_type": 2}, {"label": "开发环境", "value": "1", "field_id": "row_164", "enum_code": "DEV", "enum_name": "开发环境", "enum_type": 1, "enum_value": "1", "field_name": "开发环境", "is_required": true, "cliet_env_type": 3}, {"label": "测试环境", "value": "2", "field_id": "row_165", "enum_code": "TEST", "enum_name": "测试环境", "enum_type": 1, "enum_value": "2", "field_name": "测试环境", "is_required": true, "cliet_env_type": 3}, {"label": "UAT环境", "value": "3", "field_id": "row_166", "enum_code": "UAT", "enum_name": "UAT环境", "enum_type": 1, "enum_value": "3", "field_name": "UAT环境", "is_required": true, "cliet_env_type": 3}, {"label": "生产环境", "value": "4", "field_id": "row_167", "enum_code": "PRO", "enum_name": "生产环境", "enum_type": 1, "enum_value": "4", "field_name": "生产环境", "is_required": true, "cliet_env_type": 3}], "customTableData": []}', default, '', '2024-07-11 15:09:58', 0, default, '', '2024-07-11 15:10:02', 1);
alter table t_field_config modify field_name varchar(100) default '' not null comment '字段名';
alter table t_customer_table_config modify user_fields json not null comment '用户自定义显示字段';
alter table t_client_template modify template_name varchar(100) not null comment '模板名称';
alter table t_client modify client_name varchar(50) default '' not null comment '终端名称';

-- >>>>>>>>>>>>>>>>>>>>>> 执行业务逻辑 结束 <<<<<<<<<<<<<<<<<<<<<<
-- 以下的内容不要修改
    -- 插入 t_script_version 表
INSERT INTO t_script_version ( NAME, info ) VALUES ( myfileName, myinfo );
-- 提交事务
COMMIT;
-- 返回成功信息
SET @success = CONCAT( '成功 >> ', 'sql脚本:', myfileName, '；所属迭代:', myinfo );
    SET result = @success;
ELSE
    -- 返回sql脚本之前已被执行过，本次忽略
    SET @ignore_info = CONCAT( '忽略 >> ', 'sql脚本:', myfileName, '；所属迭代:', myinfo );
    SET result = @ignore_info;
END IF;

END $$

DELIMITER;

-- 执行存储过程
CALL execScript ( '20240702_S01.sql', 'S01', @output );

--  输出结果
SELECT @output;

-- 删除存储过程
DROP PROCEDURE IF EXISTS execScript;
