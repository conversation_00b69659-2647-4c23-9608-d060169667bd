-- 表结构更新
CREATE TABLE `t_customer_table_config_bf`  (
    `table_config_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `table_unique` varchar(50) NOT NULL DEFAULT '' COMMENT '表格唯一标识',
    `user_fields` json NOT NULL COMMENT '用户自定义显示字段',
    PRIMARY KEY (`table_config_id`) USING BTREE,
INDEX `idx_create_by_table`(`create_by` ASC, `table_unique` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '用户表头设置' ROW_FORMAT = DYNAMIC;

ALTER TABLE `t_project_user` ADD COLUMN `name_pinyin_short` varchar(50) NULL DEFAULT '' COMMENT '中文名称首拼' AFTER `user_name`;
ALTER TABLE `t_project_user` ADD COLUMN `name_pinyin_full` varchar(50) NULL DEFAULT '' COMMENT '中文名称全拼' AFTER `name_pinyin_short`;

CREATE TABLE `t_url_params_storage`  (
    `url_params_id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `params_data` text NULL COMMENT '参数数据（JSON格式）',
    `create_by` varchar(64) NULL DEFAULT NULL COMMENT '创建人',
    `create_by_name` varchar(64) NULL DEFAULT NULL COMMENT '创建人名称',
    `create_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0未删除、1已删除',
    `update_by` varchar(64) NULL DEFAULT NULL COMMENT '更新人',
    `update_by_name` varchar(64) NULL DEFAULT NULL COMMENT '更新人名称',
    `update_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `expires_at` datetime NULL DEFAULT NULL COMMENT '过期时间',
    PRIMARY KEY (`url_params_id`) USING BTREE,
    INDEX `idx_create_at`(`create_at` ASC) USING BTREE,
    INDEX `idx_expires_at`(`expires_at` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = 'URL参数存储表' ROW_FORMAT = Dynamic;


-- 表数据更新
INSERT INTO `t_field_subset` (`sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES ('demand_edit', '[{\"field_name\": \"title\"}, {\"field_name\": \"contents\"}, {\"field_name\": \"priority\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"developer_uid\"}, {\"field_name\": \"tester_uid\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"estimate_end_time\"}]', 1, '需求批量编辑可用字段', '[5]');
INSERT INTO `t_field_subset` (`sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES ('task_edit', '[{\"field_name\": \"title\"}, {\"field_name\": \"contents\"}, {\"field_name\": \"priority\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"estimate_end_time\"}, {\"field_name\": \"estimated_work_hours\"}, {\"field_name\": \"actual_work_hours\"}, {\"field_name\": \"remaining_work\"}]', 1, '任务批量编辑可用字段', '[6]');
INSERT INTO `t_field_subset` (`sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES ('flaw_edit', '[{\"field_name\": \"title\"}, {\"field_name\": \"contents\"}, {\"field_name\": \"severity_level\"}, {\"field_name\": \"priority\"}, {\"field_name\": \"parent_id\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"bug_type\"}, {\"field_name\": \"bug_source\"}, {\"field_name\": \"environment\"}, {\"field_name\": \"suspend_reason\"}, {\"field_name\": \"bug_reason\"}, {\"field_name\": \"automated_testing\"}, {\"field_name\": \"responsible_department\"}, {\"field_name\": \"reason_desc\"}, {\"field_name\": \"related_functions\"}, {\"field_name\": \"handler_uid\"}, {\"field_name\": \"developer_uid\"}, {\"field_name\": \"tester_uid\"}, {\"field_name\": \"cc_uid\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"estimate_end_time\"}, {\"field_name\": \"estimated_work_hours\"}, {\"field_name\": \"actual_work_hours\"}, {\"field_name\": \"remaining_work\"}]', 1, '缺陷批量编辑可用字段', '[7]');
INSERT INTO `t_field_subset` (`sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES ('case_edit', '[{\"field_name\": \"title\"}, {\"field_name\": \"case_tep\"}, {\"field_name\": \"preconditions\"}, {\"field_name\": \"expected_results\"}, {\"field_name\": \"use_case_type\"}, {\"field_name\": \"category_id\"}, {\"field_name\": \"is_main_process\"}, {\"field_name\": \"level\"}, {\"field_name\": \"status\"}]', 1, '测试用例批量编辑可用字段', '[8]');
INSERT INTO `t_field_subset` (`sub_key`, `field_list`, `include_custom`, `remark`, `module_id`) VALUES ('plan_edit', '[{\"field_name\": \"title\"}, {\"field_name\": \"contents\"}, {\"field_name\": \"user_case_type\"}, {\"field_name\": \"plan_type\"}, {\"field_name\": \"iteration_id\"}, {\"field_name\": \"test_manager\"}, {\"field_name\": \"estimate_start_time\"}, {\"field_name\": \"estimate_end_time\"}]', 1, '测试计划批量编辑可用字段', '[9]');

-- test更新
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-14 14:44:19', 1, '产品经理', '产品经理', 'product_manager', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"productManager\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-13 17:02:47', 1, '软件测试', '软件测试', 'software_test', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"softwareTest\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-13 17:02:47', 1, '前端开发', '前端开发', 'web_develop', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"webDevelop\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-13 17:02:47', 1, '后端开发', '后端开发', 'backend_development', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"backendDevelopment\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-13 17:02:47', 1, 'UI设计师', 'UI设计师', 'ui_designer', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"uiDesigner\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-13 17:02:47', 1, '项目经理', '项目经理', 'project_manager', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"projectManager\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
INSERT INTO `t_field_config` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `field_type`, `remark`, `field_label`, `field_name`, `field_component`, `field_sort`, `module_id`, `project_id`, `is_edit`, `template_default`, `category_id`, `allow_setting`, `component_type`) VALUES (818, '韦顺隆', '2024-11-07 10:26:41', 0, 818, '韦顺隆', '2025-02-26 14:15:57', 1, '业务架构师', '业务架构师', 'business_architect', '{\"url\": \"/devops/project/projectUser/selectorListQuery\", \"extra\": \"{\\\"project_id\\\":null,\\\"project_role\\\":\\\"businessArchitect\\\"}\", \"fields\": [{\"id\": \"row_112\", \"key\": \"en_user_name\", \"title\": \"英文名\", \"width\": \"\"}, {\"id\": \"row_118\", \"key\": \"user_name\", \"title\": \"中文名\", \"width\": \"\"}, {\"id\": \"row_125\", \"key\": \"position_name\", \"title\": \"岗位\", \"width\": \"\"}], \"method\": \"POST\", \"options\": [], \"inputKey\": [\"en_user_name\", \"user_name\", \"position_name\"], \"multiple\": true, \"valueKey\": \"user_id\", \"componentType\": \"ApiSelect\"}', 1, 4, 0, 1, 0, 0, 1, 'ApiSelect');
