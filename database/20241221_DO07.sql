-- 表结构更新
ALTER TABLE `devops`.`t_project_user` ADD COLUMN `en_user_name` varchar(50) NOT NULL DEFAULT '' COMMENT '英文名称' AFTER `user_name`;

ALTER TABLE `devops`.`t_project_user` MODIFY COLUMN `project_role` varchar(20) NOT NULL DEFAULT '' COMMENT '项目角色code(数据来源t_enum->enum_value) 字段废弃' AFTER `en_user_name`;

CREATE TABLE `devops`.`t_project_user_role`  (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
     `user_id` int NOT NULL DEFAULT 0 COMMENT '用户Id',
     `project_role` varchar(20) NOT NULL DEFAULT '' COMMENT '项目角色code(数据来源t_enum->enum_value) ',
     `project_name` varchar(20) NOT NULL DEFAULT '' COMMENT '角色名',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 62 COMMENT = '成员角色' ROW_FORMAT = DYNAMIC;








-- 表数据更新




