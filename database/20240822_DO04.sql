-- 表结构更新
ALTER TABLE `t_field_config` ADD COLUMN `project_id` int NOT NULL DEFAULT 0 COMMENT '所属项目id，属于项目的模块：4、5、6、7，只有自定义字段有所属项目这个概念，系统字段为通用，所有项目都有' AFTER `template_default`;
ALTER TABLE `t_field_config` ADD COLUMN `category_id` tinyint NOT NULL DEFAULT 0 COMMENT '分类id，1-基础字段、2-人员与时间字段、3-工时字段，只有系统字段有分类' AFTER `project_id`;
ALTER TABLE `t_field_config` ADD COLUMN `component_type` char(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '组件类型，取值为field_component中的componentType属性' AFTER `category_id`;
ALTER TABLE `t_field_config` ADD COLUMN `inline_edit` tinyint NOT NULL DEFAULT 0 COMMENT '是否可行内编辑，0-否，1-是' AFTER `component_type`;
ALTER TABLE `t_field_config` ADD COLUMN `displayDefault` tinyint NOT NULL DEFAULT 0 COMMENT '列表页是否默认展示，1-是，0否' AFTER `inline_edit`;
ALTER TABLE `t_field_config` MODIFY COLUMN `module_id` tinyint NOT NULL DEFAULT 0 COMMENT '归属模块;1-终端，2-微服务，3-产品，4-迭代，5-需求，6-任务，7-缺陷' AFTER `field_sort`;
ALTER TABLE `t_field_config` MODIFY COLUMN `allow_setting` tinyint NOT NULL DEFAULT 1 COMMENT '是否允许配置(编辑);1-允许 0-不允许' AFTER `module_id`;

ALTER TABLE `t_flow_status` COMMENT = '状态流';
ALTER TABLE `t_flow_status` ADD COLUMN `flow_status_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '类型1-迭代,2-需求' AFTER `project_id`;

ALTER TABLE `t_flow_status_enum` ADD COLUMN `status_enum_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '类型1-迭代,2-需求' AFTER `colour`;

CREATE TABLE `t_flow_status_transfer`  (
`id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
`flow_status_id` int NOT NULL DEFAULT 0 COMMENT '状态流程id',
`status_text_id` int NOT NULL DEFAULT 0 COMMENT '状态描述id',
`status_enum_id` int NOT NULL DEFAULT 0 COMMENT '状态id',
`target_status_enum_id` int NOT NULL DEFAULT 0 COMMENT '流转到的状态id',
`extends` json NOT NULL COMMENT '自定义字段',
PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '状态流转' ROW_FORMAT = Dynamic;

ALTER TABLE `t_operation_log` MODIFY COLUMN `operation_table` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '操作类型' AFTER `create_at`;

CREATE TABLE `t_project_category`  (
`project_category_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
`create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
`create_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
`create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
`is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
`update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
`update_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
`update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
`is_allow_delete` tinyint NOT NULL DEFAULT 1 COMMENT '是否允许删除1允许0禁止',
`project_id` int NOT NULL DEFAULT 0 COMMENT '项目id',
`category_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '名称',
`pid` int NOT NULL DEFAULT 0 COMMENT '父级id',
`remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
`project_category_type` tinyint UNSIGNED NOT NULL DEFAULT 2 COMMENT '分类所属2需求分类5测试用例分类',
`sort` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '排序，默认正序',
PRIMARY KEY (`project_category_id`) USING BTREE,
INDEX `idx_project_cate`(`project_id` ASC, `project_category_type` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '项目分类管理' ROW_FORMAT = Dynamic;

CREATE TABLE `t_project_category_settings`  (
`project_category_settings_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
`create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
`create_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
`create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
`is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
`update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
`update_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
`update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
`category_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '类别名称',
`category_en_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '类别英文名称',
`icon` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图标',
`template_id` int NOT NULL DEFAULT 0 COMMENT '创建页id(模版id)',
`flow_status_id` int NOT NULL DEFAULT 0 COMMENT '工作流id',
`flow_process_id` int NOT NULL DEFAULT 0 COMMENT '工作流程id',
`project_id` int NOT NULL DEFAULT 0 COMMENT '项目Id',
`is_enable` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否开启;1-是 0-否',
`sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序，默认正序',
`project_category_settings_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '类别所属1迭代2需求3任务4缺陷5测试用例6测试计划',
PRIMARY KEY (`project_category_settings_id`) USING BTREE,
INDEX `idx_project_sort`(`project_id` ASC, `sort` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '项目类别管理' ROW_FORMAT = Dynamic;

ALTER TABLE `t_template` ADD COLUMN `project_id` int NOT NULL DEFAULT 0 COMMENT '所属项目id，属于项目的模块：4、5、6、7' AFTER `module_id`;
ALTER TABLE `t_template` MODIFY COLUMN `module_id` tinyint NOT NULL DEFAULT 0 COMMENT '1-终端，2-微服务，3-产品，4-迭代，5-需求，6-任务，7-缺陷' AFTER `template_content`;

CREATE TABLE `t_work_hours`  (
`work_hours_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
`create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
`create_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
`create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
`is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
`update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
`update_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
`update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
`cnt_id` int NOT NULL DEFAULT 0 COMMENT '工作项id',
`type` tinyint NOT NULL DEFAULT 1 COMMENT '类型;1-预估,2-实际,3-剩余',
`remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '描述',
`working_hours` int NOT NULL DEFAULT 0 COMMENT '工时',
`work_date` int NOT NULL DEFAULT 0 COMMENT '工作日期',
PRIMARY KEY (`work_hours_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工时' ROW_FORMAT = Dynamic;

CREATE TABLE `t_work_items`  (
`cnt_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
`extends` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自定义字段',
`contents` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '内容',
`cnt_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '类型;1-需求,2-任务,3-缺陷',
`is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
`version` int NOT NULL DEFAULT 1 COMMENT '版本号',
PRIMARY KEY (`cnt_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工作项' ROW_FORMAT = Dynamic;

CREATE TABLE `t_work_items_comment`  (
`comment_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
`create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
`create_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
`create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
`is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
`update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
`update_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
`update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
`work_items_id` int NOT NULL DEFAULT 0 COMMENT '所属内容id',
`content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容',
`remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '其他说明',
`reply_id` int NOT NULL DEFAULT 0 COMMENT '回复的内容id(可以理解为记录pid，评论记录0，回复记录对应comment_id)',
`reply_comment_id` int NOT NULL DEFAULT 0 COMMENT '回复的评论id(当前所属评论id)',
`is_top` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否置顶1-是 0-否',
`is_fake_delete` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否假删1-是 0-否',
PRIMARY KEY (`comment_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '需求、迭代、缺陷评论表' ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `t_iteration_category`;

-- 表数据更新




