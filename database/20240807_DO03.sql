-- 表结构更新
CREATE TABLE `t_flow_process`  (
    `flow_process_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_id` int NOT NULL DEFAULT 0 COMMENT '项目Id',
    `flow_process_name` varchar(50) NOT NULL DEFAULT '' COMMENT '流程名称',
    `flow_process_desc` varchar(200) NOT NULL DEFAULT '' COMMENT '流程说明',
    `version` int NOT NULL DEFAULT 0,
    PRIMARY KEY (`flow_process_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '迭代流程模板' ROW_FORMAT = Dynamic;

CREATE TABLE `t_flow_process_node`  (
    `process_node_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `flow_process_id` int NOT NULL DEFAULT 0 COMMENT '流程Id',
    `node_name` varchar(50) NOT NULL DEFAULT '' COMMENT '节点名称',
    `node_data` json NULL COMMENT '节点设置',
    `row_id` int NOT NULL DEFAULT 0 COMMENT '节点标识',
    PRIMARY KEY (`process_node_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '流程节点模板' ROW_FORMAT = Dynamic;

CREATE TABLE `t_flow_status`  (
    `flow_status_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `flow_process_id` int NOT NULL DEFAULT 0 COMMENT '迭代流程id',
    `status_flow_name` varchar(20) NOT NULL DEFAULT '' COMMENT '工作流名称',
    `status_flow_desc` varchar(200) NOT NULL DEFAULT '' COMMENT '描述',
    `project_id` int NOT NULL DEFAULT 0 COMMENT '项目id',
    PRIMARY KEY (`flow_status_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '状态流程' ROW_FORMAT = Dynamic;

CREATE TABLE `t_flow_status_enum`  (
    `status_enum_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `name` varchar(20) NOT NULL DEFAULT '' COMMENT '名称',
    `project_id` int NOT NULL DEFAULT 0 COMMENT '项目id',
    `colour` varchar(15) NOT NULL DEFAULT '' COMMENT '颜色',
    PRIMARY KEY (`status_enum_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '状态库' ROW_FORMAT = Dynamic;

CREATE TABLE `t_flow_status_node_relation`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `flow_status_id` int NOT NULL DEFAULT 0 COMMENT '状态流程id',
    `status_text_id` int NOT NULL DEFAULT 0 COMMENT '状态描述id',
    `process_node_id` int NOT NULL DEFAULT 0 COMMENT '流程节点id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '状态与节点关系' ROW_FORMAT = Dynamic;

CREATE TABLE `t_flow_status_text`  (
    `status_text_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `flow_status_id` int NOT NULL DEFAULT 0 COMMENT '流程状态id',
    `status_enum_id` int NOT NULL DEFAULT 0 COMMENT '状态库Id',
    `status_type` tinyint NOT NULL DEFAULT 1 COMMENT '状态类型;1-开始,2-过程,3-结束',
    PRIMARY KEY (`status_text_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '状态描述' ROW_FORMAT = Dynamic;

CREATE TABLE `t_process_node_relation`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `process_node_id` int NOT NULL DEFAULT 0 COMMENT '当前Id;指process_node_id',
    `next_node_id` int NOT NULL DEFAULT 0 COMMENT '后置;指process_node_id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '迭代流程节点关系' ROW_FORMAT = Dynamic;

CREATE TABLE `t_project_user`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是,0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_id` int NOT NULL DEFAULT 0 COMMENT '项目Id',
    `user_id` int NOT NULL DEFAULT 0 COMMENT '用户Id',
    `user_name` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
    `project_role` varchar(20) NOT NULL DEFAULT '1' COMMENT '项目角色code(数据来源t_enum->enum_value)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '项目成员' ROW_FORMAT = Dynamic;

ALTER TABLE `t_client` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_client` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_customer_table_config` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_field_config` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_field_config` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_flow_process` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_flow_process` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_flow_process_node` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_flow_status` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_flow_status` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_flow_status_enum` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_flow_status_enum` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_flow_status_text` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_flow_status_text` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_iteration_category` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_iteration_category` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_microservice` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_microservice` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_operation_log` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_product` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_product` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_product_client` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_product_client` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_product_microservice` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_product_microservice` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_project` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_project` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_project_user` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_project_user` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;
ALTER TABLE `t_template` MODIFY COLUMN `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称' AFTER `create_by`;
ALTER TABLE `t_template` MODIFY COLUMN `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称' AFTER `update_by`;


-- 表数据更新
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (6, 0, 'system_role', '角色集合', '', 0, 1);
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (7, 0, 'softwareTest', '软件测试', 'softwareTest', 6, 1);
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (8, 0, 'backendDevelopment', '后端开发', 'backendDevelopment', 6, 1);
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (9, 0, 'webDevelop', '前端开发', 'webDevelop', 6, 1);
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (10, 0, 'uiDesigner', 'UI设计师', 'uiDesigner', 6, 1);
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (11, 0, 'productManager', '产品经理', 'productManager', 6, 1);
INSERT INTO t_enum (enum_id, is_delete, enum_code, enum_name, enum_value, parent_id, enum_type) VALUES (12, 0, 'projectManager', '项目经理', 'projectManager', 6, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (13, 0, 'flow_status_type', '工作流-状态类型', '', 0, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (14, 0, 'status_type_1', '开始状态', '1', 13, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (15, 0, 'status_type_2', '过程状态', '2', 13, 1);
INSERT INTO `t_enum` (`enum_id`, `is_delete`, `enum_code`, `enum_name`, `enum_value`, `parent_id`, `enum_type`) VALUES (16, 0, 'status_type_3', '结束状态', '3', 13, 1);
