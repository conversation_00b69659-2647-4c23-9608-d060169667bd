-- 表结构更新
ALTER TABLE `t_customer_table_config` MODIFY COLUMN `table_unique` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '表格唯一标识' AFTER `create_by_name`;

ALTER TABLE `t_enum` MODIFY COLUMN `enum_value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '枚举值' AFTER `enum_name`;

CREATE TABLE `t_execution_record`  (
    `execution_record_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `test_plan_id` int NOT NULL DEFAULT 0 COMMENT '计划Id',
    `plan_use_case_id` int NOT NULL DEFAULT 0 COMMENT '计划用例关联Id',
    `result` tinyint NOT NULL DEFAULT 1 COMMENT '1-通过;2-阻塞;4-不通过',
    `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`execution_record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '执行记录' ROW_FORMAT = Dynamic;

ALTER TABLE `t_field_config` ADD COLUMN `is_edit` tinyint NOT NULL DEFAULT 0 COMMENT '是否可编辑;1-允许,0-不允许' AFTER `allow_setting`;
ALTER TABLE `t_field_config` MODIFY COLUMN `module_id` tinyint NOT NULL DEFAULT 0 COMMENT '归属模块;1-终端，2-微服务，3-产品，4-迭代，5-需求，6-任务，7-缺陷，8-测试用例，9-测试计划，10-测试计划规划与执行' AFTER `field_sort`;
ALTER TABLE `t_field_config` MODIFY COLUMN `project_id` int NOT NULL DEFAULT 0 COMMENT '所属项目id，属于项目的模块：4、5、6、7、8、9，只有自定义字段有所属项目这个概念，系统字段为通用，所有项目都有' AFTER `module_id`;
ALTER TABLE `t_field_config` MODIFY COLUMN `allow_setting` tinyint NOT NULL DEFAULT 1 COMMENT '是否可添加到模版中;1-允许 0-不允许' AFTER `category_id`;
ALTER TABLE `t_field_config` MODIFY COLUMN `template_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否是模版固定字段,1:是，0:否' AFTER `is_edit`;
ALTER TABLE `t_field_config` DROP COLUMN `inline_edit`;
ALTER TABLE `t_field_config` DROP COLUMN `displayDefault`;

CREATE TABLE `t_field_subset`  (
    `sub_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `sub_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '标识',
    `field_list` json NULL COMMENT '字段集合;json格式',
    `include_custom` tinyint NOT NULL DEFAULT 0 COMMENT '是否包含自定义字段;0：不是，1：是',
    `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
    `module_id` json NULL COMMENT '归属模块',
    PRIMARY KEY (`sub_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '以标识区分各个模块字段集合的子集' ROW_FORMAT = Dynamic;

ALTER TABLE `t_flow_process` ADD COLUMN `attribution_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '归属来源id,默认0' AFTER `flow_process_id`;

ALTER TABLE `t_flow_process_node` ADD COLUMN `attribution_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '归属来源id,默认0' AFTER `process_node_id`;

ALTER TABLE `t_flow_status` ADD COLUMN `attribution_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '归属来源id,默认0' AFTER `flow_status_id`;
ALTER TABLE `t_flow_status` MODIFY COLUMN `flow_status_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '类型1迭代2需求3任务4缺陷5测试用例6测试计划' AFTER `project_id`;

ALTER TABLE `t_flow_status_enum` ADD COLUMN `attribution_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '归属来源id,默认0' AFTER `status_enum_id`;
ALTER TABLE `t_flow_status_enum` ADD COLUMN `is_permanent` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否常驻0否1是' AFTER `colour`;
ALTER TABLE `t_flow_status_enum` MODIFY COLUMN `status_enum_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '类型1迭代2需求3任务4缺陷5测试用例6测试计划' AFTER `is_permanent`;

ALTER TABLE `t_flow_status_text` ADD COLUMN `attribution_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '归属来源id,默认0' AFTER `status_text_id`;

CREATE TABLE `t_plan_use_case`  (
    `plan_use_case_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `test_plan_id` int NOT NULL DEFAULT 0 COMMENT '计划Id',
    `execution_times` int NOT NULL DEFAULT 0 COMMENT '执行次数',
    `bug_count` int NOT NULL DEFAULT 0 COMMENT '关联Bug数',
    `test_case_id` int NOT NULL DEFAULT 0 COMMENT '用例id',
    `cnt_id` int NOT NULL DEFAULT 0 COMMENT '工作项id',
    PRIMARY KEY (`plan_use_case_id`) USING BTREE,
UNIQUE INDEX `t_plan_use_case_test_plan_id_test_case_id_uindex`(`test_plan_id` ASC, `test_case_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '测试计划用例关联' ROW_FORMAT = Dynamic;

CREATE TABLE `t_plan_use_case_bug`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `plan_use_case_id` int NOT NULL DEFAULT 0 COMMENT '计划用例关联id',
    `cnt_id` int NOT NULL DEFAULT 0 COMMENT '缺陷Id',
    `test_plan_id` int NOT NULL DEFAULT 0 COMMENT '计划id',
    `execution_record_id` int NOT NULL DEFAULT 0 COMMENT '执行记录Id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '测试计划用例bug关联表' ROW_FORMAT = Dynamic;

ALTER TABLE `t_project` ADD COLUMN `project_user_count` int NOT NULL DEFAULT 0 COMMENT '项目成员数量' AFTER `product_id`;
ALTER TABLE `t_project` ADD COLUMN `project_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '项目状态1进行中2关闭' AFTER `project_user_count`;
ALTER TABLE `t_project` ADD COLUMN `project_remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '项目描述' AFTER `project_status`;
ALTER TABLE `t_project` ADD COLUMN `project_template_id` int NOT NULL DEFAULT 0 COMMENT '项目模板id' AFTER `project_remark`;
ALTER TABLE `t_project` ADD COLUMN `project_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '项目icon' AFTER `project_template_id`;
ALTER TABLE `t_project` ADD COLUMN `is_template` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是模板;1-是 0-否' AFTER `project_icon`;

ALTER TABLE `t_project_category` MODIFY COLUMN `project_category_type` tinyint UNSIGNED NOT NULL DEFAULT 2 COMMENT '分类所属1迭代2需求3任务4缺陷5测试用例6测试计划' AFTER `remark`;

ALTER TABLE `t_project_category_settings` ADD COLUMN `attribution_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '归属来源id,默认0' AFTER `project_category_settings_id`;

CREATE TABLE `t_project_favorite`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_id` int NOT NULL DEFAULT 0 COMMENT '项目id',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_project_cate`(`project_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '项目收藏表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_project_template`  (
    `project_template_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `project_template_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模板名称',
    `project_id` int NOT NULL DEFAULT 0 COMMENT '项目id',
    `is_allow_delete` tinyint NOT NULL DEFAULT 1 COMMENT '是否允许删除1允许0禁止',
    `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
    `sort` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '排序，默认正序',
    PRIMARY KEY (`project_template_id`) USING BTREE,
    INDEX `idx_project_cate`(`project_template_name` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '项目模板表' ROW_FORMAT = Dynamic;

ALTER TABLE `t_template` ADD COLUMN `attribution_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '归属来源id,默认0' AFTER `id`;
ALTER TABLE `t_template` MODIFY COLUMN `module_id` tinyint NOT NULL DEFAULT 0 COMMENT '归属模块;1-终端，2-微服务，3-产品，4-迭代，5-需求，6-任务，7-缺陷，8-测试用例，9-测试计划' AFTER `template_content`;

CREATE TABLE `t_test_case`  (
    `test_case_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `extends` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自定义字段',
    `case_tep` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用例步骤',
    `preconditions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '前置条件',
    `expected_results` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '预期结果',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `version` int NOT NULL DEFAULT 1 COMMENT '版本号',
    PRIMARY KEY (`test_case_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '测试用例' ROW_FORMAT = Dynamic;

CREATE TABLE `t_test_case_work`  (
    `test_case_work_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `test_case_id` int NOT NULL DEFAULT 0 COMMENT '测试用例id',
    `cnt_id` int NOT NULL DEFAULT 0 COMMENT '工作项id',
    PRIMARY KEY (`test_case_work_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '测试用例与工作项(需求)的关联表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_test_plan`  (
    `test_plan_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `extends` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自定义字段',
    `contents` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '描述',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `version` int NOT NULL DEFAULT 1 COMMENT '版本号',
    PRIMARY KEY (`test_plan_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '测试计划' ROW_FORMAT = Dynamic;

CREATE TABLE `t_test_plan_work_case`  (
    `test_plan_work_case_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `test_plan_id` int NOT NULL DEFAULT 0 COMMENT '计划id',
    `cnt_id` int NOT NULL DEFAULT 0 COMMENT '需求id',
    `test_case_id` int NOT NULL DEFAULT 0 COMMENT '用例id',
    PRIMARY KEY (`test_plan_work_case_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工作项下的需求与用例的关联表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_work_comment`  (
    `comment_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `work_items_id` int NOT NULL DEFAULT 0 COMMENT '所属内容id',
    `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容',
    `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '其他说明',
    `reply_id` int NOT NULL DEFAULT 0 COMMENT '回复的内容id(可以理解为记录pid，评论记录0，回复记录对应comment_id)',
    `reply_comment_id` int NOT NULL DEFAULT 0 COMMENT '回复的评论id(当前所属评论id)',
    `comment_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '类型1迭代2需求3任务4缺陷5测试用例6测试计划',
    `is_top` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否置顶1-是 0-否',
    `is_fake_delete` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否假删1-是 0-否',
    PRIMARY KEY (`comment_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工作项-评论表' ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `t_work_items_comment`;

-- 表数据更新




