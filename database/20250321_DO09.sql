-- 表结构更新
ALTER TABLE `t_flow_process` ADD COLUMN `bug_liquidation_time` time NOT NULL DEFAULT '00:00:00' COMMENT 'bug清算时间';

CREATE TABLE `t_bug_statistics`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_id` int NOT NULL COMMENT '项目ID',
    `iteration_id` int NOT NULL COMMENT '迭代ID',
    `user_id` int NOT NULL COMMENT '人员ID',
    `date` date NOT NULL COMMENT '统计日期',
    `statistics` tinyint(1) NOT NULL COMMENT '统计日期：1当天、2昨天',
    `time` time NOT NULL COMMENT 'bug日期截止时间',
    `count` int NOT NULL DEFAULT 0 COMMENT '缺陷数量',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_project_id`(`project_id` ASC) USING BTREE,
    INDEX `idx_iteration_id`(`iteration_id` ASC) USING BTREE,
    INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
    INDEX `idx_date`(`date` ASC) USING BTREE,
    INDEX `idx_project_date`(`project_id` ASC, `date` ASC) USING BTREE,
    INDEX `idx_iteration_date`(`iteration_id` ASC, `date` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '缺陷统计表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_bug_statistics_detail`  (
     `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `project_id` int NOT NULL COMMENT '项目ID',
     `iteration_id` int NOT NULL COMMENT '迭代ID',
     `user_id` int NOT NULL COMMENT '人员ID',
     `date` date NOT NULL COMMENT '统计日期',
     `time` time NOT NULL COMMENT 'bug日期截止时间',
     `statistics` tinyint(1) NOT NULL COMMENT '统计日期：1当天、2昨天',
     `bug_id` int NOT NULL COMMENT '缺陷ID',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
     PRIMARY KEY (`id`) USING BTREE,
     INDEX `idx_project_id`(`project_id` ASC) USING BTREE,
     INDEX `idx_iteration_id`(`iteration_id` ASC) USING BTREE,
     INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
     INDEX `idx_date`(`date` ASC) USING BTREE,
     INDEX `idx_bug_id`(`bug_id` ASC) USING BTREE,
     INDEX `idx_project_date`(`project_id` ASC, `date` ASC) USING BTREE,
     INDEX `idx_iteration_date`(`iteration_id` ASC, `date` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '缺陷详情表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_customize_the_working_day`  (
     `customize_the_working_day_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
     `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
     `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
     `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
     `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
     `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
     `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
     `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
     `project_id` int NOT NULL DEFAULT 0 COMMENT '项目id',
     `weekday` int NOT NULL DEFAULT 0 COMMENT '工作日设置，以右7位二进制位表示一周中的7天,1是、0否',
     `work_hours` int NOT NULL DEFAULT 0 COMMENT '一天工时',
     `skip_public_holidays` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否跳过法定节假日,1是、0否',
     PRIMARY KEY (`customize_the_working_day_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '自定义工作日表' ROW_FORMAT = DYNAMIC;

CREATE TABLE `t_customize_the_working_day_extra`  (
       `customize_the_working_day_extra_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
       `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
       `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
       `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
       `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
       `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
       `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
       `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
       `project_id` int NOT NULL DEFAULT 0 COMMENT '项目id',
       `date` date NOT NULL DEFAULT '1971-01-01' COMMENT '日期',
       `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1、休息日,2、工作日',
       `is_sync_to_all_projects` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否同步至其他项目 1-是 0-否',
       PRIMARY KEY (`customize_the_working_day_extra_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '自定义工作日表额外调整表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `t_general_approvals`  (
    `general_approvals_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `is_audit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审批类型0默认1发起2同意3拒绝',
    `business_model_id` int NOT NULL DEFAULT 0 COMMENT '业务模型id',
    `type` tinyint NOT NULL DEFAULT 0 COMMENT '业务类型,1-迭代节点修改实际结束时间t_iteration_process_node、2-需求变更t_work_items、3-工时打回t_work_items',
    `content` json NOT NULL COMMENT '内容',
    PRIMARY KEY (`general_approvals_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '通用审批表' ROW_FORMAT = DYNAMIC;

CREATE TABLE `t_iteration_demand_relation_records`  (
     `record_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `create_by` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人',
     `create_by_name` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人名称',
     `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
     `update_by` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人',
     `update_by_name` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人名称',
     `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `project_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '项目ID',
     `project_name` varchar(255) NOT NULL DEFAULT '' COMMENT '项目名称',
     `cnt_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '需求ID',
     `cnt_title` varchar(255) NOT NULL DEFAULT '' COMMENT '需求标题',
     `operator_uid` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作人ID',
     `operator_name` varchar(255) NOT NULL DEFAULT '' COMMENT '操作人名称',
     `iteration_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '迭代ID',
     `iteration_name` varchar(255) NOT NULL DEFAULT '' COMMENT '迭代名称',
     `node_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '迭代节点ID',
     `node_name` varchar(255) NOT NULL DEFAULT '' COMMENT '迭代节点名称',
     `node_stage` varchar(50) NOT NULL DEFAULT '' COMMENT '迭代节点阶段',
     `operation_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '操作类型;1-新增 2-修改',
     `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
     `old_iteration_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '原迭代ID(修改时记录)',
     `old_iteration_name` varchar(255) NOT NULL DEFAULT '' COMMENT '原迭代名称(修改时记录)',
     PRIMARY KEY (`record_id`) USING BTREE,
     INDEX `idx_project_id`(`project_id` ASC) USING BTREE,
     INDEX `idx_cnt_id`(`cnt_id` ASC) USING BTREE,
     INDEX `idx_operator_uid`(`operator_uid` ASC) USING BTREE,
     INDEX `idx_iteration_id`(`iteration_id` ASC) USING BTREE,
     INDEX `idx_node_id`(`node_id` ASC) USING BTREE,
     INDEX `idx_operation_time`(`operation_time` ASC) USING BTREE,
     INDEX `idx_operation_type`(`operation_type` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '迭代需求关联记录表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_iteration_process_node_end_record`  (
     `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `project_id` bigint UNSIGNED NOT NULL COMMENT '所属项目ID',
     `iteration_id` bigint UNSIGNED NOT NULL COMMENT '迭代ID',
     `iteration_process_node_id` bigint UNSIGNED NOT NULL COMMENT '迭代节点ID',
     `node_stage` varchar(50) NOT NULL COMMENT '节点阶段',
     `estimate_start_time` datetime NULL DEFAULT NULL COMMENT '预估开始时间',
     `estimate_end_time` datetime NULL DEFAULT NULL COMMENT '预估结束时间',
     `actual_start_time` datetime NULL DEFAULT NULL COMMENT '实际开始时间',
     `actual_end_time` datetime NULL DEFAULT NULL COMMENT '实际结束时间',
     `node_owner_id` bigint UNSIGNED NOT NULL COMMENT '节点负责人ID',
     `node_owner_name` varchar(50) NOT NULL COMMENT '节点负责人姓名',
     `group_leader_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '组长ID',
     `group_leader_name` varchar(50) NULL DEFAULT NULL COMMENT '组长姓名',
     `is_delayed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否延期：0-否 1-是',
     `create_by` bigint UNSIGNED NOT NULL COMMENT '创建人ID',
     `create_by_name` varchar(50) NOT NULL COMMENT '创建人姓名',
     `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_by` bigint UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
     `update_by_name` varchar(50) NULL DEFAULT NULL COMMENT '更新人姓名',
     `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否 1-是',
     PRIMARY KEY (`id`) USING BTREE,
     INDEX `idx_project_id`(`project_id` ASC) USING BTREE,
     INDEX `idx_iteration_id`(`iteration_id` ASC) USING BTREE,
     INDEX `idx_iteration_process_node_id`(`iteration_process_node_id` ASC) USING BTREE,
     INDEX `idx_create_at`(`create_at` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '迭代流程节点结束记录表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_meeting_collection_change`  (
     `change_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '变更ID',
     `project_id` bigint UNSIGNED NOT NULL COMMENT '项目ID',
     `task_id` bigint UNSIGNED NOT NULL COMMENT '任务ID',
     `task_title` varchar(255) NOT NULL DEFAULT '' COMMENT '任务标题',
     `meeting_type` varchar(50) NOT NULL DEFAULT '' COMMENT '会议类型（已弃用，改为使用会议类型关联表）',
     `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
     `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
     `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
     `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
     PRIMARY KEY (`change_id`) USING BTREE,
     INDEX `idx_project_id`(`project_id` ASC) USING BTREE,
     INDEX `idx_task_id`(`task_id` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '会议集合变更记录表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_meeting_collection_change_type`  (
    `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `change_id` bigint UNSIGNED NOT NULL COMMENT '变更ID',
    `type_code` varchar(50) NOT NULL DEFAULT '' COMMENT '类型编码',
    `type_name` varchar(100) NOT NULL DEFAULT '' COMMENT '类型名称',
    `is_delete` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_change_id`(`change_id` ASC) USING BTREE,
    INDEX `idx_type_code`(`type_code` ASC) USING BTREE
) ENGINE = InnoDB  COMMENT = '会议集合变更类型关联表' ROW_FORMAT = DYNAMIC;

CREATE TABLE `t_meeting_collection_change_user`  (
      `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      `change_id` bigint UNSIGNED NOT NULL COMMENT '变更ID',
      `user_id` bigint UNSIGNED NOT NULL COMMENT '用户ID',
      `user_type` tinyint UNSIGNED NOT NULL COMMENT '用户类型：1-节点负责人，2-主讲人',
      `is_delete` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
      `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
      `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
      `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
      PRIMARY KEY (`id`) USING BTREE,
      INDEX `idx_change_id`(`change_id` ASC) USING BTREE,
      INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
      INDEX `idx_user_type`(`user_type` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '会议集合变更用户关联表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_nissin_bug_statistics_rules`  (
       `nissin_bug_statistics_rules_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
       `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
       `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
       `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
       `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
       `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
       `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
       `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
       `project_id` int NOT NULL DEFAULT 0 COMMENT '项目id',
       `time` time NOT NULL DEFAULT '00:00:00' COMMENT '定时任务执行时间，时分秒',
       `statistics` tinyint(1) NOT NULL DEFAULT 0 COMMENT '统计日期：1当天、2昨天',
       `enable` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
       `use_custom_workdays` tinyint NOT NULL DEFAULT 0 COMMENT '使用自定义工作日 0-否，1-是',
       PRIMARY KEY (`nissin_bug_statistics_rules_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '日清bug统计规则设置表' ROW_FORMAT = DYNAMIC;

CREATE TABLE `t_tag_library`  (
    `tag_id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_by` int NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '创建时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` int NOT NULL DEFAULT 0 COMMENT '更新人',
    `update_by_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:00' COMMENT '更新时间',
    `tag_code` varchar(30) NOT NULL DEFAULT '' COMMENT '标签code',
    `tag_name` varchar(30) NOT NULL DEFAULT '' COMMENT '标签名称',
    `sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序，默认正序',
    `priority` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '优先级，默认正序',
    `group_type` tinyint NOT NULL DEFAULT 0 COMMENT '分组类型;1-阶段分类参数、2-会议参数',
    `can_be_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可删除0可1否',
    PRIMARY KEY (`tag_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '标签库表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_task_record`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_id` int NOT NULL COMMENT '项目ID',
    `date` date NOT NULL COMMENT '统计日期',
    `time` time NOT NULL COMMENT '定时任务执行时间',
    `statistics` tinyint(1) NOT NULL COMMENT '统计日期：1当天、2昨天',
    `count` int NOT NULL DEFAULT 0 COMMENT '缺陷数量',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_project_id`(`project_id` ASC) USING BTREE,
    INDEX `idx_date`(`date` ASC) USING BTREE,
    INDEX `idx_project_date`(`project_id` ASC, `date` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '任务记录表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_work_hours_rejection_records`  (
    `rejection_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_by` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_by_name` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
    `update_by` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人',
    `update_by_name` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `cnt_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '任务ID',
    `cnt_title` varchar(255) NOT NULL DEFAULT '' COMMENT '任务标题',
    `handler_uid` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '处理人ID',
    `handler_name` varchar(255) NOT NULL DEFAULT '' COMMENT '处理人名称',
    `rejection_reason` text NULL COMMENT '打回原因',
    `submitter_uid` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '提交人ID',
    `submitter_name` varchar(255) NOT NULL DEFAULT '' COMMENT '提交人名称',
    `submit_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    `approval_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审批时间',
    `approval_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联的审批ID',
    `working_hours` double default 0 not null comment '工时',
    PRIMARY KEY (`rejection_id`) USING BTREE,
    INDEX `idx_cnt_id`(`cnt_id` ASC) USING BTREE,
    INDEX `idx_handler_uid`(`handler_uid` ASC) USING BTREE,
    INDEX `idx_submitter_uid`(`submitter_uid` ASC) USING BTREE,
    INDEX `idx_submit_at`(`submit_at` ASC) USING BTREE,
    INDEX `idx_approval_id`(`approval_id` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '工时打回记录表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_work_items_change_records`  (
     `change_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `create_by` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人',
     `create_by_name` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人名称',
     `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除;1-是 0-否',
     `update_by` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人',
     `update_by_name` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人名称',
     `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `project_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '项目ID',
     `project_name` varchar(255) NOT NULL DEFAULT '' COMMENT '项目名称',
     `cnt_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '需求ID',
     `cnt_title` varchar(255) NOT NULL DEFAULT '' COMMENT '需求标题',
     `handler_uid` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '需求负责人ID',
     `handler_name` varchar(255) NOT NULL DEFAULT '' COMMENT '需求负责人名称',
     `iteration_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '迭代ID',
     `iteration_name` varchar(255) NOT NULL DEFAULT '' COMMENT '迭代名称',
     `node_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '迭代节点ID',
     `node_name` varchar(255) NOT NULL DEFAULT '' COMMENT '迭代节点名称',
     `node_stage` varchar(50) NOT NULL DEFAULT '' COMMENT '迭代节点阶段',
     `change_reason` text NULL COMMENT '变更原因',
     `submitter_uid` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '提交人ID',
     `submitter_name` varchar(255) NOT NULL DEFAULT '' COMMENT '提交人名称',
     `submit_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
     `approval_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审批时间',
     `approval_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联的审批ID',
     PRIMARY KEY (`change_id`) USING BTREE,
     INDEX `idx_project_id`(`project_id` ASC) USING BTREE,
     INDEX `idx_cnt_id`(`cnt_id` ASC) USING BTREE,
     INDEX `idx_handler_uid`(`handler_uid` ASC) USING BTREE,
     INDEX `idx_iteration_id`(`iteration_id` ASC) USING BTREE,
     INDEX `idx_node_id`(`node_id` ASC) USING BTREE,
     INDEX `idx_submit_at`(`submit_at` ASC) USING BTREE,
     INDEX `idx_approval_id`(`approval_id` ASC) USING BTREE
) ENGINE = InnoDB COMMENT = '需求变更记录表' ROW_FORMAT = Dynamic;


-- 表数据更新
INSERT INTO `t_tag_library` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `tag_code`, `tag_name`, `sort`, `priority`, `group_type`, `can_be_deleted`) VALUES (818, '韦顺隆', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 'tag_49809161411037815988223', '需求阶段', 0, 1, 1, 1);
INSERT INTO `t_tag_library` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `tag_code`, `tag_name`, `sort`, `priority`, `group_type`, `can_be_deleted`) VALUES (818, '韦顺隆', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 'tag_49809162008138929929038', '开发阶段', 1, 2, 1, 1);
INSERT INTO `t_tag_library` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `tag_code`, `tag_name`, `sort`, `priority`, `group_type`, `can_be_deleted`) VALUES (818, '韦顺隆', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 'tag_49809162267640520716923', '测试阶段', 2, 3, 1, 1);
INSERT INTO `t_tag_library` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `tag_code`, `tag_name`, `sort`, `priority`, `group_type`, `can_be_deleted`) VALUES (818, '韦顺隆', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 'tag_49809163104739710083060', '需求澄清会议', 0, 0, 2, 1);
INSERT INTO `t_tag_library` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `tag_code`, `tag_name`, `sort`, `priority`, `group_type`, `can_be_deleted`) VALUES (818, '韦顺隆', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 'tag_49809163548077666888096', '需求评审会议', 1, 0, 2, 1);
INSERT INTO `t_tag_library` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `tag_code`, `tag_name`, `sort`, `priority`, `group_type`, `can_be_deleted`) VALUES (818, '韦顺隆', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 'tag_49809164358291370933180', '反串讲会议', 2, 0, 2, 1);
INSERT INTO `t_tag_library` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `tag_code`, `tag_name`, `sort`, `priority`, `group_type`, `can_be_deleted`) VALUES (818, '韦顺隆', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 'tag_49809164810772888159260', '用例评审会议', 3, 0, 2, 1);
INSERT INTO `t_tag_library` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `tag_code`, `tag_name`, `sort`, `priority`, `group_type`, `can_be_deleted`) VALUES (818, '韦顺隆', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 'tag_49809165157348227374430', '转测演示会议', 4, 0, 2, 1);
INSERT INTO `t_tag_library` (`create_by`, `create_by_name`, `create_at`, `is_delete`, `update_by`, `update_by_name`, `update_at`, `tag_code`, `tag_name`, `sort`, `priority`, `group_type`, `can_be_deleted`) VALUES (818, '韦顺隆', '1971-01-01 00:00:00', 0, 0, '', '1971-01-01 00:00:00', 'tag_49809165556645934321327', 'UAT演示会议', 5, 0, 2, 1);
