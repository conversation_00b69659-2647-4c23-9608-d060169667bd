<?php
/**
 * 事务计数器解决嵌套事务
 * User: 袁志凡
 * Date-Time: 2024/10/10 10:17
 */

namespace utils;

use Exception;
use think\facade\Db;

class DBTransaction
{
    private static $transactionCount = 0;

    // 开始事务
    public static function begin()
    {
        if (self::$transactionCount == 0) {
            // 只有当这是最外层事务时才启动数据库事务
            Db::startTrans();
        }

        self::$transactionCount++;
    }

    // 提交事务
    public static function commit()
    {
        if (self::$transactionCount == 0) {
            throw new Exception("No active transaction");
        }

        self::$transactionCount--;

        if (self::$transactionCount == 0) {
            // 只有最外层事务提交时才真正提交到数据库
            Db::commit();
        }
    }

    // 回滚事务
    public static function rollback()
    {
        // 无论嵌套到第几层，只要有一个失败就回滚整个事务
        if (self::$transactionCount <> 0) {
//            throw new Exception("No active transaction to roll back");
            Db::rollBack();

        }


        self::$transactionCount = 0;
    }

}
