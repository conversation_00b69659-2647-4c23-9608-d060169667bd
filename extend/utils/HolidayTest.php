<?php
declare(strict_types=1);

namespace utils;

/**
 * 节假日工具类测试
 */
class HolidayTest
{
    /**
     * 运行测试
     */
    public static function run(): void
    {
        // 测试当前日期
        $today = date('Y-m-d');
        $today = date('2025-04-26');
        echo "今天({$today})是否为节假日: " . (Holiday::isHoliday($today) ? '是' : '否') . PHP_EOL;
        echo "今天({$today})是否为工作日: " . (Holiday::isWorkday($today) ? '是' : '否') . PHP_EOL;
        echo "今天({$today})的日期类型: " . self::getDateTypeText(Holiday::getDateType($today)) . PHP_EOL;
        echo "今天({$today})的节假日名称: " . (Holiday::getHolidayName($today) ?: '无') . PHP_EOL;
        
        echo PHP_EOL;
        
        // 测试指定日期
        $testDates = [
            '2024-01-01', // 元旦
            '2024-02-10', // 春节
            '2024-05-01', // 劳动节
            '2024-10-01', // 国庆节
            '2024-12-25', // 圣诞节
            '2024-07-06', // 周六
            '2024-07-07', // 周日
            '2024-07-08', // 周一
        ];
        
        foreach ($testDates as $date) {
            echo "日期({$date})是否为节假日: " . (Holiday::isHoliday($date) ? '是' : '否') . PHP_EOL;
            echo "日期({$date})是否为工作日: " . (Holiday::isWorkday($date) ? '是' : '否') . PHP_EOL;
            echo "日期({$date})的日期类型: " . self::getDateTypeText(Holiday::getDateType($date)) . PHP_EOL;
            echo "日期({$date})的节假日名称: " . (Holiday::getHolidayName($date) ?: '无') . PHP_EOL;
            echo PHP_EOL;
        }
        
        // 显示缓存文件
        echo "缓存文件列表:" . PHP_EOL;
        $cacheDir = Holiday::CACHE_DIR;
        if (is_dir($cacheDir)) {
            $files = glob($cacheDir . '*.json');
            foreach ($files as $file) {
                echo "- " . basename($file) . " (" . date('Y-m-d H:i:s', filemtime($file)) . ")" . PHP_EOL;
            }
        } else {
            echo "缓存目录不存在" . PHP_EOL;
        }
    }
    
    /**
     * 获取日期类型文本
     *
     * @param int $type 日期类型
     * @return string 日期类型文本
     */
    private static function getDateTypeText(int $type): string
    {
        switch ($type) {
            case Holiday::TYPE_WORKDAY:
                return '工作日';
            case Holiday::TYPE_WEEKEND:
                return '周末';
            case Holiday::TYPE_HOLIDAY:
                return '节日';
            case Holiday::TYPE_SHIFT:
                return '调休';
            default:
                return '未知';
        }
    }
} 