<?php

namespace utils;

use app\user\logic\UserLogic;
use components\platform\user\dto\UserDetailDto;
use components\platform\user\dto\UserShortInfoDto;


class Ctx
{
    public static $token;
    /** @var ?int 用户id */
    public static ?int $userId;
    /** @var UserShortInfoDto */
    public static UserShortInfoDto $user;
    /** @var UserDetailDto|null 用户详情 */
    private static ?UserDetailDto $userDetail;


    /**
     * 用户详情
     * @return UserDetailDto
     * @throws \Psr\SimpleCache\InvalidArgumentException
     */
    static function getUserDetail()
    {
        if (static::$userDetail) {
            return static::$userDetail;
        }

        static::$userDetail = (new UserLogic())->getUserDetailById(static::$userId);

        return static::$userDetail;
    }


}
