<?php

namespace utils;

use Elastic\Elasticsearch\ClientBuilder;
use think\facade\Env;

class EsClientFactory
{
    private static $instance;

    public static function getInstance()
    {
        if (static::$instance) {
            return static::$instance;
        }

        static::$instance = ClientBuilder::create()
            ->setHosts([Env::get('es.host')])
            ->setBasicAuthentication(Env::get('es.user'), Env::get('es.pass'))
            ->build();

        return static::$instance;
    }

}
