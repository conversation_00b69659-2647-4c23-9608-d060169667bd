<?php
declare (strict_types=1);

namespace utils;

use basic\BaseModel;
use DateTime;
use Elastic\Elasticsearch\ClientInterface;
use basic\BaseLogic;
use exception\BusinessException;
use exception\ParamsException;
use think\facade\Env;
use think\Paginator;

class Es extends BaseLogic
{
    private ClientInterface $esClient;
    private mixed $esTable;

    //关系相关字段必须要有
    private const REQUIRED_FIELD = [
        'cnt_id', 'parent_id', 'root_id'
    ];
    public const NOT_PAGE_MAX = 10000;// 不分页时的最大条数
    protected int $withTrashed = 0;
    protected int $cntId = 0;
    protected string $aggsFidld = '';
    protected array $queryParam = [];
    protected array $param = [];
    protected array $page = [];
    protected int $limit = 1000;
    protected array $fieldList = [];

    public function __construct(ClientInterface $client)
    {
        $this->esClient = $client;
        $this->esTable = Env::get('es.content_index');
    }

    /**
     * 实例化
     * @return static
     * User Long
     * Date 2024/9/11
     */
    public static function getInstance(): static
    {
        return new static(EsClientFactory::getInstance());
    }

    /**
     * 设置ES表名
     * @param string $esTable
     * @return $this
     * User Long
     * Date 2024/9/26
     */
    public function setEsTable(string $esTable): static
    {
        $this->esTable = $esTable;
        return $this;
    }

    /**
     * 获取ES表名
     * @return string
     * User Long
     * Date 2024/9/26
     */
    public function getEsTable(): string
    {
        return $this->esTable;
    }

    /**
     * 设置工作项id
     * @param int $cntId
     * @return $this
     * User Long
     * Date 2024/9/11
     */
    public function setCntId(int $cntId): static
    {
        $this->cntId = $cntId;
        return $this;
    }

    /**
     * 获取工作项id
     * @return int
     * User Long
     * Date 2024/9/11
     */
    public function getCntId(): int
    {
        return $this->cntId;
    }

    /**
     * 设置加载所有数据
     * @return $this
     * User Long
     * Date 2024/9/11
     */
    public function setWithTrashed()
    {
        $this->withTrashed = 1;
        return $this;
    }

    /**
     * 设置聚合参数
     * @param string $aggsFidld
     * @return $this
     * User Long
     * Date 2024/9/11
     */
    public function setAggsFidld(string $aggsFidld): static
    {
        $this->aggsFidld = $aggsFidld;
        return $this;
    }

    /**
     * 获取聚合参数
     * @return string
     * User Long
     * Date 2024/9/11
     */
    public function getAggsFidld(): string
    {
        return $this->aggsFidld;
    }

    /**
     * 设置查询参数
     * @param array $queryParam
     * @return $this
     * User Long
     * Date 2024/9/11
     */
    public function setQueryParam(array $queryParam): static
    {
        $this->queryParam[] = $queryParam;
        return $this;
    }

    /**
     * 获取查询参数
     * @return array
     * User Long
     * Date 2024/9/11
     */
    public function getQueryParam(): array
    {
        return $this->queryParam;
    }

    /**
     * 设置增改删参数
     * @param array $param
     * @return $this
     * User Long
     * Date 2024/9/11
     */
    public function setParam(array $param): static
    {
        $this->param = array_merge($this->param, $param);
        return $this;
    }

    /**
     * 获取增改删参数
     * @return array
     * User Long
     * Date 2024/9/11
     */
    public function getParam(): array
    {
        return $this->param;
    }

    /**
     * 设置指定查询字段
     * @param array $fieldList
     * @return $this
     * User Long
     * Date 2024/9/11
     */
    public function setFieldList(array $fieldList): static
    {
        $this->fieldList = $fieldList;
        return $this;
    }

    /**
     * 获取指定查询字段
     * @return array
     * User Long
     * Date 2024/9/11
     */
    public function getFieldList(): array
    {
        return $this->fieldList;
    }

    /**
     * 设置分页参数
     * @param int $page
     * @param int $listRows
     * @return $this
     * User Long
     * Date 2024/9/11
     */
    public function setPage(int $listRows, int $page = 1): static
    {
        $this->page = ['page' => $page, 'list_rows' => $listRows];
        return $this;
    }

    /**
     * 获取分页参数
     * @return array
     * User Long
     * Date 2024/9/11
     */
    public function getPage(): array
    {
        return $this->page ?? getPageSize();
    }

    /**
     * 设置查询数量
     * @param int $limit
     * @return $this
     * User Long
     * Date 2024/9/11
     */
    public function setLimit(int $limit): static
    {
//        if ($limit > self::NOT_PAGE_MAX) {
//            throw new ParamsException('最高查询 '.self::NOT_PAGE_MAX.' 条');
//        }
        $this->limit = $limit;
        return $this;
    }

    /**
     * 获取查询数量
     * @return int
     * User Long
     * Date 2024/9/11
     */
    public function getLimit(): int
    {
        return $this->limit;
    }

    /**
     * 调用ES保存
     * @return \Psr\Http\Message\StreamInterface
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/9/11
     */
    private function esSave(): \Psr\Http\Message\StreamInterface
    {
        return $this->esClient->index($this->param)->getBody();
    }

    /**
     * 调用ES更新
     * @return \Psr\Http\Message\StreamInterface
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/9/11
     */
    private function esUpdate(): \Psr\Http\Message\StreamInterface
    {
        return $this->esClient->update($this->param)->getBody();
    }

    /**
     * 调用ES删除
     * @return mixed
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/9/11
     */
    private function esDelete()
    {
        $response = $this->esClient->deleteByQuery($this->param)->getBody();

        return json_decode((string)$response, true);
    }

    /**
     * 发起ES查询
     * @return mixed
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/9/11
     */
    private function getBody()
    {
        $response = $this->esClient->search($this->queryParam)->getBody();

        return json_decode((string)$response, true);
    }

    /**
     * es数据get方法
     * @param $data
     * @return mixed
     * <AUTHOR>
     * @date 2024/9/5 16:32
     */
    private function esGetDataTransfer($data): mixed
    {
        foreach ($data as &$v) {
            foreach ($v as &$vv) {
                $result = $this->numberToDate($vv);
                if ($result) {
                    $vv = $result;
                }
            }
        }

        return $data;
    }

    /**
     * 返回没删除的es查询条件
     * @return array
     * <AUTHOR>
     * @date 2024/9/5 16:39
     */
    private function getNotDelParams(): array
    {
        return ['field_name' => 'is_delete', 'value' => BaseModel::DELETE_NOT, 'type' => 'term', 'operate_type' => 'equal'];
    }

    /**
     * 生成es查询参数
     * @param $params
     * @return array|array[]
     * <AUTHOR>
     * @date 2024/8/28 11:19
     */
    private function generateEsQueryParams($params): array
    {
        $params = $this->esSetDataTransfer($params);
        $where = [
            'must' => [],
            'must_not' => [],
            'should' => []
        ];
        foreach ($params as $value) {
            $key = $value['field_name'];
            if (isset($value['exists'])) {
                if ($value['exists']) {
//                    字段存在且值不为空,null、空字符串都不会查出来
                    $where['must'][] = ['exists' => ['field' => $key]];
                } else {
//                    为空
                    $where['must_not'][] = ['exists' => ['field' => $key]];
                }

                continue;
            }
            if (!isset($value['type'])) {
                continue;
            }

            //全文搜索
            if ($value['type'] == 'text') {
                $where['must'][] = ['match' => [$key => $value['value']]];
            }

            //精确匹配
            if ($value['type'] == 'term') {
                switch ($value['operate_type']) {
                    case 'equal':
                        $where['must'][] = ['term' => [$key => $value['value']]];
                        break;
                    case 'not_equal':
                        $where['must_not'][] = ['term' => [$key => $value['value']]];
                        break;
                    case 'between':
                        $where['must'][] = ['range' => [
                            $key => [
                                "gte" => $value['value'][0],
                                "lte" => $value['value'][1],
                            ]
                        ]];
                        break;
                    default:
                        throw new ParamsException("未知的operate_type：" . $value['operate_type']);
                }
            }

            //下拉多选
            if ($value['type'] == 'selector') {
                $where['must'][] = ['terms' => [$key => array_values($value['value'])]];
            }

            if ($value['type'] == 'date') {
                //时间区间
                $where['must'][] = ['range' => [
                    $key => (function ($value) {
                        $result = [];
                        if ($value['value'][0]) {
                            $result['gte'] = $value['value'][0];
                        }
                        if ($value['value'][1]) {
                            $result['lte'] = $value['value'][1];
                        }

                        return $result;
                    })($value)]];
            }
        }

        return $where;
    }

    /**
     * 获取默认查询字段
     * @param array $fieldList
     * @return array|string[]
     * <AUTHOR>
     * @date 2024/8/29 22:18
     */
    private function getFiledList(array $fieldList): array
    {
        return array_merge($fieldList, self::REQUIRED_FIELD);
    }

    /**
     * es数据set方法
     * @param $data
     * @return mixed
     * <AUTHOR>
     * @date 2024/9/3 16:01
     */
    private function esSetDataTransfer($data): mixed
    {
        foreach ($data as &$v) {
            $result = $this->dateToNumber($v);
            if ($result) {
                $v = $result;
            }
        }

        return $data;
    }

    /**
     * 日期去除横杠
     * @param $date
     * @return false|int
     * <AUTHOR>
     * @date 2024/9/3 16:01
     */
    private function dateToNumber($date): bool|int
    {
        if (!is_string($date)) {
            return false;
        }
        $d = DateTime::createFromFormat('Y-m-d', $date);
        // 检查日期是否正确解析并且格式完全匹配
        if ($d && $d->format('Y-m-d') === $date) {
            return (int)$d->format("Ymd");
        }

        return false;
    }

    /**
     * 日期加上横杠
     * @param $date
     * @return false|string
     * <AUTHOR>
     * @date 2024/9/3 16:01
     */
    private function numberToDate($date): bool|int|string
    {

        // 检查是否为8位数字
        if (!is_numeric($date) || strlen((string)$date) !== 8) {
            return false;
        }
        $date = (string)$date;
        // 使用 DateTime 创建对象并验证日期格式
        $year = substr($date, 0, 4);
        $month = substr($date, 4, 2);
        $day = substr($date, 6, 2);

        // 检查是否为有效日期
        $result = checkdate((int)$month, (int)$day, (int)$year);

        return $result ? ($year . '-' . $month . '-' . $day) : false;
    }

    /**
     * 新增
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/9/11
     */
    public function save(): void
    {
        $this->param = [
            'index' => $this->esTable,
            'id' => $this->cntId,
            'body' => $this->esSetDataTransfer($this->param),
            'refresh' => config('es.refresh'),//同步
        ];

        $response = $this->esSave();

        $response = json_decode((string)$response, true);
        if (!(isset($response['result']) && ($response['result'] === 'updated' || $response['result'] === 'created' || $response['result'] === 'noop'))) {
            throw new BusinessException("数据保存错误！");
        }
    }

    /**
     * 更新
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/9/11
     */
    public function update(): void
    {
        $this->param = [
            'index' => $this->esTable,
            'id' => $this->cntId,
            'body' => ['doc' => $this->esSetDataTransfer($this->param)],
            'refresh' => config('es.refresh'),//同步
        ];

        $response = $this->esUpdate();

        $response = json_decode((string)$response, true);
        if (!(isset($response['result']) && ($response['result'] === 'updated' || $response['result'] === 'created' || $response['result'] === 'noop'))) {
            throw new BusinessException("数据保存错误！");
        }
    }

    /**
     * 删除
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/9/11
     */
    public function delete(): void
    {
        if (!$this->param) {
            throw new ParamsException("条件不可为空！");
        }
        $this->param = [
            'index' => $this->esTable,
            'body' => [
                'query' => [
                    'bool' => $this->generateEsQueryParams($this->param),
                ]
            ],
            'refresh' => config('es.refresh'),//同步
        ];

        $response = $this->esDelete();

        if (!isset($response['deleted']) && $response['deleted']) {
            throw new BusinessException("数据保存错误！");
        }
    }

    /**
     * 获取聚合数据
     * @return array
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/9/11
     */
    public function getAggData(): array
    {
        if (!$this->withTrashed) {
            $this->queryParam[] = $this->getNotDelParams();
        }

        $where = $this->generateEsQueryParams($this->queryParam);

        $this->queryParam = [
            'index' => $this->esTable,
            'body' => [
                'query' => [
                    'bool' => $where
                ],
                'aggs' => [ // 聚合查询语句，这里的语法结构跟ES聚合查询语句一致
                    'categories_count' => [
                        'terms' => [
                            'field' => $this->aggsFidld,
                            'size' => $this->limit
                        ]
                    ]
                ]
            ]
        ];

        $resp = $this->getBody();

        $total = $resp['hits']['total']['value'];
        $data = $resp['hits']['hits'];
        $data = array_column($data, '_source');
        $aggs = $resp['aggregations']['categories_count']['buckets'];

        return compact( 'total', 'aggs', 'data');
    }

    /**
     * 获取分页数据
     * @return \think\paginator\driver\Bootstrap|Paginator
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/9/11
     */
    public function getPaginator(): \think\paginator\driver\Bootstrap|Paginator
    {
        if (!$this->withTrashed) {
            $this->queryParam[] = $this->getNotDelParams();
        }
        if (empty($this->page)) $this->page = getPageSize();

        $where = $this->generateEsQueryParams($this->queryParam);

        $this->queryParam = [
            'index' => $this->esTable,
            'body' => [
                'query' => [
                    'bool' => $where
                ],
                'sort' => [
                    'create_at' => [
                        'order' => 'asc'
                    ]
                ],
            ],
            'from' => ($this->page['page'] - 1) * $this->page['list_rows'], // 起始位置
            'size' => $this->page['list_rows'], // 每页数量

        ];

        if ($this->fieldList) {
            $this->queryParam['body']['_source'] = $this->getFiledList($this->fieldList);
        }

        $resp = $this->getBody();

        $total = $resp['hits']['total']['value'];
        $data = $resp['hits']['hits'];
        $data = array_column($data, '_source');

        $data = $this->esGetDataTransfer($data);

        return Paginator::make($data, $this->page['list_rows'], $this->page['page'], $total);
    }

    /**
     * 获取条数据
     * @return mixed
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/9/11
     */
    public function getList()
    {
        if (!$this->withTrashed) {
            $this->queryParam[] = $this->getNotDelParams();
        }
        $where = $this->generateEsQueryParams($this->queryParam);

        $this->queryParam = [
            'index' => $this->esTable,
            'body' => [
                'query' => [
                    'bool' => $where
                ],
                'sort' => [
                    'create_at' => [
                        'order' => 'asc'
                    ]
                ],
            ],
            'size' => $this->limit, // 数量
        ];

        if ($this->fieldList) {
            $this->queryParam['body']['_source'] = $this->getFiledList($this->fieldList);
        }

        $resp = $this->getBody();
        $data = $resp['hits']['hits'];
        $data = array_column($data, '_source');

        return $this->esGetDataTransfer($data);
    }
}
