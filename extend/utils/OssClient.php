<?php

namespace utils;

use think\facade\Env;

class OssClient
{
    private $accessKeyId;
    private $accessKeySecret;
    private $endpoint;
    private $bucket;

    /**
     * @param $args = [
     * 'accessKeyId'=>'',
     * 'accessKeySecret'=>'',
     * 'endpoint'=>'',
     * 'bucket'=>'',
     * ]
     */
    public function __construct(array $args = [])
    {
        if (empty($args)) {
            $args = [
                'accessKeyId' => Env::get('oss.access_key_id'),
                'accessKeySecret' => Env::get('oss.access_key_secret'),
                'endpoint' => Env::get('oss.endpoint'),
                'bucket' => Env::get('oss.bucket'),
            ];
        }

        $this->accessKeyId = $args['accessKeyId'];
        $this->accessKeySecret = $args['accessKeySecret'];
        $this->endpoint = $args['endpoint'];
        $this->bucket = $args['bucket'];

    }

    /**
     * 获取服务端签名策略
     * @param string $dir 表示用户上传的数据，必须是以$dir开始，不然上传会失败，这一步不是必须项，只是为了安全起见，防止用户通过policy上传到别人的目录。
     * @param int $expire //设置该policy超时时间是10s. 即这个policy过了这个有效时间，将不能访问。
     * @return array
     */
    public function getUploadPolicy(string $dir = '', int $expire = 3000): array
    {
        $dir = $dir ?: 'file/' . date('Y-m-d');
        $now = time();

        $end = $now + $expire;
        $expiration = $this->gmtIso8601($end);

        //最大文件大小.用户可以自己设置
        $condition = array(0 => 'content-length-range', 1 => 0, 2 => 1048576000);
        $conditions[] = $condition;

        // 表示用户上传的数据，必须是以$dir开始，不然上传会失败，这一步不是必须项，只是为了安全起见，防止用户通过policy上传到别人的目录。
        $start = array(0 => 'starts-with', 1 => '$key', 2 => $dir);
        $conditions[] = $start;

        $arr = array('expiration' => $expiration, 'conditions' => $conditions);
        $policy = json_encode($arr);
        $base64_policy = base64_encode($policy);
        $string_to_sign = $base64_policy;
        $signature = base64_encode(hash_hmac('sha1', $string_to_sign, $this->accessKeySecret, true));

        $response = array();
        $response['accessid'] = $this->accessKeyId;
        $response['host'] = 'https://' . $this->bucket . '.' . ltrim($this->endpoint, 'https://');
        $response['policy'] = $base64_policy;
        $response['signature'] = $signature;
        $response['expire'] = $end;
        $response['dir'] = $dir;  // 这个参数是设置用户上传文件时指定的前缀。
        $response['bucket'] = $this->bucket;

        return $response;
    }

    public function accessSign($object = '')
    {
        $timeout = 600;
        $options = [
            "response-content-disposition" => "inline"
            // "response-content-disposition"=>"attachment",
        ];


        $ossClient = new \OSS\OssClient($this->accessKeyId, $this->accessKeySecret, $this->endpoint, false);

        return $ossClient->signUrl($this->bucket, $object, $timeout, 'GET', $options);
    }

    private function gmtIso8601($time)
    {
        return str_replace('+00:00', '.000Z', gmdate('c', $time));
    }


}
