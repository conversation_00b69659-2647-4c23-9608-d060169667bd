<?php

namespace utils;

use exception\PlatformException;
use Guz<PERSON><PERSON>ttp\Client;
use Guz<PERSON><PERSON>ttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\TransferStats;
use OpenTelemetry\API\Trace\Propagation\TraceContextPropagator;
use Psr\Http\Message\RequestInterface;
use system\OpenTelemetry;

class HttpClient
{
    static $logContent = [];

    /**
     * @param string|null $authorization
     * @param array $header
     * @return Client
     */
    public static function getPlatformClient(string $authorization = null, array $header = [])
    {
        $baseUri = env('PLATFORM.HOST', '');
        if (!$baseUri) {
            throw new PlatformException('未配置中台域名!');
        }

        $headers = array_merge([
            'accept' => 'text/plain',
            'timeout' => env('PLATFORM.TIMEOUT')
        ], $header);

        if ($authorization) {
            $headers['Authorization'] = urldecode($authorization);
        }

        return self::default($baseUri, Log::PLATFORM, $headers);
    }

    /**
     * @param $baseUri
     * @param $logChannel
     * @param $header
     * @return Client
     */
    public static function default($baseUri, $logChannel, $header = [])
    {

        $headers = array_merge([
            'accept' => 'text/plain',
            'timeout' => 30
        ], $header);

        $carrier = [];
        TraceContextPropagator::getInstance()->inject($carrier);
        foreach ($carrier as $name => $value) {
            $headers[$name] = $value;
        }

        $span = null;
        $startTime = 0;
        $stack = new HandlerStack();

        $stack->setHandler(new CurlHandler());
        $stack->push(Middleware::mapRequest(function (RequestInterface $request) use (&$startTime, &$span) {
            $span = OpenTelemetry::startSpan($request->getUri());
            $startTime = getCurrentMilliseconds();

            return $request;
        }));


        return new Client([
            'base_uri' => $baseUri,
            'headers' => $headers,
            'verify' => false,
            'http_errors' => false,
            'handler' => $stack,
            'on_stats' => function (TransferStats $stats) use ($logChannel, &$startTime, &$endTime, &$span) {

                $request = $stats->getRequest();
                $logsContent["__HEADER__"] = $request->getHeaders();
                $logsContent["__REQUEST__"] = [
                    "PATH" => $request->getUri()->getPath(),
                    "METHOD" => $request->getMethod(),
                    "QUERY" => $request->getUri()->getQuery(),
                    "BODY" => $request->getBody(),
                    "TRANSFER_TIME" => $stats->getTransferTime(),
                    "STATUS_CODE" => $stats->getResponse()->getStatusCode(),
                ];
//                $span->getSpan()->setAttributes([
//                    'status' => $logsContent['__REQUEST__']['STATUS_CODE'],
//                    'time' => $logsContent['__REQUEST__']['TRANSFER_TIME']
//                ]);
                OpenTelemetry::endSpan($span);
                //判断是否有正常的响应对象
                if ($stats->hasResponse()) {
                    $response = $stats->getResponse();
                    $body = json_decode($response->getBody(), true);
                    if (json_last_error()) {
                        $body = $response->getBody();
                    }

                    $logsContent["__RESPONSE__"] = [
                        "__TIME__" => getCurrentMilliseconds() - $startTime,
                        "__BODY__" => $body,
                    ];

                } else {
                    if ($stats->getHandlerErrorData() instanceof \Exception) {

                        $error = $stats->getHandlerErrorData()->getMessage();
                    } else {
                        $error = $stats->getHandlerErrorData();
                    }
                    $logsContent["__RESPONSE__"] = [
                        "ERROR" => $error
                    ];
                }

                static::$logContent = json_encode($logsContent, 320);
                Log::instance($logChannel)->info(static::$logContent);

            }
        ]);

    }


}
