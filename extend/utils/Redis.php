<?php
/**
 * Desc Redis拓展
 * User Long
 * Date 2024/12/12
 */

namespace utils;

use exception\ParamsException;
use think\facade\Cache;

class Redis
{
    private static $instance; // 对象
    protected $config; // 配置
    protected $redis; // 实例化redis
    protected $key; // 秘钥
    protected $cache; // 缓存
    protected $second; // 过期时间 0-不限制
    protected $refreshSecond = null; // 刷新的过期时间

    /**
     * 设置配置文件、链接redis
     * @return $this
     * User Long
     * Date 2024/12/12
     */
    private function setConfig()
    {
        $this->redis = Cache::store('redis');

        return $this;
    }

    /**
     * 获取对象
     * @return self
     * User Long
     * Date 2024/12/12
     */
    public static function getInstance()
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self();
        }

        // 加载redis
        self::$instance->setConfig();

        return self::$instance;
    }

    /**
     * 设置个人对象
     * @return mixed
     * User Long
     * Date 2024/12/12
     */
    public static function getPersonalInstance()
    {
        self::getInstance();

        if (!Ctx::$userId) throw new ParamsException('使用redis 个人秘钥必须登录');
        self::$instance->key = md5Salt(Ctx::$user->name.'~redis^'.Ctx::$userId.'@personal');

        return self::$instance;
    }

    /**
     * 获取 秘钥
     * @return mixed
     * User Long
     * Date 2024/12/12
     */
    public function getKey()
    {
        return $this->key;
    }

    /**
     * 设置 秘钥
     * @param $key
     * @return $this
     * User Long
     * Date 2024/12/12
     */
    public function setKey($key)
    {
        $this->key = $key;
        return $this;
    }

    /**
     * 获取 缓存数据
     * @return mixed
     * User Long
     * Date 2024/12/12
     */
    public function getCache()
    {
        if (!empty($this->cache)) {
            return $this->cache;
        } else {
            // 检查键是否存在
            if ($this->redis->exists($this->getKey())) {
                return json_decode($this->redis->get($this->getKey()), true);
            }
        }

        return [];
    }

    /**
     * 设置 缓存数据
     * @param $cache $cache 需要保存的缓存值，支持  'a'  或  ['a' => '1111']
     * @return $this
     * User Long
     * Date 2024/12/12
     */
    public function setCache($cache)
    {
        // 检查键是否存在
        if ($this->redis->exists($this->getKey())) {
            $data = json_decode($this->redis->get($this->getKey()), true);
            if ($data === null && is_array($cache)) $data = [];

            // 类型匹对
            if (is_array($data) && !is_array($cache) || !is_array($data) && is_array($cache)) throw new ParamsException('缓存数据类型不一致，请修改格式 或 删除缓存后重新储存');

            // 数组合并
            if (is_array($data)) $cache = array_merge($data, $cache);
        }

        // 处理存储数据
        if (is_array($cache)) {
            if (is_array($this->cache)) $cache = array_merge($this->cache, $cache);

            ksort($cache);
        }

        $this->cache = $cache;
        return $this;
    }

    /**
     * 删除缓存数据
     * @param string $cache 需要删除缓存值，支持  'a'  或  ['a', 'b']
     * @return $this
     * User Long
     * Date 2024/12/12
     */
    public function delCache($cache)
    {
        // 检查键是否存在
        if (!$this->redis->exists($this->getKey())) return $this;

        // 获取存储数据
        $data = json_decode($this->redis->get($this->getKey()), true);

        // 数组处理
        if ($data == $cache) {
            $data = null;
        } elseif (is_array($data)) {
            if (is_string($cache) && isset($data[$cache])) {
                unset($data[$cache]);
            } elseif (is_array($cache)) {
                foreach ($cache as $v) {
                    if (is_string($v) && isset($data[$v])) unset($data[$v]);
                }
            }
            ksort($data);
        }

        $this->cache = $data;

        return $this;
    }

    /**
     * 获取存储时间  默认到期时间到第二天 0:0:0
     * @return int
     * User Long
     * Date 2024/12/12
     */
    public function getSecond()
    {
        return $this->second ?: strtotime('tomorrow') - time();
    }

    /**
     * 设置过期时间
     * @param $second 0-不限制
     * @return $this
     * User Long
     * Date 2024/12/12
     */
    public function setSecond($second)
    {
        $this->second = $second;
        return $this;
    }

    /**
     * 获取redis保存值
     * @return mixed
     * User Long
     * Date 2024/12/12
     */
    public function getRedis()
    {
        if (!$this->getKey()) throw new ParamsException('redis 缺失key');

        // 返回缓存数据
        return $this->redis->get($this->getKey());
    }

    /**
     * 保存并获取缓存数据
     * @return mixed
     * User Long
     * Date 2024/12/12
     */
    public function send()
    {
        if (!$this->getKey()) throw new ParamsException('redis 缺失key');

        // 检查键是否存在
        if (!$this->redis->has($this->getKey())) {
            $this->refreshSecond = $this->getSecond();//第一次设置过期时间
        }

        // 处理缓存数据
        $data = $this->getCache();
        if ($this->getCache() && is_array($this->getCache())) $data = json_encode($this->getCache(), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        // 缓存自增
        $this->redis->set($this->getKey(), $data, $this->refreshSecond);

        // 返回缓存数据
        return json_decode($this->redis->get($this->getKey()), true);
    }

    /**
     * 删除缓存数据
     * @return bool
     * User Long
     * Date 2024/12/12
     */
    public function del()
    {
        if (!$this->getKey()) throw new ParamsException('redis 缺失key');

        // 检查键是否存在
        if (!$this->redis->has($this->getKey())) return true;

        // 返回删除结果
        return (bool)$this->redis->delete($this->getKey());
    }

    // TODO 先废弃，后续有需求在改造
//    /**
//     * 自增缓存（针对数值缓存）
//     * @param int $num
//     * @return mixed
//     * User Long
//     * Date 2024/12/12
//     */
//    public function getInc(int $num = 1)
//    {
//        if (!$this->getKey()) throw new ParamsException('redis 缺失key');
//
//        // 检查键是否存在
//        if (!$this->redis->exists($this->getKey())) {
//            $expire = true;//第一次设置过期时间
//        }
//
//        // 缓存自增
//        $this->redis->incr($this->getKey(), $num);
//
//        // 设置有效时间
//        isset($expire) && $this->redis->expire($this->getKey(), $this->getSecond());
//
//        // 返回缓存数据
//        return $this->redis->get($this->getKey());
//    }
//
//    /**
//     * 自减缓存（针对数值缓存）
//     * @param int $num
//     * @return mixed
//     * User Long
//     * Date 2024/12/12
//     */
//    public function getDec(int $num = 1)
//    {
//        if (!$this->getKey()) throw new ParamsException('redis 缺失key');
//
//        // 检查键是否存在
//        if (!$this->redis->exists($this->getKey())) throw new BusinessException('redis 查无当前key数据');
//        if ($this->redis->get($this->getKey()) <= 1) throw new BusinessException('redis 自减不支持加载负数');
//
//        // 缓存自增
//        $this->redis->decr($this->getKey(), $num);
//
//        // 设置有效时间
//        $this->redis->expire($this->getKey(), $this->getSecond());
//
//        // 返回缓存数据
//        return $this->redis->get($this->getKey());
//    }

}
