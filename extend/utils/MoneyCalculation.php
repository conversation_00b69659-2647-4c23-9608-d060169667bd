<?php
/**
 * Desc 金额计算
 * User Long
 * Date 2024/7/2
 */

namespace utils;

class MoneyCalculation
{
    /**
     * 金额计算
     * @param $n1 :金额1
     * @param $symbol :加减乘除
     * @param $n2 :金额2
     * @param string $scale 保留小数
     * @return string|null
     * User Long
     * Date-Time 2022/10/29 18:41
     */
    public static function pricecalc($n1, $symbol, $n2, $scale = '2')
    {
        $res = "";
        switch ($symbol) {
            case "+"://加法
                $res = bcadd($n1, $n2, $scale);
                break;
            case "-"://减法
                $res = bcsub($n1, $n2, $scale);
                break;
            case "*"://乘法
                $res = bcmul($n1, $n2, $scale);
                break;
            case "/"://除法
                $res = bcdiv($n1, $n2, $scale);
                break;
            case "%"://求余、取模
                $res = bcmod($n1, $n2, $scale);
                break;
            default:
                $res = "";
                break;
        }
        return $res;
    }

    /**
     * 价格由元转分(用于微信支付单位转换)
     * @param $price 金额
     * @return int
     * User Long
     * Date-Time 2022/10/29 18:41
     */
    public static function priceyuantofen($price)
    {
        return intval(self::pricecalc(100, "*", $price));
    }

    /**
     * 价格由分转元
     * @param $price 金额
     * @return string|null
     * User Long
     * Date-Time 2022/10/29 18:41
     */
    public static function pricefentoyuan($price)
    {
        return self::pricecalc(self::priceformat($price), "/", 100);
    }

    /**
     * 价格格式化
     * @param $price 金额
     * @return string
     * User Long
     * Date-Time 2022/10/29 18:40
     */
    public static function priceformat($price, $decimals = 2)
    {
        return number_format($price, $decimals, '.', '');
    }
}