<?php
declare(strict_types=1);

namespace utils;

/**
 * 节假日工具类
 * 用于判断指定日期是否为节假日
 */
class Holiday
{
    /**
     * 节假日API地址
     */
    const API_URL = 'https://timor.tech/api/holiday/year/';

    /**
     * 缓存目录
     */
    const CACHE_DIR = 'public/static/holidays/';

    /**
     * 节假日类型
     */
    const TYPE_WORKDAY = 0; // 工作日
    const TYPE_WEEKEND = 1; // 周末
    const TYPE_HOLIDAY = 2; // 节日
    const TYPE_SHIFT = 3;   // 调休

    /**
     * 判断指定日期是否为节假日
     *
     * @param string|null $date 日期，格式为Y-m-d，如果为null则使用当前日期
     * @return bool 是否为节假日
     */
    public static function isHoliday(?string $date = null): bool
    {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        // 解析日期
        $timestamp = strtotime($date);
        if ($timestamp === false) {
            return false;
        }

        $year = date('Y', $timestamp);
        $month = date('m', $timestamp);
        $day = date('d', $timestamp);
        $dateKey = $month . '-' . $day;

        // 获取节假日数据
        $holidayData = self::getHolidayData($year);
        if (empty($holidayData)) {
            // 如果获取失败，则按照周末判断
            $weekDay = date('N', $timestamp);
            return $weekDay >= 6; // 6和7分别代表周六和周日
        }

        // 判断是否在holiday中
        if (isset($holidayData['holiday'][$dateKey])) {
            return $holidayData['holiday'][$dateKey]['holiday'];
        }

        // 判断是否在type中
        $fullDate = $year.'-'.$month.'-'.$day;
        if (isset($holidayData['type'][$fullDate])) {
            $type = $holidayData['type'][$fullDate]['type'];
            // 工作日和调休日不是节假日
            return ! ($type === self::TYPE_WORKDAY || $type === self::TYPE_SHIFT);
        }

//        // 默认按照周末判断
//        $weekDay = date('N', $timestamp);
//        return $weekDay >= 6;
        return false;
    }

    /**
     * 判断指定日期是否为工作日
     *
     * @param string|null $date 日期，格式为Y-m-d，如果为null则使用当前日期
     * @return bool 是否为工作日
     */
    public static function isWorkday(?string $date = null): bool
    {
        return !self::isHoliday($date);
    }

    /**
     * 获取指定日期的类型
     *
     * @param string|null $date 日期，格式为Y-m-d，如果为null则使用当前日期
     * @return int 日期类型：0-工作日，1-周末，2-节日，3-调休
     */
    public static function getDateType(?string $date = null): int
    {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        // 解析日期
        $timestamp = strtotime($date);
        if ($timestamp === false) {
            return self::TYPE_WORKDAY;
        }

        $year = date('Y', $timestamp);
        $fullDate = $date;

        // 获取节假日数据
        $holidayData = self::getHolidayData($year);
        if (empty($holidayData)) {
            // 如果获取失败，则按照周末判断
            $weekDay = date('N', $timestamp);
            return $weekDay >= 6 ? self::TYPE_WEEKEND : self::TYPE_WORKDAY;
        }

        // 判断是否在type中
        if (isset($holidayData['type'][$fullDate])) {
            return $holidayData['type'][$fullDate]['type'];
        }

        // 默认按照周末判断
        $weekDay = date('N', $timestamp);
        return $weekDay >= 6 ? self::TYPE_WEEKEND : self::TYPE_WORKDAY;
    }

    /**
     * 获取指定年份的节假日数据
     *
     * @param string $year 年份
     * @return array 节假日数据
     */
    public static function getHolidayData(string $year): array
    {
        // 检查缓存目录是否存在
        if (!is_dir(self::CACHE_DIR)) {
            mkdir(self::CACHE_DIR, 0755, true);
        }

        $cacheFile = self::CACHE_DIR . $year . '.json';

        // 如果缓存文件存在且不是当前年份，则直接返回缓存数据
        if (file_exists($cacheFile) && $year != date('Y')) {
            $cacheData = file_get_contents($cacheFile);
            return json_decode($cacheData, true) ?: [];
        }

        // 如果缓存文件存在且是当前年份，检查文件修改时间
        if (file_exists($cacheFile) && $year == date('Y')) {
            $fileModTime = filemtime($cacheFile);
            // 如果文件修改时间在24小时内，则直接返回缓存数据
            if ($fileModTime !== false && (time() - $fileModTime) < 86400) {
                $cacheData = file_get_contents($cacheFile);
                return json_decode($cacheData, true) ?: [];
            }
        }

        // 从API获取数据
        $apiData = self::fetchHolidayData($year);
        if (!empty($apiData)) {
            // 缓存数据
            file_put_contents($cacheFile, json_encode($apiData));
            return $apiData;
        }

        // 如果API获取失败但缓存文件存在，则返回缓存数据
        if (file_exists($cacheFile)) {
            $cacheData = file_get_contents($cacheFile);
            return json_decode($cacheData, true) ?: [];
        }

        return [];
    }

    /**
     * 从API获取节假日数据
     *
     * @param string $year 年份
     * @return array 节假日数据
     */
    private static function fetchHolidayData(string $year): array
    {
        $url = self::API_URL . $year . '?type=Y';
        
        // 使用curl获取数据
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode != 200 || empty($response)) {
            return [];
        }

        $data = json_decode($response, true);
        if (!isset($data['code']) || $data['code'] !== 0) {
            return [];
        }

        return $data;
    }

    /**
     * 获取指定日期的节假日名称
     *
     * @param string|null $date 日期，格式为Y-m-d，如果为null则使用当前日期
     * @return string|null 节假日名称，如果不是节假日则返回null
     */
    public static function getHolidayName(?string $date = null): ?string
    {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        // 解析日期
        $timestamp = strtotime($date);
        if ($timestamp === false) {
            return null;
        }

        $year = date('Y', $timestamp);
        $month = date('m', $timestamp);
        $day = date('d', $timestamp);
        $dateKey = $month . '-' . $day;
        $fullDate = $year . '-' . $month . '-' . $day;

        // 获取节假日数据
        $holidayData = self::getHolidayData($year);
        if (empty($holidayData)) {
            return null;
        }

        // 判断是否在holiday中
        if (isset($holidayData['holiday'][$dateKey]) && $holidayData['holiday'][$dateKey]['holiday']) {
            return $holidayData['holiday'][$dateKey]['name'];
        }

        // 判断是否在type中
        if (isset($holidayData['type'][$fullDate])) {
            $type = $holidayData['type'][$fullDate]['type'];
            if ($type === self::TYPE_HOLIDAY) {
                return $holidayData['type'][$fullDate]['name'];
            }
        }

        return null;
    }

    /**
     * 清除指定年份的缓存
     *
     * @param string|null $year 年份，如果为null则清除所有缓存
     * @return bool 是否清除成功
     */
    public static function clearCache(?string $year = null): bool
    {
        if (!is_dir(self::CACHE_DIR)) {
            return true;
        }

        if ($year === null) {
            // 清除所有缓存
            $files = glob(self::CACHE_DIR . '*.json');
            foreach ($files as $file) {
                unlink($file);
            }
            return true;
        }

        $cacheFile = self::CACHE_DIR . $year . '.json';
        if (file_exists($cacheFile)) {
            return unlink($cacheFile);
        }

        return true;
    }
} 