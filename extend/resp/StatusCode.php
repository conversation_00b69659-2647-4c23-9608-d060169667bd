<?php

namespace resp;

/**
 * 参数异常问题问题统一 4开头
 * 业务流程问题统一  5开头
 */
class StatusCode
{
    const SUCCESS_CODE = 20000; // 成功响应
    const PARAMS_ERROR_CODE = 40000; // 参数问题
    const IMPORT_ERROR_CODE = 40001; // 导入失败
    const DATA_NOT_FOUND = 40002; // 未找到对应数据
    const NO_AUTHORIZATION_CODE = 400300; // 没有登录
    const BUSINESS_CODE = 60000; // 业务流程问题
    const PLATFORM_CODE = 60600; // 中台问题
    const UPLOAD_ERROR_CODE = 50000; //上传失败
    const EXPORT_ERROR_CODE = 50001; //导出失败
    const DATA_UPDATED = 61000; //数据已被更新，请刷新后重试

    const SUCCESS_MSG = '操作成功';
    const PARAMS_MSG = '请仔细检查参数';
    const BUSINESS_MSG = '业务异常，请仔细检查业务或者联系相关管理人员';
    const NO_AUTHORIZATION_MSG = '请登录';
    const PLATFORM_MSG = '中台没有返回正确数据';
    const ERROR_MEG = '操作失败';
    const ID_EMPTY_MEG = 'ID不可为空';
    const DATA_EMPTY_MEG = '数据不存在';
    const UPLOAD_ERROR_MSG = '上传失败';
    const EXPORT_ERROR_MSG = '导出失败';
    const REQUEST_ERROR_MSG = '错误的请求';
    const BUSINESS_APPROVING = '该业务未审批完成，请等待审批结果后再操作';
    const DATA_UPDATED_MES = '数据已被更新，请刷新后重试';
    const DATA_NOT_FOUND_MSG = '未找到对应数据';
}
