<?php

namespace resp;

use \think\response\Json;

/**
 * Desc:
 * User: 曾海洋
 * Date-Time: 2022/12/2 9:56
 */
class Result
{
    public static function success($data = [], $message = StatusCode::SUCCESS_MSG): Json
    {
        return json([
            'code' => StatusCode::SUCCESS_CODE,
            'message' => $message,
            'data' => $data
        ], 200, [], [JSON_FORCE_OBJECT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES]);
    }

    public static function error($code, $message, $data = []): J<PERSON>
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => $data
        ]);
    }
}
