<?php

/**
 * Desc: 类描述
 * @author: Plum
 * @date 2025/5/20
 */

namespace fsApproval;


class Example extends Basic
{
    protected $pcUrl     = '';
    protected $mobileUrl = '';

    public function __construct($exampleId)
    {
        $this->pcUrl = config('fsApproval.pc_url').$exampleId;
        $this->mobileUrl = config('fsApproval.mobile_url').$exampleId;
    }

    public function newApproval($exampleId, $title, $fsEmployeeId)
    {
        //https://doc.shijizhongyun.com/web/#/52/898
        $params = [
            'approval_id'   => 1,
            'example_id'    => $exampleId,
            'exempt_app_id' => config('fsApproval.fs_app_id'),
            'title'         => $title,//审批标题
            'approval_user' => $fsEmployeeId,//发起人
            'pc_link'       => $this->pcUrl,//pc端审批详情链接
            'mobile_link'   => $this->mobileUrl,//移动端审批详情链接
            'start_time'    => date('Y-m-d H:i:s')
        ];

        return $this->send('example', $params);
    }

    public function syncApproval($systemExampleId, $status)
    {
        $params = [
            'system_example_id' => $systemExampleId,
            'status'            => $status-1,//1同意，2拒绝
            'end_time'          => date('Y-m-d H:i:s')
        ];
        return $this->send('syncExample', $params);
    }

    public function newTask($systemExampleId, $nodeId, $title, $auditUser, array $botSummary)
    {
        $params = [
            'system_example_id' => $systemExampleId,
            'node_id'           => $nodeId,
            'title'             => $title,
            'pc_link'           => $this->pcUrl,
            'mobile_link'       => $this->mobileUrl,
            'audit_user'        => $auditUser,
            'bot_summary_list'  => $botSummary
        ];

        return $this->send('nodeTask', $params);
    }
}
