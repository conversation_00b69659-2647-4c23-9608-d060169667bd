<?php

/**
 * Desc: 类描述
 * @author: Plum
 * @date 2025/5/20
 */

namespace fsApproval;


class Definition extends Basic
{

    /**
     * 创建飞书审批定义
     * @return mixed
     * @throws \Exception
     * @exception Exception
     * <AUTHOR>
     * @date 2025/5/20 15:39
     */
    public function newDefinition()
    {
        //https://doc.shijizhongyun.com/web/#/52/766
        $params = [
            'fs_app'        => 'audit',
            'approval_id'   => 1,
            'approval_name' => '效能平台 - 节点审批',
            'group_id'      => 13,
            'is_statistics' => 1,
            'view_type'     => 0,
        ];

        return $this->send('definition', $params);
    }
}
