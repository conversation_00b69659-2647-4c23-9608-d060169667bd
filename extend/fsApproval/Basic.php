<?php

/**
 * Desc: 类描述
 * @author: Plum
 * @date 2025/5/20
 */

namespace fsApproval;


use GuzzleHttp\Client;
use think\facade\Log;
use utils\HttpClient;

class Basic
{

    /**
     * 设置请求签名
     * @param $params
     * @return mixed
     * @exception Exception
     * <AUTHOR>
     * @date 2025/5/20 14:57
     */
    private function setSign(array $params): array
    {
        $time = date("Y-m-d H:i:s");

        ksort($params);
        $str = json_encode($params, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $params['sign'] = md5($time . base64_encode(sha1($str) . $time));

        $params['request_time'] = $time;

        return $params;
    }

    public function send($key, $params)
    {
        $params['system_id'] = 4;

        $linkUrl = config('fsApproval.domain') . config('fsApproval.urls.' . $key);
        $requestParams = $this->setSign($params);

//        dd($linkUrl,$requestParams);
        $client = HttpClient::getPlatformClient();
        $response = $client->post($linkUrl, [
            'json' => $requestParams
        ]);
        // 获取接收数据
        $ret = json_decode($response->getBody(), true);
        if (empty($ret)){
            Log::info("同步飞书审批失败,请求参数： " . json_encode($params)." 结果：".json_encode($ret));
            throw new \Exception("同步飞书审批失败");
        }

        if ($ret['code'] != 200){
            throw new \Exception("同步飞书审批失败:" . json_encode($ret) . " 请求参数" . json_encode($requestParams));
        }

        return $ret;
    }

}
