<?php

namespace exception;

use Exception;
use resp\StatusCode;


/**
 * 中台异常
 */
class PlatformException extends BaseException
{
    public function __construct(string $message = '', int $code = 0, Exception $previous = null, array $headers = [])
    {
        $code = $code ?: StatusCode::PLATFORM_CODE;
        $message = $message ?: StatusCOde::PLATFORM_MSG;
        parent::__construct($code, $message, $previous, $headers);
    }
}