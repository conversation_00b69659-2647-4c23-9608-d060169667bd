<?php

namespace exception;

use resp\StatusCode;

/**
 * 业务异常类
 */
class BusinessException extends BaseException
{
    public function __construct($message = '', $statusCode = 0, $previous = null, array $headers = [], $code = 0)
    {
        $message = empty($message) ? StatusCode::BUSINESS_MSG : $message;
        $statusCode = $statusCode == 0 ? StatusCode::BUSINESS_CODE : $statusCode;
        parent::__construct($statusCode, $message, $previous, $headers, $code);
    }
}
