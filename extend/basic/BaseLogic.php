<?php
/**
 * Desc 公共逻辑方法（基类）
 * User Long
 * Date 2023/6/26
 */

namespace basic;


use app\centerApi\basic\UserCenter;
use app\infrastructure\model\EnumModel;
use app\project\logic\ProjectUserLogic;
use app\Request;
use subscribes\TaskSubscribe;
use utils\Ctx;

class BaseLogic
{
    /**
     * 判断是否有权限可点击按钮
     * @param int $projectId
     * @param array $nodeUsers
     * @param array $role
     * @return bool|array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/18
     */
    public static function isNodeButton(int $projectId, array $nodeUsers = [], array $role = [EnumModel::PROJECT_MANAGER, EnumModel::ITERATION_LEADER]): bool|array
    {
        $users = [];

        // 获取可操作用户
        if ($nodeUsers) {
            $users = array_merge($users, $nodeUsers);
        }

        // 没有可操作用户 并且 没角色code 直接返回 false
        if (!$users && !$role) return false;

        // 获取可操作角色用户
        $roleUsers = (new ProjectUserLogic())->selectorListQuery($projectId, '', '', $role);
        if ($roleUsers) {
            $users = array_merge($users, array_column($roleUsers, 'user_id'));
        }
        if (!$users) return [];

        if (in_array(Ctx::$userId, $users)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 更新扩展字段 - 创建人信息
     * @param $extends
     * @return mixed
     * User Long
     * Date 2024/11/25
     */
    protected function updateExtendsCreateUser($extends)
    {
        $extends['create_by'] = Ctx::$userId;
        $extends['create_by_name'] = Ctx::$user->name;
        $extends['create_at'] = date('Y-m-d H:i:s');

        return $extends;
    }

    /**
     * 更新扩展字段 - 更新人信息
     * @param $extends
     * @return mixed
     * User Long
     * Date 2024/11/25
     */
    protected function updateExtendsUpdateUser($extends)
    {
        $extends['update_by'] = Ctx::$userId;
        $extends['update_by_name'] = Ctx::$user->name;
        $extends['update_at'] = date('Y-m-d H:i:s');

        return $extends;
    }
}
