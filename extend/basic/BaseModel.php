<?php
/**
 * Desc 公共model方法（基类）
 * User Long
 * Date 2022/12/27
 */

namespace basic;

use app\infrastructure\model\FieldConfigModel;
use app\project\model\ProjectUserModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

class BaseModel extends Model
{
    const DELETE_YES = 1; // 删除
    const DELETE_NOT = 0; // 未删除

    const ENABLE_YES = 1; //是否开启;1-是
    const ENABLE_NOT = 0;   //是否开启;0-否

    // 分类所属 1=>迭代 2=>需求 3=>任务 4=>缺陷 5=>测试用例 6=>测试计划 7=> 需求+任务+缺陷
    const SETTING_TYPE_ITERATION = 1;
    const SETTING_TYPE_DEMAND = 2;
    const SETTING_TYPE_TASK = 3;
    const SETTING_TYPE_DEFECT = 4;
    const SETTING_TYPE_TEST_CASE = 5;
    const SETTING_TYPE_TEST_PLAN = 6;
    const SETTING_TYPE_AGG = 7;

    // 分类所属集合
    const SETTING_TYPE_COLLECT = [
        self::SETTING_TYPE_AGG,
        self::SETTING_TYPE_ITERATION,
        self::SETTING_TYPE_DEMAND,
        self::SETTING_TYPE_TASK,
        self::SETTING_TYPE_DEFECT,
        self::SETTING_TYPE_TEST_CASE,
        self::SETTING_TYPE_TEST_PLAN
    ];

    // 分类所属集合
    const SETTING_TYPE_TEXT = [
        self::SETTING_TYPE_ITERATION => '迭代',
        self::SETTING_TYPE_DEMAND => '需求',
        self::SETTING_TYPE_TASK => '任务',
        self::SETTING_TYPE_DEFECT => '缺陷',
        self::SETTING_TYPE_TEST_CASE => '用例',
        self::SETTING_TYPE_TEST_PLAN => '计划'
    ];

    const DEFAULT_TIME = '1971-01-01 00:00:00';


    /**
     * 获取器 - 修改默认创建时间为空
     * @param $value
     * @return string
     * User Long
     * Date 2024/7/20
     */
    protected function getCreateAtAttr($value)
    {

        return ($value === BaseModel::DEFAULT_TIME) ? '' : $value;
    }

    /**
     * 获取器 - 修改默认更新时间为空
     * @param $value
     * @return string
     * User Long
     * Date 2024/7/20
     */
    protected function getUpdateAtAttr($value)
    {

        return ($value === BaseModel::DEFAULT_TIME) ? '' : $value;
    }


    /**
     * 未删除状态
     * @return static
     * User Long
     * Date 2024/7/24
     */
    public static function status()
    {
        return static::where(['is_delete' => self::DELETE_NOT]);
    }

    /**
     * 获取器 - 创建人
     * @param $value
     * @param $data
     * @return string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/2/13
     */
    protected function getCreateByNameAttr($value, $data)
    {
        if (empty($data['create_by'])) return $value;

        return $this->findUserInfo($value ?? '', $data, $data['create_by']);
    }

    /**
     * 获取器 - 更新人
     * @param $value
     * @return string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/2/13
     */
    protected function getUpdateByNameAttr($value, $data)
    {
        if (empty($data['update_by'])) return $value;

        return $this->findUserInfo($value ?? '', $data, $data['update_by']);
    }

    /**
     * 获取器 - 查询人员信息
     * @param string $value
     * @param array $data
     * @return string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/2/13
     */
    private function findUserInfo(string $value, array $data, int $userId)
    {
        // 自动保存日志 不修改人员名称
        if (isset($data['_is_create_and_update_model_trait']) && $data['_is_create_and_update_model_trait'] == 1) {
            return $value;
        }

        $userInfo = ProjectUserModel::where(['user_id' => $userId])->order('id DESC')->find();

        if (!$userInfo) return $value;

        return spliceUserName(
            $userInfo->en_user_name ?? '',
                $userInfo->user_name ?? '',
                $userInfo->position_name ?? ''
        ) ?: $value;
    }

    /**
     * 重置拓展组件数据
     * @param $fieldData
     * @return FieldConfigModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/2/26
     */
    protected function resetExtendsAttr($fieldData = [])
    {
        foreach ($fieldData as &$fieldDatum) {
            if (isset($fieldDatum['field_id']) && $fieldDatum['field_id']) {
                $fieldInfo = FieldConfigModel::status()->find($fieldDatum['field_id']);

                if ($fieldInfo) {
                    if (isset($fieldDatum['remark'])) $fieldDatum['remark'] = $fieldInfo->remark;
                    if (isset($fieldDatum['is_edit'])) $fieldDatum['is_edit'] = $fieldInfo->is_edit;
                    if (isset($fieldDatum['module_id'])) $fieldDatum['module_id'] = $fieldInfo->module_id;
                    if (isset($fieldDatum['field_name'])) $fieldDatum['field_name'] = $fieldInfo->field_name;
                    if (isset($fieldDatum['field_sort'])) $fieldDatum['field_sort'] = $fieldInfo->field_sort;
                    if (isset($fieldDatum['field_type'])) $fieldDatum['field_type'] = $fieldInfo->field_type;
                    if (isset($fieldDatum['category_id'])) $fieldDatum['category_id'] = $fieldInfo->category_id;
                    if (isset($fieldDatum['field_label'])) $fieldDatum['field_label'] = $fieldInfo->field_label;
                    if (isset($fieldDatum['allow_setting'])) $fieldDatum['allow_setting'] = $fieldInfo->allow_setting;
                    if (isset($fieldDatum['component_type'])) $fieldDatum['component_type'] = $fieldInfo->component_type;
                    if (isset($fieldDatum['field_component'])) $fieldDatum['field_component'] = $fieldInfo->field_component;
                    if (isset($fieldDatum['template_default'])) $fieldDatum['template_default'] = $fieldInfo->template_default;
                }
            } elseif (isset($fieldDatum['field']) && $fieldDatum['field']) {
                $fieldInfo = FieldConfigModel::status()->where('field_name', $fieldDatum['field'])->find();

                if ($fieldInfo) {
                    if (isset($fieldDatum['label'])) $fieldDatum['label'] = $fieldInfo->field_label;
                    if (isset($fieldDatum['componentProps']['url'])) $fieldDatum['componentProps']['url'] = $fieldInfo->field_component['url'] ?? '';
                    if (isset($fieldDatum['componentProps']['extra'])) $fieldDatum['componentProps']['extra'] = $fieldInfo->field_component['extra'] ?? '';
                    if (isset($fieldDatum['componentProps']['fields'])) $fieldDatum['componentProps']['fields'] = $fieldInfo->field_component['fields'] ?? '';
                    if (isset($fieldDatum['componentProps']['method'])) $fieldDatum['componentProps']['method'] = $fieldInfo->field_component['method'] ?? '';
                    if (isset($fieldDatum['componentProps']['options'])) $fieldDatum['componentProps']['options'] = $fieldInfo->field_component['options'] ?? '';
                    if (isset($fieldDatum['componentProps']['multiple'])) $fieldDatum['componentProps']['multiple'] = $fieldInfo->field_component['multiple'] ?? '';
                    if (isset($fieldDatum['componentProps']['valueKey'])) $fieldDatum['componentProps']['valueKey'] = $fieldInfo->field_component['valueKey'] ?? '';
                    if (isset($fieldDatum['componentProps']['componentType'])) $fieldDatum['componentProps']['componentType'] = $fieldInfo->field_component['componentType'] ?? '';
                }
            }
        }
        unset($fieldDatum);

        return $fieldData;
    }
}
