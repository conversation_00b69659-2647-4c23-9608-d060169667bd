<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/12/12 下午4:59
 */

namespace excel_utils;

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\File;

class Hanlder
{


    private static function getExcelColumn($colIndex)
    {
        $column = '';
        while ($colIndex >= 0) {
            $column = chr($colIndex % 26 + 65).$column;
            $colIndex = intval($colIndex / 26) - 1;
        }
        return $column;
    }

    public static function outputFile($data, $fileName)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $styleArray = [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                    'color'       => ['argb' => 'FFFF0000'],
                ],
            ],
        ];

        // 遍历二维数组填充数据
        foreach ($data as $rowIndex => $row) {
            foreach ($row as $colIndex => $value) {
                // 行号从1开始，列号从A开始
                $cellCoordinate = self::getExcelColumn($colIndex).($rowIndex + 1);

                if (is_array($value) && isset($value['text']) && isset($value['url'])) {
                    // This is a hyperlink cell
                    $sheet->setCellValue($cellCoordinate, $value['text']);
                    $sheet->getCell($cellCoordinate)->getHyperlink()->setUrl(env('FRONT_END_ADDRESS.url').$value['url']);
                    $sheet->getStyle($cellCoordinate)
                          ->getAlignment()
                          ->setHorizontal(Alignment::HORIZONTAL_LEFT);
                    // Style the hyperlink
                    $sheet->getStyle($cellCoordinate)->getFont()->setColor(new \PhpOffice\PhpSpreadsheet\Style\Color(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLUE));
                    $sheet->getStyle($cellCoordinate)->getFont()->setUnderline(true);
                } elseif (is_array($value) && ($value['isError'] ?? false)) {
                    $sheet->setCellValue($cellCoordinate, $value['value'])
                        ->getStyle($cellCoordinate)
                        ->getAlignment()
                        ->setHorizontal(Alignment::HORIZONTAL_LEFT)
                        ->applyFromArray($styleArray);
                } else {
                    $sheet->setCellValue($cellCoordinate, $value)
                        ->getStyle($cellCoordinate)
                        ->getAlignment()
                        ->setHorizontal(Alignment::HORIZONTAL_LEFT);

                }
//                    ->getStyle($cell)->getAlignment()->setWrapText(true);
            }
        }

        $writer = new Xlsx($spreadsheet);

        // 创建一个临时内存流 (php://temp)
        $tempStream = fopen('php://temp', 'r+');

        // 将 Excel 文件内容写入流
        $writer->save($tempStream);

        // 重置流指针，准备读取内容
        rewind($tempStream);

        // 将流内容读取为字符串
        $fileContent = stream_get_contents($tempStream);

        // 关闭流
        fclose($tempStream);

        return download($fileContent, rawurlencode("{$fileName}.xlsx"), true);
    }

    public static function readFile(File $file)
    {
        $spreadsheet = IOFactory::load($file->getRealPath());

        // 获取第一个工作表
        $sheet = $spreadsheet->getActiveSheet();

        //格式化时间格式为Y-m-d H:i:s
        foreach ($sheet->getRowIterator() as $row) {
            foreach ($row->getCellIterator() as $cell) {
                // 检查单元格是否为日期类型
                if (Date::isDateTime($cell)) {
                    // 格式化日期
                    $cell->setValue(gmdate("Y-m-d H:i:s", Date::excelToTimestamp($cell->getValue())));
                }
            }
        }

        // 读取数据
        $allData = $sheet->toArray();

        // --- Start: Remove empty columns ---
        if (!empty($allData) && !empty($allData[0])) {
            $numRows = count($allData);
            // Get number of columns from the first row
            $numCols = count($allData[0]);
            $emptyColumnIndices = [];

            for ($colIndex = 0; $colIndex < $numCols; $colIndex++) {
                $isColumnEmpty = true;
                for ($rowIndex = 0; $rowIndex < $numRows; $rowIndex++) {
                    // Check if the cell in the current column is not empty
                    if (isset($allData[$rowIndex][$colIndex]) && $allData[$rowIndex][$colIndex] !== null && $allData[$rowIndex][$colIndex] !== '') {
                        $isColumnEmpty = false;
                        break; // Column is not empty, move to the next one
                    }
                }
                if ($isColumnEmpty) {
                    $emptyColumnIndices[] = $colIndex;
                }
            }

            // If any empty columns were found, rebuild the data array without them
            if (!empty($emptyColumnIndices)) {
                $newData = [];
                foreach ($allData as $row) {
                    $newRow = [];
                    foreach ($row as $colIndex => $cellValue) {
                        if (!in_array($colIndex, $emptyColumnIndices)) {
                            $newRow[] = $cellValue;
                        }
                    }
                    $newData[] = $newRow;
                }
                $allData = $newData; // Replace old data with the new data
            }
        }
        // --- End: Remove empty columns ---

        $result = [];
        foreach ($allData as $row) {
            $isRowEmpty = true;
            // An empty row might be an array of nulls, or just an empty array after column removal
            if (empty($row)) {
                $isRowEmpty = true;
            } else {
                foreach ($row as $cell) {
                    if ($cell !== null && $cell !== '') {
                        $isRowEmpty = false;
                        break;
                    }
                }
            }
            if (!$isRowEmpty) {
                $result[] = $row;
            }
        }

        return $result;
    }
}
