<?php
/**
 * 权限 - 中间件
 * User Long
 * Date 2024/12/12
 */

declare(strict_types=1);

namespace middleware;

use app\infrastructure\logic\PermissionsLogic;
use exception\BusinessException;

class PermissionMiddleware
{
    /**
     * 路由鉴权
     * @param $request
     * @param \Closure $next
     * @return mixed
     * User Long
     * Date 2024/12/12
     */
    public function handle($request, \Closure $next)
    {
        // 权限判断
        $menu = self::validationApiPermission();

        // 判断权限
        if (!$menu && env('ENVIRONMENT') != 'local') {
            throw new BusinessException('查无权限，请联系客服人员'); // 无权限操作,请联系管理员
        }

        return $next($request);
    }

    /**
     * 验证接口访问权限
     * @return bool
     * User Long
     * Date 2024/12/12
     */
    public static function validationApiPermission()
    {
        // 获取人员权限
        $permissions = PermissionsLogic::getRulePathPermissions();

        // 管理员直接通过
        if ($permissions['is_admin']) {
            return true;
        }

        // 验证访问地址
        $root = app('request')->root();
        $path = app('request')->pathinfo();
        $info = $root . '/' . $path;
        $res = in_array($info, array_column($permissions['permissions'], 'route_path'));

        // 鉴权使用，不需要授权
        if ($info == '/permissions') {
            $res = true;
        }

        return $res;
    }
}
