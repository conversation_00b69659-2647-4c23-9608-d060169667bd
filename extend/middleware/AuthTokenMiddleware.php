<?php

namespace middleware;


use app\Request;
use app\user\logic\UserLogic;
use exception\AuthorizationException;
use utils\Ctx;


class AuthTokenMiddleware
{
    public function handle(Request $request, \Closure $next)
    {
        $token = $request->header('authorization');
        if (!$token) {
            throw new AuthorizationException();
        }

        $userLogic = new UserLogic();
        Ctx::$token = $token;
        Ctx::$user = $userLogic->getUserByToken($token);
        Ctx::$userId = Ctx::$user->userId;


        return $next($request);
    }
}
