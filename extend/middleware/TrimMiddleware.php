<?php

namespace middleware;

use app\auth\logic\PlatformToken;
use app\Request;

class TrimMiddleware
{
    public function handle(Request $request, \Closure $next)
    {
        $request->withPost(array_map(function ($value) {
            if (is_string($value)) {
                return trim($value);
            }
            return $value;
        }, $request->post()));

        return $next($request);
    }
}
