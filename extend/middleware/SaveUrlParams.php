<?php
declare (strict_types = 1);

namespace middleware;

use app\infrastructure\logic\UrlParamsStorageLogic;
use Closure;
use think\Request;
use think\Response;

class SaveUrlParams
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $url_params_id = null;

        // 获取请求URL并解析最后一个路径段
        $url = $request->url();
        $segments = explode('/', trim($url, '/'));
        $lastSegment = end($segments);
        $action = $lastSegment;

        // 判断是否以pageQuery开头
        if (str_starts_with($action, 'pageQuery')) {
            // 保存当前请求参数
            $result = UrlParamsStorageLogic::saveCurrentParams();
            $url_params_id = $result['url_params_id'];
        }

        // 获取原始响应
        $response = $next($request);

        // 如果成功保存了参数，将url_params_id添加到响应中
        if ($url_params_id !== null) {
            // 获取原始数据
            $data = $response->getData();

            // 如果原始数据是数组，添加url_params_id
            if (is_array($data)) {
                $data['url_params_id'] = $url_params_id;
                $response->data($data);
            }
        }

        return $response;
    }
}
