<?php
/**
 * Desc 指定权限角色存在 - 中间件
 * User Long
 * Date 2024/12/13
 */

namespace middleware;

use app\infrastructure\logic\PermissionsLogic;
use exception\BusinessException;

class AppointPermissionRoleMiddleware
{
    // 权限中间件
    private const PERMISSION = 'middleware\PermissionMiddleware';

    public function handle($request, \Closure $next, $role)
    {
        // 检查权限中间件是否已加载
        if (!$this->isPermissionMiddlewareLoaded($request)) {
            throw new BusinessException('请先加载权限中间件');
        }

        // 判断权限
        if (!$this->isPermission($request, $role) && env('ENVIRONMENT') != 'local') {
            throw new BusinessException('查无权限，请联系客服人员'); // 无权限操作,请联系管理员
        }

        return $next($request);
    }

    /**
     * 检查权限中间件是否已加载
     * @param $request
     * @return bool
     * User Long
     * Date 2024/12/13
     */
    private function isPermissionMiddlewareLoaded($request)
    {
        // 获取路由对象
        $rule = app('route')->getRule($request->pathinfo());

        // 获取中间件
        $middlewares = $rule[array_key_first($rule)]->getOption()['middleware'];

        // 获取中间件别名
        $data = [];
        foreach ($middlewares as $middle) {
            if (isset($middle[0]) && $middle[0]) {
                $data[] = $middle[0];
            }
        }

        return in_array(self::PERMISSION, $data);
    }

    /**
     * 角色判断权限
     * @param $request
     * @param $role
     * @return bool
     * User Long
     * Date 2024/12/13
     */
    private function isPermission($request, $role)
    {
        // 获取人员权限
        $permissions = PermissionsLogic::getRulePathPermissions(PermissionsLogic::MIDDLEWARE);

        // 管理员直接通过
        if ($permissions['is_admin']) {
            return true;
        }

        // 验证访问地址
        $path = $request->root() . '/' . $request->pathinfo();

        if (!isset($permissions['permissions'][$path])) {
            return false;
        }

        // 验证角色是否有权限
        return in_array($role, $permissions['permissions'][$path]['role']);
    }
}
