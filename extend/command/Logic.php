<?php

namespace command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

class Logic extends Command
{
    private $tableName;
    private $logicName;
    private $nameSpace;

    protected function configure()
    {
        // 指令配置
        $this->setName('logic')
            ->addArgument('name')
            ->addArgument('space')
            ->setDescription('Build Logic 模块，php think logic 模型名称 生成的目录(app\test\logic)');

    }

    protected function execute(Input $input, Output $output)
    {
        $this->logicName = ucfirst($input->getArgument('name'));
        $this->nameSpace = $input->getArgument('space');

        if (!$this->logicName || !$this->nameSpace) {
            echo '参数依次为 [逻辑层名称] [命名空间]';
            exit();
        }

        $path = str_replace('\\', '/', $this->nameSpace);
        if (!is_dir($path)) mkdir($path, 0755, true);

        $fullModelFile = $path . '/' . $this->logicName . 'Logic.php';
        if (file_exists($fullModelFile)) {
            echo $fullModelFile . '已经存在';
            exit();
        }

        $tpl['logicName'] = $this->logicName;
        $tpl['nameSpace'] = $this->nameSpace;
        $this->tableName = strtolower(preg_replace('/(?<=[a-z])([A-Z])/', '_$1', $this->logicName));
        $tpl['tableName'] = $this->tableName;
        $tpl['model_path'] = 'app\index\model';
        $exp_path = explode('logic', $path);
        if (isset($exp_path[1])) {
            $tpl['model_path'] = $exp_path[0].'model';
        }

        $columns = Db::query("SHOW FULL COLUMNS FROM t_$this->tableName");
        $tpl['fields'] = '';
        $tpl['remark'] = '';
        foreach ($columns as $column) {
            if ($tpl['fields']) $tpl['fields'] .= ',';
            $tpl['fields'] .= $column['Field'];
            if ($tpl['remark']) $tpl['remark'] .= '            ';
            $tpl['remark'] .= 'if($check->' . $column['Field'] . '!=$data["' . $column['Field'] . '"]) $remark="' . $column['Comment'] . '由：".$check->' . $column['Field'] . '."改为：".$data["' . $column['Field'] . '"]."！";' . "\n";
        }
        $tplpath = app()->getRootPath() . 'extend' . DIRECTORY_SEPARATOR . 'tpl' . DIRECTORY_SEPARATOR . 'LogicTpl.stub';

        ob_start();
        require $tplpath;
        $out = ob_get_clean();
        file_put_contents($fullModelFile, $out);
        echo $fullModelFile . ' success';
    }
}
