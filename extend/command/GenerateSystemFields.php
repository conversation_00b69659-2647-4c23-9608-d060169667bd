<?php
declare (strict_types=1);

namespace command;

use app\project\scheduled_tasks\BugStatistics;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;
use think\facade\Env;
use utils\HolidayTest;


class GenerateSystemFields extends Command
{
    protected function configure()
    {
        $this->setName('hello')
            ->addArgument('name', Argument::OPTIONAL, "your name")
            ->addOption('city', null, Option::VALUE_REQUIRED, 'city name')
            ->setDescription('Say Hello');
    }

    protected function execute(Input $input, Output $output)
    {

        BugStatistics::statistics();

//        HolidayTest::run();

    }
}
