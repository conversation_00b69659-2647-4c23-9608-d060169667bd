<?php
/**
 * Desc 示例 - 模型
 * User
 * Date {%date%}
 */

declare (strict_types = 1);

namespace app\{%app%}\model;

use basic\BaseModel;
use think\model\concern\SoftDelete;

class DemoModel extends BaseModel
{
    // 设置主键
    protected $pk = 'id';

    // 设置表名
    protected $table = 'test';

    // // 软删除
    // use SoftDelete;
    // protected $deleteTime = 'is_delete';
    // protected $defaultSoftDelete = 0;
}