<?php
/**
 * @var $tpl
 */
echo "<?php\n";

?>
/**
* Desc 示例 - 模型
* User
* Date <?= date('Y/m/d') ?>

* */

declare (strict_types = 1);

namespace <?= $tpl['nameSpace'] ?>;

use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "<?= $tpl['tableName'] ?>".
<?php foreach ($tpl['columns'] as $column): ?>
* @property string $<?php echo $column['Field'] ?> <?php echo $column['Comment'] . PHP_EOL ?>
<?php endforeach;?>
*/
class <?php echo $tpl['modelName'] ?> extends BaseModel
{
    use CreateAndUpdateModelTrait;

<?php foreach ($tpl['columns'] as $column): ?>
    <?php if ($column['Key'] == 'PRI' && $column['Extra'] == 'auto_increment') {
        echo 'protected $pk = ' . "'" . $column['Field'] . "';".PHP_EOL;
        break;
    } ?>
<?php endforeach; ?>
    protected $name = '<?= $tpl['tableName'] ?>';



}
