<?php
/**
 * Desc 示例 - 控制器
 * User
 * Date {%date%}
 */

declare (strict_types = 1);


namespace app\{%app%}\controller;

use app\{%app%}\logic\DemoLogic;
use resp\StatusCode;
use resp\Result;

class demo
{
    // 新增
    public function create()
    {
        return Result::success();
    }

    // 删除
    public function delete()
    {
        return Result::success();
    }

    // 修改（全）
    public function update()
    {
        return Result::success();
    }

    // 修改（局）
    public function edit()
    {
        return Result::success();
    }

    // 详情
    public function detail()
    {
        return Result::success();
    }

    // 分页查询
    public function pageQuery()
    {
        return Result::success();
    }

    // 列表(无分页)
    public function listQuery()
    {
        return Result::success();
    }

    // 下拉列表(下拉框)
    public function selector()
    {
        return Result::success();
    }

    // 错误返回
    public function fail()
    {
        return Result::error(StatusCode::PARAMS_ERROR_CODE, StatusCode::PARAMS_MSG);
    }
}
