<?php
/**
 * @var $tpl
 */
echo "<?php\n";

?>
declare (strict_types = 1);

namespace <?= $tpl['nameSpace'] ?>;

use <?php echo $tpl['model_path'] ?>\<?php echo $tpl['logicName'] ?>Model;
use think\facade\Db;
use think\Exception;
use basic\BaseLogic;

class <?php echo $tpl['logicName'] ?>Logic extends BaseLogic
{
    //默认查询字段
    static protected $field='<?php echo $tpl['fields'] ?>';

    //查询一条记录
    static public function find($where,$field='',$order='id desc'){
        if(!$field) $field=self::$field;
        $model=<?php echo $tpl['logicName'] ?>Model::field($field);
        if($where) $model->where($where);
        return $model->order($order)->find();
    }

    //查询一条记录，并加锁
    static public function findAndLock($where,$field='',$order='id desc'){
        if(!$field) $field=self::$field;
        $model=<?php echo $tpl['logicName'] ?>Model::field($field);
        if($where) $model->where($where);
        return $model->lock(true)->order($order)->find();
    }

    //得到某个字段的值
    static public function value($where,$field){
        return <?php echo $tpl['logicName'] ?>Model::where($where)->value($field)?:'';
    }

    //查询多条记录
    static public function select($where,$field='',$order='id desc'){
        if(!$field) $field=self::$field;
        return <?php echo $tpl['logicName'] ?>Model::field($field)->where($where)->order($order)->select();
    }

    //查询数量
    static public function count($where){
        return <?php echo $tpl['logicName'] ?>Model::where($where)->count('id');
    }

    //分页查询
    public function paginate($where,$field='',$order='id desc'){
        if(!$field) $field=self::$field;
        return <?php echo $tpl['logicName'] ?>Model::field($field)->where($where)->order($order)->paginate(['list_rows'=>$this->listRow,'query'=>request()->param()]);
    }

    //插入一条记录
    static public function insert($data){
        return <?php echo $tpl['logicName'] ?>Model::create($data);
    }

    //更新一条记录
    static public function update($where,$update){
        <?php echo $tpl['logicName'] ?>Model::update($update,$where);
    }

    //删除一条记录
    static public function delete($where){
        <?php echo $tpl['logicName'] ?>Model::destroy($where);
    }

    //提交记录信息
    public function execute($data){
        $remark='';
        $id=$data['id']?:0;
        unset($data['id']);

        //判断重复
        $where=[];
        $where[]=['id','<>',$id];
        $where[]=['status','=',1];
        $where[]=['name','=',$data['name']];
        $hasData=self::find($where);
        if($hasData) throw new Exception('名称：'.$data['name'].'已存在！');

        if ($id > 0) {
            $where=[];
            $where[]=['id','=',$id];
            $check = self::find($where);

            if(!$check) throw new Exception('未查询到记录！');
            <?php echo $tpl['remark'] ?>
            if(!$remark) throw new Exception('没有任何修改！');
            $remark=$this->userName.'修改：'.$check->name.'！'.$remark;
        } else {
            $check = new <?php echo $tpl['logicName'] ?>Model();
            $remark=$this->userName.'新增：'.$data['name'].'!';
        }

        Db::startTrans();
        try{
            $check->save($data);

            //操作日志
            $this->insertAction('<?php echo $tpl['tableName'] ?>',$check->id,$remark);

            Db::commit();
        }catch (\Throwable $e){
            Db::rollback();
            throw $e;
        }
    }

    //删除一条记录
    public function del($id){
        $check = self::find([['id','=',$id]]);
        if(!$check) throw new Exception('该记录不存在！');
        if($check->status==0) throw new Exception('该记录已删除，请刷新！');

        Db::startTrans();
        try{
            $check->status=0;
            $check->save();

            //操作日志
            $this->insertAction('<?php echo $tpl['tableName'] ?>',$check->id,$this->userName.'删除：'.$check->name.'！');
            Db::commit();
        }catch (\Throwable $e){
            Db::rollback();
            throw $e;
        }
    }
}
