<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/7/4 上午11:16
 */

namespace traits;

use app\infrastructure\model\FieldConfigModel;
use app\infrastructure\model\OperationLogModel;
use think\Collection;
use think\Model;

/**
 * 自动保存日志
 */
trait OperationLogTrait
{
    public static function onAfterInsert($model)
    {
        static::logHandle($model);
    }

    public static function onAfterUpdate(Model $model)
    {
        static::logHandle($model, 'update');
    }

    private static function logHandle($model, $operation = '')
    {
        if (!method_exists($model, 'getLogFieldList')) {
            return;
        }


        $oldData = [];
        $needRecordFields = $model->getLogFieldList();
        if ($operation == 'update') {
            $change = $model->getChangedData();
            $oldData = $model->getOrigin();
        } else {
            $change = $model->getData();
        }

        $log = [];
        foreach ($change as $field => $value) {
            if (!array_key_exists($field, $needRecordFields)) {
                continue;
            }

            if (is_string($needRecordFields[$field])) {
                $log[] = [
                    'field_name' => $field,
                    'field_label' => $needRecordFields[$field],
                    'before' => $oldData[$field] ?? '',
                    'after' => $value
                ];
                continue;
            }

            if (is_array($needRecordFields[$field])) {
                if ($needRecordFields[$field]['type'] == 'enum') {
                    $log[] = [
                        'field_name' => $field,
                        'field_label' => $needRecordFields[$field]['field_label'],
                        'before' => $needRecordFields[$field]['values'][$oldData[$field]] ?? '',
                        'after' => $needRecordFields[$field]['values'][$value] ?? '',
                    ];
                    continue;
                }

                if ($needRecordFields[$field]['type'] == 'json') {
                    $filedData = json_decode($value, true);
                    $oldFiledData = json_decode($oldData[$field] ?? '[]', true);
                    $oldFiledData = array_column($oldFiledData, null, 'field_name');
                    foreach ($filedData as $field) {
                        $nestField = $field['field_name'];
                        $nestValue = $field['default_value'] ?? '';
                        $oldValue = $oldFiledData[$nestField]['default_value'] ?? '';
                        if (@($nestValue == $oldValue)) {
                            continue;
                        }
                        $result = OperationLogJsonHandler::handle($nestField, $oldValue, $nestValue);
                        if ($result) {
                            $log[] = $result;
                        }
                    }
                }

            }

        }
        //过滤重复fieldName数据
        $log = self::filterUniqueByKey($log, 'field_name');
        static::saveLog($model, $log);
    }

    // 过滤二维数组中指定字段（如 'name'）重复的项目
    private static function filterUniqueByKey($array, $key)
    {
        $temp_array = [];
        $key_array = [];

        foreach ($array as $item) {
            if (!in_array($item[$key], $key_array)) {
                $key_array[] = $item[$key];
                $temp_array[] = $item;
            }
        }

        return $temp_array;
    }

    private static function saveLog($model, $details)
    {
        if (!$details) {
            return;
        }
        $pk = $model->getPk();
        OperationLogModel::create([
            'operation_table' => $model->getTable(),
            'table_id' => $model->$pk,
            'log_details' => json_encode($details, JSON_UNESCAPED_UNICODE),
        ]);
    }

}

