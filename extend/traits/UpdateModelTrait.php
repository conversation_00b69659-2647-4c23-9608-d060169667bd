<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/7/4 上午11:16
 */

namespace traits;

use think\Model;
use utils\Ctx;

/**
 * 自动保存日志
 */
trait UpdateModelTrait
{
    public static function onBeforeUpdate(Model $model): mixed
    {
        $model->update_by = Ctx::$user->userId;
        $model->update_by_name = Ctx::$user->name;
        $model->update_at = date('Y-m-d H:i:s');
        $model->_is_create_and_update_model_trait = 1; // 标记该方法为自动保存方法，不使用修改器修改名称

        return $model;
    }
}
