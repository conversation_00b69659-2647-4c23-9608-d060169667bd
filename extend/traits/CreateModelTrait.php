<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/7/4 上午11:16
 */

namespace traits;

use think\Model;
use utils\Ctx;

/**
 * 自动保存日志
 */
trait CreateModelTrait
{
    public static function onBeforeInsert(Model $model): mixed
    {
        $model->create_by = $model->create_by ?: Ctx::$user->userId;
        $model->create_by_name = $model->create_by_name ?: Ctx::$user->name;
        $model->create_at = $model->create_at ?: date('Y-m-d H:i:s');
        $model->_is_create_and_update_model_trait = 1; // 标记该方法为自动保存方法，不使用修改器修改名称

        return $model;
    }

    protected function getCreateByNameAttr($value, $data)
    {
        return $value;
    }

    protected  function getUpdateByNameAttr($value, $data)
    {
        return $value;
    }
}
