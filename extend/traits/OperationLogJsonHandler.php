<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/7/23 上午10:43
 */

namespace traits;

use app\infrastructure\model\FieldConfigModel;
use think\Collection;

class OperationLogJsonHandler
{
    public static function handle($name, $before, $after)
    {
        $field = FieldConfigModel::getLabelByFieldName($name);
        $componentType = $field['field_component']['componentType'] ?? '';
        if (!$componentType){
            return null;
        }
        if ($field['field_name'] == 'client_env') {
            return null;
        }
        switch ($componentType) {
            case 'Select':
                return self::select($field, $before, $after);
            case 'Casader':
                return self::casader($field, $before, $after);
            default:
                return self::input($field, $before, $after);
        }

    }

    /**
     * input类型
     * @param $field
     * @param $before
     * @param $after
     * @return array
     * <AUTHOR>
     * @date 2024/7/23 下午2:47
     */
    private static function input($field, $before, $after)
    {
        return [
            'field_name' => $field['field_name'],
            'field_label' => $field['field_label'],
            'before' => $before,
            'after' => $after,
        ];
    }

    /**
     * 下拉类型
     * @param $field
     * @param $before
     * @param $after
     * @return array
     * <AUTHOR>
     * @date 2024/7/23 下午2:47
     */
    private static function select($field, $before, $after)
    {
        $options = new Collection($field['field_component']['options']);
        //单选多选
        if ($field['field_component']['multiple']) {
            return [
                'field_name' => $field['field_name'],
                'field_label' => $field['field_label'],
                'before' => implode(',', $options->where('value', 'in', $before ?: [])->column('label')),
                'after' => implode(',', $options->where('value', 'in', $after ?: [])->column('label')),
            ];
        } else {
            return [
                'field_name' => $field['field_name'],
                'field_label' => $field['field_label'],
                'before' => $options->where('value', '=', $before)->first()['label'] ?? '',
                'after' => $options->where('value', '=', $after)->first()['label'] ?? '',
            ];
        }
    }

    /**
     * 级联类型
     * @param $field
     * @param $before
     * @param $after
     * @return array
     * <AUTHOR>
     * @date 2024/7/23 下午2:47
     */
    private static function casader($field, $before, $after)
    {
        $options = $field['field_component']['options'];
        //单选多选
        if ($field['field_component']['props']['multiple']) {
            $beforeItemList = [];
            $afterItemList = [];
            foreach (($before ?: []) as $v) {
                $beforeList = [];
                self::casaderRecursion($options, $v, $beforeList);
                $beforeItemList[] = implode('-', $beforeList);
            }

            foreach (($after ?: []) as $v) {
                $afterList = [];
                self::casaderRecursion($options, $v, $afterList);
                $afterItemList[] = implode('-', $afterList);
            }

            return [
                'field_name' => $field['field_name'],
                'field_label' => $field['field_label'],
                'before' => implode(',', $beforeItemList),
                'after' => implode(',', $afterItemList),
            ];
        } else {
            $beforeList = [];
            $afterList = [];
            self::casaderRecursion($options, $before, $beforeList);
            self::casaderRecursion($options, $after, $afterList);
            return [
                'field_name' => $field['field_name'],
                'field_label' => $field['field_label'],
                'before' => implode('-', $beforeList),
                'after' => implode('-', $afterList),
            ];
        }
    }

    /**
     * 级联类型递归值解析
     * @param $data
     * @param $value
     * @param $result
     * @return void
     * <AUTHOR>
     * @date 2024/7/23 下午2:47
     */
    private static function casaderRecursion($data, $value, &$result)
    {
        if (!$value) {
            return;
        }
        $options = new Collection($data);
        $data = $options->where('value', '=', array_shift($value))->first();
        $result[] = $data['label'];
        if ($value) {
            self::casaderRecursion($data['children'], $value, $result);
        }
    }
}
