<?php
/**
 *
 * User: 杨荣钦
 * Date-Time: 2024/1/15 15:06
 */

namespace traits;

use exception\DataUpdatedException;

trait OptimLockTrait
{
    private $locked;

    public function getWhere()
    {
        $where = parent::getWhere();
        $optimLock = $this->getOptimLockField();

        if ($optimLock && $lockVer = $this->locked) {
            $where[] = [$optimLock, '=', $lockVer];
        }
        return $where;
    }

    /**
     * 数据检查
     * @access protected
     * @return void
     */
    protected function checkData(): void
    {
        $this->isExists() ? $this->updateLockVersion() : $this->recordLockVersion();
    }

    /**
     * 更新乐观锁
     * @access protected
     * @return void
     */
    protected function updateLockVersion(): void
    {
        $optimLock = $this->getOptimLockField();

        $this->locked = ($this->getChangedData())[$optimLock] ?? 0;
        if ($optimLock && $lockVer = $this->getOrigin($optimLock)) {
            // 更新乐观锁
            $this->set($optimLock, $lockVer + 1);
        }
    }

    protected function getOptimLockField()
    {
        return property_exists($this, 'optimLock') && isset($this->optimLock) ? $this->optimLock : 'version';
    }

    /**
     * 记录乐观锁
     * @access protected
     * @return void
     */
    protected function recordLockVersion(): void
    {
        $optimLock = $this->getOptimLockField();
        if ($optimLock) {
            $this->set($optimLock, 1);
        }
    }

    protected function checkResult($result): void
    {
        if (!$result) {
            throw new DataUpdatedException();
        }
    }
}