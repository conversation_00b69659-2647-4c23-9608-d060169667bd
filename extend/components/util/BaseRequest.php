<?php

namespace components\util;

class BaseRequest
{
    protected $token;

    public function __construct($token)
    {
        $this->token = $token;
    }

    /**
     * 处理中台返回分页数据
     * @param array $center_data 中台数据
     * @param array $page 分页数据
     * @return array
     * User Long
     * Date 2024/8/12
     */
    public static function setCenterPageData(array $page, array $center_data = [])
    {
        $res['total'] = 0;
        $res['per_page'] = $page['list_rows'] ?? 20;
        $res['current_page'] = $page['page'] ?? 1;
        $res['last_page'] = $page['page'] ?? 1;
        $res['data'] = [];

        if (!$center_data) return $res;

        if (isset($center_data['data']['pagination'])) {
            $res['total'] = (int)$center_data['data']['pagination']['total'];
            $res['per_page'] = (int)$center_data['data']['pagination']['pageSize'];
            $res['current_page'] = (int)$center_data['data']['pagination']['pageIndex'];
            $res['last_page'] = (int)ceil($center_data['data']['pagination']['total'] / (int)$center_data['data']['pagination']['pageSize']);
        }

        return $res;
    }

}
