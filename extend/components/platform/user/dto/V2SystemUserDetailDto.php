<?php
/**
 * Desc 中台用户详情接口
 * User Long
 * Date 2024/8/12
 */

namespace components\platform\user\dto;

use components\util\BaseDto;

class V2SystemUserDetailDto extends BaseDto
{
    /** @var int 用户id */
    public int $user_id;

    /** @var string 用户名 */
    public string $user_name;

    /** @var string 用户英文名 */
    public string $en_user_name;

    /** @var string 工号 */
    public string $account;

    /** @var string 用户角色code */
    public string $role_code;

    /** @var string 用户角色名 */
    public string $role_name;

    /** @var int 主岗位id */
    public int $position_id;

    /** @var string 主岗位名 */
    public string $position_name;

    /** @var int 公司id */
    public int $company_id;

    /** @var string 公司名 */
    public string $company_name;

    /** @var int 部门id */
    public int $dept_id;

    /** @var string 部门名 */
    public string $dept_name;

    /** @var bool 是否在职 */
    public bool $is_enable;

    /** @var string 是否在职 */
    public string $enable_name;

    /** @var string 用户飞书头像 */
    public string $avatar;

    /** @var string 手机号码 */
    public string $phone;

    /**
     * 动态追加新字段的方法
     * @param $fieldName
     * @param $value
     * User Long
     * Date 2024/9/3
     */
    public function addField($fieldName, $value) {
        $this->$fieldName = $value;
    }
}
