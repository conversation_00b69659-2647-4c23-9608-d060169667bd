<?php
/**
 * Desc 中台用户角色下拉接口
 * User Long
 * Date 2024/12/17
 */

namespace components\platform\user\dto;

use components\util\BaseDto;

class V2SubSystemRoleUserDropDownPageListDto extends BaseDto
{
    /** @var int 用户id */
    public int $user_id;

    /** @var string 用户名 */
    public string $user_name;

    /** @var string 用户英文名 */
    public string $en_user_name;

    /** @var string 工号 */
    public string $account;

    /** @var array 用户角色code */
    public array $role_code;

    /**
     * 动态追加新字段的方法
     * @param $fieldName
     * @param $value
     * User Long
     * Date 2024/9/3
     */
    public function addField($fieldName, $value) {
        $this->$fieldName = $value;
    }
}
