<?php

namespace components\platform\user\dto;

use app\commonBase\logic\OrganizationLogic;
use app\kingDee\logic\EmpinfoLogic;
use exception\PlatformException;
use components\util\BaseDto;

class UserDetailDto extends BaseDto
{
    /** @var UserShortInfoDto 用户基本信息 */
    public $user;
    /** @var FsDto 飞书信息 */
    public $fsInfo;
    /** @var EmpInfoDto  员工信息 */
    public $empInfo;
    /** @var ?PostionDto 主岗位 */
    public $mainPosition;
    /** @var PostionDto|PostionDto[] 岗位 */
    public $positions;
    /** @var \kingDee\dto\EmpinfoDto 金蝶员信息 */
    public $kdEmpinfo;

    /**
     * 获取主岗位信息
     * @return PostionDto
     */
    public function getMasterPosition(): ?PostionDto
    {
        foreach ($this->positions as $v) {
            if ($v->isMaster()) {
                return $v;
            }
        }

        throw new PlatformException('没有获取到主岗位');
    }

    /**
     * 获取公司id
     * @return int
     * @author: 杨荣钦
     * @date: 2023/10/9 14:10
     */
    public function getCompanyId(): int
    {
        $masterPosition = $this->getMasterPosition();

        return $masterPosition->company->companyId ?? ($masterPosition->company['companyId'] ?? 0);
    }

    /**
     * 获取合同公司
     * @return int
     * <AUTHOR>
     * @date 2024/3/25 15:31
     */
    public function getContractCompanyld(): int
    {
        $masterPosition = $this->getMasterPosition();

        return $masterPosition->company->contractCompanyId ?? ($masterPosition->company['contractCompany1Id'] ?? 0);
    }

    /**
     * 获取公司code
     * @return string
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * <AUTHOR>
     * @date 2024/3/30 17:15
     */
    public function getCompanyCode(): string
    {
        $companyId = $this->getCompanyId();
        $userId = $this->user->userId;

        return OrganizationLogic::getOrgWithNearestCmpInfoByOrgIds($userId, $companyId);
    }


    /**
     * 获取金蝶员工信息
     * @return \kingDee\dto\EmpinfoDto
     * <AUTHOR>
     * @date 2024/3/27 19:07
     */
    public function getKdEmpinfo(): \kingDee\dto\EmpinfoDto
    {
        if (!empty($this->kdEmpinfo)) {
            return $this->kdEmpinfo;
        }
        return $this->setKdEmpinfo()->kdEmpinfo;
    }

    /**
     * 设置金蝶员工信息
     * @return \components\platform\user\dto\UserDetailDto
     * <AUTHOR>
     * @date 2024/3/29 9:05
     */
    public function setKdEmpinfo(): UserDetailDto
    {
        $kdEmpinfo = (new EmpinfoLogic())->view(['Number' => $this->user->employeeCode]);
        $this->kdEmpinfo = (new \kingDee\dto\EmpinfoDto())->load($kdEmpinfo);

        return $this;
    }

}
