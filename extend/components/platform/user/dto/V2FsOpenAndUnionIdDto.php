<?php
/**
 * Desc 中台子系统用户权限
 * User Long
 * Date 2024/8/12
 */

namespace components\platform\user\dto;

use components\util\BaseDto;

class V2FsOpenAndUnionIdDto extends BaseDto
{
    /** @var int 用户id */
    public int $user_id;

    /** @var string 飞书id */
    public string $fs_id;

    /** @var string 手机号 */
    public string $phone;

    /** @var string 国家码 */
    public string $country_code;

    /** @var string 飞书unionid */
    public string $union_id;

    /** @var string 飞书appOpenid */
    public string $fs_login_open_id;


    /**
     * 动态追加新字段的方法
     * @param $fieldName
     * @param $value
     * User Long
     * Date 2024/9/3
     */
    public function addField($fieldName, $value) {
        $this->$fieldName = $value;
    }
}
