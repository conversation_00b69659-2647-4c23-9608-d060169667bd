<?php
/**
 * Desc 中台子系统用户权限
 * User Long
 * Date 2024/8/12
 */

namespace components\platform\user\dto;

use components\util\BaseDto;

class V2SystemUserRoleDto extends BaseDto
{
    /** @var int 权限项Id */
    public int $id;

    /** @var string 权限项名称 */
    public string $name;

    /** @var array 权限项的链接信息 */
    public array $auth_link_infos;

    /** @var array 权限来源 */
    public array $auth_source;

    /** @var bool 是否管理员 */
    public bool $is_admin;

    /**
     * 动态追加新字段的方法
     * @param $fieldName
     * @param $value
     * User Long
     * Date 2024/9/3
     */
    public function addField($fieldName, $value) {
        $this->$fieldName = $value;
    }
}
