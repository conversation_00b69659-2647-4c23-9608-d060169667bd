<?php

namespace components\platform\user\requests;

use components\util\BaseRequest;
use exception\PlatformException;
use components\platform\user\ApiPath;
use components\platform\user\dto\TokenDto;
use components\platform\user\dto\UserShortInfoDto;
use utils\HttpClient;
use utils\Log;
use exception\AuthorizationException;
use GuzzleHttp\Promise\PromiseInterface;
use Psr\Http\Message\ResponseInterface;

class Auth extends BaseRequest
{
    /**
     * 根据token获取用户信息
     * @return PromiseInterface
     * @see UserShortInfoDto
     */
    public function getUserByToken()
    {
        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_USERS_BY_TOKEN_URI);

        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);

            if (is_array($data) && array_key_exists('code', $data) && $data['code'] == 200) {
                $user = $data['data'];
                return (new UserShortInfoDto())->load([
                    'userId' => $user['userId'],
                    'empId' => $user['employeeId'],
                    'employeeCode' => $user['account'],
                    'name' => $user['name'],
                    'enName' => $user['enName'],
                    'telPhone' => $user['telPhone']
                ]);
            }
            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);

            throw new AuthorizationException($data['msg'] ?? '中台未知错误');
        });


        return $client;

    }

    /**
     * 用老token换取新token
     * @param $token
     * @return PromiseInterface
     */
    public function refresh()
    {
        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::REFRESH_TOKEN_URI);
        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            if (array_key_exists('code', $data) && $data['code'] == 200) {
                return (new TokenDto())->load([
                    'token' => $data['data'],
                ]);
            }
            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }

}
