<?php
/**
 * Desc: 对接用户中心用户接口
 * User: lca
 * Date-Time: 2023/9/25 11:01
 */

namespace components\platform\user\requests;


use components\platform\user\dto\SystemUserDetailDto;
use components\platform\user\dto\SystemUserDto;
use components\platform\user\dto\V2FsOpenAndUnionIdDto;
use components\platform\user\dto\V2SubSystemRoleUserDropDownPageListDto;
use components\platform\user\dto\V2SystemUserDetailDto;
use components\platform\user\dto\V2SystemUserRoleDto;
use components\util\BaseDto;
use components\util\BaseRequest;
use exception\PlatformException;
use components\platform\enum\JobStatus;
use components\platform\enum\Sex;
use components\platform\user\ApiPath;
use components\platform\user\dto\CompanyDto;
use components\platform\user\dto\EmpInfoDto;
use components\platform\user\dto\FsDto;
use components\platform\user\dto\OrgDto;
use components\platform\user\dto\PostionDto;
use components\platform\user\dto\UserDetailDto;
use components\platform\user\dto\UserShortInfoDto;
use utils\HttpClient;
use utils\Log;
use GuzzleHttp\Promise\PromiseInterface;
use Psr\Http\Message\ResponseInterface;

class User extends BaseRequest
{
    /**
     * 获取指定组织ID下的员工
     * @param int $orgId 组织ID
     * @param bool $includeSubordinates 是否包含下级部门
     * @param int $jobStatus 岗位状态
     * @param bool $isMasterPosition 是否主岗位
     * @return PromiseInterface
     * @throws PlatformException
     * @see UserShortInfoDto
     */
    public function getDepartmentEmp(int $orgId, bool $includeSubordinates = true, int $jobStatus = JobStatus::WORK, bool $isMasterPosition = true): PromiseInterface
    {
        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_DEPARTMENT_EMP_URI, ['json' => [
            'orgId' => $orgId,
            'isNeedLower' => $includeSubordinates,
            'jobStatus' => $jobStatus,
            'isMasterPosition' => $isMasterPosition
        ]]);

        return $this->userBodyHandle($client);
    }

    /**
     * 根据ID数组获取用户信息
     * @param array $ids
     * @return PromiseInterface
     * @throws PlatformException
     * @see UserShortInfoDto
     */
    public function getUserByIds(array $ids = []): PromiseInterface
    {
        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_USERS_BY_ID_URI, ['json' => [
            'userIds' => $ids,
            'isMasterPosition' => true
        ]]);

        return $this->userBodyHandle($client);
    }

    /**
     * 处理用户数据
     * @param PromiseInterface $client
     * @return PromiseInterface
     */
    private function userBodyHandle(PromiseInterface $client): PromiseInterface
    {
        return $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            $result = [];
            if (array_key_exists('code', $data) && $data['code'] == 200) {
                foreach ($data['data'] as $v) {
                    $result[] = (new UserShortInfoDto())->load($v);
                }
                return $result;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });
    }

    /**
     * 获取用户详情
     * @param $userId
     * @return PromiseInterface
     * @see UserDetailDto
     */
    public function getUserDetail($userId): PromiseInterface
    {
        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_USER_DETAIL_URL, ['json' => [
            'UserIds' => [$userId],
        ]]);

        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            if (array_key_exists('code', $data) && $data['code'] == 200) {
                $currentData = $data['data'][0] ?? [];
                if (!$currentData) {
                    throw new PlatformException('未获取到用户信息[中台]!');
                }

                return $this->getUserDetailDto($currentData);
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }

    /**
     * 批量获取用户详情
     * @param $userIds
     * @return PromiseInterface
     * @see UserDetailDto[] id=>UserDetailDto
     */
    public function multiGetUserDetail($userIds = []): PromiseInterface
    {
        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::MULTI_GET_USER_DETAIL_URL, ['json' => [
            'UserIds' => $userIds,
        ]]);

        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            if (array_key_exists('code', $data) && $data['code'] == 200) {
                $result = [];
                foreach ($data['data'] as $currentData) {
                    $result[$currentData['userInfo']['id']] = $this->getUserDetailDto($currentData);
                }

                return $result;
            }
        });

        return $client;
    }

    /**
     * 根据用户详情接口数据处理成dto
     * @param $currentData
     * @return UserDetailDto
     */
    private function getUserDetailDto($currentData): UserDetailDto
    {
        $currentUserInfo = $currentData['userInfo'];
        $userInfo = (new UserShortInfoDto())->load([
            'userId' => $currentUserInfo['id'],
            'empId' => $currentUserInfo['employeeId'],
            'name' => $currentUserInfo['name'],
            'enable' => $currentUserInfo['isEnable'],
            'telPhone' => $currentUserInfo['telPhone'],
            'employeeCode' => $currentUserInfo['account'],
        ]);

        $empINfoDto = (new EmpInfoDto())->load(
            [
                'empId' => $currentData['empInfo']['id'],
                'name' => $currentData['empInfo']['name'],
                'enName' => $currentData['empInfo']['enName'],
                'age' => $currentData['empInfo']['age'],
                'telPhone' => $currentData['empInfo']['telPhone'],
                'email' => $currentData['empInfo']['mail'],
                'idCard' => $currentData['empInfo']['idCard'],
                'employeeCode' => $currentData['empInfo']['employeeCode'],
                'address' => $currentData['empInfo']['address'],
                'managerName' => $currentData['empInfo']['managerName'],
                'managerCode' => $currentData['empInfo']['managerCode'],
                'education' => $currentData['empInfo']['education'] ?? null,
                'sex' => new Sex(),
                'jobStatus' => new JobStatus()
            ]
        );
        $empINfoDto->sex->setProperties(null, $currentData['empInfo']['sex']);
        $empINfoDto->jobStatus->setProperties(null, $currentData['empInfo']['jobStatus']);

        $fsDto = (new FsDto());
        if ($currentData['fsInfo']) {
            $fsDto->load([
                'unionId' => $currentData['fsInfo']['union_id'] ?? '',
                'fsEmpId' => $currentData['fsInfo']['fs_employee_id'] ?? ''
            ]);
        }

        $generalPositionInfo = $currentData['generalPositionInfo'];
        $positionData = [];
        foreach ($generalPositionInfo as $v) {
            $position = new PostionDto();
            $position->load($v);
            $orgDto = (new OrgDto())->load($v);

            $companyDto = (new CompanyDto())->load($v);

            $orgTree = [];
            $positionRelateOrgs = (array)$v['positionnRelateOrgs'];
            foreach ($positionRelateOrgs as $org) {
                $orgTree[] = (new OrgDto())->load($org);
            }

            $position->org = $orgDto;
            $position->company = $companyDto;
            $position->orgTree = $orgTree;
            $positionData[] = $position;
        }

        $userDetail = (new UserDetailDto());
        $userDetail->user = $userInfo;
        $userDetail->fsInfo = $fsDto;
        $userDetail->empInfo = $empINfoDto;
        $userDetail->positions = $positionData;

        return $userDetail;
    }

    /**
     * 通过自定义where条件获取用户信息
     * @param array $param 入参
     * @return PromiseInterface
     * <AUTHOR>
     * @date 2023/10/10 15:10
     */
    public function getUserInfoByCustomer(array $param): PromiseInterface
    {
        $jsonParam['ids'] = $param['user_ids'] ?? [];
        $jsonParam['employeeIds'] = $param['employee_ids'] ?? [];
        !empty($param['account']) && $jsonParam['account'] = $param['account'];
        !empty($param['name']) && $jsonParam['name'] = $param['name'];
        !empty($param['en_name']) && $jsonParam['enName'] = $param['en_name'];
        !empty($param['company_id']) && $jsonParam['companyId'] = $param['company_id'];
        !empty($param['dept_id']) && $jsonParam['deptId'] = $param['dept_id'];
        !empty($param['telephone']) && $jsonParam['telPhone'] = $param['telephone'];
        isset($param['is_enable']) && $jsonParam['isEnable'] = $param['is_enable'];

        // 处理key不连贯中台报错问题
        if (!empty($jsonParam['ids'])) $jsonParam['ids'] = array_values($jsonParam['ids']);
        if (!empty($jsonParam['employeeIds'])) $jsonParam['employeeIds'] = array_values($jsonParam['employeeIds']);

        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_USER_INFO_BY_CUSTOMER, ['json' => $jsonParam ?? []]);

        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);

            if (array_key_exists('code', $data) && (string)$data['code'] === '200') {
                $return = [];
                foreach ($data['data'] ?? [] as $user) {
                    $userDetail = new UserDetailDto();
                    $userDetail->user = new UserShortInfoDto();
                    $userDetail->user->load([
                        'userId' => $user['id'],
                        'empId' => $user['empId'],
                        'employeeCode' => $user['account'],
                        'name' => $user['userName'],
                        'enName' => $user['enName'],
                        'avatar' => $user['avatar']
                    ]);

                    $userDetail->mainPosition = new PostionDto();
                    $userDetail->mainPosition->positionName = $user['jobName'];

                    $userDetail->mainPosition->company = new CompanyDto();
                    $userDetail->mainPosition->company->load([
                        'companyId' => $user['companyId'],
                        'companyName' => $user['companyName'],
                        'shortName' => $user['sCompanyName']
                    ]);

                    $userDetail->mainPosition->org = new OrgDto();
                    $userDetail->mainPosition->org->load([
                        'orgId' => $user['departmentId'],
                        'orgName' => $user['departmentName'],
                        'orgShortName' => $user['sDepartmentName']
                    ]);

                    $return[] = $userDetail;
                }

                return $return;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }

    /**
     * 通过组织id集合获取其下（含下级组织）所有用户id
     * @param array $param 入参
     * @return PromiseInterface
     * <AUTHOR>
     * @date 2023/10/16 16:09
     */
    public function getUserIdByOrgIds(array $param): PromiseInterface
    {
        $jsonParam['orgIds'] = array_values($param['organization_ids'] ?? []);
        !empty($param['job_status']) && $jsonParam['jobStatus'] = $param['job_status'];
        !empty($param['is_master_position']) && $jsonParam['isMasterPosition'] = $param['is_master_position'];

        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_USER_ID_BY_ORG_IDS, ['json' => $jsonParam ?? []]);

        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            if (array_key_exists('code', $data) && (string)$data['code'] === '200') {
                return $data['data'] ?? [];
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }

    /**
     * 通过用户id获取上级信息
     * @param $userId
     * @return PromiseInterface
     * @author: 杨荣钦
     * @date: 2023/11/3 9:30
     */
    public function getSuperiorByUserId($userId): PromiseInterface
    {
        $jsonParam = [];
        if (!empty($userId)) {
            $jsonParam['userId'] = $userId;
        }

        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_HIGHER_LEVEL_BY_USER_ID, ['form_params' => $jsonParam ?? []]);

        $client = $client->then(function (ResponseInterface $response) {

            $data = $response->getBody()->getContents();

            $data = json_decode($data, true);

            if (array_key_exists('code', $data) && $data['code'] == 200) {
                if (!empty($data['data']['userId'])) {
                    return $data['data'];
                }
                return [];
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);

        });

        return $client;
    }

    /**
     * 通过用户id获取所有节点下属信息
     * @param mixed $param 入参
     * @return PromiseInterface
     * <AUTHOR>
     * @date 2023/11/14 17:34
     */
    public function getSubordinateByUserIds($param): PromiseInterface
    {
        $jsonParam = [];
        if (!empty($param['userIds'])) {
            $jsonParam['UserIds'] = $param['userIds'];
        }

        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_SUBORDINATE_BY_USER_IDS, ['json' => $jsonParam]);
        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            if (array_key_exists('code', $data) && (string)$data['code'] === '200') {
                return $data['data'] ?? [];
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }

    /**
     * 通过用户id获取所有及下级节点下属信息
     * @param $param
     * @return PromiseInterface
     * <AUTHOR>
     * @date 2023/11/18 11:15
     */
    public function getAllSubordinateByUserIds($param): PromiseInterface
    {
        $jsonParam = [];
        if (!empty($param['userIds'])) {
            $jsonParam['UserIds'] = $param['userIds'];
        }

        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_ALL_SUBORDINATE_BY_USER_IDS, ['json' => $jsonParam]);
        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            if (array_key_exists('code', $data) && (string)$data['code'] === '200') {
                return $data['data'] ?? [];
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }

    /**
     * 定时任务使用的token
     * @param $param
     * @return PromiseInterface
     * <AUTHOR>
     * @date 2023/11/21 17:45
     */
    public function getTokenWithOaInvoke(): PromiseInterface
    {
        $jsonParam = [];


        $client = HttpClient::getPlatformClient();
        $client = $client->postAsync(ApiPath::GET_TOKEN_WITH_OA_INVOKE, ['json' => $jsonParam]);
        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            if (array_key_exists('code', $data) && (string)$data['code'] === '200') {
                return $data['data'] ?? [];
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }

    /**
     * 获取登录信息
     * @param string $account 中台账号
     * @param string $password 中台密码
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     * User Long
     * Date 2024/7/1
     */
    public function getOauthLogin(string $account, string $password)
    {
        // 中台登录公钥
        $publicKey = '-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7Nsa8do705Vvn1mLHxNN
xaiY0HqhKv2+BHQbIzXUycf8I3XKvWbqa0Ids5xU8isp0wHbKKYV53w1kwUQUM/y
U96b2cHovkxnN3OdrgLxaSpZE56tyQX+ToocCQJmrXUYJqvkqRklaDbQd9vuWW2w
EkJ0mYJXcjMsJpi+z21CyduywVmVo/j0DQOX7KZdV1UOHjjzHPT+v/htlY1nfad2
uhxcULWpLJLm11Ner+Gr/noA96Efg1ZDoC7NROMUIfXqIyl8UD0pxRCCPEg4gO9A
q6e0m8KOG5w9HwvfZ8R2WEjF3bUEBkQgOqI3ULEoZIBd0YfEsOgHOqwMsREO3bNr
4wIDAQAB
-----END PUBLIC KEY-----';

        // 公钥加密
        $password = openssl_public_encrypt($password, $encrypted, $publicKey) ? base64_encode($encrypted) : null;

        // 初始化Guzzle客户端
        $client = HttpClient::getPlatformClient();
        $res = $client->post(ApiPath::OAUTH_LOGIN, [
            'form_params' => [
                'Account' => $account,
                'Password' => $password
            ]
        ]);

        // 获取接收数据
        $data = json_decode($res->getBody(), true);

        // 接口异常，请联系开发人员
        if (!$data) throw new PlatformException($data['msg']);

        return $data['data'];
    }

    /**
     * 获取中台子系统用户列表数据
     * @param array $param
     * @return PromiseInterface
     * User Long
     * Date 2024/8/12
     */
    public function getSubSystemRoleUserPage(array $param = [])
    {
        if (!isset($param['system_id'])) {
            throw new PlatformException('子系统id必须传值');
        }

        $jsonParam['keyWord'] = $param['keyword'] ?? ''; //搜索词
        $jsonParam['roleIds'] = $param['role_ids'] ?? []; //权限id集合
        $jsonParam['roleCodes'] = $param['role_codes'] ?? []; //权限code集合
        $jsonParam['userIds'] = $param['user_ids'] ?? []; //用户id集合
        $jsonParam['systemId'] = $param['system_id']; //系统id（必填）

        $jsonParam['pageIndex'] = $param['page'] ?? BaseDto::PAGE_INDEX; //分页页码
        $jsonParam['pageSize'] = $param['list_rows'] ?? BaseDto::PAGE_SIZE; //分页条数

        $user = HttpClient::getPlatformClient($this->token);
        $user = $user->postAsync(ApiPath::SUB_SYSTEM_ROLE_USER_PAGE_LIST, ['json' => $jsonParam]);

        $user = $user->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);

            if (array_key_exists('code', $data) && $data['code'] == 200) {
                $pageData = BaseRequest::setCenterPageData([], $data);
                $return = [];

                foreach ($data['data']['list'] ?? [] as $item) {
                    $return[] = $this->setSubSystemUserPageDto($item ?? []);
                }
                $pageData['data'] = $return;

                return $pageData;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $user;
    }

    /**
     * 处理项目用户列表数据
     * @param array $userInfo
     * @return SystemUserDto
     * User Long
     * Date 2024/8/12
     */
    public function setSubSystemUserPageDto(array $userInfo)
    {
        $userListDto = new SystemUserDto();
        $userListDto->load([
            'user_id' => $userInfo['userId'] ?? 0,
            'user_name' => $userInfo['name'] ?? '',
            'en_user_name' => $userInfo['enName'] ?? '',
            'account' => $userInfo['account'] ?? '',
            'role_code' => $userInfo['roleCode'] ?? '',
            'role_name' => $userInfo['roleName'] ?? '',
            'position_name' => $userInfo['positionName'] ?? '',
            'company_id' => $userInfo['companyId'] ?? 0,
            'company_name' => $userInfo['companyName'] ?? '',
            'dept_id' => $userInfo['deptId'] ?? 0,
            'dept_name' => $userInfo['deptName'] ?? '',
            'is_enable' => $userInfo['isEnable'] ?? null,
            'enable_name' => (isset($userInfo['isEnable']) && $userInfo['isEnable']) ? '在职' : '离职'
        ]);

        return $userListDto;
    }

    /**
     * 获取中台下拉分页数据（老，2024-12-28预废弃）
     * @param array $param
     * @return PromiseInterface
     * User Long
     * Date 2024/12/28
     */
    public function getSubSystemRoleUserList(array $param = [])
    {
        if (!isset($param['system_id'])) {
            throw new PlatformException('子系统id必须传值');
        }
        if (!isset($param['user_ids'])) {
            throw new PlatformException('用户id必须传值');
        }

        $jsonParam['keyWord'] = $param['keyword'] ?? ''; //搜索词
        $jsonParam['roleIds'] = $param['role_ids'] ?? []; //权限id集合
        $jsonParam['roleCodes'] = $param['role_codes'] ?? []; //权限code集合
        $jsonParam['userIds'] = $param['user_ids']; //用户id集合
        $jsonParam['systemId'] = $param['system_id']; //系统id（必填）

        $jsonParam['pageIndex'] = $param['page'] ?? BaseDto::PAGE_INDEX; //分页页码
        $jsonParam['pageSize'] = $param['list_rows'] ?? BaseDto::PAGE_SIZE; //分页条数

        $user = HttpClient::getPlatformClient($this->token);
        $user = $user->postAsync(ApiPath::SUB_SYSTEM_ROLE_USER_PAGE_LIST, ['json' => $jsonParam]);

        $user = $user->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);

            if (array_key_exists('code', $data) && $data['code'] == 200) {
                $return = [];
                foreach ($data['data']['list'] ?? [] as $item) {
                    $return[] = $this->setSubSystemUserDto($item ?? []);
                }

                return $return;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $user;
    }

    /**
     * 设置中台下拉分页数据（老，2024-12-28预废弃）
     * @param array $userInfo
     * @return SystemUserDetailDto
     * User Long
     * Date 2024/12/28
     */
    public function setSubSystemUserDto(array $userInfo)
    {
        $userListDto = new SystemUserDetailDto();
        return $userListDto->load([
            'user_id' => $userInfo['userId'] ?? 0,
            'user_name' => $userInfo['name'] ?? '',
            'en_user_name' => $userInfo['enName'] ?? '',
            'account' => $userInfo['account'] ?? '',
            'role_code' => $userInfo['roleCode'] ?? '',
            'role_name' => $userInfo['roleName'] ?? '',
            'position_name' => $userInfo['positionName'] ?? '',
            'company_id' => $userInfo['companyId'] ?? 0,
            'company_name' => $userInfo['companyName'] ?? '',
            'dept_id' => $userInfo['deptId'] ?? 0,
            'dept_name' => $userInfo['deptName'] ?? '',
            'is_enable' => $userInfo['isEnable'] ?? null,
            'enable_name' => (isset($userInfo['isEnable']) && $userInfo['isEnable']) ? '在职' : '离职'
        ]);
    }

    /** v2 接口 **/
    /**
     * 获取中台权限数据
     * @param int $systemId
     * @return PromiseInterface
     * User Long
     * Date 2024/12/28
     */
    public function getSubSystemUserRole(int $systemId = 0)
    {
        if (!$systemId) {
            throw new PlatformException('子系统id必须传值');
        }

        $client = HttpClient::getPlatformClient($this->token, ['SubSystemId' => $systemId]);
        $clientData = $client->postAsync(ApiPath::GET_USER_RELATION_PERMISSION_ITEMS);

        $clientData = $clientData->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);

            if (array_key_exists('code', $data) && $data['code'] == 200) {
                $return = [];
                foreach ($data['data'] ?? [] as $item) {
                    $return[] = $this->setSubSystemUserRoleDto($item ?? []);
                }

                return $return;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $clientData;
    }

    /**
     * 设置中台权限数据
     * @param array $item
     * @return V2SystemUserRoleDto
     * User Long
     * Date 2024/12/28
     */
    public function setSubSystemUserRoleDto(array $item)
    {
        $userRoleDto = new V2SystemUserRoleDto();
        return $userRoleDto->load([
            'id' => $item['id'] ?? 0,
            'name' => $item['name'] ?? '',
            'auth_source' => $item['authSource'] ?? [],
            'auth_link_infos' => $item['authLinkInfos'] ?? [],
            'is_admin' => $item['isAdmin'] ?? false
        ]);
    }

    /**
     * 获取中台角色用户数据
     * @param $param
     * @return PromiseInterface
     * User Long
     * Date 2024/12/28
     */
    public function getSubSystemRoleUserDropDownPageList($param)
    {
        if (!isset($param['system_id'])) {
            throw new PlatformException('子系统id必须传值');
        }

        $jsonParam['systemId'] = $param['system_id']; //系统id（必填）
        $jsonParam['keyWord'] = $param['keyword'] ?? ''; //搜索词
        $jsonParam['roleCodes'] = $param['role_codes'] ?? []; //权限code集合
        $jsonParam['userIds'] = $param['user_ids'] ?? []; //权限code集合

        $jsonParam['pageIndex'] = $param['page'] ?? BaseDto::PAGE_INDEX; //分页页码
        $jsonParam['pageSize'] = $param['list_rows'] ?? BaseDto::PAGE_SIZE; //分页条数

        $res = HttpClient::getPlatformClient($this->token);
        $res = $res->postAsync(ApiPath::POST_SUB_SYSTEM_ROLE_USER_DROP_DOWN_PAGE_LIST, ['json' => $jsonParam]);

        return $res->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);

            if (array_key_exists('code', $data) && $data['code'] == 200) {
                $pageData = BaseRequest::setCenterPageData([], $data);
                $return = [];

                foreach ($data['data']['list'] ?? [] as $item) {
                    $return[] = $this->setV2SubSystemRoleUserDropDownPageListDto($item ?? []);
                }
                $pageData['data'] = $return;

                return $pageData;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });
    }

    /**
     * 设置中台角色用户数据
     * @param array $userInfo
     * @return V2SubSystemRoleUserDropDownPageListDto
     * User Long
     * Date 2024/12/28
     */
    public function setV2SubSystemRoleUserDropDownPageListDto(array $userInfo)
    {
        $userListDto = new V2SubSystemRoleUserDropDownPageListDto();
        return $userListDto->load([
            'user_id' => $userInfo['userId'] ?? 0,
            'user_name' => $userInfo['name'] ?? '',
            'en_user_name' => $userInfo['enName'] ?? '',
            'account' => $userInfo['account'] ?? '',
            'role_code' => $userInfo['roleItems'] ?? []
        ]);
    }

    /**
     * 获取中台用户详情
     * @param $param
     * @return PromiseInterface
     * User Long
     * Date 2024/12/28
     */
    public function getSystemUserDetail($param)
    {
        if (!isset($param['user_ids'])) {
            throw new PlatformException('用户id必须传值');
        }

//        $jsonParam['companyId'] = $param['company_id']; // 所属公司Id
        $jsonParam['userId'] = $param['user_ids']; //用户id
//        $jsonParam['userNameOrEnName'] = $param['user_name_or_en_name']; //用户名称
//        $jsonParam['account'] = $param['account']; //账号
//        $jsonParam['isSalesman'] = $param['isSalesman']; //是否公司业务员
//        $jsonParam['roleId'] = $param['roleId']; //业务角色id
        $jsonParam['roleIsEnable'] = null; //业务角色是否启用, 固定传 null
//        $jsonParam['name'] = $param['name']; //业务角色名称
//        $jsonParam['code'] = $param['code']; //业务角色编码
//        $jsonParam['customerNature'] = $param['customerNature']; 客户性质 同行:1;直客:4;海外代理:16;贸易公司:64;内部子公司:512;个人:1024;内部子部门:2048;
//        $jsonParam['customerNatureTag'] = $param['customerNatureTag']; //客户性质标签，为true时表示客户性质是所有的，为false时表示客户性质是其中一个。
//        $jsonParam['businessRoles'] = $param['businessRoles']; //业务角色枚举值
//        $jsonParam['businessRoleTag'] = $param['businessRoleTag']; //业务角色枚举
//        $jsonParam['organizationLevel'] = $param['organizationLevel']; //角色归属
//        $jsonParam['organizationLevelTag'] = $param['organizationLevelTag']; //角色归属标签
//        $jsonParam['type'] = $param['type']; //业务类型
//        $jsonParam['typeTag'] = $param['typeTag']; //业务类型标签


        $jsonParam['pageIndex'] = $param['page'] ?? BaseDto::PAGE_INDEX; //分页页码
        $jsonParam['pageSize'] = $param['list_rows'] ?? BaseDto::PAGE_SIZE; //分页条数

        $user = HttpClient::getPlatformClient($this->token);
        $user = $user->postAsync(ApiPath::POST_USER_INFORMATION, ['json' => $jsonParam]);

        return $user->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);

            if (array_key_exists('code', $data) && $data['code'] == 200) {
                $return = [];

                foreach ($data['data']['list'] ?? [] as $item) {
                    $return[] = $this->setV2SystemUserDetailDto($item ?? []);
                }

                return $return;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });
    }

    /**
     * 设置中台用户详情
     * @param array $userInfo
     * @return V2SystemUserDetailDto
     * User Long
     * Date 2024/12/28
     */
    public function setV2SystemUserDetailDto(array $userInfo)
    {
        $userListDto = new V2SystemUserDetailDto();

        return $userListDto->load([
            'user_id' => $userInfo['userId'] ?? 0,
            'user_name' => $userInfo['name'] ?? '',
            'en_user_name' => $userInfo['enName'] ?? '',
            'account' => $userInfo['account'] ?? '',
//            'role_code' => $userInfo['roleCode'] ?? '',
//            'role_name' => $userInfo['roleName'] ?? '',
            'position_id' => $userInfo['position']['id'] ?? 0,
            'position_name' => $userInfo['position']['positionName'] ?? '',
            'company_id' => $userInfo['companyInfo']['id'] ?? 0,
            'company_name' => $userInfo['companyInfo']['name'] ?? '',
            'dept_id' => $userInfo['deptInfo']['id'] ?? 0,
            'dept_name' => $userInfo['deptInfo']['name'] ?? '',
            'is_enable' => $userInfo['isEnable'] ?? null,
            'avatar' => $userInfo['feiShuURL'] ?? '',
            'enable_name' => (isset($userInfo['isEnable']) && $userInfo['isEnable']) ? '在职' : '离职',
            'phone' => $userInfo['telPhone'] ?? '',
        ]);
    }

    /**
     * 获取中台飞书详情 入参二选一即可
     * @param array $userIds 用户id集
     * @param array $phones 手机号集
     * @return PromiseInterface
     * User Long
     * Date 2025/2/27
     */
    public function getFsOpenAndUnionId( array $userIds = [], array $phones = [])
    {
        if (!$userIds && !$phones) {
            throw new PlatformException('获取飞书信息，用户id 或 手机号必须传值');
        }

        $jsonParam = [];

        // 用户id获取
        $userIds = array_unique($userIds);
        foreach ($userIds as $userId) {
            $jsonParam['fsQuery'][] = ['userId' => $userId];
        }

        // 手机号获取
        $phones = array_unique($phones);
        foreach ($phones as $phone) {
            $jsonParam['fsQuery'][] = ['mobile' => $phone];
        }

        $user = HttpClient::getPlatformClient($this->token);
        $user = $user->postAsync(ApiPath::POST_FS_OPEN_AND_UNION_ID, ['json' => $jsonParam]);

        return $user->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);

            if (array_key_exists('code', $data) && $data['code'] == 200) {
                $return = [];

                foreach ($data['data'] ?? [] as $item) {
                    $return[] = $this->setV2FsOpenAndUnionIdDto($item ?? []);
                }

                return $return;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });
    }

    /**
     * 设置中台飞书详情
     * @param array $fsInfo
     * @return V2FsOpenAndUnionIdDto
     * User Long
     * Date 2025/2/27
     */
    public function setV2FsOpenAndUnionIdDto(array $fsInfo)
    {
        $fsDto = new V2FsOpenAndUnionIdDto();

        return $fsDto->load([
            'user_id' => $fsInfo['userId'] ?? 0,
            'fs_id' => $fsInfo['fsId'] ?? '',
            'phone' => $fsInfo['mobile'] ?? '',
            'country_code' => $fsInfo['countryCode'] ?? '',
            'union_id' => $fsInfo['unionId'] ?? '',
            'fs_login_open_id' => $fsInfo['fsLoginOpenId'] ?? '',
        ]);
    }
}
