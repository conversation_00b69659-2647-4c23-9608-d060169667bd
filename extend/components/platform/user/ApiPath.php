<?php

namespace components\platform\user;

class ApiPath
{
    /** @var string 获取指定部门下的员工 */
    public const GET_DEPARTMENT_EMP_URI = '/userApi/User/GetAllUserByOrgId';
    /** @var string 批量获取指定id的用户简要信息 */
    public const GET_USERS_BY_ID_URI = '/userApi/User/GetAllUserByOrgId';
    /** @var string 根据token获取用户信息 */
    public const GET_USERS_BY_TOKEN_URI = '/userApi/User/GetCurrentRequestUser';
    /** @var string token续期 */
    public const REFRESH_TOKEN_URI = '/userApi/OAuth/GetRefreshToken';
    /** @var string 获取用户详情 */
    public const GET_USER_DETAIL_URL = '/userApi/User/GetUserDetialInfoByIds';
    /** @var string 批量获取用户详情 */
    public const MULTI_GET_USER_DETAIL_URL = '/userApi/User/GetUserDetialInfoByIds';
    /** @var string 查询员工分页列表 */
    public const GET_EMP_INFO_PAGE_LIST = '/userApi/Employee/GetEmpInfoPageList';
    /** @var string  获取所有公司 */
    public const GET_ALL_COMPANY_LIST = '/userApi/Organization/GetAllCompanyList';

    public const GET_LIST_INTERNAL_ACCOUNT = '/financeApi/Bank/GetListInternalAccount';
    /** @var string 通过自定义where条件获取用户信息 */
    public const GET_USER_INFO_BY_CUSTOMER = '/userApi/User/GetUserInfoByCustomer';

    /** @var string 获取岗位与组织的关联信息 */
    public const GET_POSITION_ORG = '/userApi/Position/GetPositionOrg';

    /** @var string 通过岗位Code、id集合查找员工 */
    public const GET_EMP_INFO_BY_POSITION_CODE_IDS = '/userApi/Employee/GetEmpInfoByPositionCodeIds';
    /** @var string 所有岗位列表 */
    public const GET_POSITION_LIST = '/userApi/Position/GetPositionList';

    /** @var string 通过组织id集合获取其下（含下级组织）所有用户id */
    public const GET_USER_ID_BY_ORG_IDS = '/userApi/User/GetUserIdByOrgIds';

    /** @var string 通过用户id获取该用户直属上级工号,再通过工号查询直属上级信息 */
    public const GET_HIGHER_LEVEL_BY_USER_ID = '/userApi/Employee/GetHigherLevelByUserId';

    /** @var string 通过用户id获取所有节点下属信息 */
    public const GET_SUBORDINATE_BY_USER_IDS = '/userApi/User/GetSubordinateByUserIds';
    /** @var string 通过用户id获取所有及下级节点下属信息 */
    public const GET_ALL_SUBORDINATE_BY_USER_IDS = '/userApi/User/GetAllSubordinateByUserIds';
    /** @var string 定时任务使用的token */
    public const GET_TOKEN_WITH_OA_INVOKE = '/userApi/OAuth/GetTokenWithOaInvoke';

    /** @var string 登录鉴权 */
    public const OAUTH_LOGIN = '/userApi/OAuth/Login';
    /** @var string 通过用户id获取详细信息 */
    public const USER_DETAIL_INFO_BY_ID = '/userApi/User/GetUserDetialInfoById';
    /** 通过 子系统ID,子系统角色，用户id集合，关键词 获取成员 */
    public const SUB_SYSTEM_ROLE_USER_PAGE_LIST = '/userApi/User/GetSubSystemRoleUserPageList';
    /** 根据请求子系统和用户id查询权限项 */
    public const GET_USER_RELATION_PERMISSION_ITEMS = '/userApi/UserAuthorization/GetUserRelationPermissionItems';
    /** 获取用户信息，数据量多别用 */
    public const POST_USER_INFORMATION = '/userApi/User/GetUserInformation';
    /** 下拉子系统下角色用户查询, 优先从缓存中获取, 缓存时间30秒, key: UserAndRoleDropListName */
    public const POST_SUB_SYSTEM_ROLE_USER_DROP_DOWN_PAGE_LIST = '/userApi/User/GetSubSystemRoleUserDropDownPageList';

    /** 根据飞书ID和手机号获取飞书用户信息 */
    public const POST_FS_OPEN_AND_UNION_ID = '/userApi/User/GetFsOpenAndUnionId';
}
