<?php
/**
 * Desc: 组织列表
 * User: lca
 * Date-Time: 2023/10/9 19:12
 */

namespace components\platform\organization\dto;

use components\util\BaseDto;

class OrgListDto extends BaseDto
{
    /** @var ?int 组织id */
    public $orgId;
    /** @var ?string 组织Code */
    public $code;
    /** @var ?string 组织$orgCode */
    public $orgCode;
    /** @var ?int 组织类型：1集团，2集团部门，3子品牌，4子品牌分类，5子品牌公司，6集团中心，7总公司，8子公司，9区域，10分公司，11部门，12小组 */
    public $type;
    /** @var  ?string 组织类型描述 */
    public $typeDesc;
    /** @var ?int 所属上级组织id */
    public $parentId;
    /** @var ?string 组织简称 */
    public $shortName;
    /** @var ?string 组织中文名 */
    public $cnName;
    /** @var ?string 组织英文名 */
    public $enName;
    /** @var ?string 业务代码 */
    public $businessCode;
    /** @var ?int 部门标识（汇佰）：1销售部，2市场部 */
    public $departIdentity;
    /** @var ?int 组织负责人用户id */
    public $manager;
    /** @var ?int hrbp */
    public $hrbp;
    /** @var ?int 区域财务 */
    public $finance;
    /** @var ?int 合同审核人id */
    public $contractReviewer;
    /** @var ?string 描述 */
    public $description;
    /** @var ?int 排序 */
    public $sort;
    /** @var ?bool 财务成本是否分摊 */
    public $isCostAllocation;
    /** @var ?bool 是否启用 */
    public $isEnable;
    /** @var ?bool 是否业务组织 */
    public $isBusinessOrg;
    /** @var ?string 上级组织名称 */
    public $upOrgName;
    /** @var ?string 上级组织英文名称 */
    public $upOrgEnName;
    /** @var ?string 上级组织简称 */
    public $upOrgShortName;
    /** @var ?array 树状组织 */
    public $tree;
    /** @var ?bool 是否考核主体 */
    public $isEvaluationSubject;
    /** @var ?bool 是否法人主体 */
    public $isLegalSubject;
}
