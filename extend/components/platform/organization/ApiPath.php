<?php
/**
 * Desc: 接口路径
 * User: lca
 * Date-Time: 2023/10/9 19:07
 */

namespace components\platform\organization;

class ApiPath
{
    /** @var string 根据组织名，获取组织表所有组织信息（只有启用） */
    public const GET_ORGANIZATION_LIST_BY_NAME = '/userApi/Organization/GetOrganizationListByName';
    /** @var string 组织树状图 */
    public const GET_TREE = '/userApi/Organization/GetTree';
    /** @var string 通过组织id获取组织信息以及所属公司信息 */
    public const GET_ORG_WITH_NEAREST_CMP_INFO_BY_ORG_IDS = '/userApi/Organization/GetOrgWithNearestCmpInfoByOrgIds';
    /** @var string 通过组织Code集合查询组织信息 */
    public const GET_EMP_INFO_BY_ORG_CODES = '/userApi/Organization/GetOrgListByOrgCodes';

}
