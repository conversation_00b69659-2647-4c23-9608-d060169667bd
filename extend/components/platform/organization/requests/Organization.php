<?php
/**
 * Desc: 组织
 * User: lca
 * Date-Time: 2023/10/9 19:10
 */

namespace components\platform\organization\requests;

use components\platform\organization\ApiPath;
use components\platform\organization\dto\OrgListDto;
use components\util\BaseRequest;
use exception\PlatformException;
use GuzzleHttp\Promise\PromiseInterface;
use Psr\Http\Message\ResponseInterface;
use utils\HttpClient;
use utils\Log;

class Organization extends BaseRequest
{
    /**
     * 根据组织名，获取组织表所有组织信息（只有启用）
     * @param array $param 入参：['organization_name' => 组织中文名, 'organization_en_name' => 组织英文名]
     * @return PromiseInterface
     * <AUTHOR>
     * @date 2023/10/9 19:44
     */
    public function getOrganizationListByName(array $param): PromiseInterface
    {
        !empty($param['organization_name']) && $jsonParam['orgName'] = $param['organization_name']; //组织中文名
        !empty($param['organization_en_name']) && $jsonParam['orgEnName'] = $param['organization_en_name']; //组织英文名

        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_ORGANIZATION_LIST_BY_NAME, ['json' => $jsonParam ?? []]);

        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            if (array_key_exists('code', $data) && (string)$data['code'] === '200') {
                $return = [];
                foreach ($data['data'] ?? [] as $item) {
                    $return[] = (new OrgListDto())->load($item);
                }

                return $return;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }

    /**
     * 获取组织树状下拉
     * @param array $param 入参
     * @return PromiseInterface
     * @author: 杨荣钦
     * @date: 2023/10/13 15:40
     */
    public function getTree(array $param): PromiseInterface
    {
        $jsonParam['IsIgnoreEnable'] = !empty($param['is_ignore_enable']); //启用状态
        !empty($param['org_id']) && $jsonParam['OrgId'] = $param['org_id']; //组织id
        !empty($param['cn_name']) && $jsonParam['CnName'] = $param['cn_name']; //组织名

        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_TREE, ['json' => $jsonParam]);

        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);

            if (array_key_exists('code', $data) && (string)$data['code'] === '200') {
                $return = [];
                foreach ($data['data'] ?? [] as $item) {
                    $return[] = (new OrgListDto())->load(['tree' => $item]);
                }

                return $return;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }

    /**
     * 通过组织id获取组织信息以及所属公司信息
     * @param array $param
     * @return \GuzzleHttp\Promise\PromiseInterface
     * <AUTHOR>
     * @date 2024/3/11 14:39
     */
    public function getOrgWithNearestCmpInfoByOrgIds(array $param): PromiseInterface
    {
        !empty($param['org_ids']) && $jsonParam['orgIds'] = $param['org_ids']; //部门Id集合。传1个即可查单个部门(两个有一必填)
        !empty($param['org_codes']) && $jsonParam['orgEnName'] = $param['organization_en_name']; //部门code集合。传1个即可查单个部门

        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_ORG_WITH_NEAREST_CMP_INFO_BY_ORG_IDS, ['json' => $jsonParam ?? []]);

        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            if (array_key_exists('code', $data) && (string)$data['code'] === '200') {
                $return = [];
                foreach ($data['data'] ?? [] as $item) {
                    $orgInfo = (new OrgListDto())->load($item['orgInfo']);
                    $nearestCmpInfo = (new OrgListDto())->load($item['nearestCmpInfo']);
                    $return[] = [
                        'org_info' => $orgInfo,
                        'nearest_cmp_info' => $nearestCmpInfo,
                    ];
                }

                return $return;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }

    /**
     * 通过组织Code集合查询组织信息
     * @param array $param
     * @return PromiseInterface
     * @author: 杨荣钦
     * @date: 2024/4/1 15:19
     */
    public function getOrgListByOrgCodes(array $param)
    {
        $jsonParam = [];
        if (!empty($param['codes'])) {
            $jsonParam['codes'] = is_array($param['codes']) ? $param['codes'] : [$param['codes']];
        }

        $client = HttpClient::getPlatformClient($this->token);
        $client = $client->postAsync(ApiPath::GET_EMP_INFO_BY_ORG_CODES, ['json' => $jsonParam]);

        $client = $client->then(function (ResponseInterface $response) {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
            if (array_key_exists('code', $data) && (string)$data['code'] === '200') {
                $return = [];
                foreach ($data['data'] ?? [] as $item) {
                    $return[] = (new OrgListDto())->load($item);
                }

                return $return;
            }

            Log::instance(Log::PLATFORM)->error(HttpClient::$logContent);
            throw new PlatformException($data['msg']);
        });

        return $client;
    }
}
