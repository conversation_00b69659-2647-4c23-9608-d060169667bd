<?php

namespace system;

use think\db\builder\Mysql;
use think\db\Query;
use think\Exception;

class MysqlCustomer extends Mysql
{
    public function select(Query $query, bool $one = false): string
    {
        $options = $query->getOptions();

        if (!$options['group'] && !$one && empty($options['limit'])) {
            $options['limit'] = "0,5000";
        }

        if (isset($options['limit']) && $options['limit']) {
            $limit = explode(",", $options['limit']);
            if (count($limit) == 1) {
                if ($limit[0] > 5000) {
                    throw new Exception('limit max 5000');
                }
            } else {
                if ($limit[1] > 5000) {
                    throw new Exception('limit max 5000');
                }
            }


        }

        return str_replace(
            ['%TABLE%', '%PARTITION%', '%DISTINCT%', '%EXTRA%', '%FIELD%', '%JOIN%', '%WHERE%', '%GROUP%', '%HAVING%', '%ORDER%', '%LIMIT%', '%UNION%', '%LOCK%', '%COMMENT%', '%FORCE%'],
            [
                $this->parseTable($query, $options['table']),
                $this->parsePartition($query, $options['partition']),
                $this->parseDistinct($query, $options['distinct']),
                $this->parseExtra($query, $options['extra']),
                $this->parseField($query, $options['field'] ?? '*'),
                $this->parseJoin($query, $options['join']),
                $this->parseWhere($query, $options['where']),
                $this->parseGroup($query, $options['group']),
                $this->parseHaving($query, $options['having']),
                $this->parseOrder($query, $options['order']),
                $this->parseLimit($query, $one ? '1' : $options['limit']),
                $this->parseUnion($query, $options['union']),
                $this->parseLock($query, $options['lock']),
                $this->parseComment($query, $options['comment']),
                $this->parseForce($query, $options['force']),
            ],
            $this->selectSql
        );
    }
}
