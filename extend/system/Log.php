<?php
/**
 * Desc:
 * User: 曾海洋
 * Date-Time: 2022/12/6 11:26
 */

namespace system;

use DateTime;
use DateTimeZone;
use think\log\driver\File;

class Log extends File
{

    public function save(array $log): bool
    {
        $destination = $this->getMasterLogFile();

        $path = dirname($destination);
        if (!is_dir($path)) {
            mkdirUnmask($path);
        }

        $info = [];

        // 日志信息封装
        $time = DateTime::createFromFormat('0.u00 U', microtime())->setTimezone(new DateTimeZone(date_default_timezone_get()))->format($this->config['time_format']);
//        $traceId = OpenTelemetry::getTraceId();
        $traceId = OpenTelemetry::getTraceId()." ".request()->url();

        foreach ($log as $type => $val) {
            $message = [];
            foreach ($val as $msg) {
                if (!is_string($msg)) {
                    $msg = var_export($msg, true);
                }

                $message[] = $this->config['json'] ?
                    json_encode(['traceId' => $traceId, 'time' => $time, 'type' => $type, 'msg' => $msg], $this->config['json_options']) :
                    sprintf($this->config['format'], $time, $type, $traceId, $msg);
            }

            if (true === $this->config['apart_level'] || in_array($type, $this->config['apart_level'])) {
                // 独立记录的日志级别
                $filename = $this->getApartLevelFile($path, $type);
                $this->write($message, $filename);
                continue;
            }

            $info[$type] = $message;
        }

        if ($info) {
            return $this->write($info, $destination);
        }

        return true;
    }

    public function write(array $message, string $destination): bool
    {
        // 检测日志文件大小，超过配置大小则备份日志文件重新生成
        $this->checkLogSize($destination);

        $info = [];

        foreach ($message as $type => $msg) {
            $info[$type] = is_array($msg) ? implode(PHP_EOL, $msg) : $msg;
        }

        $message = implode(PHP_EOL, $info) . PHP_EOL;
        $first = false;
        if (!file_exists($destination)) {
            $first = true;
        }
        $ret = error_log($message, 3, $destination);
        if ($first && is_file($destination)) {
            chmod($destination, 0777);
        }
        return $ret;
    }
}
