<?php

namespace system;

use think\cache\Driver\Redis;
use think\facade\Cache;

class LeaderElection
{
    /** @var Redis */
    private $redis;

    private $key = 'leader_key';
    private $leaseTime = 60; // Key 的有效期（秒）
    private $isLeader = false;


    public function __construct()
    {
        $this->redis = Cache::store('redis');
    }

    public function runElection()
    {
        // 尝试设置 Key，如果设置成功则成为 Leader
        $this->isLeader = false;
        $leaderValue = $this->redis->get($this->key);
        if ($leaderValue == $this->getHostName()) {
            $this->isLeader = true;

            return;
        }
        if ($leaderValue) {
            return;
        }
        if ($this->redis->rawCommand('SET', $this->key, $this->getHostName(), 'NX', 'EX', $this->leaseTime)) {
            $this->isLeader = true;
        }

    }

    public function getHostName()
    {
        return gethostname();
    }

    public function isLeader()
    {
        return $this->isLeader;
    }

    public function renewLease()
    {
        if ($this->isLeader) {
            $this->redis->expire($this->key, $this->leaseTime);
        }
    }


}
