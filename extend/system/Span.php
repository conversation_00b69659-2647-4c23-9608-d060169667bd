<?php

namespace system;

use OpenTelemetry\API\Trace\SpanInterface;
use OpenTelemetry\API\Trace\TracerInterface;

class Span
{
    /** @var SpanInterface */
    private $span;
    private $spanScope;
    private $isClose;

    /**
     * @param TracerInterface $trace
     * @param $spanName
     * @return Span
     */
    public function start($trace, $spanName): self
    {
        $this->span = $trace->spanBuilder($spanName)->startSpan();
        $this->spanScope = $this->span->activate();

        return $this;
    }

    public function end()
    {
        if ($this->isClose !== true) {
            $this->spanScope->detach();
            $this->span->end();
            $this->isClose = true;
        }

    }

    public function getTraceId()
    {
        return $this->span->getContext()->getTraceId();
    }

    public function getSpan()
    {
        return $this->span;
    }

}