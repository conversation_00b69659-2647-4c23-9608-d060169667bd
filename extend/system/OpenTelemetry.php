<?php

namespace system;

use OpenTelemetry\API\Trace\TracerInterface;
use OpenTelemetry\Contrib\Otlp\OtlpHttpTransportFactory;
use OpenTelemetry\Contrib\Otlp\SpanExporter;
use OpenTelemetry\SDK\Common\Attribute\Attributes;
use OpenTelemetry\SDK\Common\Time\ClockFactory;
use OpenTelemetry\SDK\Resource\ResourceInfo;
use OpenTelemetry\SDK\Resource\ResourceInfoFactory;
use OpenTelemetry\SDK\Trace\SpanProcessor\BatchSpanProcessor;
use OpenTelemetry\SDK\Trace\TracerProvider;
use OpenTelemetry\SemConv\ResourceAttributes;
use think\facade\Env;

class OpenTelemetry
{
    public static $tracerProvider;
    /** @var TracerInterface */
    public static $trace;
    /** @var Span */
    private static $rootSpan;
    private static $traceId;
    private static $activeSpans = [];
    private static $isClose;

    public static function shutdown()
    {
        try {
            if (!static::isOpen()) {
                return;
            }

            if (static::$isClose === false) {
                $spans = array_reverse(static::$activeSpans);
                foreach ($spans as $span) {
                    $span->end();
                }

                static::$isClose = true;
                static::$tracerProvider->shutdown();

            }
        } catch (\Exception $e) {
            error_log('OpenTelemetry shutdown error: ' . $e->getMessage());
        }

    }

    private static function isOpen()
    {
        $openFlag = Env::get('opentelemetry.open', false);

        return php_sapi_name() != 'cli' && $openFlag;
    }

    /**
     * @param $spanName
     * @return Span|null
     */
    public static function startSpan($spanName)
    {
        if (!static::isOpen()) {
            return null;
        }
        $span = new Span();
        $span->start(static::$trace, $spanName);
        static::$activeSpans[] = $span;

        return $span;
    }

    public static function start($spanName)
    {
        if (!static::isOpen()) {
            return null;
        }
        static::getTrace();


        $span = new Span();
        static::$rootSpan = $span->start(static::$trace, $spanName);
        static::$traceId = static::$rootSpan->getTraceId();
        static::$activeSpans[] = $span;
    }

    public static function getTrace()
    {
        if (static::$trace) {
            return static::$trace;
        }
        $host = Env::get('opentelemetry.host');

        $transport = (new OtlpHttpTransportFactory())->create($host . '/v1/traces', 'application/json');

        $exporter = new SpanExporter($transport);

        $serviceName = Env::get('opentelemetry.service_name');
        $resource = ResourceInfoFactory::emptyResource()->merge(ResourceInfo::create(Attributes::create([
            ResourceAttributes::SERVICE_NAMESPACE => $serviceName,
            ResourceAttributes::SERVICE_NAME => $serviceName,
            ResourceAttributes::SERVICE_VERSION => '0.1',
            ResourceAttributes::DEPLOYMENT_ENVIRONMENT_NAME => Env::get('environment'),
        ])));


        static::$tracerProvider = new TracerProvider(
            new BatchSpanProcessor(
                $exporter,
                ClockFactory::getDefault(),
                2048, //max queue size
                5000, //export timeout
                1024, //max batch size
                true, //auto flush

            ),
            null,
            $resource
        );

        static::$trace = static::$tracerProvider->getTracer($serviceName, '1');
        static::$isClose = false;

        return static::$trace;
    }

    public static function getTraceId()
    {
        return static::$traceId;
    }

    /**
     * @param Span|null $span
     * @return void
     */
    public static function endSpan($span)
    {
        if (!is_null($span)) {
            $span->end();
        }
    }

}
